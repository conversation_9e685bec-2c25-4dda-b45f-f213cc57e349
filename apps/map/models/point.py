"""
the points model
"""
from django.db import models

from apps.user.models.user import Users
from apps.core.models.base import BaseModel


class Point(BaseModel):
    """
    the point model
    """
    class Meta:
        """
        the meta fields
        """
        db_table = 'points'
        ordering = ['-created_at', '-updated_at']

    user = models.OneToOneField(Users, on_delete=models.CASCADE, null=True)
    latitude = models.FloatField()
    longitude = models.FloatField()

    @property
    def name(self):
        """
        the point name
        """
        return "NONEEEE"


def update_or_create_point(user, latitude, longitude):
    """
    Update an existing point or create a new one if it doesn't exist.

    :param user: Users instance
    :param latitude: float
    :param longitude: float
    :return: tuple (Point instance, created boolean)
    """
    point, created = Point.objects.update_or_create(
        user=user,
        defaults={
            'latitude': latitude, 'longitude': longitude
        }
    )
    return point, created
