"""
the job application view
"""
from rest_framework import generics, status
from rest_framework.response import Response

from apps.hr.models.job_application import JobApplication
from apps.hr.serializers.job_application import JobApplicationSerializer
from apps.hr.enums.job_application import StatusEnum


class JobApplicationAPIView(generics.ListCreateAPIView):
    """
    the view for listing and creating job applications
    """
    queryset = JobApplication.objects.all()
    serializer_class = JobApplicationSerializer

    def create(self, request, *args, **kwargs):
        """
        Override create method to handle custom logic if needed
        """
        chat_id = request.data.get("chat_id")
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        if chat_id:
            serializer.save(chat_id=chat_id)
        else:
            serializer.save()

        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )


class JobApplicationDetailAPIView(generics.RetrieveUpdateAPIView):
    """
    the view for job application detail
    """
    serializer_class = JobApplicationSerializer

    def get_queryset(self):
        """
        Restrict Job Application detail to manager users only.
        """
        return JobApplication.objects.all()

    def partial_update(self, request, *args, **kwargs):
        """
        Partial update to validate status and chat_id
        """
        instance = self.get_object()
        upd_status = request.data.get('status')
        chat_id = request.data.get('chat_id')

        if upd_status and upd_status not in [status.value for status in StatusEnum]:
            return Response(
                {"error": "Invalid status"},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = {"status": upd_status} if upd_status else {}
        if chat_id:
            data["chat_id"] = chat_id

        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        self.perform_update(serializer)

        return Response(serializer.data)
