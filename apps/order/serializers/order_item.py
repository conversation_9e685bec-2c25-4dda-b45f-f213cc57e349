"""
the order item serialization
"""
from rest_framework import serializers

from apps.order.models import OrderItem
from apps.product.serializers import ordered_product as ordered_product_serializer


class OrderItemSerializer(serializers.ModelSerializer):
    """
    the order item serialization
    """
    product = ordered_product_serializer.OrderdProductSerializer(required=False)

    class Meta:
        """
        the meta fields
        """
        model = OrderItem
        fields = '__all__'
