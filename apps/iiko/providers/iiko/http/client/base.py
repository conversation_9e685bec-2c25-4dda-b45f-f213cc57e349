"""
init base http client
"""
import logging
import requests

from apps.iiko.providers.iiko.http.exception import IIKOHTTPError
from apps.iiko.providers.iiko.http.exception import Unauthorized
from apps.iiko.providers.iiko.http.response import IikoErrorResponse
from apps.iiko.providers.iiko.http.exception import UserNotFoundException


class BaseHTTPClient:
    """
    Base HTTP Client for making requests to any HTTP API.
    """

    def __init__(self, base_url, log_level=logging.INFO):
        """
        Initializes the BaseHTTPClient with a base URL, sets up a session, and configures logging.

        Args:
            base_url (str): The base URL of the HTTP API.
            log_level (int): The logging level. Default is logging.INFO.
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.logger = self._setup_logger(log_level)

    def _setup_logger(self, log_level):
        """
        Sets up the logger.

        Args:
            log_level (int): The logging level.

        Returns:
            logging.Logger: A configured logger.
        """
        logger = logging.getLogger(__name__)
        return logger

    def request(self, method, endpoint, **kwargs):
        """
        Makes a generic HTTP request.

        Args:
            method (str): The HTTP method (GET, POST, etc.).
            endpoint (str): The API endpoint.
            **kwargs: Additional arguments passed to the request method.

        Returns:
            dict: The response JSON if the request is successful.

        Raises:
            IIKOHTTPError: If an error occurs during the request.
            Unauthorized: If the request is unauthorized
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        self.logger.info("Making %s request to %s with params: %s", method, url, kwargs)
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            self.logger.info("Received response: %s %s", response.status_code, response.reason)
            return response.json()

        except requests.exceptions.RequestException as exc:
            self._handle_error(exc, **kwargs)

    def get(self, endpoint, params=None, headers=None):
        """
        Makes a GET request.
        """
        return self.request('GET', endpoint, params=params, headers=headers)

    def post(self, endpoint, data=None, json=None, headers=None):
        """
        Makes a POST request.
        """
        return self.request('POST', endpoint, data=data, json=json, headers=headers)

    def _handle_error(self, exc, **kwargs):
        """
        Handles request errors and logs appropriate messages.
        """
        if hasattr(exc.response, 'status_code'):
            error = f"Unauthorized error occurred for kwargs: {kwargs} response: {exc.response.__dict__}" # noqa
            if exc.response.status_code == 401:
                raise Unauthorized(error) from exc

            elif exc.response.status_code == 500:
                error = f"Internal server error occurred for kwargs: {kwargs} response: {exc.response}" # noqa
                self.logger.error(error)
                if IikoErrorResponse(**exc.response.json()).is_deleted():
                    raise UserNotFoundException()

            elif exc.response.status_code == 400:
                error = f"Bad request error occurred for kwargs: {kwargs} response: {exc.response.text}" # noqa
                self.logger.error(error)
                raise IIKOHTTPError(error)

        elif isinstance(exc, requests.exceptions.HTTPError):
            error = f"HTTP error occurred for kwargs: {kwargs} response: {exc.response}" # noqa
            self.logger.error(error)
            raise IIKOHTTPError(error)

        elif isinstance(exc, requests.exceptions.ConnectionError):
            error = f"Connection error occurred for kwargs: {kwargs} response: {exc.response}" # noqa
            self.logger.error(error)
            raise IIKOHTTPError(error)

        elif isinstance(exc, requests.exceptions.Timeout):
            error = f"Timeout occurred for kwargs: {kwargs} response: {exc.response}" # noqa
            self.logger.error(error)
            raise IIKOHTTPError(error)

        error = f"Unknown error occurred for kwargs: {kwargs} response: {exc.response.text}" # noqa
        self.logger.error(error)
        raise IIKOHTTPError(error)
