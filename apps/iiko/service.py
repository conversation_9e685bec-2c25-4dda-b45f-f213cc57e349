"""
This module provides a high-level service interface for interacting with the IIKO system.
It encapsulates the complexity of the IIKO API client and offers simplified methods for common operations.
"""

from master_kebab.settings import CITY_ID

from apps.organization.models import Organization
from apps.iiko.providers.iiko.http.client import client


class IIKOService:
    """
    IIKO service class that provides a simplified interface for IIKO-related operations.

    This class acts as a facade for the IIKO API client, offering streamlined methods
    for common tasks such as retrieving street information.
    """

    @staticmethod
    def get_streets(organization_id=None):
        """
        Retrieve street information for a specific organization.

        This method fetches street data for a predefined city (identified by the hardcoded city_id)
        associated with the given organization.

        Args:
            organization_id (str): The unique identifier of the organization.

        Returns:
            ByCityResponseBody: A response object containing the list of streets and correlation ID.

        Note:
            The city_id is currently hardcoded. Consider making this parameter dynamic in future iterations.
        """
        if not organization_id:
            organization_obj = Organization.get_default()
            organization_id = organization_obj.external_id

        return client.address.get_streets_by_city(
            organization_id=organization_id,
            city_id=CITY_ID,
            headers=client.variables.get_headers()
        )

    @staticmethod
    def commands_status(organization_id, correlation_id):
        """
        Retrieve the status of a command.

        This method retrieves the status of a command (e.g., order preparation, delivery)
        associated with the given organization and correlation ID.

        Args:
            organization_id (str): The unique identifier of the organization.
            correlation_id (str): The unique identifier of the command.

        Returns:
            CommandStatus: A response object containing the status of the command.
        """
        return client.commands.status(
            organization_id=organization_id,
            correlation_id=correlation_id,
            headers=client.variables.get_headers()
        )

    @staticmethod
    def check_order(order_data, terminal_group_id, organization_id):
        """
        Check an order before creating it by calculating loyalty discounts and bonuses.

        This method is used by the bot to validate order data and get pricing information
        before actually creating the order in the system.

        Args:
            order_data (dict): The order data in the format expected by iiko's calculate endpoint.
                This should be a dictionary with 'order', 'terminalGroupId', and 'organizationId' keys.
                It may also contain 'coupon' for loyalty programs.
            terminal_group_id (str): The ID of the terminal group where the order will be processed.
                This is used if order_data doesn't contain terminalGroupId.
            organization_id (str): The ID of the organization.
                This is used if order_data doesn't contain organizationId.

        Returns:
            LoyaltyCalculationResponseBody: A response object containing calculated loyalty details,
                                           including discounts, available payments, and any warnings.
        """
        order = order_data.get('order')
        terminal_group_id_param = order_data.get('terminalGroupId', terminal_group_id)
        organization_id_param = order_data.get('organizationId', organization_id)
        is_loyalty_trace_enabled = order_data.get('isLoyaltyTraceEnabled', False)

        # Check if there's a coupon in the order_data
        coupon = order_data.get('coupon')

        # Call the loyalty.calculate method with all parameters
        return client.loyalty.calculate(
            order=order,
            terminal_group_id=terminal_group_id_param,
            organization_id=organization_id_param,
            is_loyalty_trace_enabled=is_loyalty_trace_enabled,
            headers=client.variables.get_headers(),
            coupon=coupon
        )
