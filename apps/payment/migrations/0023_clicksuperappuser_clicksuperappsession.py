# Generated by Django 5.0.6 on 2025-05-16 15:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payment", "0022_invoice"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ClickSuperAppUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("click_user_id", models.CharField(max_length=255, unique=True)),
                (
                    "merchant_user_id",
                    models.CharField(
                        blank=True, max_length=255, null=True, unique=True
                    ),
                ),
                ("phone", models.Char<PERSON>ield(blank=True, max_length=15, null=True)),
                ("last_login", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="click_superapp_user",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "Click SuperApp User",
                "verbose_name_plural": "Click SuperApp Users",
                "db_table": "click_superapp_user",
            },
        ),
        migrations.CreateModel(
            name="ClickSuperAppSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("session_id", models.CharField(max_length=255, unique=True)),
                ("token", models.TextField()),
                ("expires_at", models.DateTimeField()),
                ("is_active", models.BooleanField(default=True)),
                (
                    "click_user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="payment.clicksuperappuser",
                    ),
                ),
            ],
            options={
                "verbose_name": "Click SuperApp Session",
                "verbose_name_plural": "Click SuperApp Sessions",
                "db_table": "click_superapp_session",
            },
        ),
    ]
