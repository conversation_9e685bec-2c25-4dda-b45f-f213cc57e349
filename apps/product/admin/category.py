"""
the product admin page
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.product.models.product import Category


class CategoryUI(ModelAdmin):
    """
    the product admin page
    """
    list_display = ('title', 'title_ru', 'title_uz', 'title_en', 'position_id')
    list_display_links = ('title', )
    list_editable = ('title_ru', 'title_en', 'title_uz', 'position_id',)


admin.site.register(Category, CategoryUI)
