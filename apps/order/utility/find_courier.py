"""
utility functions for finding nearest couriers
"""
from apps.order.models.order import Order
from apps.map.utility.near import haversine
from apps.courier.models.courier import Courier
from apps.user.enum.agent import AgentStatusEnums
from apps.courier.models.orders import MissedOrder
from apps.courier.models.shift import CourierShifts
from apps.order.enums.order import CourierFindingStatus
from apps.order.typing import new_order as new_order_type
from apps.courier.tasks.notify import send_notification_task
from apps.order.tasks.find_courier import reassign_order_if_no_response_task


class FindCourier:
    """
    finding nearest couriers
    """
    def assign_to_next_courier(self, order: Order):
        """
        assign to next courier
        """
        if order.attempt_count >= 10:
            order.mark_as_courier_finding_error(
                log="Exceeded maximum attempt count for finding a courier."
            )
            return None

        next_courier = self.find_next_closest_courier(order)

        if next_courier:
            order.delivery_agent = next_courier
            order.courier_finding_status = CourierFindingStatus.IN_SEARCHING
            order.save()
            self.notify_courier(next_courier, order)
            MissedOrder.update_or_create(
                courier_id=next_courier.id,
                order_id=order.id,
                is_missed=False
            )

        # Schedule a Celery task to check if the order was accepted after 60 seconds
        reassign_order_if_no_response_task.apply_async(
            kwargs={
                "order_id": order.id
            },
            countdown=180  # sec
        )

    def find_next_closest_courier(self, order: Order):
        target_lat = order.organization.latitude
        target_lon = order.organization.longitude

        statuses_to_exclude = [
            AgentStatusEnums.INACTIVE.value,
            AgentStatusEnums.IS_BLOCKED.value,
            AgentStatusEnums.BUSY.value
        ]

        # getting unnotified couriers
        couriers = Courier.objects.exclude(
            id__in=order.sent_to_couriers.values_list('id', flat=True)
        )

        # excluding couriers with using exclude statuses
        couriers = couriers.exclude(
            status__in=statuses_to_exclude,
            is_verified=False,
            is_active=False
        )

        # getting validated couriers
        couriers = couriers.filter(
            is_shift_active=True,
            courierpoint__isnull=False
        ).select_related("courierpoint")

        closest_courier = None
        closest_distance = float('inf')

        for courier in couriers:
            active_shift = CourierShifts.get_active_shift(courier.id)

            if active_shift.organization != order.organization:
                continue  # skipping

            courier_lat, courier_lon = courier.location.latitude, courier.location.longitude
            distance = haversine(target_lat, target_lon, courier_lat, courier_lon)

            if distance < closest_distance:
                closest_distance = distance
                closest_courier = courier

        return closest_courier

    def notify_courier(self, courier: Courier, order: Order):
        order.sent_to_couriers.add(courier)
        message = new_order_type.NewOrderMessage(
            type="new_order_message",
            data=new_order_type.OrderData(
                order_id=order.id,
                order_location=new_order_type.OrderLocation(
                    latitude=order.order_detail.latitude,
                    longitude=order.order_detail.longitude
                ),
                address=order.order_detail.delivery_address,
                client_phone=order.order_detail.phone,
                delivery_cost=order.order_detail.delivery_cost,
                complete_before=order.order_detail.complete_before
            )
        )
        send_notification_task.delay(
            courier_id=courier.id,
            message=message
        )


find_courier_util = FindCourier()
