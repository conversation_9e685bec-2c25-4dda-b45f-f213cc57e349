version: '3.11'

services:
  redis:
    image: redis:latest
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  celery_beat:
    container_name: celery_beat_container
    build:
      context: .
    command: celery -A master_kebab beat -l INFO
    deploy:
      replicas: 1
    depends_on:
      redis:
        condition: service_healthy

  celery_worker:
    container_name: celery_worker_container
    build:
      context: .
    command: celery -A master_kebab worker -Q bot,sms,iiko,iiko_updates,order,user,delivery,map --loglevel=info --concurrency=10 --pool threads
    deploy:
      replicas: 1
    depends_on:
      redis:
        condition: service_healthy

  sms_event_listener:
    container_name: sms_event_listener
    build:
      context: .
    command: python3 manage.py run_sms_service
    deploy:
      replicas: 1

  web_socket:
    container_name: web_socket_container
    build:
      context: .

    # command: daphne -p 8001 -b 0.0.0.0 master_kebab.asgi:application
    # command: uvicorn master_kebab.asgi:application --host 0.0.0.0 --port 8001 --reload
    command: python3 manage.py runwebsocket
    ports:
      - "8001:8001"
    depends_on:
      - redis

  master_kebab:
    container_name: master_kebab_container
    env_file:
      - .env
    build:
      context: .
    command: >
      sh -c "python3 manage.py migrate &&
             python3 manage.py createadmin &&
             python3 manage.py runserver 0.0.0.0:8005"
    volumes:
      - .:/app
    ports:
      - "8000:8005"
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1


volumes:
  redis_data:
