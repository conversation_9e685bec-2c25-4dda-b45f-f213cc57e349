"""
implementation of state tasks
"""
from celery import Task

from master_kebab import celery_app
from apps.courier.helper.notification import delivery_notification_helper


@celery_app.task(bind=True, acks_late=False, task_acks_on_failure_or_timeout=False, serializer="pickle")
def send_notification_task(self: Task, courier_id: str, message: str):
    """
    sending messages to courier
    """
    delivery_notification_helper.send_notification(
        courier_id=courier_id,
        message=message
    )
