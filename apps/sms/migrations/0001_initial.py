# Generated by Django 5.0.6 on 2024-08-20 11:29

import apps.sms.models.verification
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='VerificationCodes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('code', models.IntegerField(editable=False)),
                ('expires_at', models.DateTimeField(default=apps.sms.models.verification.default_expiration)),
                ('is_used', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'verification_codes',
            },
        ),
    ]
