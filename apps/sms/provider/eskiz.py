"""
init eskiz client
"""
from eskiz.client.sync import ClientSync
from eskiz.exception import TokenExpired

from apps.sms.provider.interface import ISMSProvider

from master_kebab.settings import ESKIZ_EMAIL, ESKIZ_PASSWORD


class EskizSMSProvider(ISMSProvider, ClientSync):
    """
    Eskiz SMS provider
    """
    def __init__(self, email, password):
        self.client = ClientSync(email=email, password=password)

    def send_sms(self, phone, message):
        try:
            resp = self.client.send_sms(phone, message)
            return resp
        except TokenExpired:
            self.client.login()
            resp = self.client.send_sms(phone, message)
            return resp
        except Exception as exc:
            if hasattr(exc, 'response') and exc.response is not None:
                error_detail = exc.response.text  # Get JSON or text from Eskiz
                print(f"Eskiz API error details: {error_detail}")
            raise

    def check_balance(self):
        try:
            resp = self.client.get_balance()
        except TokenExpired:
            self.client.login()
            self.client.get_balance()
        return resp


eskiz_sms_provider = EskizSMSProvider(
    email=ESKIZ_EMAIL,
    password=ESKIZ_PASSWORD,
)
