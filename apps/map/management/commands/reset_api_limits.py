"""
Management command to reset daily API limits.

This command should be run daily (e.g., via cron job) to reset the
limit_expired flag for all API keys, allowing them to be used again
for the new day.
"""

from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.map.models.api_keys import MapApiKey


class Command(BaseCommand):
    """
    Django management command to reset daily API limits.
    
    Usage:
        python manage.py reset_api_limits
        
    This command:
    1. Resets the limit_expired flag for all API keys
    2. Logs the number of keys that were reset
    3. Can be safely run multiple times per day
    """
    
    help = 'Reset daily API limits for all map API keys'
    
    def add_arguments(self, parser):
        """Add command arguments."""
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be reset without actually doing it',
        )
        
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about the reset process',
        )
    
    def handle(self, *args, **options):
        """Execute the command."""
        dry_run = options['dry_run']
        verbose = options['verbose']
        
        # Get current date for logging
        current_date = timezone.now().date()
        
        if verbose:
            self.stdout.write(f"Starting API limit reset for date: {current_date}")
        
        # Count keys that need to be reset
        expired_keys = MapApiKey.objects.filter(limit_expired=True)
        expired_count = expired_keys.count()
        
        if expired_count == 0:
            self.stdout.write(
                self.style.SUCCESS("No API keys need to be reset.")
            )
            return
        
        if verbose:
            self.stdout.write(f"Found {expired_count} API keys with expired limits:")
            for key in expired_keys:
                self.stdout.write(f"  - {key.get_app_name_display()} / "
                                f"{key.get_usage_type_display()} "
                                f"(Owner: {key.owner or 'Unknown'})")
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f"DRY RUN: Would reset {expired_count} API keys"
                )
            )
            return
        
        # Reset the limits
        reset_count = MapApiKey.reset_daily_limits()
        
        if reset_count > 0:
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully reset daily limits for {reset_count} API keys"
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("No API keys needed to be reset")
            )
        
        if verbose:
            # Show summary of all API keys
            all_keys = MapApiKey.objects.all()
            total_keys = all_keys.count()
            active_keys = all_keys.filter(limit_expired=False).count()
            
            self.stdout.write(f"\nSummary:")
            self.stdout.write(f"  Total API keys: {total_keys}")
            self.stdout.write(f"  Active keys: {active_keys}")
            self.stdout.write(f"  Expired keys: {total_keys - active_keys}")
            
            # Show breakdown by service type
            yandex_geo_keys = all_keys.filter(
                app_name=MapApiKey.YANDEX_MAP,
                usage_type=MapApiKey.YANDEX_GEOSUGGESTION
            )
            yandex_geo_active = yandex_geo_keys.filter(limit_expired=False).count()
            yandex_geo_total = yandex_geo_keys.count()
            
            self.stdout.write(f"  Yandex Geosuggestion: {yandex_geo_active}/{yandex_geo_total} active")
        
        self.stdout.write(
            self.style.SUCCESS(f"API limit reset completed at {timezone.now()}")
        )
