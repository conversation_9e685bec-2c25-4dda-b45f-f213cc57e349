"""
operations custom managers
"""
from django.contrib.auth.models import BaseUserManager
from django.utils.translation import gettext_lazy as _

from apps.user.enum.role import UserRoleEnums


class OperationsManager(BaseUserManager):
    """
    The operations manager.
    """
    def create_user(self, phone, role, password=None, **extra_fields):
        if not phone:
            raise ValueError(_('The Phone number must be set'))
        if not role:
            raise ValueError(_('The Role must be set'))

        phone = self.normalize_email(phone)
        user = self.model(phone=phone, role=role, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, phone, role='admin', password=None, **extra_fields):
        """
        Create and return a superuser with the given phone, role, and password.
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))

        return self.create_user(phone, role, password, **extra_fields)

    def managers(self):
        return self.filter(role=UserRoleEnums.MANAGERS.value)

    def operators(self):
        return self.filter(role=UserRoleEnums.OPERATOR.value)

    def dispatchers(self):
        return self.filter(role=UserRoleEnums.DISPATCHER.value)

    def marketologs(self):
        return self.filter(role=UserRoleEnums.MARKETOLOG.value)

    def superadmins(self):
        return self.filter(role=UserRoleEnums.ADMIN.value)
