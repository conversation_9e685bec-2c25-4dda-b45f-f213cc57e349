"""
kithcen order update event serialization
"""
from typing import List, Optional
from pydantic import BaseModel


class Product(BaseModel):
    """
    product serialization representation model
    """
    id: str
    name: str


class OrderType(BaseModel):
    """
    order type serialization representation model
    """
    id: str
    name: str
    orderServiceType: str


class Item(BaseModel):
    """
    item serialization representation model
    """
    type: str = "Product"
    amount: float
    product: Product
    modifiers: List[dict] = []
    course: int
    isCookingStarted: bool
    printTime: str
    estimatedCookingBeginTime: str
    processing1BeginTime: Optional[str] = None
    processing2BeginTime: Optional[str] = None
    processing3BeginTime: Optional[str] = None
    processing4BeginTime: Optional[str] = None
    processingCompleteTime: Optional[str] = None
    serveTime: Optional[str] = None
    processingStatus: int
    cookingTime: str
    kitchenSectionId: str
    size: Optional[str] = None
    deleted: bool
    serveGroupNumber: int
    comment: Optional[str] = None
    positionId: str


class KitchenOrder(BaseModel):
    """
    kitchenOrder serialization representation model
    """
    id: str
    number: int
    tableId: str
    # waiter: Optional[str] = None
    items: List[Item]
    posOrderId: str
    isTopCookingPriority: bool
    cookingPriority: int
    terminalGroupId: str
    frontRevision: int
    stornedOrderId: Optional[str] = None
    orderId: Optional[str] = None
    orderType: Optional[OrderType] = None


class EventInfo(BaseModel):
    """
    eventInfo serialization representation model
    """
    id: str
    terminalGroupUocId: str
    organizationId: str
    timestamp: int
    kitchenOrder: KitchenOrder
    isDeleted: bool


class EventBody(BaseModel):
    """
    eventBody serialization representation model
    """
    eventType: str = "KitchenOrderUpdate"
    eventTime: str
    organizationId: str
    correlationId: str
    eventInfo: EventInfo


class KitchenOrderUpdateEvent(BaseModel):
    """
    kitchenOrderUpdateEvent serialization representation model
    """
    event_body: list[EventBody]
