# Generated by Django 5.0.6 on 2025-05-12 21:04

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('loyalty', '0008_update_promocodeusage'),
        ('organization', '0005_organization_region'),
    ]

    operations = [
        migrations.AddField(
            model_name='promo',
            name='is_organization_specific',
            field=models.BooleanField(default=False, help_text='Whether this promo code is specific to certain organizations'),
        ),
        migrations.AddField(
            model_name='promocodeusage',
            name='organization',
            field=models.ForeignKey(blank=True, help_text='Organization where this promo code was used', null=True, on_delete=django.db.models.deletion.SET_NULL, to='organization.organization'),
        ),
        migrations.AddField(
            model_name='promocodeusage',
            name='organization_external_id',
            field=models.CharField(blank=True, help_text='External ID of the organization where this promo code was used', max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name='promocodeusage',
            name='program',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='loyalty.loyaltyprogram'),
        ),
        migrations.CreateModel(
            name='PromoOrganization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allowed_promos', to='organization.organization')),
                ('promo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='allowed_organizations', to='loyalty.promo')),
            ],
            options={
                'unique_together': {('promo', 'organization')},
            },
        ),
    ]
