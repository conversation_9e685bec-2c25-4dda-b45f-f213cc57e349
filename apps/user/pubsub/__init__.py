# pylint: skip-file
# flake8: noqa
# mypy: ignore-errors

import django

from faststream import FastStream
from faststream.redis import RedisBroker
from asgiref.sync import sync_to_async

from apps.user.pubsub import model
from master_kebab.settings import PUB_SUB_BROKER_URL as BROKER_URL

django.setup()

# pylint: disable=C0413
from apps.core.views import ServiceBaseView  # noqa
from apps.user.tasks.create import create_telegram_user_task

broker = RedisBroker(BROKER_URL)
app = FastStream(broker=broker)


@broker.subscriber("user.telegram_create_user")
async def create_user(data: model.CreateClientEventModel):
    """
    Handler for creating a Telegram user.
    """
    try:
        # Use sync_to_async to call Django ORM methods in an async context
        # await sync_to_async(TelegramUser.create, thread_sensitive=True)(
        #     id=data.chat_id,
        #     username=data.username,
        #     first_name=data.first_name,
        #     last_name=data.last_name,
        # )
        create_telegram_user_task.delay(
            chat_id=data.chat_id,
            username=data.username,
            first_name=data.first_name,
            last_name=data.last_name,
        )

    except Exception as exc:
        # Use sync_to_async for synchronous exception handling
        await sync_to_async(ServiceBaseView().call_service_exception, thread_sensitive=True)(
            error_type='internal_error',
            message=f'Failed to create user: {exc} data: {data}',
            status_code=500,
            notify_admin=True,
        )
