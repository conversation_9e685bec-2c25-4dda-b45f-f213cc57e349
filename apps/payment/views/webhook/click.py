import hashlib

from apps.click_up import exceptions
from apps.click_up.views import ClickWebhook
from apps.click_up.models import ClickTransaction
from apps.click_up.typing.request import ClickShopApiRequest

from apps.order.models import Order
from apps.payment.models.method import PaymentMethod
from apps.payment.enums.method import PaymentMethodEnum
from apps.payment.enums.state import TransactionStateEnum
from apps.payment.models.transaction import PaymentTransaction
from apps.payment.models.credentials import ProviderCredentials


# pylint: disable=E1101
class ClickWebhookAPIView(ClickWebhook):
    """
    A view to handle Click Webhook API calls.
    This view will handle all the Click Webhook API events.
    """
    def get_creds(self, params):
        """
        get creds by param
        """
        order = Order.get_by_id(params.merchant_trans_id)
        return ProviderCredentials.get_by_payment_method(
            payment_method=PaymentMethodEnum.CLICK.value,
            organization_id=order.organization_id
        )

    def check_auth(self, params, service_id=None, secret_key=None):
        try:
            creds = self.get_creds(params)
            creds.decrypt_credentials()
        except Exception as exc:
            raise exceptions.AuthFailed("invalid auth") from exc

        text_parts = [
            params.click_trans_id,
            creds.service_id,
            creds.key,
            params.merchant_trans_id,
            params.merchant_prepare_id or "",
            params.amount,
            params.action,
            params.sign_time,
        ]
        text = ''.join(map(str, text_parts))

        calculated_hash = hashlib.md5(text.encode('utf-8')).hexdigest()

        if calculated_hash != params.sign_string:
            raise exceptions.AuthFailed("invalid signature")

    def created_payment(self, params):
        print(params)
        transaction = ClickTransaction.objects.get(
            transaction_id=params.click_trans_id
        )
        order = Order.objects.get(id=transaction.account.id)
        transaction, _ = PaymentTransaction.update_or_create(
            transaction_id=params.click_trans_id,
            amount=params.amount,
            state=TransactionStateEnum.CREATED,
            payment_method=PaymentMethod.get_by_code(
                code=PaymentMethodEnum.CLICK,
                organization_id=order.organization.id
            ),
            order=order
        )

    def successfully_payment(self, params: ClickShopApiRequest):
        """
        successfully payment method process you can ovveride it
        """
        transaction = ClickTransaction.objects.get(
            transaction_id=params.click_trans_id
        )
        order = Order.objects.get(id=transaction.account.id)

        transaction, _ = PaymentTransaction.update_or_create(
            transaction_id=params.click_trans_id,
            amount=params.amount,
            state=TransactionStateEnum.COMPLETED,
            payment_method=PaymentMethod.get_by_code(
                code=PaymentMethodEnum.CLICK,
                organization_id=order.organization.id
            ),
            order=order
        )

        # pylint: disable=C0415
        from apps.payment.service import PaymentService
        PaymentService.mark_as_approved(transaction.order)

    def cancelled_payment(self, params: ClickShopApiRequest):
        """
        cancelled payment method process you can ovveride it
        """
        transaction = ClickTransaction.objects.get(
            transaction_id=params.click_trans_id
        )
        order = Order.objects.get(id=transaction.account.id)

        transaction, _ = PaymentTransaction.update_or_create(
            transaction_id=params.click_trans_id,
            amount=params.amount,
            state=TransactionStateEnum.CANCELLED,
            payment_method=PaymentMethod.get_by_code(
                code=PaymentMethodEnum.CLICK,
                organization_id=order.organization.id
            ),
            order=order
        )
