# Generated by Django 5.0.6 on 2024-08-20 11:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('organization', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CourierPoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('latitude', models.FloatField()),
                ('longitude', models.FloatField()),
            ],
            options={
                'db_table': 'courier_point',
                'ordering': ['-created_at', '-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='CourierShifts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Courier Shift',
                'verbose_name_plural': 'Courier Shifts',
                'db_table': 'courier_shifts',
                'ordering': ['-start_time', '-end_time'],
            },
        ),
        migrations.CreateModel(
            name='FCMDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('token', models.CharField(max_length=255, unique=True)),
            ],
            options={
                'verbose_name': 'FCM device',
                'verbose_name_plural': 'FCM devices',
                'db_table': 'fcm_device',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JWTCourierTokenBlockList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('token', models.CharField(max_length=500, verbose_name='Token')),
            ],
            options={
                'verbose_name': 'JWT Courier Token Block List',
                'verbose_name_plural': 'JWT Courier Token Block Lists',
                'db_table': 'jwt_courier_token_blacklist',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JWTCourierTokenList',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('token', models.CharField(max_length=500, verbose_name='Token')),
            ],
            options={
                'verbose_name': 'JWT Courier Token List',
                'verbose_name_plural': 'JWT Courier Token Lists',
                'db_table': 'jwt_courier_token_list',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Courier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('phone', models.CharField(max_length=15, unique=True)),
                ('name', models.CharField(max_length=255, verbose_name='FIO')),
                ('password', models.CharField(blank=True, max_length=128, null=True)),
                ('passport', models.CharField(blank=True, max_length=15, null=True, verbose_name='Passport')),
                ('address', models.CharField(blank=True, max_length=255, null=True, verbose_name='Address')),
                ('status', models.CharField(choices=[('Available', 'Available'), ('Unavailable', 'Unavailable'), ('OnWay', 'Onway'), ('Waiting', 'Waiting'), ('Delivered', 'Delivered'), ('InWork', 'Inwork'), ('Busy', 'Busy'), ('Arrived', 'Arrived'), ('Inactive', 'Inactive'), ('IsBlocked', 'Isblocked')], default='Available', max_length=50)),
                ('external_id', models.CharField(db_index=True, max_length=255, null=True)),
                ('role', models.CharField(default='courier', max_length=50)),
                ('is_shift_active', models.BooleanField(default=False)),
                ('is_connected', models.BooleanField(default=False)),
                ('is_blocked', models.BooleanField(default=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('is_staff', models.BooleanField(default=False)),
                ('groups', models.ManyToManyField(blank=True, related_name='courier_user_set', to='auth.group', verbose_name='groups')),
                ('organization', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='organization.organization')),
                ('user_permissions', models.ManyToManyField(blank=True, related_name='courier_user_permissions', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'db_table': 'courier',
                'ordering': ['-created_at', '-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='CourierMissedOrders',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_missed', models.BooleanField(default=False)),
                ('courier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='missed_orders', to='courier.courier')),
            ],
            options={
                'verbose_name': 'Courier Missed Orders',
                'verbose_name_plural': 'Courier Missed Orders',
                'db_table': 'courier_missed_orders',
            },
        ),
    ]
