"""
Customer data retrieval for Softphone.Pro integration
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from apps.core.views import ServiceBaseView
from apps.user.models.user import Users
from apps.order.models.order import Order


class CustomerDataAPIView(APIView, ServiceBaseView):
    """
    API view for retrieving customer data based on phone number
    This endpoint is called by Softphone.Pro to get customer information
    """
    
    def get(self, request):
        """
        Retrieve customer data by phone number
        Expected parameters:
        - phone: Customer phone number
        """
        try:
            phone = request.query_params.get('phone')
            
            if not phone:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Phone number is required",
                    status_code=400
                )
            
            # Clean phone number
            clean_phone = phone.replace('+', '')
            
            try:
                customer = Users.objects.get(phone=clean_phone)
                
                # Get customer statistics
                total_orders = Order.objects.filter(user=customer).count()
                completed_orders = Order.objects.filter(
                    user=customer, 
                    status__in=['Delivered', 'Closed']
                ).count()
                
                # Calculate total spent
                total_spent = 0
                orders_with_details = Order.objects.filter(
                    user=customer,
                    order_detail__isnull=False
                ).select_related('order_detail')
                
                for order in orders_with_details:
                    if order.order_detail and order.order_detail.total_cost:
                        total_spent += order.order_detail.total_cost
                
                # Get last order
                last_order = Order.objects.filter(user=customer).order_by('-created_at').first()
                last_order_info = None
                
                if last_order:
                    last_order_info = {
                        "id": last_order.id,
                        "status": last_order.status,
                        "created_at": last_order.created_at.isoformat(),
                        "total_cost": last_order.order_detail.total_cost if last_order.order_detail else 0,
                        "delivery_address": last_order.order_detail.delivery_address if last_order.order_detail else None
                    }
                
                # Prepare customer data
                customer_data = {
                    "found": True,
                    "customer": {
                        "id": customer.id,
                        "name": customer.name,
                        "phone": customer.phone,
                        "email": customer.email,
                        "role": customer.role,
                        "is_verified": customer.is_verified,
                        "lang": customer.lang,
                        "gender": customer.gender,
                        "date_of_birth": customer.date_of_birth.isoformat() if customer.date_of_birth else None,
                        "created_at": customer.created_at.isoformat(),
                        "is_active": customer.is_active
                    },
                    "statistics": {
                        "total_orders": total_orders,
                        "completed_orders": completed_orders,
                        "total_spent": total_spent,
                        "success_rate": (completed_orders / total_orders * 100) if total_orders > 0 else 0
                    },
                    "last_order": last_order_info,
                    "customer_type": self._determine_customer_type(total_orders, total_spent),
                    "actions": {
                        "create_order": f"/admin/order/order/add/?customer_id={customer.id}",
                        "view_profile": f"/admin/user/users/{customer.id}/change/",
                        "view_orders": f"/admin/order/order/?user__id__exact={customer.id}"
                    }
                }
                
                return Response(customer_data, status=status.HTTP_200_OK)
                
            except Users.DoesNotExist:
                # Customer not found
                return Response({
                    "found": False,
                    "phone": clean_phone,
                    "message": "Customer not found in database",
                    "actions": {
                        "create_customer": f"/admin/user/users/add/?phone={clean_phone}",
                        "create_order": f"/admin/order/order/add/?phone={clean_phone}"
                    }
                }, status=status.HTTP_200_OK)
                
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to retrieve customer data: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
    
    def _determine_customer_type(self, total_orders, total_spent):
        """
        Determine customer type based on order history and spending
        """
        if total_orders == 0:
            return "new"
        elif total_orders == 1:
            return "first_time"
        elif total_orders < 5:
            return "occasional"
        elif total_orders < 20:
            return "regular"
        else:
            return "vip"


class CustomerSearchAPIView(APIView, ServiceBaseView):
    """
    API view for searching customers by various criteria
    """
    
    def get(self, request):
        """
        Search customers by phone, name, or email
        Expected parameters:
        - q: Search query
        - type: Search type (phone, name, email, all)
        - limit: Number of results to return (default: 10)
        """
        try:
            query = request.query_params.get('q', '').strip()
            search_type = request.query_params.get('type', 'all')
            limit = int(request.query_params.get('limit', 10))
            
            if not query:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Search query is required",
                    status_code=400
                )
            
            # Build search queryset based on type
            customers = Users.objects.all()
            
            if search_type == 'phone':
                customers = customers.filter(phone__icontains=query)
            elif search_type == 'name':
                customers = customers.filter(name__icontains=query)
            elif search_type == 'email':
                customers = customers.filter(email__icontains=query)
            else:  # search_type == 'all'
                from django.db.models import Q
                customers = customers.filter(
                    Q(phone__icontains=query) |
                    Q(name__icontains=query) |
                    Q(email__icontains=query)
                )
            
            customers = customers[:limit]
            
            # Prepare results
            results = []
            for customer in customers:
                # Get basic stats
                total_orders = Order.objects.filter(user=customer).count()
                
                results.append({
                    "id": customer.id,
                    "name": customer.name,
                    "phone": customer.phone,
                    "email": customer.email,
                    "role": customer.role,
                    "is_verified": customer.is_verified,
                    "total_orders": total_orders,
                    "created_at": customer.created_at.isoformat(),
                    "actions": {
                        "call": f"sip:{customer.phone}",
                        "view_profile": f"/admin/user/users/{customer.id}/change/",
                        "create_order": f"/admin/order/order/add/?customer_id={customer.id}"
                    }
                })
            
            return Response({
                "query": query,
                "search_type": search_type,
                "total_found": len(results),
                "results": results
            }, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to search customers: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
