"""
init order finish view for delivery agents
"""
from django.db import transaction

from rest_framework import views
from rest_framework import response

from apps.order.helper import order_status_helper
from apps.core.exceptions import ServiceAPIException
from apps.core.throttling import OneRequestPerFiveSecondsThrottle


class OrderArrivedAPIView(views.APIView):
    """
    the order finish api view for delivery agents
    """
    throttle_classes = [OneRequestPerFiveSecondsThrottle]

    def post(self, request, *args, **kwargs):
        """
        the post method for finish an order.
        """
        order_id = kwargs.get("order_id")
        delivery_agent_user_id = request.user.id

        if not order_id or not delivery_agent_user_id:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Missing required parameters: order_id, delivery_agent_user_id",
                status_code=400
            )

        try:
            self.arrive_order_without_provider(order_id)

        except Exception as exc:
            msg = f"finish order failed exc: {exc}"
            raise ServiceAPIException(
                error_type="internal_error",
                message=msg
            ) from exc

        return response.Response()

    def arrive_order_without_provider(self, order_id):
        with transaction.atomic():
            order = order_status_helper.get_order_by_id(order_id)
            if not order.is_status_on_way():
                raise ServiceAPIException(
                    error_type="invalid_status",
                    message="Order is not in OnWay status",
                    status_code=400
                )

            order.mark_as_arrived()
            order.delivery_agent.mark_as_arrived()
