"""
Softphone app signals
"""
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone

from apps.softphone.models.call_log import CallLog
from apps.user.models.user import Users
from apps.order.models.order import Order


@receiver(post_save, sender=CallLog)
def associate_call_with_user_and_order(sender, instance, created, **kwargs):
    """
    Automatically associate call log with user and order when created
    """
    if created and instance.caller_id:
        # Try to find user by phone number
        if not instance.user:
            try:
                user = Users.objects.get(phone=instance.caller_id)
                instance.user = user
                
                # Try to find recent order for this user
                if not instance.order:
                    recent_order = Order.objects.filter(
                        user=user,
                        status__in=['Created', 'Approved', 'InProgress', 'WaitCooking', 'CookingStarted'],
                        created_at__gte=timezone.now() - timezone.timedelta(hours=24)
                    ).order_by('-created_at').first()
                    
                    if recent_order:
                        instance.order = recent_order
                
                instance.save(update_fields=['user', 'order'])
                
            except Users.DoesNotExist:
                pass


@receiver(post_save, sender=Order)
def create_call_log_for_order(sender, instance, created, **kwargs):
    """
    Create a call log entry when an order is created via call center
    """
    if created and instance.is_initiator_call_center and instance.user:
        # Check if there's already a call log for this user in the last hour
        recent_call = CallLog.objects.filter(
            caller_id=instance.user.phone,
            start_time__gte=timezone.now() - timezone.timedelta(hours=1)
        ).order_by('-start_time').first()
        
        if recent_call and not recent_call.order:
            # Associate the recent call with this order
            recent_call.order = instance
            recent_call.save(update_fields=['order'])
