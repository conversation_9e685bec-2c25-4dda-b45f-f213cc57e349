"""
the product admin page
"""
from django.contrib import admin

from apps.core import models as core_model
from apps.core.admin.modeladmin import ModelAdmin


class SystemParameterUI(ModelAdmin):
    """
    the sys params admin page
    """
    list_display = [
        "id",
        "name",
        "value",
        "is_enabled",
        "description",
    ]
    list_display_links = list_display


admin.site.register(core_model.SystemParameter, SystemParameterUI)
