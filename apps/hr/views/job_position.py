"""
the job position view
"""
from rest_framework import generics, status
from rest_framework.response import Response

from apps.hr.models.job_position import JobPosition
from apps.hr.serializers.job_position import JobPositionSerializer


class JobPositionAPIView(generics.ListCreateAPIView):
    """
    The Job position retrieve, create view.
    """
    queryset = JobPosition.objects.all()
    serializer_class = JobPositionSerializer

    def create(self, request, *args, **kwargs):
        """
        create method for job position view
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )
