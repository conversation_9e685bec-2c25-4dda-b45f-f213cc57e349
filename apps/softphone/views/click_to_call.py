"""
Click-to-call functionality for Softphone.Pro integration
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from apps.core.views import ServiceBaseView
from apps.user.models.user import Users
from apps.order.models.order import Order


class ClickToCallAPIView(APIView, ServiceBaseView):
    """
    API view for handling click-to-call requests
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        Handle click-to-call requests
        Expected payload:
        {
            "phone_number": "+************",
            "customer_id": 123,
            "order_id": 456,
            "sip_account": "account1",
            "ext_id": "custom_id"
        }
        """
        try:
            phone_number = request.data.get('phone_number')
            customer_id = request.data.get('customer_id')
            order_id = request.data.get('order_id')
            sip_account = request.data.get('sip_account', '1')
            ext_id = request.data.get('ext_id')
            
            if not phone_number:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Phone number is required",
                    status_code=400
                )
            
            # Clean phone number (remove + if present, ensure it starts with 998)
            clean_phone = phone_number.replace('+', '')
            if not clean_phone.startswith('998'):
                clean_phone = '998' + clean_phone
            
            # Build click-to-call URL
            call_url = f"sip:{clean_phone}"
            
            # Add parameters if provided
            params = []
            if sip_account:
                params.append(f"account={sip_account}")
            if ext_id:
                params.append(f"extid={ext_id}")
            elif customer_id:
                params.append(f"extid=customer_{customer_id}")
            elif order_id:
                params.append(f"extid=order_{order_id}")
            
            if params:
                call_url += "?" + "&".join(params)
            
            # Get customer info if customer_id provided
            customer_info = None
            if customer_id:
                try:
                    customer = Users.objects.get(id=customer_id)
                    customer_info = {
                        "id": customer.id,
                        "name": customer.name,
                        "phone": customer.phone,
                        "email": customer.email
                    }
                except Users.DoesNotExist:
                    pass
            
            # Get order info if order_id provided
            order_info = None
            if order_id:
                try:
                    order = Order.objects.get(id=order_id)
                    order_info = {
                        "id": order.id,
                        "status": order.status,
                        "total_cost": order.order_detail.total_cost if order.order_detail else None,
                        "delivery_address": order.order_detail.delivery_address if order.order_detail else None
                    }
                except Order.DoesNotExist:
                    pass
            
            return Response({
                "success": True,
                "call_url": call_url,
                "phone_number": clean_phone,
                "customer_info": customer_info,
                "order_info": order_info,
                "message": "Click-to-call URL generated successfully"
            }, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to generate click-to-call URL: {str(exc)}",
                status_code=500,
                notify_admin=True
            )


class BulkClickToCallAPIView(APIView, ServiceBaseView):
    """
    API view for bulk click-to-call operations (for call center operators)
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        Handle bulk click-to-call requests
        Expected payload:
        {
            "phone_numbers": ["+************", "+************"],
            "sip_account": "account1",
            "delay_seconds": 30
        }
        """
        try:
            phone_numbers = request.data.get('phone_numbers', [])
            sip_account = request.data.get('sip_account', '1')
            delay_seconds = request.data.get('delay_seconds', 0)
            
            if not phone_numbers or not isinstance(phone_numbers, list):
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Phone numbers list is required",
                    status_code=400
                )
            
            call_urls = []
            for i, phone_number in enumerate(phone_numbers):
                # Clean phone number
                clean_phone = phone_number.replace('+', '')
                if not clean_phone.startswith('998'):
                    clean_phone = '998' + clean_phone
                
                # Build call URL with delay
                call_url = f"sip:{clean_phone}"
                params = [f"account={sip_account}"]
                
                if delay_seconds > 0:
                    total_delay = delay_seconds * i
                    params.append(f"delay={total_delay}")
                
                call_url += "?" + "&".join(params)
                call_urls.append({
                    "phone": clean_phone,
                    "url": call_url,
                    "delay": delay_seconds * i if delay_seconds > 0 else 0
                })
            
            return Response({
                "success": True,
                "call_urls": call_urls,
                "total_numbers": len(phone_numbers),
                "message": "Bulk click-to-call URLs generated successfully"
            }, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to generate bulk click-to-call URLs: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
