"""
Telegram bot message sender task
"""
import logging
from typing import Optional

from celery import Task

from master_kebab import celery_app
from apps.notify.service import notification_service
from apps.bot.services.storage_service import StorageService
from apps.bot.services.telegram_service import TelegramService


logger = logging.getLogger(__name__)


@celery_app.task(
    bind=True,
    max_retries=3,
    default_retry_delay=5,
    queue='bot',
    name='apps.bot.tasks.send_bot_message.send_bot_message_task'
)
def send_bot_message_task(
    self: Task,
    chat_id: str,
    title: Optional[str] = None,
    message: Optional[str] = None,
    media_path: Optional[str] = None,
    is_last: bool = False,
    notification_id: Optional[str] = None
) -> None:
    """
    Send message to Telegram chat
    """
    message_id = None
    try:
        if self.request.retries == 0:
            notification_service.create_notification_accepter(
                notification_id=notification_id,
                user_id=chat_id,
                user_type="telegram",
                status="processing",
            )

        message_id = _send_message(chat_id, title, message, media_path, notification_id)
        logger.info(f"Successfully sent message to chat {chat_id} with message_id {message_id}")
        if is_last and media_path:
            StorageService.delete_file(media_path)
        notification_service.set_notification_identity(
            notification_id=notification_id,
            user_id=chat_id,
            notification_identity=message_id,
            status="sent"
        )

    except Exception as exc:
        if self.request.retries >= self.max_retries:
            if is_last and media_path:
                StorageService.cleanup_file(media_path, "max retries")

            notification_service.set_notification_identity(
                notification_id=notification_id,
                user_id=chat_id,
                notification_identity=None,
                status="failed",
                error_message=str(exc)
            )
            logger.error(f"Unexpected error sending message to chat {chat_id}: {str(exc)}")

        raise self.retry(exc=exc)


def _send_message(
    chat_id: str, title: Optional[str], message: Optional[str],
    media_path: Optional[str], notification_id: Optional[str]
) -> None:
    """
    Send message to Telegram chat and follow up with menu button message

    Args:
        chat_id: Telegram chat ID
        message: Optional text message
        media_path: Optional media file path
    """
    message_id = None

    if media_path and isinstance(media_path, str):
        try:
            media_url = StorageService.get_file_url(media_path)
            # Determine media type based on file extension
            if media_path.lower().endswith(('.jpg', '.jpeg', '.png', '.gif')):
                message_id = TelegramService.send_photo_message(
                    chat_id, media_url, title, message, notification_id
                )
            elif media_path.lower().endswith(('.mp4', '.mov', '.avi', '.webm')):
                message_id = TelegramService.send_video_message(
                    chat_id, media_url, title, message, notification_id
                )
            else:
                raise ValueError(f"Unsupported media type for file: {media_path}")
        except Exception as e:
            logger.error(f"Failed to process file at path {media_path}: {str(e)}")
            raise

    elif message:
        message_id = TelegramService.send_text_message(chat_id, title, message, notification_id)



    return message_id
