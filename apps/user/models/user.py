"""
the user model
"""
import random
import string
import datetime

from django.db import models
from django.http.request import HttpRequest
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin

from apps.core.models.base import BaseModel
from apps.user.enum.role import UserRoleEnums
from apps.user.manager.user import UserManager
from apps.core.enums.initiator import Initiator as InitiatorEnum
from apps.core.enums.lang import LanguageEnum


class Users(BaseModel, AbstractBaseUser, PermissionsMixin):
    """
    Custom user model that extends AbstractBaseUser and PermissionsMixin.
    Uses phone number as the unique identifier instead of username
    """
    class Meta:
        """
        the meta fields
        """
        verbose_name = "user"
        db_table = 'users'

    GENDER_CHOICES = (
        ('M', 'Male'),
        ('F', 'Female'),
    )

    phone = models.CharField(max_length=15, unique=True)
    role = models.CharField(max_length=50, default="client")
    name = models.CharField(max_length=255, default="MasterKebab Client", blank=True, null=True)
    email = models.EmailField(max_length=250, null=True, blank=True)
    gender = models.CharField(choices=GENDER_CHOICES, null=True, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)
    lang = models.CharField(
        max_length=10,
        default="uz",
        choices=LanguageEnum.choices(),
    )
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)
    external_id = models.CharField(max_length=255, null=True, db_index=True)

    objects = UserManager()

    USERNAME_FIELD = 'phone'
    REQUIRED_FIELDS = ['role']

    def __str__(self):
        """
        Return a string representation of the user.
        """
        return str(self.phone)

    def is_operator(self) -> bool:
        """
        check if the user is an operator
        """
        return self.role == UserRoleEnums.OPERATOR.value

    @classmethod
    def get_by_id(cls, id):
        """
        get user by id
        """
        return cls.objects.get(id=id)

    @staticmethod
    def generate_random_password(length=8):
        """
        generate a random password
        """
        characters = string.ascii_uppercase + string.digits
        password = ''.join(random.choice(characters) for _ in range(length))
        return password

    @classmethod
    def get_or_create_by_phone(cls, phone):
        """
        get or create user by phone
        """
        user = None

        if phone:
            user, _ = cls.objects.get_or_create(phone=phone)

        return user

    @classmethod
    def check_user_exsist(cls, phone):
        """
        check if the user exists
        """
        return cls.objects.filter(phone=phone, is_active=True)

    @classmethod
    def get_by_phone(cls, phone):
        """
        get user by phone
        """
        return cls.objects.get(phone=phone)

    def set_user_lang(self, lang):
        """
        settings user language
        """
        self.lang = lang
        self.save()

    def age(self):
        """
        Calculate and return the customer's current age.
        """
        today = datetime.date.today()
        age = today.year - self.date_of_birth.year
        if (today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day):
            age -= 1
        return age


def detect_initiator(request: HttpRequest) -> InitiatorEnum:
    """
    Detect the initiator of the request based on the `X-Initiator` header.

    This function retrieves the `X-Initiator` header from the incoming request and
    attempts to match its value with a valid entry in `InitiatorEnum`. If no `X-Initiator`
    header is found or if the value does not correspond to a valid `InitiatorEnum`, it raises
    a `ValueError` as part of Python's Enum handling.

    Args:
        request (HttpRequest): The incoming HTTP request.

    Returns:
        InitiatorEnum: The corresponding initiator from the `InitiatorEnum`, based on the `X-Initiator` header.

    Raises:
        ValueError: If the header is not found or its value doesn't match any valid `InitiatorEnum`.
    """
    initiator = request.headers.get('X-Initiator')

    if not initiator:
        raise ValueError("No X-Initiator header found in the request.")

    return InitiatorEnum(initiator.lower())
