# Generated by Django 5.0.6 on 2025-02-06 11:46

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hr", "0011_jobposition_working_hours_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="WorkingHours",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("hours", models.Char<PERSON>ield(max_length=100)),
                ("hours_uz", models.CharField(blank=True, max_length=100, null=True)),
                ("hours_ru", models.CharField(blank=True, max_length=100, null=True)),
                ("hours_en", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.RemoveField(
            model_name="jobposition",
            name="working_hours_en",
        ),
        migrations.RemoveField(
            model_name="jobposition",
            name="working_hours_ru",
        ),
        migrations.RemoveField(
            model_name="jobposition",
            name="working_hours_uz",
        ),
        migrations.RemoveField(
            model_name="jobposition",
            name="working_hours",
        ),
        migrations.AddField(
            model_name="jobposition",
            name="working_hours",
            field=models.ManyToManyField(
                blank=True, related_name="job_positions", to="hr.workinghours"
            ),
        ),
    ]
