"""
Service layer for loyalty functionality
"""
import logging
from typing import <PERSON><PERSON>, Dict, Any, Optional

from django.db.models import QuerySet
from django.core.exceptions import ValidationError
from django.core.cache import cache

from apps.bot.models.telegram import TelegramUser
from apps.loyalty.models import Promo, PromoCodeUsage
from apps.core.exceptions.service import PromoCodeAlreadyExists
from apps.core.enums.initiator import Initiator
from apps.organization.models import Organization

logger = logging.getLogger(__name__)


class LoyaltyService:
    """
    Loyalty service interfaces for promo code management and validation
    """
    # Cache key prefix for active promo codes
    PROMO_CACHE_KEY_PREFIX = "active_promo_code"
    # Cache timeout for promo codes (3 minutes)
    PROMO_CACHE_TIMEOUT = 60 * 3

    @classmethod
    def apply_promo(
        cls,
        user_id: int,
        promo_name: str,
        organization_id: str = None,
        initiator: str = Initiator.BOT.value
    ) -> <PERSON><PERSON>[PromoCodeUsage, bool]:
        """
        Apply promo code for user

        Args:
            user_id: User ID
            promo_name: Promo code name
            organization_id: Organization external ID
            initiator: Order initiator type (default: BOT)

        Returns:
            Tuple[PromoCodeUsage, bool]: Created PromoCodeUsage instance and
            boolean indicating if it was created

        Raises:
            ValidationError: If promo is invalid or user already has program
        """
        try:
            promo = Promo.get_by_name(promo_name)

            # Check if program is active
            if not promo.program.is_active:
                raise ValidationError("Promo code program is not active")

            # Check program date validity
            if not promo.program.is_valid_date():
                raise ValidationError("Promo code has expired")

            # Check initiator restrictions if set
            if initiator and not promo.program.is_initiator_allowed(initiator):
                raise ValidationError(
                    "Promo code cannot be used with this initiator"
                )

            # Check if promo is valid for this organization
            if organization_id and hasattr(promo, 'is_organization_specific'):
                if promo.is_organization_specific and not promo.is_valid_for_organization(organization_id):
                    raise ValidationError(
                        f"Promo code {promo_name} is not valid for this organization"
                    )

            # Check if user already has this program
            if PromoCodeUsage.user_has_program(user_id, promo.program_id):
                promo_usage = PromoCodeUsage.objects.filter(
                    user_id=user_id,
                    program_id=promo.program_id
                ).first()

                if promo_usage and promo_usage.is_used:
                    raise PromoCodeAlreadyExists(
                        "You have already used this promo code"
                    )

                # User has the promo but hasn't used it yet
                # Update organization information if provided
                if organization_id:
                    try:
                        organization = Organization.objects.get(external_id=organization_id)
                        promo_usage.organization = organization
                    except Organization.DoesNotExist:
                        logger.warning(f"Organization with external_id {organization_id} not found")

                    promo_usage.organization_external_id = organization_id
                    promo_usage.save(update_fields=['organization', 'organization_external_id'])

                return promo_usage, False

            # Create new promo usage with organization information if provided
            create_data = {
                'user_id': user_id,
                'promo': promo,
                'program': promo.program,
                'is_used': False
            }

            # Add organization data if provided
            if organization_id:
                create_data['organization_external_id'] = organization_id
                try:
                    organization = Organization.objects.get(external_id=organization_id)
                    create_data['organization'] = organization
                except Organization.DoesNotExist:
                    logger.warning(f"Organization with external_id {organization_id} not found")

            promo_usage = PromoCodeUsage.objects.create(**create_data)
            return promo_usage, True

        except Promo.DoesNotExist as exc:
            raise ValidationError("Invalid promo code") from exc
        except (ValidationError, PromoCodeAlreadyExists) as exc:
            # Re-raise these exceptions without wrapping
            raise exc
        except Exception as e:
            logger.error("Error applying promo: %s", e)
            raise ValidationError("Error applying promo code") from e

    @classmethod
    def apply_promo_for_telegram_user(
        cls,
        chat_id: int,
        promo_name: str,
        organization_id: str
    ) -> Tuple[PromoCodeUsage, bool]:
        """
        Apply promo code for Telegram user

        Args:
            chat_id: Telegram chat ID
            promo_name: Promo code name
            organization_id: Organization external ID

        Returns:
            Tuple[PromoCodeUsage, bool]: Created PromoCodeUsage instance and
            boolean indicating if it was created

        Raises:
            ValidationError: If promo is invalid, user not found, or user
            already has program
        """
        try:
            # Get Telegram user by chat_id
            telegram_user = TelegramUser.get_by_chat_id(chat_id)

            # Check if Telegram user has an associated user account
            if not telegram_user.user:
                raise ValidationError(
                    "Telegram user does not have an associated user account"
                )

            # Apply promo for the associated user
            return cls.apply_promo(
                user_id=telegram_user.user.id,
                promo_name=promo_name,
                organization_id=organization_id,
                initiator=Initiator.BOT.value
            )

        except TelegramUser.DoesNotExist as exc:
            logger.error("Telegram user with chat_id %s not found", chat_id)
            raise ValidationError(
                f"Telegram user with chat_id {chat_id} not found"
            ) from exc
        except (ValidationError, PromoCodeAlreadyExists) as exc:
            # Re-raise these exceptions without wrapping
            raise exc
        except Exception as e:
            logger.error("Error applying promo for Telegram user: %s", e)
            raise ValidationError("Error applying promo for Telegram user") from e

    @classmethod
    def get_active_promos(cls, user_id: int) -> QuerySet[PromoCodeUsage]:
        """
        Get all active promos for the user

        Args:
            user_id: User ID

        Returns:
            QuerySet: QuerySet of active PromoCodeUsage instances
        """
        return PromoCodeUsage.objects.filter(
            user_id=user_id,
            is_used=False
        ).select_related('promo', 'program')

    @classmethod
    def get_active_promos_for_telegram_user(
        cls,
        chat_id: int
    ) -> QuerySet[PromoCodeUsage]:
        """
        Get all active promos for the Telegram user

        Args:
            chat_id: Telegram chat ID

        Returns:
            QuerySet: QuerySet of active PromoCodeUsage instances

        Raises:
            ValidationError: If Telegram user not found or has no associated
            user account
        """
        try:
            # Get Telegram user by chat_id
            telegram_user = TelegramUser.get_by_chat_id(chat_id)

            # Check if Telegram user has an associated user account
            if not telegram_user.user:
                raise ValidationError(
                    "Telegram user does not have an associated user account"
                )

            # Get active promos for the associated user
            return cls.get_active_promos(telegram_user.user.id)

        except TelegramUser.DoesNotExist as exc:
            logger.error("Telegram user with chat_id %s not found", chat_id)
            raise ValidationError(
                f"Telegram user with chat_id {chat_id} not found"
            ) from exc
        except ValidationError:
            # Re-raise without wrapping
            raise
        except Exception as e:
            logger.error("Error getting active promos for Telegram user: %s", e)
            raise ValidationError("Error getting active promos")

    @classmethod
    def validate_promo_code(
        cls,
        promo_name: str,
        user_id: int = None,
        order_amount: float = 0,
        initiator: str = None,
        organization_id: str = None,
        items_cost: float = None
    ) -> Dict[str, Any]:
        """
        Validate a promo code without applying it

        Args:
            promo_name: Promo code name
            user_id: User ID (optional)
            order_amount: Total order amount (optional)
            initiator: Order initiator type (optional)
            organization_id: Organization external ID (optional)
            items_cost: Cost of items only, excluding delivery (optional)

        Returns:
            Dict with validation result and promo details if valid
        """
        result = {
            "is_valid": False,
            "promo": None,
            "discount_amount": 0,
            "message": ""
        }

        try:
            # Get promo by name
            promo = Promo.get_by_name(promo_name)

            # Check if program is active and valid date
            if not promo.program.is_active:
                result["message"] = "Promo code program is not active"
                result["error_type"] = "inactive_program"
                return result

            # Check program date validity
            if not promo.program.is_valid_date():
                result["message"] = "Promo code has expired"
                result["error_type"] = "expired_promo"
                return result

            # Check initiator restrictions if set
            if initiator and not promo.program.is_initiator_allowed(initiator):
                result["message"] = (
                    "Promo code cannot be used with this initiator"
                )
                result["error_type"] = "invalid_initiator"
                return result

            # Check if promo is valid for this organization
            if organization_id and hasattr(promo, 'is_organization_specific'):
                if promo.is_organization_specific and not promo.is_valid_for_organization(organization_id):
                    result["message"] = (
                        f"Promo code {promo_name} is not valid for this organization"
                    )
                    result["error_type"] = "invalid_organization"
                    return result

            # Check if user already used this program (if user_id provided)
            if user_id and PromoCodeUsage.user_has_program(user_id, promo.program_id):
                promo_usage = PromoCodeUsage.objects.filter(
                    user_id=user_id,
                    program_id=promo.program_id
                ).first()

                if promo_usage and promo_usage.is_used:
                    result["message"] = "You have already used this promo code"
                    result["error_type"] = "promo_code_already_exists"
                    return result

            # Check minimum order amount (if order_amount provided)
            if (order_amount > 0 and promo.min_order_amount > 0 and
                    order_amount < promo.min_order_amount):
                result["message"] = (
                    f"Minimum order amount for this promo code is "
                    f"{promo.min_order_amount}"
                )
                result["error_type"] = "minimum_order_amount"
                return result

            # Calculate discount amount (if order_amount provided)
            discount_amount = 0
            if order_amount > 0:
                if promo.promocode_type == Promo.AMOUNT:
                    discount_amount = promo.promo_amount
                elif promo.promocode_type == Promo.PERCENT:
                    # For percentage-based promos, apply discount only to items cost (not delivery)
                    if items_cost is not None:
                        discount_amount = (promo.promo_amount / 100) * items_cost
                        logger.info(
                            "Validation: Applied %s%% discount to items cost %s = %s",
                            promo.promo_amount, items_cost, discount_amount
                        )
                    else:
                        # Fallback to total order amount if items_cost not provided
                        discount_amount = (promo.promo_amount / 100) * order_amount
                        logger.info(
                            "Validation: Applied %s%% discount to total order amount %s = %s",
                            promo.promo_amount, order_amount, discount_amount
                        )

                # Ensure discount doesn't exceed order amount
                discount_amount = min(discount_amount, order_amount)

            # Set success result
            result["is_valid"] = True
            result["promo"] = promo
            result["discount_amount"] = discount_amount
            result["message"] = "Promo code is valid"

            # Cache the promo code for 3 minutes if user_id is provided
            if user_id:
                cls.cache_promo_code(user_id, promo_name)
                logger.info(f"Cached promo code {promo_name} for user {user_id} during validation")

            return result

        except Promo.DoesNotExist:
            result["message"] = "Invalid promo code"
            result["error_type"] = "invalid_promo_code"
            return result
        except Exception as e:
            logger.error("Error validating promo code: %s", e)
            result["message"] = "Error validating promo code"
            result["error_type"] = "system_error"
            return result

    @classmethod
    def apply_promo_to_order(
        cls,
        user_id: int,
        promo_id: int,
        order_id: int,
        order_amount: float,
        discount_amount: float,
        organization_id: str = None
    ) -> bool:
        """
        Apply a promo code to an order and record usage

        Args:
            user_id: User ID
            promo_id: Promo ID
            order_id: Order ID
            order_amount: Total order amount before discount
            discount_amount: Discount amount applied
            organization_id: Organization external ID (optional)

        Returns:
            bool: True if successfully applied
        """
        try:
            # Get promo usage
            promo_usage = PromoCodeUsage.objects.filter(
                user_id=user_id,
                promo_id=promo_id,
                is_used=False
            ).first()

            if not promo_usage:
                # Create a new record if none exists
                PromoCodeUsage.record_usage(
                    user_id=user_id,
                    promo_id=promo_id,
                    order_id=order_id,
                    discount_amount=discount_amount,
                    order_total=order_amount,
                    organization_id=organization_id
                )
            else:
                # Mark existing record as used
                promo_usage.mark_as_used(
                    order_id=order_id,
                    discount_amount=discount_amount,
                    order_total=order_amount,
                    organization_id=organization_id
                )

            # Clear the cached promo code when it's used
            cls.clear_cached_promo_code(user_id)

            return True

        except Exception as e:
            logger.error("Error applying promo to order: %s", e)
            return False

    @classmethod
    def cache_promo_code(cls, user_id: int, promo_name: str) -> None:
        """
        Cache a promo code for a user for 3 minutes

        Args:
            user_id: User ID
            promo_name: Promo code name
        """
        cache_key = f"{cls.PROMO_CACHE_KEY_PREFIX}:{user_id}"
        cache.set(cache_key, promo_name, timeout=cls.PROMO_CACHE_TIMEOUT)
        logger.info(f"Cached promo code {promo_name} for user {user_id} for 3 minutes, key:{cache_key}")

    @classmethod
    def get_cached_promo_code(cls, user_id: int) -> Optional[str]:
        """
        Get cached promo code for a user

        Args:
            user_id: User ID

        Returns:
            str: Cached promo code name or None if not found
        """
        cache_key = f"{cls.PROMO_CACHE_KEY_PREFIX}:{user_id}"
        promo_name = cache.get(cache_key)
        if promo_name:
            logger.info(f"Found cached promo code {promo_name} for user {user_id}")
        return promo_name

    @classmethod
    def clear_cached_promo_code(cls, user_id: int) -> None:
        """
        Clear cached promo code for a user

        Args:
            user_id: User ID
        """
        cache_key = f"{cls.PROMO_CACHE_KEY_PREFIX}:{user_id}"
        cache.delete(cache_key)
        logger.info(f"Cleared cached promo code for user {user_id}")

    @classmethod
    def revert_promo_code_for_order(cls, order_id: int) -> bool:
        """
        Revert promo code usage for a canceled order

        Args:
            order_id: The ID of the order to revert promo code usage for

        Returns:
            bool: True if a promo code was found and reverted, False otherwise
        """
        return PromoCodeUsage.revert_usage_for_order(order_id)

    @classmethod
    def apply_promo_code_during_order_creation(
        cls,
        user_id: int,
        promo_code: str,
        order_id: int,
        order_amount: float,
        items_cost: float = None,
        organization_id: str = None
    ) -> tuple[bool, float, Promo]:
        """
        Apply a promo code during order creation

        Args:
            user_id: User ID
            promo_code: Promo code name
            order_id: Order ID
            order_amount: Total order amount before discount
            items_cost: Cost of items only (excluding delivery cost)
            organization_id: Organization external ID (optional)

        Returns:
            tuple: (success, discount_amount, promo_object)
                - success: True if promo was successfully applied
                - discount_amount: Amount of discount applied
                - promo_object: The Promo object that was applied
        """
        try:
            # Get the promo by name
            promo = Promo.get_by_name(promo_code)

            # Validate promo code
            if promo.is_organization_specific and organization_id and organization_id != promo.organization_id:
                logger.warning(
                    "Promo code %s is organization-specific but doesn't match order organization",
                    promo_code
                )
                return False, 0, None

            # Check minimum order amount
            if order_amount < promo.min_order_amount:
                logger.warning(
                    "Order amount %s is less than minimum required %s for promo code %s",
                    order_amount, promo.min_order_amount, promo_code
                )
                return False, 0, None

            # Calculate discount amount
            discount_amount = promo.promo_amount
            if promo.promocode_type == Promo.PERCENT:
                # For percentage-based promos, apply discount only to items cost (not delivery)
                if items_cost is not None:
                    discount_amount = (promo.promo_amount / 100) * items_cost
                    logger.info(
                        "Applied %s%% discount to items cost %s = %s",
                        promo.promo_amount, items_cost, discount_amount
                    )
                else:
                    # Fallback to total order amount if items_cost not provided
                    discount_amount = (promo.promo_amount / 100) * order_amount
                    logger.info(
                        "Applied %s%% discount to total order amount %s = %s",
                        promo.promo_amount, order_amount, discount_amount
                    )

            # Ensure discount doesn't exceed order amount
            discount_amount = min(discount_amount, order_amount)

            # Apply promo to order
            success = cls.apply_promo_to_order(
                user_id=user_id,
                promo_id=promo.id,
                order_id=order_id,
                order_amount=order_amount,
                discount_amount=discount_amount,
                organization_id=organization_id
            )

            if success:
                # Clear the cached promo code if it exists
                cls.clear_cached_promo_code(user_id)
                return True, discount_amount, promo
            return False, 0, None

        except Promo.DoesNotExist:
            logger.warning("Invalid promo code: %s", promo_code)
            return False, 0, None
        except Exception as e:
            logger.error("Error applying promo code during order creation: %s", e)
            return False, 0, None
