"""
init customer active orders
"""
from rest_framework import views
from rest_framework import response

from apps.core.exceptions import ServiceAPIException
from apps.order.serializers.order import OrderSerializer
from apps.order.helper.order_status import order_status_helper


class ActiveOrderAPIView(views.APIView):
    """
    the customer active orders api view
    """
    def get(self, request, *args, **kwargs):
        """
        the get method for getting active orders for a customer.
        """
        user_id = kwargs.get("pk")
        user_type = kwargs.get("user_type")

        if not user_id or not user_type:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Missing required parameters: (user_id, user_type)",
                status_code=400
            )

        if user_type not in [
            "customer",
            "delivery-agent",
        ]:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Invalid user type. Expected 'customer' or 'delivery-agent'",
                status_code=400
            )

        try:
            order = order_status_helper.get_user_active_order(user_id, user_type)

        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_server",
                message=str(exc),
                status_code=500
            ) from exc

        if order is None:
            raise ServiceAPIException(
                error_type="not_found",
                message=f"No active orders found for user {user_id} of type {user_type}",
                status_code=404,
            )

        return response.Response({
            "result": OrderSerializer(order, many=True).data
        })
