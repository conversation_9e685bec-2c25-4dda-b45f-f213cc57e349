"""
init iiko nomenclature class
"""
import logging

from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http import exception as iiko_exception
from apps.iiko.providers.iiko.http.request import delivery as delivery_request
from apps.iiko.providers.iiko.http.response import delivery as delivery_response


logger = logging.getLogger(__name__)


class IIKODeliveryAPI:
    """
    IIKO getting nomenclature API client
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def order_create_without_ghost(
        self,
        organization_id: str,
        terminal_group_id: str,
        order: delivery_request.Order,
        headers: dict
    ) -> delivery_response.OrderCreateWithoutGhostResponsetBody:
        """
        Based on the Organization ID you received above, request a menu.
        """
        endpoint = "/api/1/deliveries/create"

        request_body = delivery_request.OrderCreateWithoutGhostRequestBody(
            organizationId=organization_id,
            terminalGroupId=terminal_group_id,
            order=order
        )
        request_body = request_body.model_dump_json()
        logger.info(f"sending to create a new order to iiko: {request_body}")
        response_body = self.http.post(endpoint, json=request_body, headers=headers)

        return delivery_response.OrderCreateWithoutGhostResponsetBody(**response_body)

    def confirm(
        self,
        order_id,
        organization_id: str,
        headers: dict
    ) -> delivery_response.ConfirmResponseBody:
        """
        confirm order
        """
        endpoint = "/api/1/deliveries/confirm"
        request_body = delivery_request.ConfirmRequestBody(
            organization_id=organization_id,
            order_id=order_id,
        )
        request_body = request_body.as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)

        return delivery_response.ConfirmResponseBody(**response_body)

    def update_order_courier(
        self,
        order_id,
        organization_id: str,
        employee_id: str,
        headers: dict
    ) -> delivery_response.UpdateOrderCourierResponseBody:
        """
        binding courier for given order
        """
        endpoint = "/api/1/deliveries/update_order_courier"
        request_body = delivery_request.OpdateOrderCourierRequestBody(
            organization_id=organization_id,
            order_id=order_id,
            employee_id=employee_id,
        )
        request_body = request_body.as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        response_body = delivery_response.UpdateOrderCourierResponseBody(**response_body)

        if response_body.errorDescription is not None:
            raise iiko_exception.UpdateOrderCourierResponseBody(response_body.errorDescription)

        return response_body

    def update_order_delivery_status(
        self,
        organization_id: str,
        order_id: str,
        delivery_status: str,
        headers: dict,
    ) -> delivery_response.UpdateOrderDeliveryStatusResponseBody:
        """
        update delivery status
        """
        endpoint = "/api/1/deliveries/update_order_delivery_status"
        request_body = delivery_request.UpdateOrderDeliveryStatusRequestBody(
            organization_id=organization_id,
            order_id=order_id,
            delivery_status=delivery_status,
        )

        request_body = request_body.as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        return delivery_response.UpdateOrderDeliveryStatusResponseBody(**response_body)

    def cancel_confirmation(
        self,
        order_id: str,
        organization_id: str,
        headers: dict
    ) -> delivery_response.CancelConfirmationResponseBody:
        """
        cancel confirm order
        """
        endpoint = "/api/1/deliveries/cancel_confirmation"
        request_body = delivery_request.CancelConfirmationRequestBody(
            organization_id=organization_id,
            order_id=order_id,
        )

        request_body = request_body.as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        return delivery_response.CancelConfirmationResponseBody(**response_body)

    def delivery_restrictions(self, organizations_ids, headers) -> delivery_response.DeliveryRestrictionsResponseBody:
        """
        get delivery restrictions for given organizations
        """
        endpoint = "/api/1/delivery_restrictions"

        request_body = delivery_request.DeliveryRestrictionsRequestBody(
            organizationIds=organizations_ids
        )
        request_body = request_body.as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        return delivery_response.DeliveryRestrictionsResponseBody(**response_body)


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
delivery = IIKODeliveryAPI(http=http_base_client)
