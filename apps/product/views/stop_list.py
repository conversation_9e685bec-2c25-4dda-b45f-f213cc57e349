"""
the stoplist apiview
"""
from rest_framework import views
from rest_framework import response

from apps.organization.models import Organization
from apps.organization.models import TerminalGroup

from apps.core.exceptions import ServiceAPIException
from apps.iiko.providers.iiko.http.client import client


class StopListAPIView(views.APIView):
    """
    the stoplist apiview
    """
    def get(self, request, *args, **kwargs):
        """
        the get method for getting stoplist.
        """
        stoplist = []  # replace with actual stoplist data

        organization_id = request.query_params.get('organization_id', None)

        if not organization_id:
            raise ServiceAPIException(
                error_type="bad_request",
                message="No query parameter provided for organization_id"
            )

        try:
            organization = Organization.get_by_id(organization_id)

        except Organization.DoesNotExist as exc:
            raise ServiceAPIException(
                error_type="not_found",
                message="Organization not found",
                status_code=404,
            ) from exc

        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_server_error",
                message=f"An error occurred: {exc}",
                status_code=500,
            ) from exc

        try:
            terminal_group = TerminalGroup.get_active_terminal_group(
                organization_id=organization.id
            )

        except TerminalGroup.DoesNotExist as exc:
            raise ServiceAPIException(
                error_type="not_found",
                message="Terminal group not found",
                status_code=404,
            ) from exc

        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_server_error",
                message=f"An error occurred: {exc}",
                status_code=500,
            ) from exc

        try:
            stoplist = client.menu.stop_lists(
                organization_ids=[organization.external_id],
                terminal_ids=[terminal_group.external_id],
                headers=client.variables.get_headers()
            )

        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_server_error",
                message=f"An error occurred while getting stoplist: {exc}",
                status_code=500,
            ) from exc

        return response.Response(stoplist)
