"""
implementation of state tasks
"""
from celery import Task

from master_kebab import celery_app

from apps.core import core
from apps.order.models import Order
from apps.sms.service import SMSService
from apps.payment.service import PaymentService
from apps.payment.enums.method import PaymentMethodEnum
from apps.payment.providers.client import ProviderFactory


@celery_app.task(bind=True, acks_late=False, task_acks_on_failure_or_timeout=False)
def update_payment_methods(self: Task):
    """
    update payment methods backgroud tasks
    """
    core.log("info", "updating payment methods")

    try:
        PaymentService.update_payment_methods()
    except Exception as exc:
        core.log("error", f"Error updating payment methods: {exc}")


@celery_app.task(bind=True)
def send_invoice(self, order_id):
    """
    sending invoice to client task
    """
    order = Order.get_by_id(order_id)

    if order.is_payme_order:
        provider = ProviderFactory.create_provider(
            provider_type=PaymentMethodEnum.PAYME,
            organization_id=order.organization_id
        )
        provider.send_invoice(
            order_id=order_id,
            amount=order.total_cost,
            phone=order.payment_phone
        )
        return

    payment_link = PaymentService.create_payment(order_id)
    message = f"Masterkebab filialida {order_id} raqamli buyurtma uchun to'lov havolasi {payment_link}"
    SMSService.send_sms(phone=order.payment_phone, message=message)
