"""
IIKO Loyalty API client
"""
from typing import Dict, Optional

from master_kebab import settings
from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.response import LoyaltyCalculationResponseBody # noqa


class IIKOLoyaltyAPI:
    """
    IIKO Loyalty API client for customer management
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def calculate(
        self,
        order: Dict,
        terminal_group_id: str,
        organization_id: str,
        is_loyalty_trace_enabled: bool = False,
        headers: Optional[Dict] = None,
        coupon: Optional[str] = None
    ) -> LoyaltyCalculationResponseBody:
        """
        Calculate loyalty discounts and bonuses for an order

        Args:
            order: Order details dictionary
            terminal_group_id: Terminal group ID
            organization_id: Organization ID
            is_loyalty_trace_enabled: Enable loyalty trace flag
            headers: Optional request headers
            coupon: Optional coupon code for loyalty program

        Returns:
            LoyaltyCalculationResponseBody: Response containing calculated loyalty details
        """
        endpoint = "/api/1/loyalty/iiko/calculate"

        request_body = {
            "order": order,
            "terminalGroupId": terminal_group_id,
            "organizationId": organization_id,
            "isLoyaltyTraceEnabled": is_loyalty_trace_enabled
        }

        if coupon:
            request_body["coupon"] = coupon
            request_body["order"]["loyaltyInfo"] = {}
            request_body["order"]["loyaltyInfo"]["coupon"] = coupon
            print(f"Adding coupon {coupon} to IIKO loyalty calculate request")

        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        print(f"IIKO Loyalty Calculate Response: {response_body}")
        return LoyaltyCalculationResponseBody(**response_body)


# Initialize the client
http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
loyalty = IIKOLoyaltyAPI(http=http_base_client)
