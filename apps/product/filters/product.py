"""
the getting products by given filtersets
"""
from django_filters import rest_framework as filters

from apps.product.models.product import Product


class ProductFilter(filters.FilterSet):
    """
    the product filter
    """
    category_id = filters.NumberFilter(field_name='category_id')
    title = filters.CharFilter(field_name='title', lookup_expr='icontains')

    class Meta:
        """
        the meta fields
        """
        model = Product
        fields = ['category', 'title', 'product_type', 'external_id']
