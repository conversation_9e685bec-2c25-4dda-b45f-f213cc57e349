"""
the map api keys module
"""
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.map.service import MapService
from apps.core.views import ServiceBaseView


class MapAPIKeysAPIView(APIView, ServiceBaseView):
    """
    Retrieve the API keys for the map service
    """
    def get(self, request):
        api_key_obj = MapService.get_active_api_key()

        if not api_key_obj:
            raise self.call_service_exception(
                error_type="not_found",
                message="No active API Key found",
                status_code=404
            )

        return Response({
            "api_key": api_key_obj.api_key,
            "app_name": api_key_obj.app_name,
        })

    def post(self, request):
        api_key = request.data.get("api_key")

        if not api_key:
            raise self.call_service_exception(
                error_type="bad_request",
                message="API Key is required",
                status_code=400
            )

        new_api_key = MapService.mark_as_expired_api_key(api_key)

        if not new_api_key:
            raise self.call_service_exception(
                error_type="not_found",
                message="No active API Key found",
                status_code=404
            )

        return Response({
            "api_key": new_api_key.api_key,
            "app_name": new_api_key.app_name,
            "message": "API Key successfully updated"
        })
