"""
user's admin interface
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.courier.models.courier import Courier


class CourierUI(ModelAdmin):
    """
    the user admin page
    """
    list_display = [
        "id",
        "name",
        "phone",
        "status",
        "is_shift_active",
        "updated_at",
    ]


admin.site.register(Courier, CourierUI)
