"""
point serialization
"""
from rest_framework import serializers

from apps.map.models.point import Point
from apps.map.utility.status import is_within_circle


class PointSerializer(serializers.ModelSerializer):
    """
    point serialization
    """
    class Meta:
        """
        the meta fields
        """
        model = Point
        fields = ['user', 'latitude', 'longitude']

    def validate(self, attrs):
        """
        validate the fields
        """
        if not is_within_circle(
            lat_point=attrs['latitude'],
            lon_point=attrs['longitude']
        ):
            raise serializers.ValidationError(
                'User is not within the circle Navoi'
            )

        return super().validate(attrs)
