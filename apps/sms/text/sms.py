"""
init sms text template module
"""
CREDENTIALS = {
    "en": {
        "message": "Your credentials for Master <PERSON><PERSON><PERSON> delivery agent\nLogin: {phone}\nPassword: {password}"
    },
    "uz": {
        "message": "Master <PERSON><PERSON><PERSON> yet<PERSON><PERSON><PERSON> berish agenti uchun hisob ma'lumotlaringiz\nLogin: {phone}\nParol: {password}"
    },
    "ru": {
        "message": "Ваши учетные данные для агента по доставке Master Kebab\nЛогин: {phone}\nПароль: {password}"
    }
}


def get_credentials_text(phone, password, lang) -> str:
    """
    Get delivery agent credentials
    """
    # Use the default language as 'en' if the provided language is not found
    credentials = CREDENTIALS.get(lang, CREDENTIALS["en"])

    # Format the message with the provided phone and password
    formatted_message = credentials["message"].format(phone=phone, password=password)

    return formatted_message
