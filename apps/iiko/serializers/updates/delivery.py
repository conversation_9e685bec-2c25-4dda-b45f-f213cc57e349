"""
delivery update serialization
"""
from datetime import datetime
from typing import List, Optional

from pydantic import BaseModel


class Product(BaseModel):
    """
    product represents a product
    """
    id: str
    name: str


class Item(BaseModel):
    """
    item represents an item
    """
    type: str
    product: Product
    modifiers: List = []
    price: float
    cost: float
    pricePredefined: bool
    positionId: str
    taxPercent: Optional[float]
    resultSum: float
    status: str
    # deleted: Optional[bool]
    amount: float
    comment: Optional[str]
    whenPrinted: Optional[datetime]
    comboInformation: Optional[str]


class PaymentType(BaseModel):
    """
    paymentType represents a payment type
    """
    id: str
    name: str
    kind: str


class Payment(BaseModel):
    """
    payment represents a payment
    """
    paymentType: PaymentType
    sum: float
    isPreliminary: bool
    isExternal: bool
    isProcessedExternally: bool
    isFiscalizedExternally: bool
    isPrepay: bool


class GuestsInfo(BaseModel):
    """
    the guests info object representing model
    """
    count: int
    splitBetweenPersons: bool


class OrderType(BaseModel):
    """
    order type representing model
    """
    id: str
    name: str
    orderServiceType: str


class Courier(BaseModel):
    """
    the Courier object representing
    """
    id: str
    name: str
    phone: Optional[str] = None


class CourierInfo(BaseModel):
    """
    The CourierInfo model representing the courier information.
    """
    courier: Courier | None
    isCourierSelectedManually: bool

    def is_without_courier(self) -> bool:
        return self is None


class Problem(BaseModel):
    """
    the Problem object representing model
    """
    hasProblem: bool
    description: str


class Order(BaseModel):
    """
    order representing model
    """
    parentDeliveryId: Optional[str]
    customer: Optional[str]
    phone: str
    deliveryPoint: Optional[dict]
    status: str
    # cancelInfo: Optional[str]
    completeBefore: datetime
    whenCreated: datetime
    whenConfirmed: Optional[datetime]
    whenPrinted: Optional[datetime]
    whenCookingCompleted: Optional[datetime]
    whenSended: Optional[datetime]
    whenDelivered: Optional[datetime]
    comment: Optional[str]
    # operator: Optional[str]
    marketingSource: Optional[str]
    deliveryDuration: int
    indexInCourierRoute: Optional[int]
    cookingStartTime: datetime
    isDeleted: bool
    whenReceivedByApi: datetime
    whenReceivedFromFront: datetime
    movedFromDeliveryId: Optional[str]
    movedFromTerminalGroupId: Optional[str]
    movedFromOrganizationId: Optional[str]
    externalCourierService: Optional[str]
    movedToDeliveryId: Optional[str]
    movedToTerminalGroupId: Optional[str]
    movedToOrganizationId: Optional[str]
    menuId: Optional[str]
    deliveryZone: Optional[str]
    sum: float
    number: int
    sourceKey: str
    whenBillPrinted: Optional[datetime]
    whenClosed: Optional[datetime]
    conception: Optional[str]
    guestsInfo: GuestsInfo
    items: List[Item]
    combos: List = []
    payments: List[Payment]
    tips: List = []
    discounts: List = []
    orderType: OrderType
    terminalGroupId: str
    processedPaymentsSum: float
    loyaltyInfo: dict
    externalData: Optional[str]
    courierInfo: Optional[CourierInfo] = None
    problem: Optional[Problem] = None


class EventInfo(BaseModel):
    """
    the event info object representing model
    """
    id: str
    posId: str
    externalNumber: Optional[str]
    organizationId: str
    timestamp: int
    creationStatus: str
    errorInfo: Optional[str]
    order: Order
    number: Optional[int] = None


class EventBody(BaseModel):
    """
    the event body object representing model
    """
    eventType: str
    eventTime: datetime
    organizationId: str
    correlationId: str
    eventInfo: EventInfo


class DeliveryOrderUpdateEvent(BaseModel):
    """
    the delivery order update event object representing model
    """
    event_body: list[EventBody]
