"""
init login view
"""
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken

from apps.core import exceptions
from apps.user.models import Users
from apps.user.enum.role import UserRoleEnums
from apps.core.exceptions import ServiceAPIException
from apps.user.serializers.token import GetTokenSerializer


class GetTokenView(APIView):
    """
    Verify phone and password view for authentication
    """
    def post(self, request):
        """
        Verify phone and password, allows only POST requests
        """
        serializer = GetTokenSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data
        phone = data.get('phone')
        password = data.get('password')

        try:
            user = Users.get_by_phone(phone)

            if user.role not in UserRoleEnums.allowed_values():
                raise exceptions.ServiceAPIException(
                    error_type="bad_request",
                    message="invalid role",
                    status_code=400
                )

        except Users.DoesNotExist as exc:
            raise exceptions.ServiceAPIException(
                error_type="user_not_found",
                message=f"user does not exist phone: {phone}",
                status_code=404
            ) from exc

        if not user.check_password(password):
            raise exceptions.ServiceAPIException(
                error_type="bad_request",
                message="invalid password",
                status_code=400
            )

        user.is_verified = True
        user.save()

        refresh = RefreshToken.for_user(user)

        return Response({
            'refresh': str(refresh),
            'access': str(refresh.access_token),
            'role': str(user.role),
            "id": str(user.id),
        }, status=status.HTTP_200_OK)


class LogoutView(APIView):
    """
    Logout view for removing refresh token
    """
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response(status=status.HTTP_205_RESET_CONTENT)

        except Exception as exc:
            raise ServiceAPIException(
                error_type="bad_request",
                message=f"Invalid refresh token: {str(exc)}"
            ) from exc


class DeleteAccountView(APIView):
    """
    Delete account view
    """
    permission_classes = (IsAuthenticated,)

    def post(self, request):
        user = request.user
        user.delete()

        return Response()
