"""
This module provides services related to product operations.
"""

import logging

from django.core.cache import cache
from django.db import transaction

from apps.core import models as core_models
from apps.core.enums import SystemParamsEnum
from apps.core.views import ServiceBaseView
from apps.iiko.providers.iiko.http.client import client
from apps.iiko.providers.iiko.http.response import MenuByIdResponseBody
from apps.organization.models import Organization
from apps.product.models.category import Category
from apps.product.models.product import (
    build_attribute,
    update_or_create_attributes,
    update_or_create_labels,
    update_products,
)
from apps.product.utils.updaters import ModifierUpdater
from master_kebab import settings

logger = logging.getLogger(__name__)


class ProductService(ServiceBaseView):
    """
    Service class for handling product-related operations.
    """

    @transaction.atomic
    def update_products_and_categories(self, is_manual_update=False):
        """
        Updating categories and products polling
        """

        sys_param_key = SystemParamsEnum.IIKO

        if not core_models.SystemParameter.get_sys_params(sys_param_key).is_enabled or not is_manual_update:
            return

        headers = client.variables.get_headers()

        organization_ids = []
        for organization in Organization.get_active_organizations():
            organization_ids.append(
                organization.external_id
            )

        response = client.menu.by_id(
            organization_ids=organization_ids,
            menu_id=settings.MENU_ID,
            headers=headers
        )
        try:
            ProductService.__update_categories(response)
            ProductService.__update_products(response)
            ProductService.__update_modifiers(response)

        except Exception as exc:
            message = f"Failed to update products and categories: {exc}"

            raise self.call_service_exception(
                error_type="internal_server_error",
                message=message,
                notify_admin=True,
            )

        for key in cache.keys('views.decorators.cache.cache_header.products.*'):
            cache.delete(key)

    @staticmethod
    def __update_modifiers(response: MenuByIdResponseBody):
        """
        Update modifiers based on the provided response.

        Parameters:
        - response: MenuByIdResponseBody instance containing modifier data.
        """
        try:
            updater = ModifierUpdater(response)
            updater.update_modifiers()
        except Exception as exc:
            logger.error(f"Failed to update modifiers: {exc}")

    @staticmethod
    def __update_categories(response: MenuByIdResponseBody):
        categories = []
        categories_resp = response.itemCategories

        for category in categories_resp:
            category_obj = Category.build_category(
                title=category.name,
                external_id=category.id
            )
            categories.append(category_obj)

        Category.update_categories(categories=categories)

    @staticmethod
    def __update_products(response: MenuByIdResponseBody) -> None:
        """
        Update products based on the provided response.

        Parameters:
        - response: NomenclatureResponseBody instance containing product data.
        """
        categories = response.itemCategories
        categories_objs = Category.get_all_categories()
        category_map = {str(category.external_id): category for category in categories_objs}

        for category in categories:
            for product in category.items:

                if product.isHidden:
                    # If product is Hidden, we should not update
                    continue

                item_sizes = product.itemSizes
                attributes = []

                for item_size in item_sizes:
                    product_name = product.name
                    product_description = product.description

                    for item in item_size.prices:
                        size_name = item_size.sizeName or "Default"

                        attribute = build_attribute(
                            size=size_name,
                            size_code=item_size.sizeCode,
                            size_id=item_size.sizeId,
                            sku=item_size.sku,
                            is_default=item_size.isDefault or False,
                            measure_unit_type=item_size.measureUnitType,
                            price=item.price,
                            description=product_name,
                            organization_external_id=item.organizationId,
                            external_id=product.itemId,
                        )
                        attributes.append(attribute)

                category_obj = category_map.get(category.id)
                if category_obj:
                    defaults = {
                        'title': product.name,
                        'category': category_obj,
                        "description": product_description,
                        'product_type': product.type,
                    }
                    product_instance = update_products(
                        external_id=product.itemId,
                        defaults=defaults
                    )

                    update_or_create_attributes(
                        product=product_instance,
                        attributes=attributes,
                    )
                    update_or_create_labels(product_instance, product.labels)
