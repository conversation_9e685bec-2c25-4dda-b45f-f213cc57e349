"""
The organization model defines the structure and behaviors of an organization.
It includes methods for retrieving, updating, and performing operations on
organizations such as finding the nearest open organization within a radius.
"""

from django.db import models
from django.core.cache import cache
from django.utils import timezone

from apps.map.typing import UserLocation
from apps.core.models.base import BaseModel
from apps.map.utility.near import find_nearest_locations


class Organization(BaseModel):
    """
    The Organization model represents various organizations with attributes
    such as name, location (latitude and longitude), operation hours, and
    other relevant details.
    """
    REGION_CHOICES = [
        ('N', 'Navoi'),
        ('B', 'Bukhoro')
    ]

    name = models.CharField(max_length=255, unique=True)
    external_id = models.CharField(unique=True, db_index=True)
    code = models.CharField(max_length=255, null=True, blank=True)
    address = models.CharField(max_length=255, null=True, blank=True)
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    in_use = models.BooleanField(default=False)
    is_alive = models.BooleanField(default=False)
    is_default = models.BooleanField(default=False)
    delivery_duration = models.IntegerField(null=True, blank=True)
    self_service_duration = models.IntegerField(null=True, blank=True)
    default_min_sum = models.IntegerField(null=True, blank=True)
    is_delivery_available = models.BooleanField(default=True)
    is_pickup_available = models.BooleanField(default=True)
    region = models.CharField(
        max_length=2,
        choices=REGION_CHOICES,
        default="N",
        verbose_name="Region"
    )
    opens_at = models.TimeField(null=True, blank=True, verbose_name="Opening Time")
    closes_at = models.TimeField(null=True, blank=True, verbose_name="Closing Time")

    class Meta:
        """
        The meta fields for the Organization model.
        """
        db_table = 'organization'

    def __str__(self):
        """
        Returns the string representation of the organization, which is its name.
        """
        return str(self.name)

    @property
    def is_closed(self):
        """
        Dynamically determines if the organization is currently closed based on
        its opening and closing times.

        Returns:
            bool: True if the organization is closed, False otherwise.
        """
        now = timezone.now().time()

        if self.opens_at is None or self.closes_at is None:
            return True

        if self.opens_at < self.closes_at:
            return not (self.opens_at <= now <= self.closes_at)

        # Overnight case: opens in the evening, closes the next day.
        return not (now >= self.opens_at or now <= self.closes_at)

    @classmethod
    def update_restrictions(
        cls,
        organization_id: str,
        delivery_duration: int,
        self_service_duration: int,
        default_min_sum: int
    ):
        """
        Updates delivery and self-service restrictions for an organization.

        Args:
            organization_id (str): The external ID of the organization.
            delivery_duration (int): The updated delivery duration in minutes.
            self_service_duration (int): The updated self-service duration in minutes.
            default_min_sum (int): The updated minimum sum for services.

        Returns:
            None
        """
        organization = cls.objects.get(external_id=organization_id)
        organization.delivery_duration = delivery_duration
        organization.self_service_duration = self_service_duration
        organization.default_min_sum = default_min_sum
        organization.save()

    @classmethod
    def update_or_create_organization(
        cls,
        name: str,
        external_id: str,
        code: str = None
    ) -> "Organization":
        """
        Updates an existing organization or creates a new one based on external ID.

        Args:
            name (str): The name of the organization.
            external_id (str): The unique external ID of the organization.
            code (str, optional): An optional code for the organization.

        Returns:
            Organization: The updated or newly created organization instance.
        """
        organization, _ = cls.objects.update_or_create(
            external_id=external_id,
            defaults={'name': name, 'code': code}
        )
        return organization

    @classmethod
    def get_active_organizations(cls, region=None):
        """
        Retrieve the list of currently active (open) organizations, cached for 15 minutes.
        If a region is provided, filter by the region; otherwise, return all active organizations.
        """
        cache_key = f'organization_list_{region}' if region else 'organization_list_all'
        organizations = cache.get(cache_key)
        if not organizations:
            filters = {'in_use': True}
            if region:
                filters['region'] = region
            organizations = list(cls.objects.filter(**filters))

            cache.set(cache_key, organizations, 15 * 60)

        return organizations

    @classmethod
    def update_cache(cls):
        """
        Updates the cache for active organizations.
        """
        cache_key = 'organization_list'
        organizations = list(cls.objects.filter(
            in_use=True
        ))
        cache.set(cache_key, organizations, 15 * 60)

    @classmethod
    def get_by_external_id(cls, external_id: str) -> "Organization":
        """
        Retrieves an organization by its external ID.

        Args:
            external_id (str): The unique external ID of the organization.

        Returns:
            Organization: The organization instance matching the external ID.
        """
        return cls.objects.get(external_id=external_id)

    @classmethod
    def get_by_id(cls, id: int) -> "Organization":
        """
        Retrieves an organization by its internal ID.

        Args:
            id (int): The internal ID of the organization.

        Returns:
            Organization: The organization instance matching the ID.
        """
        return cls.objects.get(id=id)

    @classmethod
    def find_nearest_organization(
        cls,
        latitude: float,
        longitude: float,
        radius: float
    ) -> tuple["Organization", float]:
        """
        Retrieves the nearest organization within a specified radius.
        Prioritizes open organizations; if none are open, returns the nearest closed organization.

        Args:
            latitude (float): The latitude of the reference point.
            longitude (float): The longitude of the reference point.
            radius (float): The radius within which to search for organizations.

        Returns:
            tuple: A tuple containing the nearest organization (open or closed)
                and the distance to it. Returns (None, None) if no organizations
                are found within the radius.
        """
        organizations = cls.objects.filter(
            latitude__isnull=False,
            longitude__isnull=False,
        )

        locations = [
            UserLocation(
                user_id=org.id,
                name=org.name,
                latitude=org.latitude,
                longitude=org.longitude,
            )
            for org in organizations
        ]

        nearest_locations = find_nearest_locations(latitude, longitude, locations, radius)

        if not nearest_locations:
            return None, None

        nearest_open_org = None
        nearest_closed_org = None
        nearest_distance = None

        for location, dist in nearest_locations:
            org = next((o for o in organizations if o.id == location.user_id), None)
            if org and not org.is_closed:
                nearest_open_org = org
                nearest_distance = dist
                break
            if org and nearest_closed_org is None:
                nearest_closed_org = org
                nearest_distance = dist

        return (nearest_open_org or nearest_closed_org, nearest_distance)

    @classmethod
    def get_in_use_all(cls):
        """
        Retrieves all organizations that are marked as in use.

        Returns:
            QuerySet: A queryset of organizations that are currently in use.
        """
        return cls.objects.filter(in_use=True)

    @classmethod
    def get_default(cls) -> "Organization":
        """
        Retrieves the default organization.

        Returns:
            Organization: The default organization, or None if no default organization exists.
        """
        return cls.objects.filter(is_default=True).first()

    def activate(self):
        """
        Marks the organization as alive (active).

        Returns:
            None
        """
        self.is_alive = True
        self.save()

    def deactivate(self):
        """
        Marks the organization as in-alive (inactive).

        Returns:
            None
        """
        self.is_alive = False
        self.save()

    def update_organization(self, **kwargs):
        """
        Update the organization's details with the provided keyword arguments.

        Args:
            **kwargs: Key-value pairs of fields to update.

        Returns:
            Organization: The updated organization instance.
        """
        for field, value in kwargs.items():
            if hasattr(self, field):
                setattr(self, field, value)
        self.save()
        return self
