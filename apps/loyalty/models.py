"""
Models for the loyalty app
"""
import logging

from django.db import models
from django.utils import timezone
from django.core.cache import cache

from apps.user.models import Users
from apps.organization.models import Organization


logger = logging.getLogger(__name__)


class LoyaltyProgram(models.Model):
    """
    Loyalty program model for managing promotional campaigns
    """
    name = models.CharField(max_length=100)
    service_from = models.DateField()
    service_to = models.DateField()
    is_active = models.BooleanField(default=True)
    program_type = models.IntegerField()
    refill_type = models.IntegerField()
    external_id = models.CharField(max_length=100, unique=True)
    # Initiator choices based on the Initiator enum
    INITIATOR_CHOICES = [
        ('bot', 'Bot'),
        ('ios', 'iOS'),
        ('web', 'Web'),
        ('android', 'Android'),
        ('call-center', 'Call Center'),
        ('unknown', 'Unknown'),
    ]

    # Legacy field - kept for backward compatibility
    initiator = models.Char<PERSON><PERSON>(
        max_length=20,
        choices=INITIATOR_CHOICES,
        blank=True,
        null=True,
        help_text="Legacy field - use LoyaltyProgramInitiator instead"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.name)

    def is_valid_date(self):
        """
        Check if the program is valid based on service dates
        """
        today = timezone.now().date()
        return self.service_from <= today <= self.service_to

    def get_allowed_initiators(self):
        """
        Get a list of allowed initiators for this program

        Returns:
            list: List of initiator values allowed for this program
        """
        # First check if we have any initiators in the new model
        initiators = list(self.initiators.values_list('initiator', flat=True))

        # If we have initiators in the new model, return those
        if initiators:
            return initiators

        # Otherwise, fall back to the legacy field if it's set
        if self.initiator:
            return [self.initiator]

        # If no initiators are set, allow all initiators
        return []

    def is_initiator_allowed(self, initiator_value):
        """
        Check if the given initiator is allowed for this program

        Args:
            initiator_value: The initiator value to check

        Returns:
            bool: True if the initiator is allowed, False otherwise
        """
        allowed_initiators = self.get_allowed_initiators()

        # If no initiators are set, allow all initiators
        if not allowed_initiators:
            return True

        return initiator_value in allowed_initiators


class LoyaltyProgramInitiator(models.Model):
    """
    Model to track which initiators are allowed for a loyalty program
    """
    program = models.ForeignKey(
        LoyaltyProgram,
        on_delete=models.CASCADE,
        related_name='initiators'
    )
    initiator = models.CharField(
        max_length=20,
        choices=LoyaltyProgram.INITIATOR_CHOICES,
        help_text="Initiator allowed to use this program"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        """
        Meta options for LoyaltyProgramInitiator model
        """
        unique_together = [('program', 'initiator')]
        verbose_name = "Loyalty Program Initiator"
        verbose_name_plural = "Loyalty Program Initiators"

    def __str__(self):
        return f"{self.program.name} - {self.initiator}"


class Promo(models.Model):
    """
    Promo code model for discounts and promotions
    """
    # Promo code type choices
    AMOUNT = 'amount'
    PERCENT = 'percent'
    PROMO_TYPE_CHOICES = [
        (AMOUNT, 'Fixed Amount'),
        (PERCENT, 'Percentage'),
    ]

    name = models.CharField(max_length=8)
    referal_id = models.CharField(max_length=100, unique=True)
    program = models.ForeignKey(LoyaltyProgram, on_delete=models.CASCADE)
    promo_amount = models.FloatField(
        default=0,
        help_text="Discount amount applied by the promo code"
    )
    promocode_type = models.CharField(
        max_length=10,
        choices=PROMO_TYPE_CHOICES,
        default=AMOUNT,
        help_text="Type of discount: fixed amount or percentage"
    )
    min_order_amount = models.FloatField(
        default=0,
        help_text="Minimum order amount required to use this promo code"
    )
    is_organization_specific = models.BooleanField(
        default=False,
        help_text="Whether this promo code is specific to certain organizations"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.program.name}"

    @classmethod
    def get_by_name(cls, name: str) -> "Promo":
        """
        Get promo by name with related program
        """
        return cls.objects.select_related('program').get(name=name)

    def is_valid_for_organization(self, organization_id: str) -> bool:
        """
        Check if this promo code is valid for the specified organization

        Args:
            organization_id: Organization external ID to check

        Returns:
            bool: True if valid for this organization, False otherwise
        """
        # If not organization-specific, valid for all organizations
        if not self.is_organization_specific:
            return True

        # Check if this organization is in the allowed list
        return PromoOrganization.objects.filter(
            promo=self,
            organization__external_id=organization_id
        ).exists()


class PromoOrganization(models.Model):
    """
    Model to track which organizations a promo code can be used with
    """
    promo = models.ForeignKey(
        Promo,
        on_delete=models.CASCADE,
        related_name='allowed_organizations'
    )
    organization = models.ForeignKey(
        Organization,
        on_delete=models.CASCADE,
        related_name='allowed_promos'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        """
        Meta options for PromoOrganization model
        """
        unique_together = [('promo', 'organization')]

    def __str__(self):
        return f"{self.promo.name} - {self.organization.name}"


class PromoCodeUsage(models.Model):
    """
    Model to track promo code usage and assignment
    """
    # Cache key prefix for active promo codes - must match LoyaltyService.PROMO_CACHE_KEY_PREFIX
    PROMO_CACHE_KEY_PREFIX = "active_promo_code"
    user = models.ForeignKey(Users, on_delete=models.CASCADE)
    promo = models.ForeignKey(Promo, on_delete=models.CASCADE)
    program = models.ForeignKey(LoyaltyProgram, on_delete=models.CASCADE, null=True, blank=True)
    organization = models.ForeignKey(
        Organization,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Organization where this promo code was used"
    )
    organization_external_id = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        help_text="External ID of the organization where this promo code was used"
    )
    order_id = models.IntegerField(
        null=True,
        blank=True,
        help_text="ID of the order where promo was used"
    )
    is_used = models.BooleanField(default=False)
    discount_amount = models.FloatField(
        default=0,
        help_text="Actual discount amount applied to the order"
    )
    order_total = models.FloatField(
        default=0,
        help_text="Total order amount before discount"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        """
        Meta options for PromoCodeUsage model
        """
        unique_together = [('user', 'program')]

    def __str__(self):
        status = "Used" if self.is_used else "Active"
        if self.order_id:
            return (f"{self.user.phone} - {self.promo.name} - "
                    f"Order #{self.order_id} - {status}")
        return f"{self.user.phone} - {self.promo.name} - {status}"

    @classmethod
    def user_has_program(cls, user_id: int, program_id: int) -> bool:
        """
        Check if a user already has a specific loyalty program

        Args:
            user_id: User ID to check
            program_id: Program ID to check

        Returns:
            bool: True if user has the program, False otherwise
        """
        return cls.objects.filter(
            user_id=user_id, program_id=program_id
        ).exists()

    @classmethod
    def has_active_loyalty_program(cls, user_id):
        """
        Check if a user has an active (unused) loyalty program.
        Only checks the cache for a recently applied promo code.

        Args:
            user_id: The ID of the user to check.

        Returns:
            tuple: (promo_usage, external_id) where promo_usage is the
                  PromoCodeUsage object if found, and external_id is the
                  user's external ID in IIKO if available.
        """
        try:
            # Check if there's a cached promo code for this user
            cache_key = f"{cls.PROMO_CACHE_KEY_PREFIX}:{user_id}"
            cached_promo_name = cache.get(cache_key)

            if cached_promo_name:
                logger.info(f"Found cached promo code {cached_promo_name} for user {user_id}")
                # Look up the promo code in the database
                try:
                    promo = Promo.get_by_name(cached_promo_name)
                    promo_usage = cls.objects.filter(
                        user_id=user_id,
                        promo=promo,
                        is_used=False
                    ).select_related('promo', 'program').first()

                    if promo_usage:
                        external_id = promo_usage.user.external_id
                        return promo_usage, external_id
                except Exception as e:
                    logger.error(f"Error retrieving cached promo code: {e}")

            # If no cached promo, return None
            return None, None

        except Exception as e:
            logger.error("Failed to check active loyalty program: %s", e)
            return None, None

    def mark_as_used(self, order_id=None, discount_amount=0, order_total=0, organization_id=None):
        """
        Mark this promo code as used for an order

        Args:
            order_id: Order ID (optional)
            discount_amount: Discount amount applied (optional)
            order_total: Total order amount before discount (optional)
            organization_id: Organization external ID (optional)
        """
        self.is_used = True
        if order_id:
            self.order_id = order_id
        if discount_amount:
            self.discount_amount = discount_amount
        if order_total:
            self.order_total = order_total

        # Update organization information if provided
        if organization_id:
            self.organization_external_id = organization_id
            try:
                organization = Organization.objects.get(external_id=organization_id)
                self.organization = organization
            except Organization.DoesNotExist:
                logger.warning(f"Organization with external_id {organization_id} not found")

        self.save()

    def mark_as_unused(self):
        """
        Mark this promo code as unused, reverting it to its original state

        This is used when an order is canceled and we want to allow the user
        to use the promo code again.
        """
        self.is_used = False
        self.order_id = None
        self.discount_amount = 0
        self.order_total = 0
        self.save()

        logger.info(f"Marked promo code {self.promo.name} as unused for user {self.user.id}")

    @classmethod
    def revert_usage_for_order(cls, order_id):
        """
        Revert promo code usage for a specific order

        This is used when an order is canceled and we want to allow the user
        to use the promo code again.

        Args:
            order_id: The ID of the order to revert promo code usage for

        Returns:
            bool: True if a promo code was found and reverted, False otherwise
        """
        try:
            # Find promo code usage for this order
            promo_usage = cls.objects.filter(
                order_id=order_id,
                is_used=True
            ).first()

            if promo_usage:
                promo_usage.mark_as_unused()
                logger.info(f"Reverted promo code usage for order {order_id}")
                return True

            return False
        except Exception as e:
            logger.error(f"Error reverting promo code usage for order {order_id}: {e}")
            return False

    @classmethod
    def record_usage(cls, user_id, promo_id, order_id, discount_amount=0,
                     order_total=0, organization_id=None):
        """
        Record promo code usage for an order

        Args:
            user_id: User ID
            promo_id: Promo ID
            order_id: Order ID
            discount_amount: Discount amount applied
            order_total: Total order amount before discount
            organization_id: Organization external ID (optional)

        Returns:
            PromoCodeUsage: Created instance
        """
        # Get the promo and program
        promo = Promo.objects.get(id=promo_id)

        # Try to find an existing unused promo code for this user and program
        promo_usage = cls.objects.filter(
            user_id=user_id,
            program_id=promo.program_id,
            is_used=False
        ).first()

        # Get organization if organization_id is provided
        organization = None
        if organization_id:
            try:
                organization = Organization.objects.get(external_id=organization_id)
            except Organization.DoesNotExist:
                logger.warning(f"Organization with external_id {organization_id} not found")

        if promo_usage:
            # Update the existing record
            promo_usage.mark_as_used(
                order_id=order_id,
                discount_amount=discount_amount,
                order_total=order_total
            )

            # Update organization information if provided
            if organization_id:
                promo_usage.organization_external_id = organization_id
                if organization:
                    promo_usage.organization = organization
                promo_usage.save(update_fields=['organization', 'organization_external_id'])

            return promo_usage

        # Create a new record if none exists
        create_data = {
            'user_id': user_id,
            'promo_id': promo_id,
            'program_id': promo.program_id,
            'order_id': order_id,
            'is_used': True,
            'discount_amount': discount_amount,
            'order_total': order_total
        }

        # Add organization data if provided
        if organization_id:
            create_data['organization_external_id'] = organization_id
            if organization:
                create_data['organization'] = organization

        return cls.objects.create(**create_data)
