"""
the is owner or courier permission
"""
from rest_framework import permissions


from apps.user.enum.role import UserRoleEnums


class IsCourierRole(permissions.BasePermission):
    """
    Custom permission to allow only users with the courier role.
    """
    def has_permission(self, request, view):
        return all([
            request.user,
            request.user.is_authenticated,
            request.user.role == UserRoleEnums.COURIER.value
        ])


class IsAdminOrCourierOrManager(permissions.BasePermission):
    """
    Object-level permission to allow only admins, couriers, or managers to edit objects.
    """

    def has_permission(self, request, view):
        # General-level permission check
        return request.user.is_authenticated and request.user.role in [
            UserRoleEnums.ADMIN.value,
            UserRoleEnums.COURIER.value,
            UserRoleEnums.MANAGERS.value,
        ]

    def has_object_permission(self, request, view, obj):
        # Object-level permission check
        return self.has_permission(request, view) or obj.ower == request.user
