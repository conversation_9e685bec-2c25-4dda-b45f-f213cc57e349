from django.apps import AppConfig


class ProductConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.product'

    def ready(self):
        # Debug place :)
        # from apps.product.service import ProductService

        # product_service = ProductService()
        # product_service.update_products_and_categories(is_manual_update=True)
        return super().ready()
