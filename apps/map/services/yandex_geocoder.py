"""
Yandex Geocoder Service Module

This module provides the YandexGeocoderService class for making
requests to the Yandex Geocoder API. It handles API key management,
usage tracking, and response formatting for frontend consumption.
"""

import logging
import requests
from typing import Dict, List, Optional, Tuple
from django.conf import settings

from apps.map.models.api_keys import MapApiKey


logger = logging.getLogger(__name__)


class YandexGeocoderService:
    """
    Service class for interacting with Yandex Geocoder API.
    
    This service handles:
    - API key management and rotation
    - Usage tracking and daily limits
    - HTTP requests to Yandex Geocoder API
    - Response parsing and formatting for frontend
    """
    
    BASE_URL = "https://geocode-maps.yandex.ru/v1/"
    DEFAULT_RESULTS = 10
    DEFAULT_LANGUAGE = "en_US"
    DEFAULT_FORMAT = "json"
    
    def __init__(self):
        """Initialize the Yandex Geocoder service."""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MasterKebab/1.0',
            'Accept': 'application/json',
        })
    
    def geocode_location(self, name: str, language: str = DEFAULT_LANGUAGE, 
                        results: int = DEFAULT_RESULTS) -> Dict:
        """
        Geocode a location name using Yandex Geocoder API.
        
        Args:
            name (str): Location name to geocode.
            language (str): Response language (default: en_US).
            results (int): Number of results to return (default: 10).
            
        Returns:
            Dict: Formatted response with location data for frontend.
            
        Raises:
            ValueError: If no API key is available or daily limit exceeded.
            requests.RequestException: If API request fails.
        """
        # Get API key and track usage
        try:
            api_key, remaining_requests = MapApiKey.use_yandex_geocoder_key()
            logger.info(f"Using Yandex Geocoder API key (Owner: {api_key.owner or 'Unknown'}, "
                       f"Remaining: {remaining_requests})")
        except ValueError as e:
            logger.error(f"Failed to get Yandex Geocoder API key: {e}")
            raise
        
        # Prepare request parameters
        params = {
            'apikey': api_key.api_key,
            'geocode': name,
            'lang': language,
            'format': self.DEFAULT_FORMAT,
            'results': min(results, 50),  # Limit to max 50 results
        }
        
        try:
            logger.info(f"Making Yandex Geocoder request for: {name}")
            response = self._make_request(params)
            
            # Format and return response
            formatted_response = self._format_response(response, remaining_requests)
            
            logger.info(f"Successfully retrieved {len(formatted_response.get('locations', []))} locations")
            return formatted_response
            
        except requests.RequestException as e:
            logger.error(f"Yandex Geocoder API request failed: {e}")
            raise
    
    def _make_request(self, params: Dict) -> Dict:
        """
        Make HTTP request to Yandex Geocoder API.
        
        Args:
            params (Dict): Request parameters.
            
        Returns:
            Dict: Raw API response.
            
        Raises:
            requests.RequestException: If request fails.
        """
        response = self.session.get(self.BASE_URL, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    
    def _format_response(self, raw_response: Dict, remaining_requests: int) -> Dict:
        """
        Format the raw Yandex Geocoder API response for frontend consumption.
        
        Args:
            raw_response (Dict): Raw response from Yandex API.
            remaining_requests (int): Number of remaining API requests for today.
            
        Returns:
            Dict: Formatted response with locations, URIs, and coordinates.
        """
        locations = []
        
        try:
            # Navigate through the complex response structure
            geo_collection = raw_response.get('response', {}).get('GeoObjectCollection', {})
            feature_members = geo_collection.get('featureMember', [])
            
            for member in feature_members:
                geo_object = member.get('GeoObject', {})
                
                # Extract basic information
                name = geo_object.get('name', '')
                description = geo_object.get('description', '')
                uri = geo_object.get('uri', '')
                
                # Extract coordinates from Point
                point_data = geo_object.get('Point', {})
                pos = point_data.get('pos', '')
                coordinates = None
                
                if pos:
                    try:
                        lon, lat = pos.split(' ')
                        coordinates = {
                            'longitude': float(lon),
                            'latitude': float(lat)
                        }
                    except (ValueError, IndexError):
                        logger.warning(f"Failed to parse coordinates: {pos}")
                
                # Extract metadata
                metadata = geo_object.get('metaDataProperty', {}).get('GeocoderMetaData', {})
                precision = metadata.get('precision', '')
                kind = metadata.get('kind', '')
                formatted_address = metadata.get('text', '')
                
                # Extract address components
                address_info = metadata.get('Address', {})
                country_code = address_info.get('country_code', '')
                components = address_info.get('Components', [])
                
                # Extract bounded area
                bounded_by = geo_object.get('boundedBy', {})
                envelope = bounded_by.get('Envelope', {})
                lower_corner = envelope.get('lowerCorner', '')
                upper_corner = envelope.get('upperCorner', '')
                
                # Format the location data for frontend
                location = {
                    'name': name,
                    'description': description,
                    'formatted_address': formatted_address,
                    'uri': uri,
                    'coordinates': coordinates,
                    'precision': precision,
                    'kind': kind,
                    'country_code': country_code,
                    'components': [
                        {
                            'kind': comp.get('kind', ''),
                            'name': comp.get('name', '')
                        }
                        for comp in components
                    ],
                    'bounds': {
                        'lower_corner': lower_corner,
                        'upper_corner': upper_corner
                    } if lower_corner and upper_corner else None
                }
                
                locations.append(location)
                
        except Exception as e:
            logger.error(f"Error parsing Yandex Geocoder response: {e}")
            # Return partial results if parsing fails
        
        # Extract metadata from response
        meta_data = raw_response.get('response', {}).get('GeoObjectCollection', {}).get('metaDataProperty', {})
        geocoder_meta = meta_data.get('GeocoderResponseMetaData', {})
        
        return {
            'locations': locations,
            'total_results': len(locations),
            'found': int(geocoder_meta.get('found', 0)),
            'request': geocoder_meta.get('request', ''),
            'remaining_requests': remaining_requests,
            'status': 'success'
        }
    
    @classmethod
    def check_api_availability(cls) -> Tuple[bool, str]:
        """
        Check if Yandex Geocoder API is available.
        
        Returns:
            Tuple[bool, str]: (availability status, message)
        """
        try:
            api_key = MapApiKey.get_yandex_geocoder_key()
            if not api_key:
                return False, "No available Yandex Geocoder API keys"
            
            from apps.map.models.api_usage import ApiUsage
            remaining = ApiUsage.get_remaining_requests(api_key)
            
            if remaining <= 0:
                return False, "Daily limit exceeded for all API keys"
            
            return True, f"API available with {remaining} remaining requests"
            
        except Exception as e:
            logger.error(f"Error checking API availability: {e}")
            return False, f"Error checking API availability: {str(e)}"
