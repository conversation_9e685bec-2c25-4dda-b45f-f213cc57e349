"""
ASGI config for master_kebab project.

It exposes the ASGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/howto/deployment/asgi/
"""
import os

from django.core.asgi import get_asgi_application
from channels.auth import AuthMiddlewareStack

django_asgi = get_asgi_application()

# pylint: disable=C0413
from channels.routing import ProtocolTypeRouter, URLRouter  # noqa: E402

from apps.courier.routing import websocket_urlpatterns  # noqa: E402


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'master_kebab.settings')


application = ProtocolTypeRouter({
    "http": django_asgi,
    "websocket": AuthMiddlewareStack(URLRouter(
        websocket_urlpatterns,
    ))
})
