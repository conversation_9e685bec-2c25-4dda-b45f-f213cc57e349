# Generated by Django 5.0.6 on 2024-09-14 16:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0006_orderedproductmodifier"),
    ]

    operations = [
        migrations.AddField(
            model_name="itemmodifiergroups",
            name="description_en",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="itemmodifiergroups",
            name="description_ru",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="itemmodifiergroups",
            name="description_uz",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="itemmodifiergroups",
            name="name_en",
            field=models.CharField(max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="itemmodifiergroups",
            name="name_ru",
            field=models.Char<PERSON>ield(max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="itemmodifiergroups",
            name="name_uz",
            field=models.Char<PERSON>ield(max_length=100, null=True),
        ),
        migrations.AddField(
            model_name="modifieritems",
            name="description_en",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="modifieritems",
            name="description_ru",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="modifieritems",
            name="description_uz",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name="modifieritems",
            name="name_en",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="modifieritems",
            name="name_ru",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="modifieritems",
            name="name_uz",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="orderedproductmodifier",
            name="ordered_product",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="product_modifier",
                to="product.orderedproduct",
            ),
        ),
    ]
