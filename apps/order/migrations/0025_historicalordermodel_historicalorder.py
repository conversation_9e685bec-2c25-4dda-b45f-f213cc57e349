# Generated by Django 5.0.6 on 2024-10-01 07:58

import django.db.models.deletion
import simple_history.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0024_alter_order_delivery_agent_alter_orderdetail_order"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="HistoricalOrderModel",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("status", models.CharField(max_length=50)),
            ],
            options={
                "db_table": "order_historicalorder",
                "ordering": ["-created_at"],
                "managed": False,
            },
        ),
        migrations.CreateModel(
            name="HistoricalOrder",
            fields=[
                (
                    "id",
                    models.BigIntegerField(
                        auto_created=True, blank=True, db_index=True, verbose_name="ID"
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(blank=True, db_index=True, editable=False),
                ),
                (
                    "updated_at",
                    models.DateTimeField(blank=True, db_index=True, editable=False),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("Created", "Created"),
                            ("Approved", "Approved"),
                            ("CreationError", "Creationerror"),
                            ("Arrived", "Arrived"),
                            ("WebhookError", "Webhookerror"),
                            ("Timeout", "Timeout"),
                            ("InProgress", "Inprogress"),
                            ("Unconfirmed", "Unconfirmed"),
                            ("WaitCooking", "Waitcooking"),
                            ("CookingStarted", "Cookingstarted"),
                            ("Cancelled", "Cancelled"),
                            ("CookingCompleted", "Cookingcompleted"),
                            ("ConnectionError", "Connectionerror"),
                            ("Delivered", "Delivered"),
                            ("DeliveryOrderError", "Deliveryordererror"),
                            ("OnWay", "Onway"),
                            ("Waiting", "Waiting"),
                            ("Closed", "Closed"),
                        ],
                        default="Created",
                        max_length=50,
                    ),
                ),
                ("history_id", models.AutoField(primary_key=True, serialize=False)),
                ("history_date", models.DateTimeField(db_index=True)),
                ("history_change_reason", models.CharField(max_length=100, null=True)),
                (
                    "history_type",
                    models.CharField(
                        choices=[("+", "Created"), ("~", "Changed"), ("-", "Deleted")],
                        max_length=1,
                    ),
                ),
                (
                    "history_user",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="+",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "historical order",
                "verbose_name_plural": "historical orders",
                "ordering": ("-history_date", "-history_id"),
                "get_latest_by": ("history_date", "history_id"),
            },
            bases=(simple_history.models.HistoricalChanges, models.Model),
        ),
    ]
