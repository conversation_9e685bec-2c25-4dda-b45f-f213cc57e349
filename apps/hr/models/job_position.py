"""
the job position model
"""
from django.db import models

from apps.core.models.base import BaseModel


class JobPosition(BaseModel):
    """
    The model with fields: name, description for bot
    """
    class Meta:
        """
        the meta class
        """
        db_table = 'hr_job_position'

    name = models.CharField(max_length=100)
    name_uz = models.CharField(max_length=100, null=True, blank=True)
    name_ru = models.CharField(max_length=100, null=True, blank=True)
    name_en = models.CharField(max_length=100, null=True, blank=True)
    description = models.TextField(blank=True, null=True)
    description_uz = models.TextField(blank=True, null=True)
    description_ru = models.TextField(blank=True, null=True)
    description_en = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"{self.name}"


class JobWorkingHour(BaseModel):
    """
    Stores multiple working hours for a JobPosition.
    """
    class Meta:
        """
        the meta class
        """
        db_table = 'hr_job_working_hour'

    job_position = models.ForeignKey(JobPosition, on_delete=models.CASCADE, related_name="working_hours")

    working_hours = models.CharField(max_length=100)
    working_hours_uz = models.CharField(max_length=100, null=True, blank=True)
    working_hours_ru = models.CharField(max_length=100, null=True, blank=True)
    working_hours_en = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return f"{self.working_hours} ({self.job_position.name})"
