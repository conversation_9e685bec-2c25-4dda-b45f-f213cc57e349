"""
this permission helps to protect objects that means that only object owner can
edit or delete that object
"""
from rest_framework import permissions

from apps.user.enum.role import UserRoleEnums


class IsAdminOrManager(permissions.BasePermission):
    """
    Object-level permission to allow only admins or managers to edit objects.
    """

    def has_permission(self, request, view):
        # Check for permission on a general level
        return request.user.is_authenticated and request.user.role in [
            UserRoleEnums.ADMIN.value,
            UserRoleEnums.MANAGERS.value
        ]

    def has_object_permission(self, request, view, obj):
        # Object-level permission
        return self.has_permission(request, view)


class IsAdminOrManagerOrOperator(permissions.BasePermission):
    """
    Object-level permission to allow only admins, managers or operators to edit objects.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in [
            UserRoleEnums.ADMIN.value,
            UserRoleEnums.MANAGERS.value,
            UserRoleEnums.OPERATOR.value
        ]


class IsAdminOrManagerOrMarketolog(permissions.BasePermission):
    """
    Object-level permission to allow only admins, managers or marketoligists to edit objects.
    """
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.role in [
            UserRoleEnums.ADMIN.value,
            UserRoleEnums.MANAGERS.value,
            UserRoleEnums.MARKETOLOG.value
        ]
