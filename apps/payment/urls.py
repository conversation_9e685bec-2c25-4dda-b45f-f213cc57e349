"""
the payments urls
"""
from django.urls import path

from apps.payment.views.link import PaymentLinkAPIView
from apps.payment.views import PaymentMethodListView
from apps.payment.views.webhook.click import ClickWebhookAPIView
from apps.payment.views.webhook.payment import PaymentWebhookView
from apps.payment.views.method import PaymentMethodUpdateView
from apps.payment.views.superapp import ClickSuperAppAuthView


urlpatterns = [
    # Payment webhook endpoints
    path("updates/", PaymentWebhookView.as_view()),
    path("click/updates/", ClickWebhookAPIView.as_view()),
    path("payment-link/", PaymentLinkAPIView.as_view()),

    # Payment methods endpoints
    path('methods/', PaymentMethodListView.as_view(), name='payment-method-list'),
    path('methods/<int:payment_method_id>/update', PaymentMethodUpdateView.as_view(), name='payment-method-update'),

    # Click SuperApp endpoints
    path('click/superapp/auth/', ClickSuperAppAuthView.as_view(), name='click-superapp-auth'),
]
