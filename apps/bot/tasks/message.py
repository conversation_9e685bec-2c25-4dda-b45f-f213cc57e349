"""
telegram message actions
"""

import logging
from telebot import types

from celery import Task

from master_kebab import celery_app
from master_kebab.settings import ORDERS_CHANNEL

from apps.bot.client import bot
from apps.bot.enum import OrderStates
from apps.bot.models.messages import Messages


logger = logging.getLogger(__name__)


@celery_app.task(bind=True)
def send_message_task(self: Task, message, chat_id=ORDERS_CHANNEL, state: OrderStates = None, order_id=None, parse_mode=None, button_text=None, button_url=None):  # noqa
    """
    Sends a Telegram message as a background task, optionally with an inline button.

    Args:
        chat_id (int): The ID of the chat where the message will be sent.
        message (str): The text content of the message to be sent.
        state (OrderStates, optional): The state associated with the message. Default is None.
        order_id (str, optional): The ID of the order related to the message. Default is None.
        button_text (str, optional): The text for the inline button. Default is None.
        button_url (str, optional): The URL for the inline button. Default is None.
        parse_mode (str, optional): The parse mode of the message (e.g., 'Markdown', 'HTML'). Default is None.
    """
    try:
        # Define states that require cache logic
        if state in [
            OrderStates.ORDER_CREATED,
            OrderStates.ORDER_APPROVED,
            OrderStates.ORDER_CLOSED,
            OrderStates.ORDER_TIMEOUT,
            OrderStates.ORDER_DELIVERED,
            OrderStates.COURIER_ARRIVED,
            OrderStates.COURIER_ON_WAY,
        ]:
            # Find and delete the previous message if it exists
            try:
                old_message = Messages.get_by_order_id(
                    order_id=order_id,
                )
                bot.delete_message(
                    chat_id=chat_id,
                    message_id=old_message.message_id
                )
                old_message.delete()

            except Messages.DoesNotExist:
                pass

            except Exception as e:
                logger.error(f"Failed to delete message: {e}")

            if button_text and button_url:
                markup = types.InlineKeyboardMarkup()
                button = types.InlineKeyboardButton(
                    text=button_text, url=button_url
                )
                markup.add(button)
            else:
                markup = None

            resp = bot.send_message(
                chat_id=chat_id,
                text=message,
                reply_markup=markup,
                parse_mode=parse_mode
            )

            try:
                Messages.create(
                    order_id=order_id,
                    chat_id=chat_id,
                    message_id=resp.message_id,
                    message=message,
                    state=state,
                )
            except Exception as e:
                logger.error(f"Failed to save message to database: {e}")

            return

        bot.send_message(
            chat_id=chat_id,
            text=message,
            reply_markup=markup if button_text and button_url else None,
            parse_mode=parse_mode
        )

    except Exception as exc:
        logger.error(f"send_message_task exception: {exc}")
        return
