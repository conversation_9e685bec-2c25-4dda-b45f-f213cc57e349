"""
This module provides an API client for interacting with the IIKO address-related endpoints.
It includes functionality to retrieve street information for a given city and organization.
"""
from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import ByCityRequestBody
from apps.iiko.providers.iiko.http.response import ByCityResponseBody


class IIKOAddressAPI:
    """
    IIKO Address API client for retrieving address-related information.

    This class provides methods to interact with IIKO's address-related endpoints,
    specifically for fetching street information by city.
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def get_streets_by_city(self, organization_id: str, city_id: str, headers: dict) -> ByCityResponseBody:
        """
        Retrieve street information for a given city and organization.

        Args:
            organization_id (str): The unique identifier of the organization.
            city_id (str): The unique identifier of the city.
            headers (dict): HTTP headers to be included in the request.

        Returns:
            ByCityResponseBody: A response object containing the list of streets and correlation ID.
        """
        endpoint = "/api/1/streets/by_city"
        request_body = ByCityRequestBody(
            organizationId=organization_id, cityId=city_id, includeDeleted=False
        ).as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        return ByCityResponseBody(**response_body)


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
address = IIKOAddressAPI(http=http_base_client)
