"""
This module defines the ProviderCredentials model, which stores the credentials
for various payment providers. The credentials include the payment method,
provider ID, provider key, provider URL, minimal payment amount, and transaction key.
The identification and key fields are encrypted before saving to the database.
"""
from django.db import models
from django.conf import settings

from cryptography.fernet import Fernet

from apps.core.models.base import BaseModel
from apps.organization.models import Organization
from apps.payment.enums.method import PaymentMethodEnum


fernet = Fernet(settings.FERNET_KEY)


class ProviderCredentials(BaseModel):
    """
    The provider credentials model.
    """
    class Meta:
        """
        The meta fields.
        """
        db_table = 'provider_credentials'
        verbose_name_plural = 'Provider Credentials'
        ordering = ['-created_at']
        verbose_name = 'Provider Credentials'
        unique_together = ['organization', 'payment_method', 'key']

    title = models.CharField(
        max_length=255, null=True
    )
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE
    )
    payment_method = models.CharField(
        max_length=20,
        choices=PaymentMethodEnum.choices(),
        verbose_name='Payment Method',
        db_index=True
    )
    identification = models.CharField(
        max_length=255,
        verbose_name='Provider ID',
        blank=True
    )
    key = models.CharField(
        max_length=255,
        verbose_name='Provider Key',
        blank=True
    )
    service_id = models.IntegerField(
        verbose_name="Service ID",
        null=True,
        blank=True,
        help_text='The service ID for this provider'
    )
    url = models.URLField(
        verbose_name='Provider URL',
        max_length=200,
    )
    return_url = models.URLField(
        verbose_name='Return URL',
        max_length=200,
        null=True,
        blank=True,
    )
    minimal_amount = models.IntegerField(
        verbose_name='Minimal Payment Amount',
        default=0,
        help_text='The minimal amount for this provider to accept payments',
    )
    transaction_key = models.CharField(
        max_length=50,
        verbose_name='Transaction Key',
        help_text='The transaction key for this provider',
    )

    def save(self, *args, **kwargs):
        """
        Override the save method to encrypt the identification and key fields
        before saving to the database.
        """
        self.identification = fernet.encrypt(self.identification.encode()).decode()
        self.key = fernet.encrypt(self.key.encode()).decode()
        super().save(*args, **kwargs)

    def decrypt_credentials(self):
        """
        Decrypt the identification and key fields.
        """
        self.identification = fernet.decrypt(self.identification.encode()).decode()
        self.key = fernet.decrypt(self.key.encode()).decode()

    @classmethod
    def get_by_merchant_key(cls, identifier):
        """
        Get provider credentials by merchant key.
        """
        all_credentials = cls.objects.all()
        for credentials in all_credentials:
            decrypted_identification = fernet.decrypt(credentials.key.encode()).decode()
            if decrypted_identification == identifier:
                return credentials

        raise cls.DoesNotExist(f"No ProviderCredentials found for identification: {identifier}")

    @classmethod
    def get_by_payment_method(cls, payment_method, organization_id) -> "ProviderCredentials":
        """
        Get provider credentials by payment method and organization.
        """
        return cls.objects.get(
            payment_method=payment_method,
            organization_id=organization_id
        )

    @classmethod
    def has_key(cls, key) -> bool:
        """
        Check if the provided key is valid.
        """
        all_credentials = cls.objects.all()
        for credentials in all_credentials:
            decrypted_identification = fernet.decrypt(credentials.key.encode()).decode()
            if decrypted_identification == key:
                return True

        return False
