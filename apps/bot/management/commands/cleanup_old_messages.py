"""
Management command to manually trigger the cleanup of old Telegram messages.
"""
import logging
from django.core.management.base import BaseCommand
from apps.bot.tasks.clean_up import cleanup_old_messages

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    """
    Django management command to manually trigger the cleanup of old Telegram messages.
    
    This command can be used to test the cleanup functionality or to run it manually
    outside of the scheduled Celery task.
    
    Usage:
        python manage.py cleanup_old_messages
    """
    help = 'Manually trigger the cleanup of old Telegram messages'

    def handle(self, *args, **options):
        """
        Execute the command to cleanup old Telegram messages.
        """
        self.stdout.write(self.style.SUCCESS('Starting cleanup of old Telegram messages...'))
        
        try:
            # Call the cleanup task directly
            result = cleanup_old_messages()
            
            self.stdout.write(self.style.SUCCESS('Cleanup completed successfully!'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error during cleanup: {e}'))
            logger.error(f'Error during cleanup: {e}', exc_info=True)
