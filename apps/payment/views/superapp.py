"""
Views for Click SuperApp integration.
"""
import logging

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.core.views import ServiceBaseView
from apps.payment.service import PaymentService
from apps.payment.serializers.superapp import ClickSuperAppAuthSerializer


logger = logging.getLogger(__name__)


class ClickSuperAppAuthView(APIView, ServiceBaseView):
    """
    View for Click SuperApp authentication.
    """
    permission_classes = []

    def post(self, request):
        """
        Handle POST request for Click SuperApp authentication.
        """
        serializer = ClickSuperAppAuthSerializer(data=request.data)

        if not serializer.is_valid():
            return Response(
                {"error": "Invalid request data", "details": serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        data = serializer.validated_data
        click_web_session = data.get('click_web_session')
        logger.info(f"Received click_web_session: {click_web_session}")

        try:
            # Use PaymentService to authenticate the user
            response_data = PaymentService.authenticate_click_user(click_web_session)

            if not response_data:
                return Response(
                    {"error": "Authentication failed"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in Click SuperApp authentication: {e}")
            return Response(
                {"error": "Authentication failed", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
