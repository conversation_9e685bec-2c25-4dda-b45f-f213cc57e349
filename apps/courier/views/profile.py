from rest_framework import status, views
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response

from apps.core.views import ServiceBaseView
from apps.courier.models.courier import Courier
from apps.courier.serializers.courier import CourierModelSerializer
from apps.courier.permissions.is_courier import IsCourierRole


class CourierProfileAPIView(views.APIView, ServiceBaseView):
    """
    API View for retrieving and updating the courier's profile.
    """
    permission_classes = [IsAuthenticated, IsCourierRole]

    def get(self, request, *args, **kwargs):
        """
        Retrieve the profile of the currently authenticated courier.
        """
        try:
            serializer = CourierModelSerializer(request.user)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Courier.DoesNotExist:
            return Response({"detail": "Courier not found."}, status=status.HTTP_404_NOT_FOUND)

    def put(self, request, *args, **kwargs):
        """
        Update the profile of the currently authenticated courier (full update).
        """
        return self.update_profile(request, partial=False)

    def patch(self, request, *args, **kwargs):
        """
        Partially update the profile of the currently authenticated courier.
        """
        return self.update_profile(request, partial=True)

    def update_profile(self, request, partial):
        """
        Helper method to update the profile of the courier.
        """
        try:
            serializer = CourierModelSerializer(request.user, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Courier.DoesNotExist as exc:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Courier not found.",
                status_code=404,
            ) from exc

        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"An error occurred updating the courier profile: {exc}",
                status_code=500,
                notify_admin=True,
            )
