"""
Serializers for Click SuperApp integration.
"""
from rest_framework import serializers

from apps.payment.models.superapp import ClickSuperAppUser


class ClickSuperAppUserSerializer(serializers.ModelSerializer):
    """
    Serializer for ClickSuperAppUser model.
    """
    phone = serializers.CharField(required=False)
    name = serializers.CharField(source='user.name', required=False)

    class Meta:
        """
        Meta class for ClickSuperAppUserSerializer.
        """
        model = ClickSuperAppUser
        fields = ['click_user_id', 'phone', 'name', 'last_login']
        read_only_fields = ['last_login']


class ClickSuperAppAuthSerializer(serializers.Serializer):
    """
    Serializer for Click SuperApp authentication.
    """
    click_web_session = serializers.CharField(required=True)
