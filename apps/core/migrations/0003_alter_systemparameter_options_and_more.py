# Generated by Django 5.0.6 on 2024-08-30 18:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_systemparameter_name'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='systemparameter',
            options={'ordering': ['id']},
        ),
        migrations.AlterField(
            model_name='systemparameter',
            name='description',
            field=models.TextField(blank=True, help_text='Optional description for the parameter'),
        ),
        migrations.AlterField(
            model_name='systemparameter',
            name='is_enabled',
            field=models.BooleanField(default=False, help_text='Flag to indicate if the parameter is enabled'),
        ),
        migrations.AlterField(
            model_name='systemparameter',
            name='name',
            field=models.CharField(choices=[('iiko', 'Iiko'), ('search_courier_scheduler', 'Search courier scheduler')], help_text='Unique name for the system parameter', max_length=50, unique=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='systemparameter',
            name='value',
            field=models.<PERSON><PERSON><PERSON><PERSON>(help_text='Value of the system parameter', max_length=255),
        ),
    ]
