"""
Management command to initialize Softphone.Pro configuration
"""
from django.core.management.base import BaseCommand
from apps.softphone.models.softphone_config import SoftphoneConfig


class Command(BaseCommand):
    """
    Initialize default Softphone.Pro configuration
    """
    help = 'Initialize default Softphone.Pro configuration'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--base-url',
            type=str,
            default='http://localhost:8000',
            help='Base URL for the application'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force recreate configuration if exists'
        )
    
    def handle(self, *args, **options):
        base_url = options['base_url']
        force = options['force']
        
        # Check if configuration already exists
        existing_config = SoftphoneConfig.objects.filter(name='default').first()
        
        if existing_config and not force:
            self.stdout.write(
                self.style.WARNING(
                    'Default Softphone configuration already exists. Use --force to recreate.'
                )
            )
            return
        
        if existing_config and force:
            existing_config.delete()
            self.stdout.write(
                self.style.WARNING('Deleted existing configuration.')
            )
        
        # Create default configuration
        config = SoftphoneConfig.objects.create(
            name='default',
            screen_popup_url=f'{base_url}/softphone/popup/',
            customer_data_url=f'{base_url}/api/v1/softphone/customer-data/',
            call_log_url=f'{base_url}/api/v1/softphone/log-call/',
            recording_upload_url=f'{base_url}/api/v1/softphone/upload-recording/',
            default_sip_account='1',
            enable_screen_popup=True,
            enable_call_logging=True,
            enable_recording_upload=True,
            click_to_call_enabled=True,
            is_active=True
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created Softphone configuration: {config.name}'
            )
        )
        
        # Display configuration details
        self.stdout.write('\nConfiguration Details:')
        self.stdout.write(f'  Screen Popup URL: {config.screen_popup_url}')
        self.stdout.write(f'  Customer Data URL: {config.customer_data_url}')
        self.stdout.write(f'  Call Log URL: {config.call_log_url}')
        self.stdout.write(f'  Recording Upload URL: {config.recording_upload_url}')
        self.stdout.write(f'  Default SIP Account: {config.default_sip_account}')
        
        self.stdout.write('\nFeatures Enabled:')
        self.stdout.write(f'  Screen Popup: {config.enable_screen_popup}')
        self.stdout.write(f'  Call Logging: {config.enable_call_logging}')
        self.stdout.write(f'  Recording Upload: {config.enable_recording_upload}')
        self.stdout.write(f'  Click-to-Call: {config.click_to_call_enabled}')
        
        self.stdout.write('\nNext Steps:')
        self.stdout.write('1. Configure Softphone.Pro with these URLs')
        self.stdout.write('2. Test click-to-call functionality')
        self.stdout.write('3. Test screen popup on incoming calls')
        self.stdout.write('4. Configure call recording upload')
