# Generated by Django 5.0.6 on 2024-08-20 11:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        ('organization', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Operations',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('phone', models.Char<PERSON><PERSON>(max_length=15, unique=True)),
                ('name', models.Char<PERSON>ield(max_length=255, verbose_name='Full Name')),
                ('password', models.CharField(blank=True, max_length=128, null=True)),
                ('passport', models.CharField(blank=True, max_length=15, null=True, verbose_name='Passport')),
                ('external_id', models.CharField(db_index=True, max_length=255, null=True)),
                ('role', models.CharField(choices=[('operator', 'Operator'), ('manager', 'Manager'), ('dispatcher', 'Dispatcher'), ('admin', 'Super Admin')], max_length=50)),
                ('is_blocked', models.BooleanField(default=False)),
                ('is_deleted', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('is_verified', models.BooleanField(default=False)),
                ('is_staff', models.BooleanField(default=False)),
                ('groups', models.ManyToManyField(blank=True, related_name='operations_user_set', to='auth.group', verbose_name='groups')),
                ('organization', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='organization.organization')),
                ('user_permissions', models.ManyToManyField(blank=True, related_name='operations_user_permissions', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'db_table': 'operations',
                'ordering': ['-created_at', '-updated_at'],
            },
        ),
    ]
