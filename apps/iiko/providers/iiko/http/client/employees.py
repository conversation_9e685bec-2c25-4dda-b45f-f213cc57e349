"""
init iiko nomenclature class
"""
from typing import List, Dict

from master_kebab import settings

from apps.iiko.providers.iiko.enum import RoleEnum
from apps.iiko.providers.iiko.http import request as iiko_request
from apps.iiko.providers.iiko.http import response as iiko_response
from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient


class IIKOEmployeeAPI:
    """
    IIKO getting nomenclature API client
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def _post_request(self, endpoint: str, request_body: Dict, headers: Dict[str, str]) -> Dict:
        """
        Helper method to make a POST request and return the response body.
        """
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        return response_body

    def couriers(
        self,
        organization_ids: List[str],
        headers: Dict[str, str]
    ) -> iiko_response.EmployeeResponseBody:
        """
        Request a list of couriers based on the organization IDs.
        """
        endpoint = "/api/1/employees/couriers"
        request_body = iiko_request.CouriersRequestBody(
            organization_id=organization_ids
        ).as_request_body()

        response_body = self._post_request(endpoint, request_body, headers)
        return iiko_response.EmployeeResponseBody(**response_body)

    def info(
        self,
        organization_id: str,
        _id: str,
        headers: Dict[str, str]
    ) -> iiko_response.EmployeeInfoResponseBody:
        """
        Get employee info by ID.
        """
        endpoint = "/api/1/employees/info"
        request_body = iiko_request.EmployeeInfoRequestBody(
            employee_id=_id, organization_id=organization_id
        ).as_request_body()

        response_body = self._post_request(endpoint, request_body, headers)
        return iiko_response.EmployeeInfoResponseBody(**response_body)

    def by_role(
        self,
        role: RoleEnum,
        organization_ids: List[str],
        headers: Dict[str, str]
    ) -> iiko_response.EmployeesWithCheckRolesResponse:
        """
        Get employees by role.
        """
        endpoint = "/api/1/employees/couriers/by_role"
        request_body = iiko_request.EmployeesByRoleRequestBody(
            role=role, organization_ids=organization_ids
        ).as_request_body()

        response_body = self._post_request(endpoint, request_body, headers)
        return iiko_response.EmployeesWithCheckRolesResponse(**response_body)

    def shift_clockin(
        self,
        organization_id: str,
        terminal_group_id: str,
        employee_id: str,
        headers: Dict[str, str],
        role_id: str = None,
    ) -> iiko_response.EmployeesWithCheckRolesResponse:
        """
        Placeholder for shift clock-in functionality.
        """
        endpoint = "/api/1/employees/shift/clockin"
        request_body = iiko_request.EmployeesShiftClockInRequestBody(
            organization_id=organization_id,
            terminal_group_id=terminal_group_id,
            employee_id=employee_id,
        ).as_request_body()

        response_body = self._post_request(endpoint, request_body, headers)
        response_body = iiko_response.UpdateOrderCourierResponseBody(**response_body)
        response_body.handle_error_response()

        return response_body

    def shift_is_open(
        self,
        organization_id: str,
        terminal_group_id: str,
        employee_id: str,
        headers: Dict[str, str],
        role_id: str = None,
    ) -> iiko_response.EmployeesShiftIsOpenResponseBody:
        """
        Placeholder for shift is open functionality.
        """
        endpoint = "/api/1/employees/shift/is_open"
        request_body = iiko_request.EmployeesShiftIsOpenRequestBody(
            organization_id=organization_id,
            terminal_group_id=terminal_group_id,
            employee_id=employee_id,
        ).as_request_body()

        response_body = self._post_request(endpoint, request_body, headers)
        response_body = iiko_response.EmployeesShiftIsOpenResponseBody(**response_body)
        response_body.handle_error_response()

        return response_body

    def shift_clockout(
        self,
        organization_id: str,
        terminal_group_id: str,
        employee_id: str,
        headers: Dict[str, str],
        role_id: str = None,
    ) -> iiko_response.EmployeesShiftIsOpenResponseBody:
        """
        Placeholder for shift clock-out functionality.
        """
        endpoint = "/api/1/employees/shift/clockout"
        request_body = iiko_request.EmployeesShiftClockOutRequestBody(
            organization_id=organization_id,
            terminal_group_id=terminal_group_id,
            employee_id=employee_id,
        ).as_request_body()

        response_body = self._post_request(endpoint, request_body, headers)
        response_body = iiko_response.EmployeesShiftClockOutResponseBody(**response_body)
        response_body.handle_error_response()

        return response_body


# Initialize the API client
http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
employee = IIKOEmployeeAPI(http=http_base_client)
