"""
the getting couriers by given filtersets
"""
from django_filters import rest_framework as filters

from apps.courier.models.courier import Courier


class CourierFilter(filters.FilterSet):
    """
    the courier filter
    """
    phone = filters.NumberFilter(field_name='phone')
    name = filters.CharFilter(field_name='name', lookup_expr='icontains')
    passport = filters.CharFilter(field_name='passport')
    status = filters.CharFilter(field_name='status')

    class Meta:
        """
        the meta fields
        """
        model = Courier
        fields = ['phone', 'name', 'passport', 'status']
