"""
init nomenclature response body models
"""
from typing import List, Optional
from pydantic import BaseModel, Field


class Price(BaseModel):
    """
    the prices model
    """
    isIncludedInMenu: bool
    nextPrice: Optional[float]
    nextIncludedInMenu: Optional[bool]
    nextDatePrice: Optional[str]
    current_price: float = Field(alias="currentPrice")


class SizePrice(BaseModel):
    """
    the size price model
    """
    size_id: Optional[str] = Field(alias="sizeId")
    price: Price


class ChildModifier(BaseModel):
    """
    child modifier model
    """
    id: str
    defaultAmount: int
    minAmount: int
    maxAmount: int
    required: bool
    hideIfDefaultAmount: bool
    splittable: bool
    freeOfChargeAmount: int


class GroupModifier(BaseModel):
    """
    group modifier model
    """
    id: str
    minAmount: int
    maxAmount: int
    required: bool
    childModifiersHaveMinMaxRestrictions: bool
    childModifiers: List[ChildModifier]
    hideIfDefaultAmount: bool
    defaultAmount: int
    splittable: bool
    freeOfChargeAmount: int


class Product(BaseModel):
    """
    the product model
    """
    fatAmount: float
    proteinsAmount: float
    carbohydratesAmount: float
    energyAmount: float
    fatFullAmount: float
    proteinsFullAmount: float
    carbohydratesFullAmount: float
    energyFullAmount: float
    weight: float
    group_id: Optional[str] = Field(alias="groupId")
    productCategoryId: Optional[str]
    type: str
    order_item_type: str = Field(alias="orderItemType")
    modifierSchemaId: Optional[str]
    modifierSchemaName: Optional[str]
    splittable: bool
    measureUnit: str
    size_price: List[SizePrice] = Field(alias="sizePrices")
    modifiers: List
    groupModifiers: List[GroupModifier]
    imageLinks: List
    doNotPrintInCheque: bool
    parent_group: Optional[str] = Field(alias="parentGroup")
    order: int
    fullNameEnglish: str
    useBalanceForSell: bool
    canSetOpenPrice: bool
    paymentSubject: str
    id: str
    code: str
    name: str
    description: Optional[str]
    additionalInfo: Optional[str]
    tags: Optional[List]
    is_deleted: bool = Field(alias="isDeleted")
    seoDescription: Optional[str]
    seoText: Optional[str]
    seoKeywords: Optional[str]
    seoTitle: Optional[str]


class Group(BaseModel):
    """
    the group model
    """
    id: str
    isIncludedInMenu: bool
    isGroupModifier: bool
    order: Optional[int] = 0
    code: Optional[str] = None
    name: Optional[str] = None
    imageLinks: Optional[List] = None
    parentGroup: Optional[str] = None
    additionalInfo: Optional[str] = None
    tags: Optional[List] = None
    is_deleted: bool = Field(alias="isDeleted")
    seoDescription: Optional[str] = None
    seoText: Optional[str] = None
    seoKeywords: Optional[str] = None
    seoTitle: Optional[str] = None
    description: Optional[str] = None


class ProductCategory(BaseModel):
    """
    the product category model
    """
    id: str
    name: str
    is_deleted: bool = Field(alias="isDeleted")


class NomenclatureResponseBody(BaseModel):
    """
    the nomenclature response model
    """
    correlationId: str
    groups: List[Group]
    productCategories: List[ProductCategory]
    products: List[Product]
    sizes: List
    revision: int
