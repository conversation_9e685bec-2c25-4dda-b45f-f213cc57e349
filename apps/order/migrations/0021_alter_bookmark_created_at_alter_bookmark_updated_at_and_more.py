# Generated by Django 5.0.6 on 2024-09-25 07:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0020_alter_order_options_remove_order_is_courier_accepted_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="bookmark",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="bookmark",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="deliveryprice",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="deliveryprice",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="order",
            name="created_at",
            field=models.DateTime<PERSON>ield(auto_now_add=True, db_index=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="order",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="orderdetail",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="orderdetail",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="orderitem",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="orderitem",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
    ]
