"""
Models for Click SuperApp integration.
"""
from django.db import models
from django.utils import timezone

from apps.core.models.base import BaseModel
from apps.user.models import Users


class ClickSuperAppUser(BaseModel):
    """
    Model to store Click SuperApp user information.
    """
    user = models.OneToOneField(
        Users,
        on_delete=models.CASCADE,
        related_name='click_superapp_user'
    )
    click_user_id = models.CharField(max_length=255, unique=True)
    merchant_user_id = models.CharField(max_length=255, unique=True, null=True, blank=True)
    phone = models.CharField(max_length=15, null=True, blank=True)
    last_login = models.DateTimeField(null=True, blank=True)

    class Meta:
        """
        Meta class for ClickSuperAppUser.
        """
        db_table = 'click_superapp_user'
        verbose_name = 'Click SuperApp User'
        verbose_name_plural = 'Click SuperApp Users'

    def __str__(self):
        return f"Click User: {self.click_user_id} - {self.user.phone}"

    def update_last_login(self):
        """
        Update the last login timestamp.
        """
        self.last_login = timezone.now()
        self.save()

    @classmethod
    def get_by_click_user_id(cls, click_user_id):
        """
        Get a user by Click user ID.
        """
        return cls.objects.get(click_user_id=click_user_id)

    @classmethod
    def get_by_merchant_user_id(cls, merchant_user_id):
        """
        Get a user by merchant user ID.
        """
        return cls.objects.get(merchant_user_id=merchant_user_id)
    
    @classmethod
    def get_or_create_by_click_user_id(cls, click_user_id, phone=None):
        """
        Get or create a user by Click user ID.
        """
        try:
            return cls.objects.get(click_user_id=click_user_id), False
        except cls.DoesNotExist:
            # Create a new user if one doesn't exist
            user, created = Users.objects.get_or_create(
                phone=phone,
                defaults={
                    'role': 'client',
                    'is_verified': True
                }
            )

            click_user = cls.objects.create(
                user=user,
                click_user_id=click_user_id,
                phone=phone
            )
            return click_user, True


class ClickSuperAppSession(BaseModel):
    """
    Model to store Click SuperApp session information.
    """
    click_user = models.ForeignKey(
        ClickSuperAppUser, 
        on_delete=models.CASCADE, 
        related_name='sessions'
    )
    session_id = models.CharField(max_length=255, unique=True)
    token = models.TextField()
    expires_at = models.DateTimeField()
    is_active = models.BooleanField(default=True)

    class Meta:
        """
        Meta class for ClickSuperAppSession.
        """
        db_table = 'click_superapp_session'
        verbose_name = 'Click SuperApp Session'
        verbose_name_plural = 'Click SuperApp Sessions'

    def __str__(self):
        return f"Session: {self.session_id} - {self.click_user.click_user_id}"

    def is_expired(self):
        """
        Check if the session is expired.
        """
        return timezone.now() > self.expires_at

    def invalidate(self):
        """
        Invalidate the session.
        """
        self.is_active = False
        self.save()

    @classmethod
    def get_active_session(cls, session_id):
        """
        Get an active session by session ID.
        """
        return cls.objects.get(
            session_id=session_id,
            is_active=True,
            expires_at__gt=timezone.now()
        )

    @classmethod
    def create_session(cls, click_user, session_id, token, expires_in=86400):
        """
        Create a new session.
        """
        expires_at = timezone.now() + timezone.timedelta(seconds=expires_in)

        # Invalidate any existing sessions for this user
        cls.objects.filter(click_user=click_user, is_active=True).update(is_active=False)

        return cls.objects.create(
            click_user=click_user,
            session_id=session_id,
            token=token,
            expires_at=expires_at
        )
