# Generated by Django 5.0.6 on 2024-09-18 17:18

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0019_bookmark_name"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="order",
            options={"ordering": ["-created_at"]},
        ),
        migrations.RemoveField(
            model_name="order",
            name="is_courier_accepted",
        ),
        migrations.AddField(
            model_name="order",
            name="courier_finding_status",
            field=models.CharField(
                choices=[
                    ("NotStarted", "Notstarted"),
                    ("InSearching", "Insearching"),
                    ("SearchingError", "Searchingerror"),
                    ("Found", "Found"),
                    ("NotFound", "Notfound"),
                ],
                default="NotStarted",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="order",
            name="status",
            field=models.CharField(
                choices=[
                    ("Created", "Created"),
                    ("Approved", "Approved"),
                    ("CreationError", "Creationerror"),
                    ("Arrived", "Arrived"),
                    ("WebhookError", "Webhookerror"),
                    ("Timeout", "Timeout"),
                    ("InProgress", "Inprogress"),
                    ("Unconfirmed", "Unconfirmed"),
                    ("WaitCooking", "Waitcooking"),
                    ("CookingStarted", "Cookingstarted"),
                    ("Cancelled", "Cancelled"),
                    ("CookingCompleted", "Cookingcompleted"),
                    ("ConnectionError", "Connectionerror"),
                    ("Delivered", "Delivered"),
                    ("DeliveryOrderError", "Deliveryordererror"),
                    ("OnWay", "Onway"),
                    ("Waiting", "Waiting"),
                    ("Closed", "Closed"),
                ],
                default="Created",
                max_length=50,
            ),
        ),
    ]
