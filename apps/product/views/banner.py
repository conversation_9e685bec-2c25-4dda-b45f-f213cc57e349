"""
API views for managing banners.
"""
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

from apps.operations.permissions.manager import IsAdminOrManager
from apps.product.models.banner import Banner
from apps.product.serializers.banner import BannerSerializer


class BannerAPIView(generics.ListCreateAPIView):
    """
    Handles listing and creating banners.
    """
    serializer_class = BannerSerializer

    def get_permissions(self):
        """
        Set custom permissions based on method.
        """
        if self.request.method == 'GET':
            return []

        return [IsAuthenticated(), IsAdminOrManager()]

    def get_queryset(self):
        """
        Restrict Banner detail to manager users only.
        """
        return Banner.objects.all()


class BannerDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    """
    Handles retrieving, updating, and deleting a specific banner.
    """
    serializer_class = BannerSerializer

    def get_permissions(self):
        """
        Set custom permissions based on method.
        """
        if self.request.method == 'GET':
            return []

        return [IsAuthenticated(), IsAdminOrManager()]

    def get_queryset(self):
        """
        Restrict Banner detail to manager users only.
        """
        return Banner.objects.all()
