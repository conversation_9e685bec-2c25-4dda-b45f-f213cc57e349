from django_filters import rest_framework as filters

from apps.notify.models import Notification


class NotificationFilter(filters.FilterSet):
    """
    Notification filter
    """
    provider = filters.ChoiceFilter(choices=Notification.PROVIDER_CHOICES)

    class Meta:
        """
        Meta class for NotificationFilter
        """
        model = Notification
        fields = ['provider', ]
