"""
Payme Payment Gateway Client Implementation

This module provides the integration with the Payme payment gateway for handling
various transaction-related operations such as transaction creation, performing,
cancellation, and status checking. It defines an observer class that listens for
webhook events from the Payme system and processes them accordingly.

The key responsibilities of this module include:
    - Authorization: Validates incoming requests from Payme using the provided
      authorization headers to ensure that requests originate from the correct source.
    - Transaction Processing: Manages the creation, cancellation, and execution of
      payment transactions, ensuring that the appropriate business logic is applied
      for each case.
    - Webhook Handling: Responds to incoming webhook events from Payme, mapping
      each event to the appropriate handler method.
    - Response Generation: Constructs and sends responses to Payme in accordance
      with their API protocol, ensuring smooth communication between systems.
    - Transaction State Management: Converts internal transaction states to Payme
      transaction states, ensuring that the two systems remain in sync.

Classes:
    - PaymeObserver: Implements the IWebHookPaymentObserver interface to handle
      webhook events for the Payme payment system.

Functions and Methods:
    - authorize(request): Validates the incoming request's authorization header.
    - update(request): Processes Payme's webhook update requests, delegating the
      request to the appropriate method based on the provided "method" parameter.
    - check_perform_transaction(params): Checks if a transaction is allowed to be
      performed based on the provided parameters.
    - create_transaction(params): Handles the creation of a new transaction.
    - perform_transaction(params): Performs an already created transaction.
    - cancel_transaction(params): Cancels an existing transaction.
    - check_transaction(params): Checks the status of a given transaction.
    - get_statement(params): Retrieves a list of transactions for a given statement period.
    - __convert_state(state): Converts internal transaction states to the corresponding
      Payme states.
    - __convert_time(time): Converts a datetime object to milliseconds for Payme's
      required format.

Logging:
    - This module uses logging to track key events such as authorization attempts,
      transaction processing, and error conditions. The logs are helpful for debugging
      and monitoring transaction flows.

Usage Example:
    To integrate this module, you can register the PaymeObserver class to listen
    for webhook events and process incoming requests from the Payme system.

        from apps.payment.providers.payme import payme_observer

        # Example usage in a webhook view
        def webhook_view(request):
            payme_observer.authorize(request)
            return payme_observer.update(request)

Dependencies:
    - Django framework and settings
    - Payme-related enums, exceptions, and response objects
    - PaymentTransaction, PaymentMethod, and Order models
    - Custom exceptions and interfaces for the Payme system

Documentation:
    - https://developer.help.paycom.uz
    - https://test.paycom.uz/instruction
"""
import base64
import logging
import binascii
from datetime import datetime

from payme import Payme

from apps.order.models.order import Order
from apps.core.views import ServiceBaseView
from apps.payment.providers import interface
from apps.payment.providers.payme import enums
from apps.payment.providers.payme import exceptions
from apps.payment.models.method import PaymentMethod
from apps.payment.enums.method import PaymentMethodEnum
from apps.payment.enums.state import TransactionStateEnum
from apps.payment.providers.payme.response import merchant
from apps.payment.models.transaction import PaymentTransaction
from apps.payment.models.credentials import ProviderCredentials
from apps.payment.enums.state import TransactionCancelReasonEnum
from apps.payment.providers.payme.enums import PaymeReasonCodesEnum
from apps.payment.providers.payme.enums import PaymeTransactionStateEnum


logger = logging.getLogger(__name__)


class PaymeObserver(
    ServiceBaseView,
    interface.IWebHookPaymentObserver
):
    """
    The Payme observer implementation for listening to webhook updates
    and handling transaction processing based on incoming requests.
    """

    def __get_merchant_key(self, request):
        password = request.META.get('HTTP_AUTHORIZATION')
        if not password:
            msg = "Missing authorization header in request (Payme)."
            logger.error(msg)
            raise exceptions.PermissionDenied(msg)

        password = password.split()[-1]

        try:
            password = base64.b64decode(password).decode('utf-8')
            logger.info("Authorization header successfully decoded (Payme).")
        except (binascii.Error, UnicodeDecodeError) as exc:
            msg = f"Failed to decode authorization header (Payme): {exc}"
            logger.error(msg)
            raise exceptions.PermissionDenied(msg)

        merchant_key = password.split(':')[-1]

        return merchant_key

    def authorize(self, request) -> bool:
        merchant_key = self.__get_merchant_key(request)

        from apps.payment.service import PaymentService  # pylint: disable=C0415

        if not PaymentService.has_key(merchant_key):
            msg = "Invalid merchant key provided in authorization (Payme)."
            logger.error(msg)
            raise exceptions.PermissionDenied(msg)

        logger.info("Merchant successfully authorized (Payme).")
        return True

    def update(self, request):
        """
        Handles webhook updates by invoking the corresponding method
        based on the `method` parameter in the request.
        """
        method, params = request.data["method"], request.data["params"]
        logger.info(f"Received update for method: {method} with params: {params} (Payme)")

        merchant_key = self.__get_merchant_key(request)

        method_map = {
            enums.Methods.CHECK_PERFORM_TRANSACTION: self.check_perform_transaction,
            enums.Methods.CREATE_TRANSACTION: self.create_transaction,
            enums.Methods.PERFORM_TRANSACTION: self.perform_transaction,
            enums.Methods.CANCEL_TRANSACTION: self.cancel_transaction,
            enums.Methods.CHECK_TRANSACTION: self.check_transaction,
            enums.Methods.GET_STATEMENT: self.get_statement,
        }

        if method in method_map:
            logger.info(f"Processing method: {method} with params: {params} (Payme)")
            return method_map[method](params, merchant_key)

        logger.warning(f"Method {method} not found in method map (Payme).")
        return {"error": "method not found"}

    def check_perform_transaction(self, params, merchant_key) -> merchant.CheckPerformTransactionsResponse:
        """
        Checks if a transaction can be performed based on the amount.
        """
        creds = ProviderCredentials.get_by_merchant_key(merchant_key)

        amount = params.get("amount")
        transaction_key = params['account'][creds.transaction_key]
        logger.info(f"Checking if transaction can be performed with amount: {amount} (Payme)")

        try:
            order = Order.get_by_id(transaction_key)
            logger.info(f"Order {transaction_key} found (Payme).")
        except Order.DoesNotExist as exc:
            error_message = f"Order ID: {transaction_key} does not exist (Payme)."
            logger.error(error_message)
            raise exceptions.TransactionDoesNotExist(error_message) from exc

        if (order.total_cost * 100) != amount:
            error_message = f"Amount in order {transaction_key} does not match the provided amount (Payme)."
            logger.error(error_message)
            raise exceptions.IncorrectAmount(error_message)

        allow_transaction = amount >= creds.minimal_amount
        if allow_transaction:
            logger.info(f"Transaction allowed with amount: {amount} (Payme).")
        else:
            logger.warning(f"Transaction denied. Amount {amount} is below the minimum allowed (Payme).")

        result = merchant.CheckPerformTransactionsResponse(allow=allow_transaction)
        return result.as_resp()

    def create_transaction(self, params, merchant_key) -> merchant.CreateTransactionResponse:
        """
        Creates a new transaction for the given parameters.
        """
        creds = ProviderCredentials.get_by_merchant_key(merchant_key)

        amount = params["amount"]
        transaction_id = params["id"]
        transaction_key = params['account'][creds.transaction_key]

        logger.info(f"Attempting to create transaction with ID: {transaction_id} and amount: {amount} (Payme)")

        try:
            order = Order.get_by_id(transaction_key)
            logger.info(f"Order {transaction_key} found (Payme).")
        except Order.DoesNotExist as exc:
            error_message = f"Order ID: {transaction_key} does not exist (Payme)."
            logger.error(error_message)
            raise exceptions.TransactionDoesNotExist(error_message) from exc

        if (order.total_cost * 100) != amount:
            error_message = f"Amount in order {transaction_key} does not match the provided amount (Payme)."
            logger.error(error_message)
            raise exceptions.IncorrectAmount(error_message)

        # check to dedublicate
        transaction = PaymentTransaction.objects.filter(
            order=order,
            payment_method__code=PaymentMethodEnum.PAYME,
        )

        if transaction:
            if transaction.first().transaction_id != transaction_id:
                logger.warning(f"Transaction {transaction_id} already exists (Payme).")
                raise exceptions.TransactionAlreadyExists(f"Transaction ID: {transaction_id} already exists (Payme).")

        transaction, created = PaymentTransaction.update_or_create(
            transaction_id=transaction_id,
            amount=amount,
            state=TransactionStateEnum.CREATED,
            payment_method=PaymentMethod.get_by_code(
                code=PaymentMethodEnum.PAYME, organization_id=order.organization.id
            ),
            order=order
        )

        if transaction.transaction_id != transaction_id:
            logger.warning(f"Transaction {transaction_id} already exists (Payme).")
            raise exceptions.TransactionAlreadyExists(f"Transaction ID: {transaction_id} already exists (Payme).")

        if created:
            logger.info(f"Transaction {transaction_id} created successfully (Payme).")
        else:
            logger.info(f"Transaction {transaction_id} already exists (Payme).")

        result = merchant.CreateTransactionResponse(
            transaction=transaction.transaction_id,
            state=self.__convert_state_to_payme(transaction),
            create_time=self.__get_event_times(transaction, for_create_time=True)
        )
        return result.as_resp()

    def perform_transaction(self, params, merchant_key) -> merchant.PerformTransactionResponse:
        """
        Performs the transaction with the given transaction ID.
        """
        transaction_id = params["id"]
        logger.info(f"Attempting to perform transaction with ID: {transaction_id} (Payme)")

        try:
            transaction = PaymentTransaction.get_by_transaction_id(transaction_id)
            if transaction.is_completed():
                logger.info(f"Transaction {transaction_id} is already completed (Payme).")
                result = merchant.PerformTransactionResponse(
                    transaction=transaction_id,
                    state=self.__convert_state_to_payme(transaction),
                    perform_time=self.__get_event_times(transaction, for_perform_time=True)
                )
                return result.as_resp()

            transaction = transaction.mark_as_completed()
        except PaymentTransaction.DoesNotExist as exc:
            error_message = f"Transaction ID: {transaction_id} does not exist (Payme)."
            logger.error(error_message)
            raise exceptions.TransactionDoesNotExist(error_message) from exc

        except Exception as exc:
            error_message = f"Failed to perform transaction with ID: {transaction_id} (Payme): {exc}"
            logger.error(error_message)
            raise exceptions.InternalServiceError(error_message) from exc

        result = merchant.PerformTransactionResponse(
            transaction=transaction_id,
            state=self.__convert_state_to_payme(transaction),
            perform_time=self.__get_event_times(transaction, for_perform_time=True)
        )

        # pylint: disable=C0415
        from apps.payment.service import PaymentService
        PaymentService.mark_as_approved(transaction.order)

        return result.as_resp()

    def cancel_transaction(self, params, merchant_key) -> merchant.CancelTransactionResponse:
        """
        Cancels the transaction with the given transaction ID.
        """
        transaction_id = params["id"]
        cancel_reason = params["reason"]
        logger.info(f"Attempting to cancel transaction with ID: {transaction_id} (Payme)")

        try:
            transaction = PaymentTransaction.get_by_transaction_id(transaction_id=transaction_id)

            if cancel_reason in [
                PaymeReasonCodesEnum.REFUND.value,
                PaymeReasonCodesEnum.TRANSACTION_FAILED.value
            ] and transaction.state == TransactionStateEnum.CREATED:
                cancel_reason = TransactionCancelReasonEnum.CREATED_AND_REFUNDED

            elif cancel_reason in [
                PaymeReasonCodesEnum.REFUND.value,
                PaymeReasonCodesEnum.TRANSACTION_FAILED.value
            ] and transaction.state == TransactionStateEnum.COMPLETED:
                cancel_reason = TransactionCancelReasonEnum.COMPLETED_AND_REFUNDED

            if transaction.is_cancelled():
                logger.info(f"Transaction {transaction_id} is already cancelled (Payme).")
                result = merchant.CancelTransactionResponse(
                    transaction=transaction_id,
                    state=self.__convert_state_to_payme(transaction),
                    cancel_time=self.__get_event_times(transaction, for_cancel_time=True)
                )
                return result.as_resp()

            elif transaction.is_completed():
                transaction = transaction.mark_as_cancelled(cancel_reason)
                result = merchant.CancelTransactionResponse(
                    transaction=transaction_id,
                    state=PaymeTransactionStateEnum.CANCELLED_WITHDRAWAL_IN_PROGRESS_2,
                    cancel_time=self.__get_event_times(transaction, for_cancel_time=True)
                )
                return result.as_resp()

            elif transaction.is_created():
                transaction = transaction.mark_as_cancelled(cancel_reason)
                result = merchant.CancelTransactionResponse(
                    transaction=transaction_id,
                    state=PaymeTransactionStateEnum.CANCELLED_AFTER_WITHDRAWAL_IN_PROGRESS_1,
                    cancel_time=self.__get_event_times(transaction, for_cancel_time=True)
                )
                return result.as_resp()

            error_message = f"Error: {transaction_id} reason: unknown transaction state: {transaction.state}"
            raise exceptions.InternalServiceError(error_message)

        except PaymentTransaction.DoesNotExist as exc:
            error_message = f"Transaction ID: {transaction_id} does not exist (Payme)."
            logger.error(error_message)
            raise exceptions.TransactionDoesNotExist(error_message) from exc

        except Exception as exc:
            error_message = f"Failed to cancel transaction with ID: {transaction_id} (Payme): {exc}"
            logger.error(error_message)
            raise exceptions.InternalServiceError(error_message) from exc

    def check_transaction(self, params, merchant_key) -> merchant.CheckTransactionResponse:
        """
        Checks the status of a transaction by its ID.
        """
        transaction_id = params["id"]
        logger.info(f"Checking status of transaction with ID: {transaction_id} (Payme)")

        transaction = PaymentTransaction.get_by_transaction_id(transaction_id=transaction_id)
        logger.info(f"Transaction {transaction_id} found with state: {transaction.state} (Payme)")

        result = merchant.CheckTransactionResponse(
            transaction=transaction.transaction_id,
            reason=self.__get_cancel_reason(transaction),
            state=self.__convert_state_to_payme(transaction),
            create_time=self.__get_event_times(transaction, for_create_time=True),
            perform_time=self.__get_event_times(transaction, for_perform_time=True),
            cancel_time=self.__get_event_times(transaction, for_cancel_time=True)
        )
        return result.as_resp()

    def get_statement(self, params, merchant_key) -> merchant.GetStatementResponse:
        """
        Retrieves a statement of transactions.
        """
        logger.info(f"Generating statement for transactions with params: {params} (Payme).")
        _from = params['from']
        _to = params['to']

        transactions = PaymentTransaction.objects.filter(
            created_at__range=[
                self.__convert_from_milliseconds(_from),
                self.__convert_from_milliseconds(_to)
            ]
        ).order_by('-created_at')

        result = merchant.GetStatementResponse(transactions=[])

        for transaction in transactions:
            result.transactions.append({
                "transaction_id": transaction.transaction_id,
                "amount": transaction.amount,
                "reason": self.__get_cancel_reason(transaction),
                "state": self.__convert_state_to_payme(transaction),
                "create_time": self.__get_event_times(transaction, for_create_time=True),
                "perform_time": self.__get_event_times(transaction, for_perform_time=True),
                "cancel_time": self.__get_event_times(transaction, for_cancel_time=True)
            })

        return result.as_resp()

    def __convert_state_to_payme(self, transaction: PaymentTransaction) -> int:
        """
        Converts internal transaction state to Payme transaction state.
        """
        if transaction.state == TransactionStateEnum.CREATED:
            return PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_1

        elif transaction.state == TransactionStateEnum.COMPLETED:
            return PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_2

        elif transaction.state == TransactionStateEnum.CANCELLED and \
                transaction.cancel_reason == TransactionCancelReasonEnum.CREATED_AND_REFUNDED:
            return PaymeTransactionStateEnum.CANCELLED_AFTER_WITHDRAWAL_IN_PROGRESS_1

        elif transaction.state == TransactionStateEnum.CANCELLED and \
                transaction.cancel_reason == TransactionCancelReasonEnum.COMPLETED_AND_REFUNDED:
            return PaymeTransactionStateEnum.CANCELLED_WITHDRAWAL_IN_PROGRESS_2

        logger.error(f"Unsupported transaction state: {transaction.state} (Payme).")
        raise NotImplementedError("Transaction state is not supported")

    def __convert_to_milliseconds(self, time) -> int:
        """
        Converts a datetime object to milliseconds since epoch.
        """
        return int(time.timestamp() * 1000)

    def __convert_from_milliseconds(self, milliseconds: int) -> datetime:
        """
        Converts milliseconds since the epoch to a datetime object.
        """
        return datetime.fromtimestamp(milliseconds / 1000)

    def __get_cancel_reason(self, transaction: PaymentTransaction) -> int:
        """
        Returns the cancel reason for a transaction.
        """
        if transaction.cancel_reason == TransactionCancelReasonEnum.COMPLETED_AND_REFUNDED:
            return PaymeReasonCodesEnum.REFUND

        elif transaction.cancel_reason == TransactionCancelReasonEnum.CREATED_AND_REFUNDED:
            return PaymeReasonCodesEnum.TRANSACTION_FAILED

        return None

    def __get_event_times(
        self,
        transaction: PaymentTransaction,
        for_create_time: bool = False,
        for_perform_time: bool = False,
        for_cancel_time: bool = False,
    ) -> int:
        """
        Update transaction times (if applicable) based on the transaction state.
        """
        if for_create_time:
            return self.__convert_to_milliseconds(transaction.created_at)

        elif for_perform_time and transaction.performed_at:
            return self.__convert_to_milliseconds(transaction.performed_at)

        elif for_cancel_time and transaction.cancelled_at:
            return self.__convert_to_milliseconds(transaction.cancelled_at)

        return 0


class PaymeClient:
    """
    PaymeClient is responsible for generating payment links for the Payme payment gateway.

    Attributes:
        payme_id (str): The merchant ID for Payme.
        payme_key (str): The merchant key for Payme.
        payme_url (str): The base URL for Payme.
        payme_account (str): The account identifier for Payme.
        payme_return_url (str): The return URL after payment is completed.
    """

    def __init__(self, payme_id: str, payme_key: str, payme_url: str, payme_account: str, payme_return_url: str):
        """
        Initializes the PaymeClient with the necessary credentials and URLs.

        Args:
            payme_id (str): The merchant ID for Payme.
            payme_key (str): The merchant key for Payme.
            payme_url (str): The base URL for Payme.
            payme_account (str): The account identifier for Payme.
            payme_return_url (str): The return URL after payment is completed.
        """
        self.payme_id = payme_id
        self.payme_key = payme_key
        self.payme_url = payme_url
        self.payme_account = payme_account
        self.payme_return_url = payme_return_url
        self.payme_pkg = Payme(
            payme_id=payme_id,
            payme_key=payme_key
        )

    def create_invoice(self, order_id, amount):
        """
        Create a new invoice
        """
        account = {
            "order_id": order_id
        }
        amount = amount * 100
        return self.payme_pkg.receipts.create(
            account=account,
            amount=amount
        )

    def send_invoice(self, order_id, amount, phone):
        """
        Implement sending invoice logic using the provided phone number
        """
        response = self.create_invoice(order_id, amount)
        return self.payme_pkg.receipts.send(
            receipts_id=response.result.receipt._id,
            phone=phone,
            timeout=10
        )

    def generate_pay_link(self, order_id: str, amount: int, return_url=None) -> str:
        """
        Generates a payment link for the given order ID and amount.

        Args:
            order_id (str): The ID of the order.
            amount (int): The amount to be paid.

        Returns:
            str: The generated payment link.
        """
        if not return_url:
            return_url = self.payme_return_url

        amount = amount * 100
        params = f'm={self.payme_id};ac.{self.payme_account}={order_id};a={amount};c={return_url}'
        encoded_params = base64.b64encode(params.encode("utf-8")).decode("utf-8")
        return f"{self.payme_url}/{encoded_params}"


payme_observer = PaymeObserver()
