"""
the agent helper object for representing
"""
from apps.user.models.user import Users
from apps.bot.tasks import send_message_task
from apps.sms.text import get_credentials_text
from apps.courier.models.courier import Courier
from apps.sms.tasks import send_sms_message_task
from apps.courier.models import courier as agent_model


from master_kebab.settings import ORDERS_CHANNEL


class AgentHelper:
    """
    The agent helper class
    """
    @classmethod
    def check_external_id_exists(cls, external_id) -> bool:
        """
        check external id exists
        """
        return agent_model.Courier.check_external_id_exists(external_id)

    @classmethod
    def change_agent_status(cls, courier_id, status) -> None:
        """
        change agent status
        """
        Courier.change_status(courier_id, status)

    @classmethod
    def create_agent_with_external_id(
        cls, data: dict
    ) -> agent_model.Courier:
        """
        Create agent with external id
        """
        external_id = data.get("id")
        organization_id = data.get("organization_id")
        name = data.get("name")
        phone = data.get("phone")

        if not phone:
            raise ValueError("Phone is required")

        password = Users.generate_random_password()

        courier = Courier.create(
            name=name,
            phone=phone,
            password=password,
            email=data.get("email"),
            organization_id=organization_id,
            external_id=external_id,
            passport=data.get("passport"),
            address=data.get("address"),
        )

        message = get_credentials_text(phone=phone, password=password, lang="ru")
        send_sms_message_task.delay(phone=phone, message=message)
        send_message_task.delay(chat_id=ORDERS_CHANNEL, message=message)

        return courier

    def get_by_id(self, courier_id) -> Courier:
        """
        get agent by id
        """
        return Courier.get_by_id(courier_id)


agent_helper = AgentHelper()
