"""
the product admin page
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.payment.models.method import PaymentMethod


class PaymentMethodUI(ModelAdmin):
    """
    the product admin page
    """
    list_display = [
        'id',
        'name',
        'code',
        'payment_type_kind',
        'organization',
        'is_processed_externally',
        'is_enabled_for_dashboard',
        'is_enabled'
    ]
    list_filter = [
        'is_enabled',
        'is_processed_externally',
        'is_enabled_for_dashboard'
    ]
    list_editable = [
        'is_enabled',
        'is_processed_externally',
        'is_enabled_for_dashboard'
    ]
    readonly_fields = [
        'name',
        'external_id',
        'code',
        'payment_type_kind'
    ]
    search_fields = ['name']
    fieldsets = (
        (None, {
            'fields': (
                'name',
                'is_enabled',
                'code',
                'external_id',
                'payment_type_kind',
                'is_processed_externally',
                'is_enabled_for_dashboard'
            ),
        }),
        ('Display Names', {
            'fields': (
                'display_name',
                'display_name_uz',
                'display_name_ru',
                'display_name_en',
            ),
            'description': 'Provide the display names in different languages.',
        }),
    )


admin.site.register(PaymentMethod, PaymentMethodUI)
