"""
login.py

This module contains views for obtaining JWT tokens for different roles in the `Operations` model.
"""

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from apps.operations.models import Operations
from apps.core.exceptions import ServiceAPIException


class TokenObtainPairView(APIView):
    """
    View to obtain JWT tokens for any role in the Operations model.
    """
    def post(self, request, *args, **kwargs):
        """
        Handle POST request to obtain JWT tokens for the specified role.
        """
        phone = request.data.get('phone')
        password = request.data.get('password')

        if not phone or not password:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Phone, password, and role are required",
                status_code=400
            )

        try:
            # Adjust this method to get the user by phone and role
            user = Operations.get_by_phone(phone)
        except Operations.DoesNotExist as exc:
            raise ServiceAPIException(
                error_type="not_found",
                message="User not found",
                status_code=404
            ) from exc

        if not user.check_password(password):
            raise ServiceAPIException(
                error_type="internal_error",
                message="Invalid credentials",
                status_code=401
            )

        resp = self.create_token_for_user(user)

        return Response(resp)

    @staticmethod
    def create_token_for_user(user):
        """
        Create JWT tokens for a user instance.
        """
        # Create a new refresh token
        refresh = RefreshToken()

        # Add custom claims to the refresh token
        refresh['name'] = user.name
        refresh['phone'] = user.phone
        refresh['id'] = user.id
        refresh['role'] = user.role

        # Create the access token associated with the refresh token
        access_token = refresh.access_token

        # Add custom claims to the access token
        access_token['name'] = user.name
        access_token['phone'] = user.phone
        access_token['role'] = user.role

        # Return tokens as strings in a dictionary
        return {
            'refresh': str(refresh),
            'access': str(access_token),
        }
