"""
implementation of create tasks
"""
from celery import Task

from master_kebab import celery_app
from apps.bot.models import TelegramUser
from apps.core import core


@celery_app.task(bind=True)
def create_telegram_user_task(
    self: Task,
    chat_id: int,
    username: str = None,
    first_name: str = None,
    last_name: str = None
):
    """
    Create a new Telegram user in background task.

    Args:
        chat_id (int): Telegram chat ID
        username (str, optional): Telegram username
        first_name (str, optional): User's first name
        last_name (str, optional): User's last name
    """
    try:
        user = TelegramUser.create(
            id=chat_id,
            username=username,
            first_name=first_name,
            last_name=last_name
        )
        log = f"Created new telegram user: {user}"
        core.log("info", log)

    except Exception as exc:
        log = f"Failed to create telegram user: {exc}"
        core.log("error", log)
