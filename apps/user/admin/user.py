"""
user's admin interface
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.user.models.user import Users


class UserUI(ModelAdmin):
    """
    the user admin page
    """
    search_fields = (
        "phone",
        "name",
    )

    list_display = [
        "id",
        "name",
        "phone",
    ]

    list_display_links = list_display


admin.site.register(Users, UserUI)
