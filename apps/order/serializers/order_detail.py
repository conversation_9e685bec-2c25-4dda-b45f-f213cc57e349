"""
the order item serialization
"""
from rest_framework import serializers

from apps.order.models import OrderDetail
from apps.loyalty.serializers import PromoSerializer


class OrderDetailSerializer(serializers.ModelSerializer):
    """
    the order item serialization
    """
    total_cost = serializers.SerializerMethodField(method_name="get_total_cost")
    promo = PromoSerializer(read_only=True)

    class Meta:
        """
        the meta fields
        """
        model = OrderDetail
        fields = "__all__"

    def get_total_cost(self, obj: OrderDetail):
        """
        Method to get the total cost of the order
        """
        return obj.total_cost


class OrderDetailForListSerializer(serializers.ModelSerializer):
    """
    the order item serialization
    """
    class Meta:
        """
        the meta fields
        """
        model = OrderDetail
        fields = (
            'delivery_address',
            'subaddress',
            'payment_method',
            'delivery_type',
            'total_cost',
            'promo'
        )
