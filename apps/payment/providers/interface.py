"""
Payment Interface Module

This module defines the `IWebHookPaymentObserver` interface, which outlines the
necessary methods for handling payment-related operations such as authorization,
transaction creation, performance, cancellation, and status checking. This interface
is designed to be implemented by various payment gateway observers to ensure a
consistent approach to processing payment transactions and handling webhook events.

Classes:
    - IWebHookPaymentObserver: An abstract base class that defines the required
      methods for payment processing and webhook handling.

Dependencies:
    - abc: Provides the Abstract Base Class (ABC) and abstractmethod decorators.
    - requests: Used for handling HTTP requests.

Usage Example:
    To create a custom payment observer, subclass the `IWebHookPaymentObserver`
    and implement all the abstract methods defined in the interface.
"""
from abc import ABC, abstractmethod

import requests


class IWebHookPaymentObserver(ABC):
    """
    IWebHookPaymentObserver Interface

    This abstract base class defines the required methods for handling payment
    processing and webhook events. Subclasses must implement all the abstract
    methods to provide custom logic for different payment gateways.

    Methods:
        - authorize(request): Authorizes the incoming request.
        - update(request): Handles webhook updates.
        - check_perform_transaction(params): Pre-validates a transaction before execution.
        - create_transaction(params): Creates a new transaction.
        - perform_transaction(params): Performs an existing transaction.
        - cancel_transaction(params): Cancels an existing transaction.
        - check_transaction(params): Checks the status of a transaction.
        - get_statement(params): Retrieves a list of transactions for a given statement period.
    """

    @abstractmethod
    def authorize(self, request):
        """
        Authorize Method

        This method is responsible for authorizing the incoming request. It must be
        implemented by subclasses to provide custom authorization logic.

        Parameters:
        ----------
        request : HttpRequest
            The incoming HTTP request to be authorized.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("authorize is not implemented")

    @abstractmethod
    def update(self, request: requests.request):
        """
        Update Method

        This method handles webhook updates by processing the incoming request. It must
        be implemented by subclasses to provide custom update logic.

        Parameters:
        ----------
        request : requests.request
            The incoming HTTP request containing the webhook update.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("update is not implemented")

    @abstractmethod
    def check_perform_transaction(self, params, merchant_key):
        """
        Check Perform Transaction Method

        This method is responsible for pre-validating a transaction before execution.
        It ensures that the transaction meets all required conditions, such as fund
        availability, transaction limits, user authorization, and security measures.
        The method must be implemented by subclasses and should raise an error if the
        transaction is not valid.

        Parameters:
        ----------
        params : dict
            A dictionary or parameter set that contains details of the transaction to be
            validated, such as the amount, user information, and transaction type.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("check_perform_transaction is not implemented.")

    @abstractmethod
    def create_transaction(self, params, merchant_key):
        """
        Create Transaction Method

        This method is responsible for creating a new transaction. It must be implemented
        by subclasses to provide custom transaction creation logic.

        Parameters:
        ----------
        params : dict
            A dictionary or parameter set that contains details of the transaction to be
            created.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("create_transaction is not implemented")

    @abstractmethod
    def perform_transaction(self, params, merchant_key):
        """
        Perform Transaction Method

        This method is responsible for performing an existing transaction. It must be
        implemented by subclasses to provide custom transaction performance logic.

        Parameters:
        ----------
        params : dict
            A dictionary or parameter set that contains details of the transaction to be
            performed.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("perform_transaction is not implemented")

    @abstractmethod
    def cancel_transaction(self, params, merchant_key):
        """
        Cancel Transaction Method

        This method is responsible for canceling an existing transaction. It must be
        implemented by subclasses to provide custom transaction cancellation logic.

        Parameters:
        ----------
        params : dict
            A dictionary or parameter set that contains details of the transaction to be
            canceled.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("cancel_transaction is not implemented")

    @abstractmethod
    def check_transaction(self, params, merchant_key):
        """
        Check Transaction Method

        This method is responsible for checking the status of a transaction. It must be
        implemented by subclasses to provide custom transaction status checking logic.

        Parameters:
        ----------
        params : dict
            A dictionary or parameter set that contains details of the transaction to be
            checked.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("check_transaction is not implemented")

    @abstractmethod
    def get_statement(self, params, merchant_key):
        """
        Get Statement Method

        This method is responsible for retrieving a list of transactions for a given
        statement period. It must be implemented by subclasses to provide custom
        statement retrieval logic.

        Parameters:
        ----------
        params : dict
            A dictionary or parameter set that contains details of the statement period
            and other relevant information.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("get_statement is not implemented")


class IProvider(ABC):
    """
    Provider Interface

    This abstract base class defines the required methods for payment providers.
    Subclasses must implement all the abstract methods to provide custom logic for
    different payment gateways.
    """
    @abstractmethod
    def create_invoice(self, order_id, amount):
        """
        Create Invoice Method

        This method is responsible for creating an invoice for the specified order.
        It must be implemented by subclasses to provide custom invoice creation logic.

        Parameters:
        ----------
        order_id : int
            The unique identifier of the order for which the invoice is being created.

        amount : int
            The total amount of the invoice, in cents.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("create_invoice is not implemented")

    @abstractmethod
    def send_invoice(self, order_id, amount, phone):
        """
        Send Invoice Method

        This method is responsible for sending an invoice to the specified phone number.
        It must be implemented by subclasses to provide custom invoice sending logic.

        Parameters:
        ----------
        invoice : dict
            A dictionary containing details of the invoice to be sent, such as the
            invoice ID, recipient's phone number, and any additional information.

        Raises:
        -------
        NotImplementedError
            This exception is raised when the method is not implemented in a subclass.
        """
        raise NotImplementedError("send_invoice is not implemented")

    @abstractmethod
    def generate_pay_link(self, order_id: int, amount: int, return_url=None) -> str:
        """
        Generate Pay Link Method

        This method generates a payment link for the given order. It must be implemented
        by subclasses to provide custom payment link generation logic.
        """
        raise NotImplementedError("generate_pay_link is not implemented")
