"""
the getting users by given filtersets
"""
from django_filters import rest_framework as filters

from apps.user.models.user import Users


class UsersFilter(filters.FilterSet):
    """
    the users filter
    """
    phone = filters.NumberFilter(field_name='phone', lookup_expr='icontains')
    lang = filters.CharFilter(field_name='lang', lookup_expr='icontains')
    gender = filters.CharFilter(field_name='gender')

    class Meta:
        """
        the meta fields
        """
        model = Users
        fields = ['phone', 'lang', 'gender']
