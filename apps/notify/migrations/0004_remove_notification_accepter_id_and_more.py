# Generated by Django 5.0.6 on 2025-01-13 15:03

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("notify", "0003_alter_notification_accepter_id"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="notification",
            name="accepter_id",
        ),
        migrations.RemoveField(
            model_name="notification",
            name="notification_id",
        ),
        migrations.RemoveField(
            model_name="notification",
            name="status",
        ),
        migrations.CreateModel(
            name="NotificationAccepters",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("notification_identity", models.IntegerField(blank=True, null=True)),
                ("user_id", models.IntegerField()),
                (
                    "user_type",
                    models.CharField(
                        choices=[("telegram", "Telegram")], max_length=255
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("processing", "Processing"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                        ],
                        max_length=255,
                    ),
                ),
                (
                    "notification",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="notify.notification",
                    ),
                ),
            ],
        ),
    ]
