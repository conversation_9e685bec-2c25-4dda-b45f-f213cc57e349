"""
django settings for master_kebab project.
"""
import os
import sys

from pathlib import Path
from environs import Env

from django.utils.translation import gettext_lazy as _


BASE_DIR = Path(__file__).resolve().parent.parent
sys.path.insert(0, os.path.join(BASE_DIR, 'apps'))

env = Env()
env.read_env()

SECRET_KEY = env.str("SECRET_KEY")
FERNET_KEY = env.str("FERNET_KEY", "F9k-B5a6UrhR7IrGf7djkGuR2OBZmhPH6z6bDfYr38Q=")

DEBUG = env.bool("IS_DEBUG", False)
IS_TEST_MODE = env.bool("IS_TEST_MODE", False)
ALLOWED_HOSTS = env.list("ALLOWED_HOSTS", "*")

INSTALLED_APPS = [
    'modeltranslation',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'whitenoise.runserver_nostatic',
    'django.contrib.staticfiles',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'master_kebab.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'master_kebab.wsgi.application'
ASGI_APPLICATION = 'master_kebab.asgi.application'

TIME_ZONE = 'Asia/Tashkent'
USE_TZ = False

USE_I18N = True


LANGUAGE_CODE = 'en'

LANGUAGES = (
    ('ru', _('Russia')),
    ('en', _('English')),
    ('uz', _('Uzbek')),
)

MODELTRANSLATION_LANGUAGES = ("uz", "ru", "en", )

LOCALE_PATHS = [
    BASE_DIR / 'locale/',
]


STATIC_URL = 'static/'
STATIC_ROOT = BASE_DIR.parent / 'static'
STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'


DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

TELEGRAM_REDIRECT_URL = env.str("TELEGRAM_REDIRECT_URL", "https://t.me/master_kebabbot") # noqa


LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': './master_kebab.log',
            'maxBytes': 100 * 1024 * 1024,  # 100 MB
            'backupCount': 5,  # Number of backup files
            'formatter': 'verbose',
        },
    },
    'loggers': {
        '': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
    },
}
