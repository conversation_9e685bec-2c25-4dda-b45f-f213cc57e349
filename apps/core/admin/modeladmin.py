"""
ModelAdmin Initialization Module

This module is responsible for initializing the ModelAdmin and StackedInline
classes used throughout the Django admin interface. It allows for customizable
admin interfaces for managing models within the Django application, while also
offering the option to integrate with the Unfold UI framework for an enhanced
user experience.

Key Features:
- Provides a foundation for creating admin interfaces for Django models.
- Supports conditional integration with the Unfold UI for a modern and
  visually appealing interface when enabled.

Usage:
- Import this module in your Django admin classes to access the customized
  ModelAdmin and StackedInline classes.
- Extend the ModelAdmin class to create tailored admin interfaces for your
  application models.

Conditional Imports:
- The `IS_UNFOLD_UI_ENABLED` setting determines whether to use the standard
  Django ModelAdmin and StackedInline classes or the Unfold UI versions.
- When `IS_UNFOLD_UI_ENABLED` is set to True, the module imports the
  ModelAdmin and StackedInline from the Unfold UI library, providing
  a richer UI experience.

Example:
```python
from apps.order.admin import OrderAdmin

class OrderAdmin(ModelAdmin):
    list_display = ('id', 'user', 'status', 'created_at')
    # Additional configuration for the OrderAdmin class...
"""
# pylint: disable=W0611, W0404
from django.contrib.admin import ModelAdmin  # NOQA
from django.contrib.admin import StackedInline # NOQA
from django.contrib.admin import TabularInline # NOQA
from django.forms import PasswordInput as PasswordInputForm # NOQA

from master_kebab.settings import IS_UNFOLD_UI_ENABLED


if IS_UNFOLD_UI_ENABLED:
    from unfold.admin import ModelAdmin # NOQA
    from unfold.admin import StackedInline # NOQA
    from unfold.admin import TabularInline # NOQA
    from master_kebab.settings.external.unfold.forms import UnfoldPasswordField as PasswordInputForm # NOQA
