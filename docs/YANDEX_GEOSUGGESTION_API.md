# Yandex Geosuggestion API Documentation

## Overview

The Yandex Geosuggestion API provides location suggestions based on search text using Yandex Maps API. This service includes automatic API key management, usage tracking, and daily limit enforcement (1000 requests per day per API key).

## Features

- 🔍 **Location Search**: Get nearby location suggestions based on search text
- 🔑 **API Key Management**: Automatic rotation and management of Yandex API keys
- 🔄 **Automatic Key Switching**: Seamlessly switches to next available key when daily limit is reached
- 📊 **Usage Tracking**: Daily request counting and limit enforcement
- 🛡️ **Rate Limiting**: Built-in throttling and daily limits
- 📱 **RESTful API**: Clean, consistent API endpoints
- 🔐 **Authentication**: Secure access with user authentication

## API Endpoints

### 1. Get Location Suggestions

**Endpoint:** `GET /api/v1/map/geosuggestion/`

**Description:** Get location suggestions based on search text.

**Authentication:** Required (Bearer <PERSON>ken)

**Query Parameters:**
- `name` (required): Search text for location suggestions (min 2 characters)
- `coordinates` (optional): Coordinates in format "longitude,latitude" (e.g., "65.38055,40.09075")
- `results` (optional): Number of results to return (1-10, default: 5)

**Example Request:**
```bash
curl -X GET "https://api.master-kebab.uz/api/v1/map/geosuggestion/?name=Aeroport&coordinates=65.38055,40.09075&results=5" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Example Response:**
```json
{
  "suggestions": [
    {
      "title": "Navoi Airport",
      "subtitle": "Navoi, Uzbekistan",
      "uri": "ymapsbm1://geo?ll=65.38055%2C40.09075",
      "distance": "2.5 km",
      "tags": ["airport", "transport"],
      "coordinates": {
        "longitude": 65.38055,
        "latitude": 40.09075
      }
    }
  ],
  "total_results": 1,
  "remaining_requests": 999,
  "status": "success"
}
```

### 2. Check API Status

**Endpoint:** `GET /api/v1/map/geosuggestion/status/`

**Description:** Check API availability and remaining daily requests.

**Authentication:** Required (Bearer Token)

**Example Request:**
```bash
curl -X GET "https://api.master-kebab.uz/api/v1/map/geosuggestion/status/" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

**Example Response:**
```json
{
  "available": true,
  "message": "API available with 999 remaining requests",
  "remaining_requests": 999
}
```

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid parameters",
  "details": {
    "name": ["This field is required."]
  },
  "status": "error"
}
```

### 429 Too Many Requests
```json
{
  "error_type": "rate_limit_exceeded",
  "message": "Daily API limit exceeded. Please try again tomorrow.",
  "status_code": 429
}
```

### 503 Service Unavailable
```json
{
  "error_type": "service_unavailable",
  "message": "Geosuggestion service is currently unavailable",
  "status_code": 503
}
```

## Usage Examples

### JavaScript/Fetch
```javascript
const response = await fetch('/api/v1/map/geosuggestion/?name=Restaurant&results=3', {
  headers: {
    'Authorization': 'Bearer ' + accessToken,
    'Content-Type': 'application/json'
  }
});

const data = await response.json();
console.log('Suggestions:', data.suggestions);
```

### Python/Requests
```python
import requests

headers = {'Authorization': f'Bearer {access_token}'}
params = {
    'name': 'Cafe',
    'coordinates': '65.38055,40.09075',
    'results': 5
}

response = requests.get(
    'https://api.master-kebab.uz/api/v1/map/geosuggestion/',
    headers=headers,
    params=params
)

data = response.json()
for suggestion in data['suggestions']:
    print(f"{suggestion['title']} - {suggestion['subtitle']}")
```

## Configuration

### API Key Setup

1. **Add API Keys in Django Admin:**
   - Go to Django Admin → Map → Map API Keys
   - Create new API key with:
     - `app_name`: "Yandex Map"
     - `usage_type`: "Yandex Geosuggestion"
     - `api_key`: Your Yandex API key
     - `owner`: Optional owner name

2. **Daily Limits:**
   - Default: 1000 requests per day per API key
   - Configurable in Django Admin → Map → API Usage Records
   - Automatic key switching when limits are reached

3. **Automatic Key Rotation:**
   - When an API key reaches its daily limit, the system automatically switches to the next available key
   - Keys are marked as `limit_expired` when they reach their daily quota
   - Daily limits reset automatically at midnight (or can be reset manually)

### Environment Variables
No additional environment variables required. The service uses existing Django settings.

### Daily Limit Reset

The system includes both Celery tasks and management commands for API limit management:

#### Automatic Celery Tasks (Recommended)

The following Celery task runs automatically:

- **Daily Limit Reset**: Runs every day at 12:30 AM (24.5 hours cycle)
  - Resets all `limit_expired` flags on API keys
  - Creates/updates daily usage records for all API keys
  - Resets usage counts to 0 for the new day
  - Cleans up old usage records (older than 30 days)

#### Manual Management Command

```bash
# Reset all API key limits (manual execution)
python manage.py reset_api_limits

# Dry run to see what would be reset
python manage.py reset_api_limits --dry-run

# Verbose output with detailed information
python manage.py reset_api_limits --verbose
```

#### Celery Task Execution

You can also manually trigger the Celery tasks:

```python
from apps.map.tasks import reset_daily_api_limits

# Manually trigger daily reset (resets keys and updates usage records)
reset_daily_api_limits.delay()
```

## Monitoring

### Django Admin Interface

1. **API Usage Tracking:**
   - Path: Django Admin → Map → API Usage Records
   - View daily usage statistics
   - Monitor remaining requests
   - Track usage trends

2. **API Key Management:**
   - Path: Django Admin → Map → Map API Keys
   - Manage API keys
   - View key status and ownership
   - Monitor expired keys

### Usage Statistics
- Daily request counts per API key
- Usage percentage visualization
- Remaining requests tracking
- Automatic key rotation when limits exceeded

## Rate Limiting

- **Throttling:** 1 request per 5 seconds per user
- **Daily Limits:** 1000 requests per day per API key
- **Automatic Rotation:** Keys are automatically rotated when limits are reached
- **Error Handling:** Graceful degradation when all keys are exhausted

## Testing

Run the test suite:
```bash
python manage.py test apps.map.tests.test_yandex_geosuggestion
```

## Troubleshooting

### Common Issues

1. **"No available API keys"**
   - Add Yandex Geosuggestion API keys in Django Admin
   - Ensure keys have `usage_type` set to "Yandex Geosuggestion"

2. **"Daily limit exceeded"**
   - Wait until next day for limit reset
   - Add additional API keys for higher capacity

3. **Invalid coordinates format**
   - Use format: "longitude,latitude" (e.g., "65.38055,40.09075")
   - Ensure longitude is between -180 and 180
   - Ensure latitude is between -90 and 90

### Logs
Check Django logs for detailed error information:
```bash
tail -f logs/django.log | grep geosuggestion
```
