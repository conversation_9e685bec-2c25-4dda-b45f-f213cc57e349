# Generated by Django 5.0.6 on 2024-12-28 00:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0017_alter_banner_image_url_alter_product_image_url"),
    ]

    operations = [
        migrations.CreateModel(
            name="Label",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("code", models.CharField(blank=True, max_length=50, null=True)),
                ("name", models.CharField(max_length=100)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.AddField(
            model_name="productattribute",
            name="labels",
            field=models.ManyToManyField(
                blank=True, related_name="product_attributes", to="product.label"
            ),
        ),
    ]
