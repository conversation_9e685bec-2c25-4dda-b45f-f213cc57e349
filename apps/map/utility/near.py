"""
utility for finding near locations
"""
from typing import List
from math import radians, cos, sin, sqrt, atan2

# Assuming UserLocation is a class with attributes latitude and longitude
from apps.map.typing import UserLocation


def haversine(lat1, lon1, lat2, lon2, r=6371):
    # Haversine formula to calculate the distance between two points on the Earth's surface
    dlat = radians(lat2 - lat1)
    dlon = radians(lon2 - lon1)
    a = sin(dlat / 2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon / 2)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))
    distance = r * c
    return distance


def find_nearest_locations(my_lat: float, my_lon: float, locations: List[UserLocation], radius: float) -> List[tuple[UserLocation, float]]: # noqa
    """
    Find locations within the given radius ordered by nearest distance.

    :param my_lat: Latitude of the reference point.
    :param my_lon: Longitude of the reference point.
    :param locations: List of Location objects to search within.
    :param radius: Radius within which to search for locations, in kilometers.
    :return: List of tuples containing Location objects and their distances, ordered by nearest distance.
    """
    nearby_locations = []

    for location in locations:
        loc_lat, loc_lon = location.latitude, location.longitude

        distance = haversine(my_lat, my_lon, loc_lat, loc_lon)
        if distance <= radius:
            nearby_locations.append((location, distance))

    # Sort the locations by distance
    nearby_locations.sort(key=lambda loc: loc[1])  # loc[1] is the distance

    return nearby_locations
