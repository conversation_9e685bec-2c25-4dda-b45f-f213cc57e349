"""
order filters
"""
import django_filters

from apps.order.models import Order


class OrderFilter(django_filters.FilterSet):
    """
    filter for orders
    """
    user_id = django_filters.NumberFilter(field_name="user__id")
    operator_id = django_filters.NumberFilter(field_name="operator_id")
    agent_user_id = django_filters.NumberFilter(field_name="delivery_agent__user__id")
    start_date = django_filters.DateFilter(field_name="created_at", lookup_expr='gte')
    end_date = django_filters.DateFilter(field_name="created_at", lookup_expr='lte')
    phone_number = django_filters.CharFilter(field_name="user__phone")

    class Meta:
        """
        the meta fields
        """
        model = Order
        fields = [
            'id',
            'user_id',
            'phone_number',
            'initiator',
            'status',
            'agent_user_id',
            'taker_id'
        ]
