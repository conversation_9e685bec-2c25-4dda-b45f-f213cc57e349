"""
Yandex Geosuggestion API Views

This module provides API views for the Yandex Geosuggestion service,
including location suggestion endpoints and API availability checks.
"""

import logging
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from apps.core.views import ServiceBaseView
from apps.core.throttling import OneRequestPerFiveSecondsThrottle
from apps.map.services.yandex_geosuggestion import YandexGeosuggestionService
from apps.map.serializer.yandex_geosuggestion import (
    YandexGeosuggestionRequestSerializer,
    YandexGeosuggestionResponseSerializer,
    ApiAvailabilitySerializer
)


logger = logging.getLogger(__name__)



class YandexGeosuggestionAPIView(APIView, ServiceBaseView):
    """
    API view for Yandex Geosuggestion service.
    
    Provides location suggestions based on search text using Yandex Maps API.
    Includes usage tracking and daily limit enforcement.
    """
    
    permission_classes = [IsAuthenticated]
    throttle_classes = [OneRequestPerFiveSecondsThrottle]
    
    def get(self, request):
        """
        Get location suggestions from Yandex Geosuggestion API.
        
        Query Parameters:
            - name (required): Search text for location suggestions
            - coordinates (optional): Coordinates in format 'longitude,latitude'
            - results (optional): Number of results to return (1-10, default: 5)
            
        Returns:
            Response: JSON response with location suggestions and metadata
        """
        # Validate request parameters
        serializer = YandexGeosuggestionRequestSerializer(data=request.query_params)
        
        if not serializer.is_valid():
            return Response(
                {
                    'error': 'Invalid parameters',
                    'details': serializer.errors,
                    'status': 'error'
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        validated_data = serializer.validated_data
        search_text = validated_data['name']
        coordinates = validated_data.get('coordinates')
        results_count = validated_data.get('results', 5)
        
        try:
            # Initialize service and get suggestions
            service = YandexGeosuggestionService()
            response_data = service.get_suggestions(
                text=search_text,
                coordinates=coordinates,
                results=results_count
            )
            
            # Validate response format
            response_serializer = YandexGeosuggestionResponseSerializer(data=response_data)
            if response_serializer.is_valid():
                logger.info(f"Successfully processed geosuggestion request for: {search_text}")
                return Response(response_serializer.validated_data, status=status.HTTP_200_OK)
            else:
                logger.error(f"Response validation failed: {response_serializer.errors}")
                raise self.call_service_exception(
                    error_type="internal_error",
                    message="Failed to format response data",
                    status_code=500
                )
        
        except ValueError as e:
            # Handle API key or limit issues
            error_message = str(e)
            logger.warning(f"Geosuggestion API limit/key error: {error_message}")
            
            if "daily limit" in error_message.lower():
                raise self.call_service_exception(
                    error_type="rate_limit_exceeded",
                    message="Daily API limit exceeded. Please try again tomorrow.",
                    status_code=429
                )
            else:
                raise self.call_service_exception(
                    error_type="service_unavailable",
                    message="Geosuggestion service is currently unavailable",
                    status_code=503
                )
        
        except Exception as e:
            # Handle other errors
            logger.error(f"Unexpected error in geosuggestion API: {str(e)}")
            raise self.call_service_exception(
                error_type="internal_error",
                message="An unexpected error occurred while processing your request",
                status_code=500
            )


class YandexGeosuggestionStatusAPIView(APIView, ServiceBaseView):
    """
    API view for checking Yandex Geosuggestion service availability.
    
    Provides information about API key availability and remaining daily limits.
    """
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Check Yandex Geosuggestion API availability.
        
        Returns:
            Response: JSON response with availability status and remaining requests
        """
        try:
            available, message = YandexGeosuggestionService.check_api_availability()
            
            response_data = {
                'available': available,
                'message': message
            }
            
            # Add remaining requests count if available
            if available:
                try:
                    from apps.map.models.api_keys import MapApiKey
                    from apps.map.models.api_usage import ApiUsage
                    
                    api_key = MapApiKey.get_yandex_geosuggestion_key()
                    if api_key:
                        remaining = ApiUsage.get_remaining_requests(api_key)
                        response_data['remaining_requests'] = remaining
                except Exception:
                    # Don't fail the whole request if we can't get remaining count
                    pass
            
            # Validate response
            serializer = ApiAvailabilitySerializer(data=response_data)
            if serializer.is_valid():
                return Response(serializer.validated_data, status=status.HTTP_200_OK)
            else:
                logger.error(f"Status response validation failed: {serializer.errors}")
                raise self.call_service_exception(
                    error_type="internal_error",
                    message="Failed to format status response",
                    status_code=500
                )

        except Exception as e:
            logger.error(f"Error checking geosuggestion API status: {str(e)}")
            raise self.call_service_exception(
                error_type="internal_error",
                message="Failed to check API status",
                status_code=500
            )
