"""
Client for Click payment system.
"""
from apps.click_up import ClickUp


class ClickClient(ClickUp):
    """
    The ClickUp client class
    """
    def generate_pay_link(self, order_id, amount, return_url):
        """
        Generates a payment link for the given order ID and amount.
        """
        return self.initializer.generate_pay_link(order_id, amount, return_url)

    def generate_superapp_pay_link(self, order_id, amount, return_url, auth=True, hide_cross=True):
        """
        Generates a payment link for Click SuperApp for the given order ID and amount.

        Args:
            order_id (int): The ID of the order
            amount (int): The amount to pay
            return_url (str): The URL to return to after payment
            auth (bool, optional): Whether to require authentication. Defaults to True.
            hide_cross (bool, optional): Whether to hide the cross button. Defaults to True.

        Returns:
            str: The generated payment link
        """
        return self.initializer.generate_superapp_pay_link(
            order_id, amount, return_url, auth, hide_cross
        )
