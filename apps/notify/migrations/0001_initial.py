# Generated by Django 5.0.6 on 2025-01-12 01:27

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "provider",
                    models.CharField(
                        choices=[
                            ("firebase", "Firebase"),
                            ("apns", "APNS"),
                            ("telegram", "Telegram"),
                        ],
                        max_length=255,
                    ),
                ),
                ("notification_id", models.Char<PERSON>ield(max_length=255)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("sent", "Sent"),
                            ("failed", "Failed"),
                        ],
                        max_length=255,
                    ),
                ),
                ("title", models.Char<PERSON>ield(max_length=255)),
                ("body", models.TextField()),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
