"""
the task helper for detecting task statuses
"""
from datetime import datetime, timedelta

import pytz

from celery.result import AsyncResult

from django.utils.timezone import make_aware


def calculate_countdown(complete_before_str: str, timezone_str: str = 'Asia/Tashkent', minutes=15) -> int:
    """
    Calculates the countdown in seconds, starting 15 minutes before the complete_before time.

    Args:
        complete_before_str (str): The complete_before datetime as a string in the format "%Y-%m-%d %H:%M:%S.%f".
        timezone_str (str): The timezone string. Default is 'Asia/Tashkent'.

    Returns:
        int: The countdown time in seconds.
    """
    # Parse the complete_before string into a datetime object
    if not isinstance(minutes, int):
        raise TypeError("The 'minutes' parameter must be an integer.")

    complete_before = datetime.strptime(complete_before_str, "%Y-%m-%d %H:%M:%S.%f")

    # Convert the datetime to a timezone-aware datetime in the specified timezone
    tz = pytz.timezone(timezone_str)
    complete_before_aware = tz.localize(complete_before)

    # Get the current time in the specified timezone
    now_tz = make_aware(datetime.now(), tz)

    # Calculate the difference between complete_before and now
    time_difference = complete_before_aware - now_tz

    # Subtract 15 minutes from the time difference
    countdown_time = time_difference - timedelta(minutes=minutes)

    # Convert the countdown time to seconds
    countdown_seconds = int(countdown_time.total_seconds())

    # Ensure countdown_seconds is not negative
    return max(countdown_seconds, 0)


def get_task_status(task_id: str) -> AsyncResult:
    """
    Retrieve the AsyncResult of a Celery task using its task ID.

    Args:
        task_id (str): The ID of the Celery task.

    Returns:
        AsyncResult: The AsyncResult object for the task.
    """
    return AsyncResult(task_id)


def terminate_task(task_result: AsyncResult) -> bool:
    """
    Terminate a Celery task if it is in progress or finished.

    Args:
        task_result (AsyncResult): The AsyncResult object for the task.

    Returns:
        bool: True if the task was terminated, False otherwise.
    """
    if task_result.state in ["PENDING", "STARTED"]:
        task_result.revoke(terminate=True)
        return True

    return False
