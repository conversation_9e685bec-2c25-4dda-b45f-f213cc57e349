"""
Telegram service for handling message sending operations
"""
import time
import logging
from typing import Optional

from sulguk import transform_html

from telebot.types import Message, InlineKeyboardMarkup, InlineKeyboardButton
from telebot.apihelper import ApiTelegramException

from apps.bot.client import bot
from apps.bot.models.telegram import TelegramUser
from apps.bot.tasks.delete import delete_message_task
from master_kebab.settings import MINI_APP_URL


logger = logging.getLogger(__name__)


MENU_TEXT = {
    "uz": "Menyu",
    "ru": "Меню",
    "en": "Menu",
}

LANGUAGE_SETTINGS = {
    "uz": "🌐 Tilni sozlash",
    "ru": "🌐 Настройки языка",
    "en": "🌐 Language settings",
}

MY_ORDERS_TEXT = {
    "uz": "📋 Mening buyurtmalarim",
    "ru": "📋 Мои заказы",
    "en": "📋 My Orders",
}



MINI_APP_URL_TEMPLATE = "{host}/?chat_id={chat_id}&lang={lang}&view={view}"


def create_notification_inline_keyboard(chat_id: str, user_lang: str = "ru") -> InlineKeyboardMarkup:
    """
    Create inline keyboard with menu and language settings buttons for notifications

    Args:
        chat_id: Telegram chat ID
        user_lang: User's language preference

    Returns:
        InlineKeyboardMarkup: Inline keyboard with menu and language buttons
    """

    menu_url = MINI_APP_URL_TEMPLATE.format(
        host=MINI_APP_URL,
        chat_id=chat_id,
        lang=user_lang,
        view="menu"
    )

    orders_url = MINI_APP_URL_TEMPLATE.format(
        host=MINI_APP_URL,
        chat_id=chat_id,
        lang=user_lang,
        view="orders"
    )
    orders_url = f"{orders_url}&ts={int(time.time())}"

    keyboard = []

    menu_button = InlineKeyboardButton(
        text=f"🍽️ {MENU_TEXT.get(user_lang, MENU_TEXT['ru'])}",
        url=menu_url
    )

    orders_button = InlineKeyboardButton(
        text=MY_ORDERS_TEXT.get(user_lang, MY_ORDERS_TEXT['ru']),
        url=orders_url
    )

    language_button = InlineKeyboardButton(
        text=LANGUAGE_SETTINGS.get(user_lang, LANGUAGE_SETTINGS['ru']),
        callback_data=f"settings_language_{chat_id}"
    )

    keyboard.append([menu_button])
    keyboard.append([orders_button])
    keyboard.append([language_button])

    return InlineKeyboardMarkup(keyboard)



class TelegramService:
    """Service for handling Telegram operations"""

    @staticmethod
    def send_photo_message(
        chat_id: str, file_url: str, title: Optional[str] = None,
        caption: Optional[str] = None, notification_id: Optional[str] = None
    ) -> int:
        """
        Send photo message to Telegram chat with inline keyboard

        Args:
            chat_id: Telegram chat ID
            file_url: URL of the photo to send
            caption: Optional message caption

        Raises:
            ApiTelegramException: If sending fails
        """
        try:
            telegram_user = TelegramUser.objects.get(id=chat_id)
            user_lang = telegram_user.lang
        except TelegramUser.DoesNotExist:
            user_lang = "ru"

        reply_markup = create_notification_inline_keyboard(chat_id, user_lang)

        if caption:
            result = transform_html(caption)

            caption = result.text
            caption_entities = Message.parse_entities(result.entities)

            response = bot.send_photo(
                chat_id,
                file_url,
                caption=caption,
                caption_entities=caption_entities,
                reply_markup=reply_markup
            )
        else:
            response = bot.send_photo(chat_id, file_url, reply_markup=reply_markup)

        if not response:
            return

        message_id = response.message_id
        return message_id

    @staticmethod
    def send_video_message(
        chat_id: str, file_url: str, title: Optional[str] = None,
        caption: Optional[str] = None, notification_id: Optional[str] = None
    ) -> int:
        """
        Send video message to Telegram chat with inline keyboard

        Args:
            chat_id: Telegram chat ID
            file_url: URL of the video to send
            caption: Optional message caption

        Raises:
            ApiTelegramException: If sending fails
        """
        try:
            # Get user language from database
            try:
                telegram_user = TelegramUser.objects.get(id=chat_id)
                user_lang = telegram_user.lang
            except TelegramUser.DoesNotExist:
                user_lang = "ru"  # Default language

            # Create inline keyboard with menu and language settings
            reply_markup = create_notification_inline_keyboard(chat_id, user_lang)

            if caption:
                result = transform_html(caption)
                caption = result.text
                caption_entities = Message.parse_entities(result.entities)

                response = bot.send_video(
                    chat_id,
                    file_url,
                    caption=caption,
                    caption_entities=caption_entities,
                    reply_markup=reply_markup
                )
            else:
                response = bot.send_video(chat_id, file_url, reply_markup=reply_markup)

            if not response:
                return None

            return response.message_id

        except Exception as e:
            logger.error(f"Failed to send video to chat {chat_id}: {str(e)}")
            raise

    @staticmethod
    def send_text_message(
        chat_id: str, title: Optional[str], message: str, notification_id: Optional[str] = None
    ) -> None:
        """
        Send text message to a chat with inline keyboard

        Args:
            chat_id: Telegram chat ID
            message: Text message to send

        Raises:
            ApiTelegramException: If sending fails
        """
        result = transform_html(message)

        message = result.text
        message_entities = Message.parse_entities(result.entities)

        try:
            telegram_user = TelegramUser.objects.get(id=chat_id)
            user_lang = telegram_user.lang
        except TelegramUser.DoesNotExist:
            user_lang = "ru"

        reply_markup = create_notification_inline_keyboard(chat_id, user_lang)

        response = bot.send_message(
            chat_id=chat_id,
            text=message,
            entities=message_entities,
            reply_markup=reply_markup
        )
        if not response:
            return

        message_id = response.message_id
        return message_id

    @staticmethod
    def deactivate_user(chat_id: str, reason: str) -> None:
        """
        Deactivate a Telegram user

        Args:
            chat_id: Telegram chat ID
            reason: Reason for deactivation
        """
        try:
            TelegramUser.objects.filter(id=chat_id).update(is_active=False)
            logger.info(f"Deactivated user {chat_id}. Reason: {reason}")
        except Exception as e:
            logger.error(f"Failed to deactivate user {chat_id}: {str(e)}")

    @staticmethod
    def should_deactivate_user(error: ApiTelegramException) -> bool:
        """
        Check if user should be deactivated based on error

        Args:
            error: Telegram API exception

        Returns:
            bool: True if user should be deactivated
        """
        error_msg = str(error).lower()
        error_code = getattr(error, 'error_code', None)

        deactivation_reasons = [
            "chat not found",
            "bot was blocked",
            "user is deactivated",
            "bot can't initiate conversation"
        ]

        return (
            error_code == 403 or
            any(reason in error_msg for reason in deactivation_reasons)
        )



    @staticmethod
    def delete_message(chat_id, message_id):
        """
        Delete messages by IDs

        Args:
            ids: IDs of messages to delete
        """
        delete_message_task.delay(
            chat_id=chat_id,
            message_id=message_id
        )
