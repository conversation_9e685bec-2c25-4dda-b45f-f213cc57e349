"""
the payment webhook for given providers
"""
from rest_framework import views
from rest_framework import response

from apps.core import core
from apps.payment.providers.payme.client import payme_observer


class PaymentWebhookView(views.APIView):
    """
    The payment webhook view
    """
    allowed_providers = {
        "payme": payme_observer,
    }

    def post(self, request):
        """
        The post method for the payment webhook
        """
        query_params = request.GET

        if not query_params:
            core.log("error", message="No provider specified in query parameters")
            return response.Response(status=400)

        provider_type = query_params.get("provider_type")

        if not provider_type:
            core.log("error", message="Missing 'provider_type' in query parameters")
            return response.Response(status=400)

        observer = self.allowed_providers.get(provider_type)

        if not observer:
            core.log("error", message=f"Invalid provider_type: {provider_type}")
            return response.Response(status=400)

        observer.authorize(request)
        result = observer.update(request)

        return response.Response(result)
