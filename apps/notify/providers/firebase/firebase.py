"""
the fire base notification class initalizes
"""
import logging

import firebase_admin
from firebase_admin import credentials, messaging

from apps.bot.tasks.message import send_message_task


logger = logging.getLogger(__name__)


service_account_info = {
    "type": "service_account",
    "project_id": "masterkebab-delivery",
    "private_key_id": "6823c629883cf96b3132c29f5db44f555cfe3aa0",
    "private_key": """-----B<PERSON>IN PRIVATE KEY-----
MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCGoeWYXKShjg6X
M3oCQGO+KPnOIRCKyplB1zqnEBkzO+NXbWhOCUlcLVQBXVfOxcjg7Z9qWfX/8saE
/toBrCpiyM19uq3pgUeHZoeM2IM5JasuLz0nsJCEnZ/r10jRJA1fTb3qlZskmhyN
E0tAg24hjOxUVcXOCYql3MDdtt6m7pedJGU8TjEH2re/tIiCxLIFwJZWfJTmyVS4
epoORukxpSa86qvmGsskFTUoM9loVV6YEfM1TROrEoqTAekWeNP5kE9KiadnQQJu
bIN18P4lIQ8VYFk1xy7a/OoK7zWeGBfJZqiRNBcuaMXmKYPkE4GMsQLJAomNYhT0
aNNS4MLLAgMBAAECggEAD9MYELRpo2r2UGkRYRIJcfC7YAenO/ERWi7+qrlWhPJN
SARI8MyK+LsujGnY0tSQ8SNCYXaegXhF7kb4VUmpF8Bg1Z5A0oP1ZjcJHWcrkWbZ
5o1Qjl7ktbnDR4N1Vc57heVg5SGy68vzftTdkQEsdjlsDoOgbZXV2/tabhUQZFAr
uRMFrGHdSpjXPAzqRPrgwNxIaJ3IrXrl/AKaZcUCvueVKGi/Aqn9lrwC+jyPW3Hk
bHY7bHz/MdqVyayRyXQefik5/8oQTTPn0KfGc7U75yxwoyDV42Gz1EICk4Gg67vv
W1LCrgx33UoXHcV5VQbTpyptNh89tuoY9Vf6qlhgrQKBgQC+NoCcmwF+HdQJ4crA
+16P7Qsk5xF/DhJaDBMhrLCXF/tyS6mHLC4K7tOksY/JevxMb/Ob15zA1+7/gzOk
njm0kooZb6affYxywKE+FWkjuY8rH6mKXiSWI5gmwrQ5mSC1NLPNodMJ+YzlMT0K
iQIqzgz7Dj3fCt6qxwVeowbA9QKBgQC1MkizQNhIfIp0NggCyXeLjFpooylOqpWv
tUvx+tEhtcwy31YzBPy7qgt/hHQZJMRS8aTx0gvE9SpvMsFL+V2jUUE88yOtZMyg
f5MrhEnxGtdCfp5i+5AIzq8r8v9SiQRrpoNjHpkw0XiocTJ+Ir+9d+mc//jgCDXz
4Wt0H0wcvwKBgHcDxyk125M7uqaMPr7NCXfoi6aFZe8nsNfi4j2ZvcxsVwpGyUYo
oI56rLZJ23vnleGF0tlZ6VzIP2RkhXWlAto3Lm2H7KuydKSAXuNnpieSPUBwSTao
fR/sGZF3FilL5Kk81pv9evysxjnJuzXSUCbUUk7rtE7QkE4uxO9X9ivZAoGASrzq
78UXcQ6trTF8LGodPRovUGm8aA5En60QHkdua5EpIPKuA21TMbT5+ScRHC3mLLAv
cO9Km3rkfelZgZTh4rNpTVKcrLCfFrSIZ7KxLRn9IBhKDFnnFzrpVh9s4QZTJHQS
TZgZZ+F++zt1sEF1BRmo6nFKnl3Eo0ufns4p/tECgYAA1IyiXUehD5e6poNrtPns
jCesBzbWcoLcfby9RB4a6UzoH0N1xK0ZqbGlttNZBWUs5KJQmrlwM9LOrW++Rmvj
2zfAxt9TWNissj4iZJgzSGeJgRu9lfWxOEpGKgrBewHIyh2HQAuR0X9LkmCQJjAE
dZwLghmuC2e8ab9xgDgZGg==
-----END PRIVATE KEY-----""",
    "client_email": "<EMAIL>",
    "client_id": "100757467705833258270",
    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
    "token_uri": "https://oauth2.googleapis.com/token",
    "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
    "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>", # noqa
    "universe_domain": "googleapis.com"
}


class FirebaseNotificationService:
    """
    The Fire Base notification class.
    """
    def __init__(self, service_account_info: str = service_account_info):
        """
        Initialize the Firebase Admin SDK with the service account.

        :param service_account_info: Path to the service account JSON file.
        """
        self.service_account_info = service_account_info
        self._initialize_firebase()

    def _initialize_firebase(self):
        """
        Initialize the Firebase app with the service account credentials.
        """
        cred = credentials.Certificate(self.service_account_info)
        firebase_admin.initialize_app(cred)

    def send_notification(self, title: str, body: str, token: str) -> str:
        """
        Send a notification message to a specific device.

        :param title: Title of the notification.
        :param body: Body content of the notification.
        :param token: The FCM token of the target device.
        :return: Response from the FCM server.
        """
        body = body.to_firebase()
        message = messaging.Message(
            notification=messaging.Notification(
                title=title,
            ),
            token=token,
            data=body
        )

        send_message_task.delay(
            message=str(body)
        )
        return messaging.send(message)


firebase_provider = FirebaseNotificationService()
