# Softphone Integration App

Master <PERSON> backend tizimi uchun Softphone.Pro integratsiya moduli.

## 🎯 Maqsad

Bu modul call center operatorlariga quyidagi imkoniyatlarni taqdim etadi:
- Mi<PERSON><PERSON>larga to'g'ridan-to'g'ri qo'ng'iroq qilish (Click-to-Call)
- Ki<PERSON><PERSON><PERSON> qo'ng'iroqlarda mijoz ma'lumotlarini ko'rish
- Qo'ng'iroqlarni avtomatik loglash va yozib olish
- Qo'ng'iroq statistikasi va hisobotlar

## 📦 O'rnatish

1. **Migration ishga tushirish:**
```bash
python manage.py makemigrations softphone
python manage.py migrate
```

2. **Boshlang'ich konfiguratsiya:**
```bash
python manage.py init_softphone_config --base-url=http://localhost:8000
```

3. **Test ma'lumotlari (ixtiyoriy):**
```bash
python manage.py create_sample_calls --count=10
```

## 🔧 Konfiguratsiya

### Django Settings
```python
# settings.py
INSTALLED_APPS = [
    # ...
    'apps.softphone',
]

# Softphone sozlamalari
SOFTPHONE_ENABLED = True
SOFTPHONE_DEFAULT_SIP_ACCOUNT = "1"
SOFTPHONE_RECORDING_STORAGE = "local"
```

### URLs
```python
# urls.py
urlpatterns = [
    # API endpoints
    path('api/v1/softphone/', include('apps.softphone.urls')),
    # Frontend views
    path('softphone/', include('apps.softphone.urls')),
]
```

## 🚀 Foydalanish

### 1. Click-to-Call
```html
<!-- HTML da -->
<a href="sip:************">+998 90 123 45 67</a>

<!-- JavaScript bilan -->
<script>
function makeCall(phone, customerId) {
    window.location.href = `sip:${phone}?extid=${customerId}`;
}
</script>
```

### 2. API orqali qo'ng'iroq
```python
import requests

response = requests.post('http://localhost:8000/api/v1/softphone/click-to-call/', {
    'phone_number': '************',
    'customer_id': 123
})
```

### 3. Mijoz ma'lumotlarini olish
```python
response = requests.get('http://localhost:8000/api/v1/softphone/customer-data/', {
    'phone': '************'
})
customer_data = response.json()
```

## 📊 Modellar

### CallLog
Qo'ng'iroqlar tarixi va ma'lumotlari:
- `caller_id` - Qo'ng'iroq qiluvchi raqami
- `called_number` - Qo'ng'iroq qilingan raqam
- `direction` - Yo'nalish (inbound/outbound)
- `status` - Holat (answered/missed/busy/etc.)
- `duration` - Davomiyligi (soniyalarda)
- `recording_file` - Yozuv fayli
- `user` - Bog'langan mijoz
- `order` - Bog'langan buyurtma

### SoftphoneConfig
Integratsiya sozlamalari:
- `screen_popup_url` - Screen popup URL
- `customer_data_url` - Mijoz ma'lumotlari URL
- `call_log_url` - Qo'ng'iroq loglash URL
- `recording_upload_url` - Yozuv yuklash URL
- `enable_*` - Funksiyalarni yoqish/o'chirish

## 🔗 API Endpoints

| Method | Endpoint | Tavsif |
|--------|----------|--------|
| POST | `/api/v1/softphone/click-to-call/` | Click-to-call URL yaratish |
| GET | `/api/v1/softphone/popup/` | Screen popup ma'lumotlari |
| GET | `/api/v1/softphone/customer-data/` | Mijoz ma'lumotlari |
| POST | `/api/v1/softphone/log-call/` | Qo'ng'iroqni loglash |
| GET | `/api/v1/softphone/call-history/` | Qo'ng'iroqlar tarixi |
| GET | `/api/v1/softphone/call-stats/` | Qo'ng'iroq statistikasi |
| POST | `/api/v1/softphone/upload-recording/` | Yozuv yuklash |

## 🎨 Frontend

### Screen Popup
Kiruvchi qo'ng'iroqlarda avtomatik ochiladi:
- Mijoz ma'lumotlari
- Oxirgi buyurtmalar
- Qo'ng'iroq tarixi
- Qo'ng'iroqni boshqarish tugmalari

### Admin Interface
Django admin panelida:
- Qo'ng'iroqlar ro'yxati va filtrlash
- Yozuvlarni tinglash
- Konfiguratsiya boshqaruvi
- Statistika ko'rish

## 🧪 Test qilish

### Unit testlar
```bash
python manage.py test apps.softphone
```

### Manual test
```bash
# Customer data test
curl "http://localhost:8000/api/v1/softphone/customer-data/?phone=************"

# Screen popup test
open "http://localhost:8000/softphone/popup/?callerid=************"
```

## 📈 Monitoring

### Call Statistics
```python
from apps.softphone.models import CallLog

# Bugungi qo'ng'iroqlar
today_calls = CallLog.objects.filter(
    start_time__date=timezone.now().date()
).count()

# Javob berish foizi
answer_rate = CallLog.objects.filter(
    status='answered'
).count() / CallLog.objects.count() * 100
```

### Performance Metrics
- API response time
- Call popup load time
- Recording upload success rate
- Database query optimization

## 🔐 Security

### Authentication
API endpointlar authentication talab qiladi:
```python
from rest_framework.permissions import IsAuthenticated

class CallLoggingAPIView(APIView):
    permission_classes = [IsAuthenticated]
```

### Rate Limiting
```python
# settings.py
REST_FRAMEWORK = {
    'DEFAULT_THROTTLE_RATES': {
        'softphone': '1000/hour'
    }
}
```

## 🐛 Debugging

### Log sozlamalari
```python
# settings.py
LOGGING = {
    'loggers': {
        'apps.softphone': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

### Common Issues
1. **Click-to-call ishlamayapti**: Softphone.Pro o'rnatilganligini tekshiring
2. **Screen popup ochilmayapti**: URL va server holatini tekshiring
3. **Recording upload xatolik**: File permissions va storage sozlamalarini tekshiring

## 🚀 Production

### Environment Variables
```bash
SOFTPHONE_ENABLED=True
SOFTPHONE_BASE_URL=https://your-domain.com
SOFTPHONE_RECORDING_STORAGE=s3
```

### Nginx Configuration
```nginx
location /softphone/ {
    proxy_pass http://127.0.0.1:8000;
}
```

## 📚 Qo'shimcha ma'lumot

- [Softphone.Pro Documentation](https://softphone.pro/en/developers)
- [Master Kebab API Documentation](../docs/API.md)
- [Integration Guide](../docs/SOFTPHONE_INTEGRATION.md)

## 🤝 Contributing

1. Fork repository
2. Feature branch yarating
3. Testlar yozing
4. Pull request yuboring

## 📄 License

MIT License - [LICENSE](../LICENSE) faylini ko'ring.

---

**Muallif**: Master Kebab Development Team  
**Versiya**: 1.0.0  
**Oxirgi yangilanish**: 2024-01-01
