"""
init order typing
"""
from typing import Literal

from pydantic import BaseModel


class Data(BaseModel):
    """
    new order message data field
    """
    courier_id: int


class SocketDisconnected(BaseModel):
    """
    new order message model type
    """
    type: Literal['socket_disconnected']
    data: Data

    @classmethod
    def make_message(cls, courier_id):
        """
        make socket disconnected message
        """
        return cls(
            type="socket_disconnected",
            data=Data(
                courier_id=courier_id
            )
        ).model_dump()
