"""
This module defines the HistoricalOrderSerializer, which is responsible for
serializing and deserializing instances of the HistoricalOrderModel. This allows
for easy conversion between model instances and JSON representations, facilitating
the transfer and manipulation of historical order data within the application.

Usage:
    To serialize a HistoricalOrderModel instance, create an instance of the
    HistoricalOrderSerializer and pass the model instance to it. To deserialize
    JSON data into a HistoricalOrderModel instance, pass the JSON data to the
    serializer's `data` parameter and call the `is_valid` and `save` methods.

Example:
    serializer = HistoricalOrderSerializer(historical_order_instance)
    json_data = serializer.data

    deserializer = HistoricalOrderSerializer(data=json_data)
    if deserializer.is_valid():
        historical_order_instance = deserializer.save()
"""

from rest_framework import serializers
from apps.order.models.historical import HistoricalOrderModel


class HistoricalOrderSerializer(serializers.ModelSerializer):
    """
    Serializer for the HistoricalOrderModel.

    This serializer converts HistoricalOrderModel instances to and from JSON
    representations, allowing for easy serialization and deserialization of
    historical order data.

    Meta:
        model (HistoricalOrderModel): The model that this serializer is for.
        fields (list): The fields to include in the serialized representation.
    """
    class Meta:
        """
        Meta class for defining serializer options.
        """
        model = HistoricalOrderModel
        fields = ['status', 'created_at', 'updated_at']
