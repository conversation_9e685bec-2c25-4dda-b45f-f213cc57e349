# Generated manually

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('loyalty', '0005_promo_promo_amount'),
        ('user', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='loyaltyprogram',
            name='initiator',
            field=models.CharField(
                max_length=20,
                choices=[
                    ('bot', 'Bot'),
                    ('ios', 'iOS'),
                    ('web', 'Web'),
                    ('android', 'Android'),
                    ('call-center', 'Call Center'),
                    ('unknown', 'Unknown'),
                ],
                blank=True,
                null=True,
                help_text="Initiator allowed to use this program"
            ),
        ),
        migrations.AddField(
            model_name='promo',
            name='promocode_type',
            field=models.CharField(
                choices=[('amount', 'Fixed Amount'), ('percent', 'Percentage')],
                default='amount',
                help_text='Type of discount: fixed amount or percentage',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='promo',
            name='min_order_amount',
            field=models.FloatField(
                default=0,
                help_text='Minimum order amount required to use this promo code'
            ),
        ),
        migrations.CreateModel(
            name='PromoCodeUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_id', models.IntegerField(help_text='ID of the order where promo was used')),
                ('discount_amount', models.FloatField(default=0, help_text='Actual discount amount applied to the order')),
                ('order_total', models.FloatField(default=0, help_text='Total order amount before discount')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('promo', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='loyalty.promo')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.users')),
            ],
        ),
    ]
