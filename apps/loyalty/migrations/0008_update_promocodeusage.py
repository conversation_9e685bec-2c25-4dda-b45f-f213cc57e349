# Generated manually

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('loyalty', '0007_remove_userpromo'),
    ]

    operations = [
        migrations.AddField(
            model_name='promocodeusage',
            name='is_used',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='promocodeusage',
            name='program',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='loyalty.loyaltyprogram',
                null=True
            ),
        ),
        migrations.AddField(
            model_name='promocodeusage',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='promocodeusage',
            name='order_id',
            field=models.IntegerField(
                blank=True,
                help_text='ID of the order where promo was used',
                null=True
            ),
        ),
        migrations.AlterUniqueTogether(
            name='promocodeusage',
            unique_together={('user', 'program')},
        ),
    ]
