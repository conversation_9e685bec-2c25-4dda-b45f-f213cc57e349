"""
init operations serializers
"""
from rest_framework import serializers

from apps.operations.models import Operations
from apps.organization.serializers.organization import OrganizationSerializer


class OrganizationSerializerUPT(OrganizationSerializer):
    """
    The organization serialization for update
    """
    class Meta(OrganizationSerializer.Meta):
        """
        The meta fields for update
        """
        fields = (
            'id',
        )


class OperationsSerializer(serializers.ModelSerializer):
    """
    Serializer for the Operations model.
    Handles serialization and deserialization of Operations instances,
    which include roles such as manager, operator, dispatcher, and super admin.
    """
    password = serializers.CharField(write_only=True, required=False, allow_blank=True)
    # organization = OrganizationSerializerUPT(required=False)

    class Meta:
        """
        the meta class for the operations serializer
        """
        model = Operations
        fields = [
            'id',
            'phone',
            'name',
            'passport',
            'organization',
            'password',
            'is_active',
            'role',
            'is_verified',
            'is_staff',
            'is_blocked',
            'is_deleted',
        ]
        read_only_fields = ['id', 'is_staff', 'is_deleted', 'role']

    def validate_role(self, value):
        """
        Ensure that the role field contains a valid role.
        """
        valid_roles = ['manager', 'operator', 'dispatcher']
        if value not in valid_roles:
            raise serializers.ValidationError("Invalid role. Choose from: manager, operator, dispatcher, super_admin.")
        return value

    def create(self, validated_data):
        """
        Create a new Operations instance with the given validated data.
        """
        # Normalize phone number by removing leading '+'
        phone = validated_data.get('phone', '').lstrip('+')
        validated_data['phone'] = phone
        print(validated_data)

        password = validated_data.pop('password', None)
        print(password)
        operations_instance = Operations.objects.create(**validated_data)
        if password:
            operations_instance.set_password(password)
        operations_instance.save()
        return operations_instance

    def update(self, instance, validated_data):
        """
        Update an existing Operations instance with the given validated data.
        """
        password = validated_data.pop('password', None)
        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        if password:
            instance.set_password(password)

        instance.save()
        return instance


class OperatorForOrderListSerializer(OperationsSerializer):
    """
    Serializer for listing operators in the context of an order.
    """

    class Meta(OperationsSerializer.Meta):
        """
        Meta class for OperatorForOrderListSerializer.
        """
        fields = (
            'id',
            'name',
            'phone',
            'role',
        )
