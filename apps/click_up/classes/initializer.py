
"""
Initializer module for Click payment system.
"""
import urllib.parse

# pylint: disable=W0622


class Initializer:
    """
    Initializer class for generating payment URLs for the Paylik system.
    """
    def __init__(self, service_id, merchant_id):
        """
        Initialize Initializer object

        Args:
            service_id (str): Service ID provided by CLICK
            merchant_id (str): Merchant ID provided by CLICK
        """
        self.service_id = service_id
        self.merchant_id = merchant_id

    def generate_pay_link(self, id, amount, return_url):
        """
        Generate a payment URL for the Paylik system.

        :param amount: Payment amount (float or string)
        :param id: Transaction-specific parameter (string)
        :param return_url: URL to return to after payment (string)
        :return: Generated Paylik URL (string)
        """
        base_url = "https://my.click.uz/services/pay"
        paylik_url = (
            f"{base_url}?service_id={self.service_id}&merchant_id={self.merchant_id}" # noqa
            f"&amount={amount}&transaction_param={id}"
            f"&return_url={return_url}"
        )
        return paylik_url

    def generate_superapp_pay_link(self, id, amount, return_url, auth=True, hide_cross=True):
        """
        Generate a payment URL for the Click SuperApp.

        :param amount: Payment amount (float or string)
        :param id: Transaction-specific parameter (string)
        :param return_url: URL to return to after payment (string)
        :param auth: Whether to require authentication (boolean)
        :param hide_cross: Whether to hide the cross button (boolean)
        :return: Generated SuperApp URL (string)
        """
        # Create the base deep link
        auth_str = str(auth).lower()
        hide_cross_str = str(hide_cross).lower()
        deep_link = (
            f"https://my.click.uz/app/webView?auth={auth_str}"
            f"&hide_cross={hide_cross_str}&url="
        )

        # Add the page to the deep link and encode it
        encoded_deep_link_with_page = deep_link + urllib.parse.quote(return_url)

        # Encode the final deep link again
        encoded_final_deep_link = urllib.parse.quote(encoded_deep_link_with_page)

        # Create the final URL
        base_url = "https://my.click.uz/services/pay"
        final_url = (
            f"{base_url}/?service_id={self.service_id}&merchant_id={self.merchant_id}" # noqa
            f"&amount={amount}&transaction_param={id}"
            f"&return_url={encoded_final_deep_link}"
        )

        return final_url
