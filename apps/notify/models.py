from django.db import models

from apps.core.models.base import BaseModel


class Notification(BaseModel):
    """
    Notification model

    example for get notification_accepter
    notification = Notification.objects.all()
    notification_accepter = notification.notificationaccepters.all()
    """
    PROVIDER_CHOICES = [
        ('firebase', 'Firebase'),
        ('apns', 'APNS'),
        ('telegram', 'Telegram'),
    ]
    provider = models.CharField(max_length=255, choices=PROVIDER_CHOICES)
    title = models.CharField(max_length=255)
    body = models.TextField()

    @classmethod
    def create_notification(cls, provider: str, title: str, body: str):
        return cls.objects.create(
            provider=provider,
            title=title,
            body=body
        )


class NotificationAccepters(models.Model):
    """
    Notification accepters model
    """
    STATUS_CHOICES = [
        ('processing', 'Processing'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
    ]
    USER_TYPE_CHOICES = [
        ('telegram', 'Telegram'),
    ]

    notification = models.ForeignKey(
        Notification,
        on_delete=models.CASCADE,
        null=True,
        related_name="notificationaccepters"
    )
    notification_identity = models.BigIntegerField(null=True, blank=True)
    user_id = models.BigIntegerField()
    user_type = models.CharField(max_length=255, choices=USER_TYPE_CHOICES)
    status = models.CharField(max_length=255, choices=STATUS_CHOICES)
    error_message = models.TextField(null=True, blank=True)

    @classmethod
    def create_notification_accepter(
        cls, notification_id: str, user_id: int, user_type: int, status: str, notification_identity=None
    ):
        return cls.objects.create(
            notification_id=notification_id,
            user_id=user_id,
            user_type=user_type,
            status=status,
            notification_identity=notification_identity
        )

    @classmethod
    def set_notification_identity(
        cls, notification_id: str, user_id: int, notification_identity: str, status: str, error_message: str = None
    ):
        """
        Set notification identity and status
        """
        cls.objects.filter(
            notification_id=notification_id,
            user_id=user_id
        ).update(
            notification_identity=notification_identity,
            status=status,
            error_message=error_message
        )
