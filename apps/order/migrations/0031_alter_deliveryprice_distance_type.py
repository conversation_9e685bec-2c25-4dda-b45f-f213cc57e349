# Generated by Django 5.0.6 on 2024-12-18 21:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0030_bookmark_is_client_bookmarked"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="deliveryprice",
            name="distance_type",
            field=models.CharField(
                choices=[
                    ("VN", "Very Nearby (0-3 km)"),
                    ("SN", "Slightly Near (1-2km)"),
                    ("NB", "Nearby (3-5 km)"),
                    ("LC", "Local Close (5-6 km)"),
                    ("LM", "Local Mid (6-8 km)"),
                    ("LF", "Local Far (8-10 km)"),
                    ("DC", "Distant Close (10-12 km)"),
                    ("DM", "Distant Mid (12-15 km)"),
                    ("DF", "Distant Far (15-18 km)"),
                    ("RM", "Remote (18-20 km)"),
                ],
                default="NB",
                help_text="Type of delivery based on distance",
                max_length=8,
                unique=True,
            ),
        ),
    ]
