"""
This module defines the data structures for making HTTP requests related to addresses
in the iiko system. It uses Pydantic for data validation and serialization.
"""

from pydantic import BaseModel


class ByCityRequestBody(BaseModel):
    """
    ByCityRequestBody represents the payload required to fetch address-related information
    from the iiko system.

    Attributes:
        organizationId (UUID): The unique identifier of the organization.
        cityId (UUID): The unique identifier of the city.
        includeDeleted (bool): Flag to include deleted addresses in the response.
    """
    organizationId: str
    cityId: str
    includeDeleted: bool

    def as_request_body(self):
        return {
            "organizationId": self.organizationId,
            "cityId": self.cityId,
            "includeDeleted": False
        }
