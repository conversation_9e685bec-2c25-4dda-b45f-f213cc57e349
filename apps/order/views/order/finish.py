"""
init order finish view for delivery agents
"""
from rest_framework import views, response

from apps.order.models.bookmarks import Bookmark
from apps.order.helper import order_status_helper
from apps.core.exceptions import ServiceAPIException
from apps.core.throttling import OneRequestPerFiveSecondsThrottle


class OrderFinishAPIView(views.APIView):
    """
    API view to handle the completion of orders by delivery agents.
    """
    throttle_classes = [OneRequestPerFiveSecondsThrottle]

    def post(self, request, *args, **kwargs):
        """
        Handle POST request to finish an order.
        """
        order_id = kwargs.get("order_id")
        latitude = request.data.get("latitude")
        longitude = request.data.get("longitude")

        self.validate_parameters(order_id, latitude, longitude)
        latitude, longitude = self.convert_coordinates(latitude, longitude)

        try:
            self.process_order(order_id, latitude, longitude)
        except ServiceAPIException as exc:
            raise exc  # Reraise known exceptions
        except Exception as exc:
            msg = f"Finish order failed: {exc}"
            raise ServiceAPIException(
                error_type="internal_error",
                message=msg
            ) from exc

        return response.Response(status=200)

    def validate_parameters(self, order_id, latitude, longitude):
        """
        Validate required parameters.
        """
        if not all([latitude, longitude, order_id, self.request.user.id]):
            raise ServiceAPIException(
                error_type="bad_request",
                message="Missing required parameters: order_id, latitude, longitude",
                status_code=400
            )

    def convert_coordinates(self, latitude, longitude):
        """
        Convert latitude and longitude to float.
        """
        try:
            return float(latitude), float(longitude)
        except ValueError as exc:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Invalid latitude or longitude value",
                status_code=400
            ) from exc

    def process_order(self, order_id, latitude, longitude):
        """
        Finish the order and update the bookmark.
        """
        order = order_status_helper.get_order_by_id(order_id)
        if not order.is_status_arrived():
            raise ServiceAPIException(
                error_type="invalid_status",
                message="Order is not in ARRIVED status",
                status_code=400
            )

        order_status_helper.finish_order_with_provider(
            order_ext_id=order.external_id
        )
        Bookmark.update_point(
            order_id=order_id,
            latitude=latitude,
            longitude=longitude
        )
