"""
init iiko nomenclature class
"""
from typing import List, Dict

from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import MenuByIdRequestBody
from apps.iiko.providers.iiko.http.response import MenuByIdResponseBody
from apps.iiko.providers.iiko.http.request.menu import StopListsRequestBody
from apps.iiko.providers.iiko.http.response.menu import StopListResponseBody


class IIKOMenuAPI:
    """
    IIKO getting nomenclature API client
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def by_id(self, organization_ids: list[str], menu_id: int, headers: dict) -> MenuByIdResponseBody:
        """
        Based on the Organization ID you received above, request a menu.
        """
        endpoint = "/api/2/menu/by_id"
        request_body = MenuByIdRequestBody(
            organizationIds=organization_ids,
            externalMenuId=menu_id
        ).as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        response_body = MenuByIdResponseBody(**response_body)
        return response_body

    def stop_lists(
        self,
        organization_ids: List[str],
        terminal_ids: List[str],
        headers: Dict[str, str]
    ) -> StopListResponseBody:
        """
        Request a list of stop lists for a set of terminal IDs.
        """
        endpoint = "/api/1/stop_lists"
        request_body = StopListsRequestBody(
            organizationIds=organization_ids,
            terminalGroupsIds=terminal_ids
        ).as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        response_body = StopListResponseBody(**response_body).model_dump()
        return response_body


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
manu = IIKOMenuAPI(http=http_base_client)
