"""
the payment method serialization
"""
from rest_framework import serializers

from apps.payment.models import PaymentMethod


class PaymentMethodSerializer(serializers.ModelSerializer):
    """
    the payment method serialization
    """
    organization_name = serializers.SerializerMethodField(
        method_name='get_organization_name'
    )

    class Meta:
        """
        the meta fields
        """
        model = PaymentMethod
        fields = [
            'id',
            'name',
            'code',
            'display_name',
            'is_enabled',
            'organization_name',
            'is_enabled_for_dashboard',
        ]

    def get_organization_name(self, obj: PaymentMethod) -> str:
        """
        Get the organization name for the given PaymentMethod instance.

        Args:
            obj (PaymentMethod): The PaymentMethod instance.

        Returns:
            str: The name of the organization.
        """
        return obj.organization.name
