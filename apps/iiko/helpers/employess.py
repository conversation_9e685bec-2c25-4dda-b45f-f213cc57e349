"""
the employess helper for easy implementation for internal service
"""
from apps.iiko.providers.iiko.http.client import client, IIKOClientAPI


class EmployeesHelper:
    """
    The employess helper class
    """
    def __init__(self, provider: IIKOClientAPI):
        self.client = provider

    def open_shift(self, external_id, organization_id, terminal_group_id):
        return self.client.employees.shift_clockin(
            organization_id=organization_id,
            terminal_group_id=terminal_group_id,
            employee_id=external_id,
            headers=client.variables.get_headers()
        )

    def close_shift(self, external_id, organization_id, terminal_group_id):
        return self.client.employees.shift_clockout(
            organization_id=organization_id,
            terminal_group_id=terminal_group_id,
            employee_id=external_id,
            headers=client.variables.get_headers()
        )


employess_helper = EmployeesHelper(
    provider=client
)
