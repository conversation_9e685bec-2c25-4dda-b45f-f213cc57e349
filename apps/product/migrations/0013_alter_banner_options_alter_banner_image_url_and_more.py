# Generated by Django 5.0.6 on 2024-11-18 22:38

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0012_banner"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="banner",
            options={"ordering": ["id"]},
        ),
        migrations.AlterField(
            model_name="banner",
            name="image_url",
            field=models.ImageField(
                upload_to="banners",
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "png", "webp"]
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="banner",
            name="position",
            field=models.IntegerField(unique=True),
        ),
    ]
