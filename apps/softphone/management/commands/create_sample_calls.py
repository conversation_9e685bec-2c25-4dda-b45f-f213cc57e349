"""
Management command to create sample call data for testing
"""
from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
import random

from apps.softphone.models.call_log import CallLog
from apps.user.models.user import Users


class Command(BaseCommand):
    """
    Create sample call data for testing
    """
    help = 'Create sample call data for testing Softphone integration'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=20,
            help='Number of sample calls to create'
        )
    
    def handle(self, *args, **options):
        count = options['count']
        
        # Sample phone numbers
        sample_phones = [
            '998901234567',
            '998907654321',
            '998909876543',
            '998901111111',
            '998902222222'
        ]
        
        # Get or create sample users
        users = []
        for phone in sample_phones:
            user, created = Users.objects.get_or_create(
                phone=phone,
                defaults={
                    'name': f'Test Customer {phone[-4:]}',
                    'role': 'client'
                }
            )
            users.append(user)
            if created:
                self.stdout.write(f'Created user: {user.name} ({user.phone})')
        
        # Create sample calls
        call_statuses = ['answered', 'missed', 'busy', 'no_answer']
        directions = ['inbound', 'outbound']
        
        created_calls = 0
        
        for i in range(count):
            # Random call data
            user = random.choice(users)
            direction = random.choice(directions)
            status = random.choice(call_statuses)
            duration = random.randint(30, 600) if status == 'answered' else 0
            
            # Random time in the last 30 days
            start_time = timezone.now() - timedelta(
                days=random.randint(0, 30),
                hours=random.randint(0, 23),
                minutes=random.randint(0, 59)
            )
            
            end_time = start_time + timedelta(seconds=duration) if duration > 0 else None
            
            # Create call log
            call_log = CallLog.objects.create(
                caller_id=user.phone if direction == 'inbound' else '************',
                called_number='************' if direction == 'inbound' else user.phone,
                direction=direction,
                status=status,
                duration=duration,
                start_time=start_time,
                end_time=end_time,
                user=user if direction == 'inbound' else None,
                external_id=f'test_call_{i+1}',
                did_number='************',
                sip_account='1',
                notes=f'Sample call #{i+1} for testing'
            )
            
            created_calls += 1
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {created_calls} sample calls'
            )
        )
        
        # Display statistics
        total_calls = CallLog.objects.count()
        answered_calls = CallLog.objects.filter(status='answered').count()
        inbound_calls = CallLog.objects.filter(direction='inbound').count()
        outbound_calls = CallLog.objects.filter(direction='outbound').count()
        
        self.stdout.write('\nCall Statistics:')
        self.stdout.write(f'  Total Calls: {total_calls}')
        self.stdout.write(f'  Answered Calls: {answered_calls}')
        self.stdout.write(f'  Inbound Calls: {inbound_calls}')
        self.stdout.write(f'  Outbound Calls: {outbound_calls}')
        
        self.stdout.write('\nTest URLs:')
        self.stdout.write('  Screen Popup: http://localhost:8000/softphone/popup/?callerid=998901234567')
        self.stdout.write('  Customer Data: http://localhost:8000/api/v1/softphone/customer-data/?phone=998901234567')
        self.stdout.write('  Call History: http://localhost:8000/api/v1/softphone/call-history/')
        self.stdout.write('  Call Stats: http://localhost:8000/api/v1/softphone/call-stats/')
