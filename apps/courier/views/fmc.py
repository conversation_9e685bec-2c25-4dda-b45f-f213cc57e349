"""
the storing fcm tokens
"""
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from apps.courier.models.fcm import FCMDevice
from apps.core.exceptions.service import ServiceAPIException
from apps.courier.permissions.is_courier import IsCourierRole


class FCMDeviceAPIView(APIView):
    """
    API View for creating or updating FCMDevice.
    """
    permission_classes = [IsAuthenticated, IsCourierRole]

    def post(self, request):
        """
        Create or update an FCM device for the authenticated courier.

        Args:
            request (Request): The request object containing the token.

        Returns:
            Response: The response object with the status of the operation.
        """
        courier = request.user
        token = request.data.get('token')

        if not token:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Missing required parameter: token",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            self.process(courier_id=courier.id, token=token)

        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_server_error",
                message=f"Failed to update FCM device: {exc}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            ) from exc

        return Response({"message": "FCM device updated successfully."}, status=status.HTTP_200_OK)

    def process(self, courier_id, token):
        """
        Process the FCM device.
        """
        # Create or update the FCM device
        FCMDevice.create_or_update(courier_id=courier_id, token=token)
