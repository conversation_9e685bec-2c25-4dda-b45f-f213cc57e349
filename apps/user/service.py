from datetime import datetime

from apps.user.models.user import Users


class UserService:
    @staticmethod
    def get_client_statistics(start_date: datetime, end_date: datetime):
        """
        Get statistics for new registered clients and total clients count.
        If both start_date and end_date are None, returns statistics for all time.
        """
        # Total clients count
        total_clients = Users.objects.count()

        # New registered clients for the period
        if start_date is None and end_date is None:
            new_clients = total_clients  # All clients are considered new when getting all-time stats
        else:
            new_clients = Users.objects.filter(created_at__range=(start_date, end_date)).count()

        return {
            'total_clients': total_clients,
            'new_clients': new_clients,
        }
