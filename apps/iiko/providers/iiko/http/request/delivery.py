"""
the delivery http request module
"""
from typing import List, Optional

from pydantic import BaseModel, Field, validator

from master_kebab.settings import DELIVERY_DURATION_TIME


class Modifier(BaseModel):
    """
    the modifier model representing
    """
    productId: str
    amount: int
    productGroupId: Optional[str] = None
    price: Optional[float] = None

    @classmethod
    def make_obj(cls, product_id, amount, product_group_id, price=0.0):
        """
        make modifier instance
        """
        return cls(productId=product_id, amount=amount, productGroupId=product_group_id, price=price)


class Item(BaseModel):
    """
    the item model representing
    """
    productId: str
    type: str
    amount: int
    comment: Optional[str] = None
    productSizeId: Optional[str] = None
    modifiers: Optional[list[Modifier]] = None


class Street(BaseModel):
    """
    the street model representing
    """
    classifierId: Optional[str] = None
    id: Optional[str] = None
    name: Optional[str] = None
    city: Optional[str] = None


class Address(BaseModel):
    """
    the address model representing
    """
    building: Optional[str] = None
    doorphone: Optional[str] = None
    entrance: Optional[str] = None
    flat: Optional[str] = None
    floor: Optional[str] = None
    house: str = None
    index: Optional[str] = None
    regionId: Optional[str] = None
    street: Street
    type: str


class Coordinates(BaseModel):
    """
    the coordinates model representing
    """
    latitude: float
    longitude: float


class DeliveryPoint(BaseModel):
    """
    delivery point model representing
    """
    address: Address
    coordinates: Coordinates
    externalCartographyId: Optional[str] = None
    comment: Optional[str] = None


class Payment(BaseModel):
    """
    the payment model representing
    """
    paymentTypeKind: str
    sum: float
    paymentTypeId: str
    isProcessedExternally: bool
    isFiscalizedExternally: bool


class LoyaltyInfo(BaseModel):
    """
    Loyalty information for orders with promo codes
    """
    coupon: str


class Order(BaseModel):
    """
    the order model representing
    """
    sourceKey: str
    phone: str
    orderTypeId: str
    completeBefore: Optional[str] = None
    items: List[Item]
    payments: List[Payment]
    deliveryPoint: Optional[DeliveryPoint] = None
    deliveryDuration: int = DELIVERY_DURATION_TIME  # by default 40 minutes
    externalNumber: Optional[str] = None
    comment: Optional[str] = None
    loyaltyInfo: Optional[LoyaltyInfo] = None

    @validator('phone')
    def ensure_phone_starts_with_plus(cls, v):
        if not v.startswith('+'):
            return '+' + v
        return v


class OrderRequest(BaseModel):
    """
    the order request model representing
    """
    organizationId: Optional[str] = Field(None, alias="organizationId")
    terminalGroupId: str
    order: Order


class OrderCreateWithoutGhostRequestBody(BaseModel):
    """
    the http request model for creating a order
    without ghost account
    """
    organizationId: Optional[str] = Field(None, alias="organizationId")
    terminalGroupId: str
    order: Order


class ConfirmRequestBody(BaseModel):
    """
    the http request model for confirming a order
    """
    organization_id: str
    order_id: str

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organization_id,
            "orderId": self.order_id,
        }


class OpdateOrderCourierRequestBody(BaseModel):
    """
    the order data model representing
    """
    organization_id: str
    order_id: str
    employee_id: str

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organization_id,
            "orderId": self.order_id,
            "employeeId": self.employee_id,
        }


class UpdateOrderDeliveryStatusRequestBody(BaseModel):
    """
    the update order delivery status request model representing
    """
    organization_id: str
    order_id: str
    delivery_status: str

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organization_id,
            "orderId": self.order_id,
            "deliveryStatus": self.delivery_status,
        }


class CancelConfirmationRequestBody(BaseModel):
    """
    the cancel confirmation request model representing
    """
    organization_id: str
    order_id: str

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organization_id,
            "orderId": self.order_id,
        }


class DeliveryRestrictionsRequestBody(BaseModel):
    """
    the organization ids request model
    """
    organizationIds: list[str]

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationIds": self.organizationIds,
        }
