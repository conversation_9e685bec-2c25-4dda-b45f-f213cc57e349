# Generated by Django 5.0.6 on 2024-09-01 18:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('map', '0003_mapapikey'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='mapapikey',
            options={'verbose_name': 'Map API Key', 'verbose_name_plural': 'Map API Keys'},
        ),
        migrations.AddField(
            model_name='mapapikey',
            name='owner',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='mapapikey',
            name='app_name',
            field=models.CharField(choices=[('yandex_map', 'Yandex Map'), ('google_map', 'Google Map'), ('mapbox', 'Mapbox')], default='yandex_map', max_length=255),
        ),
    ]
