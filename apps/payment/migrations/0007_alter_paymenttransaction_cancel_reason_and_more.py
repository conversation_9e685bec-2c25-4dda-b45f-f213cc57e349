# Generated by Django 5.0.6 on 2024-09-28 13:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payment", "0006_paymenttransaction_cancelled_at_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="paymenttransaction",
            name="cancel_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("Refund", "Refund"),
                    ("RecipientNotFound", "Recipientnotfound"),
                    ("DebitOperationFailed", "Debitoperationfailed"),
                    ("TransactionFailed", "Transactionfailed"),
                    ("TransactionTimeout", "Transactiontimeout"),
                    ("CreatedAndRefund", "Createdandrefund"),
                    ("CompletedAndRefund", "Completedandrefund"),
                    ("UnknownError", "Unknownerror"),
                ],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="paymenttransaction",
            name="state",
            field=models.CharField(
                choices=[
                    ("Created", "Created"),
                    ("Waiting", "Waiting"),
                    ("Processing", "Processing"),
                    ("Completed", "Completed"),
                    ("Refunded", "Refunded"),
                    ("Cancelled", "Cancelled"),
                    ("Expired", "Expired"),
                    ("Rejected", "Rejected"),
                    ("Pending", "Pending"),
                    ("Reversed", "Reversed"),
                    ("Reversed and Waiting", "Reversed and waiting"),
                ],
                default="Waiting",
            ),
        ),
    ]
