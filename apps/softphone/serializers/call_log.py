"""
Call log serializers
"""
from rest_framework import serializers
from apps.softphone.models.call_log import CallLog
from apps.user.serializers.user import UserSerializer


class CallLogSerializer(serializers.ModelSerializer):
    """
    Serializer for CallLog model
    """
    user = UserSerializer(read_only=True)
    operator = UserSerializer(read_only=True)
    formatted_duration = serializers.ReadOnlyField()
    
    class Meta:
        model = CallLog
        fields = [
            'id',
            'caller_id',
            'called_number',
            'direction',
            'status',
            'duration',
            'formatted_duration',
            'start_time',
            'end_time',
            'user',
            'operator',
            'order',
            'recording_url',
            'recording_file',
            'external_id',
            'notes',
            'did_number',
            'sip_account',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class CallLogCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating CallLog entries
    """
    
    class Meta:
        model = CallLog
        fields = [
            'caller_id',
            'called_number',
            'direction',
            'status',
            'duration',
            'start_time',
            'end_time',
            'external_id',
            'recording_url',
            'notes',
            'did_number',
            'sip_account'
        ]
    
    def validate_caller_id(self, value):
        """Clean caller ID"""
        return value.replace('+', '') if value else ''
    
    def validate_called_number(self, value):
        """Clean called number"""
        return value.replace('+', '') if value else ''


class CallLogStatsSerializer(serializers.Serializer):
    """
    Serializer for call statistics
    """
    total_calls = serializers.IntegerField()
    answered_calls = serializers.IntegerField()
    missed_calls = serializers.IntegerField()
    average_duration = serializers.FloatField()
    total_duration = serializers.IntegerField()
    answer_rate = serializers.FloatField()
    
    direction_breakdown = serializers.ListField(
        child=serializers.DictField()
    )
    status_breakdown = serializers.ListField(
        child=serializers.DictField()
    )
