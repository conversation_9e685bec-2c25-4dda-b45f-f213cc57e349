version: "3"
services:
  master-kebab-backend:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-backend
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - python manage.py purge_queues && python3 manage.py migrate && python3 manage.py init_data && python3 manage.py createadmin && gunicorn master_kebab.wsgi:application --bind 127.0.0.1:10003
    network_mode: host
    volumes:
      - /var/www/master_kebab/static:/application/static
    env_file:
      - .env

  master-kebab-celery-beat:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-celery-beat
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - celery -A master_kebab beat -l INFO
    network_mode: host
    env_file:
      - .env

  master-kebab-celery-worker:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-celery-worker
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - celery -A master_kebab worker -Q bot,sms,iiko,iiko_updates,order,user,delivery,core --loglevel=info --concurrency=10
    network_mode: host
    env_file:
      - .env

  master-kebab-sms-listener:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-sms-listener
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - python3 manage.py run_sms_service
    network_mode: host
    env_file:
      - .env

  master-kebab-user-service-listener:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-user-service-listener
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - python3 manage.py run_user_service
    network_mode: host
    env_file:
      - .env
