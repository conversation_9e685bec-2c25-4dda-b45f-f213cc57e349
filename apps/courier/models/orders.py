"""
Models for tracking orders missed by couriers.
"""

from django.db import models
from apps.order.models.order import Order
from apps.courier.models.courier import Courier
from apps.core.models.base import BaseModel


class MissedOrder(BaseModel):
    """
    Represents an order that was missed by a courier.
    """
    class Meta:
        """
        The meta fields for the MissedOrder model.
        """
        verbose_name = "Missed Order"
        verbose_name_plural = "Missed Orders"
        db_table = "missed_orders"

    courier = models.ForeignKey(Courier, on_delete=models.CASCADE, related_name="missed_orders")
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name="missed_orders")
    is_missed = models.BooleanField(default=False)

    @classmethod
    def update_or_create(cls, courier_id, order_id, is_missed=False) -> "MissedOrder":
        """
        Update an existing missed order or create a new one.

        Args:
            courier_id: ID of the courier.
            order_id: ID of the order.
            is_missed: Whether the order was missed.

        Returns:
            The MissedOrder instance.
        """
        return cls.objects.update_or_create(
            courier_id=courier_id,
            order_id=order_id,
            defaults={'is_missed': is_missed}
        )

    @classmethod
    def mark_order_as_missed(cls, order_id):
        """
        Mark an order as missed.

        Args:
            order_id: ID of the order to mark as missed.
        """
        cls.objects.filter(order_id=order_id, is_missed=False).update(is_missed=True)

    @classmethod
    def mark_as_missed_by_courier(cls, courier_id, order_id):
        """
        Mark an order as missed by a specific courier.

        Args:
            courier_id: ID of the courier.
            order_id: ID of the order.
        """
        cls.objects.filter(courier_id=courier_id, order_id=order_id).update(is_missed=True)

    @classmethod
    def has_courier_missed_order(cls, courier_id, order_id) -> bool:
        """
        Check if a courier has already missed a specific order.

        Args:
            courier_id: ID of the courier.
            order_id: ID of the order.

        Returns:
            True if the courier has missed the order, False otherwise.
        """
        return cls.objects.filter(courier_id=courier_id, order_id=order_id, is_missed=True).exists()

    @classmethod
    def remove_order(cls, courier_id, order_id):
        """
        Delete a missed order entry for a courier.

        Args:
            courier_id: ID of the courier.
            order_id: ID of the order.

        Returns:
            The number of entries deleted.
        """
        return cls.objects.filter(courier_id=courier_id, order_id=order_id).delete()

    @classmethod
    def get_missed_orders_by_courier(cls, courier_id) -> models.QuerySet:
        """
        Retrieve all missed orders for a specific courier.

        Args:
            courier_id: ID of the courier.

        Returns:
            A queryset of missed orders.
        """
        return cls.objects.filter(courier_id=courier_id, is_missed=True)

    @classmethod
    def get_unmissed_orders_by_courier(cls, courier_id) -> models.QuerySet:
        """
        Retrieve all orders that were not missed by a specific courier.

        Args:
            courier_id: ID of the courier.

        Returns:
            A queryset of unmissed orders.
        """
        return cls.objects.filter(courier_id=courier_id, is_missed=False)
