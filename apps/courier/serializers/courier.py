"""
init delivery agent
"""
import re

from rest_framework import serializers

from apps.core import core
from apps.courier.models.courier import Courier
from apps.user.enum.agent import AgentStatusEnums
from apps.organization.serializers.organization import OrganizationSerializer


class OrganizationSerializerUPT(OrganizationSerializer):
    """
    The organization serialization for update
    """
    class Meta(OrganizationSerializer.Meta):
        """
        The meta fields for update
        """
        fields = (
            'name',
        )


class CourierModelSerializer(serializers.ModelSerializer):
    """
    the delivery agent serialization
    """
    location = serializers.SerializerMethodField(method_name="courier_location")
    organization = OrganizationSerializerUPT()

    class Meta:
        """
        the meta fields
        """
        model = Courier
        fields = [
            "id",
            "phone",
            "name",
            "passport",
            "address",
            "status",
            "external_id",
            "role",
            "organization",
            "is_blocked",
            "is_active",
            "location"
        ]

    def courier_location(self, obj: Courier):
        """
        Method to get the location of the delivery agent
        """
        try:
            location = obj.location

            if location is not None:
                return {
                    "latitude": location.latitude,
                    "longitude": location.longitude
                }

        except Exception as exc:
            core.log("error", f"Error getting location exc: {exc}")


class CourierSerializer(serializers.Serializer):
    """
    the delivery agent serialization
    """


class CourierSerializerByExternalID(serializers.Serializer):
    """
    the delivery agent serialization by external ID
    """
    id = serializers.CharField()
    organization_id = serializers.CharField()
    phone = serializers.CharField(required=False, allow_null=True)
    name = serializers.CharField(required=False, allow_null=True)
    passport = serializers.CharField(required=False, allow_null=True)
    address = serializers.CharField(required=False, allow_null=True)


class CourierChangeStatusSerializer(serializers.Serializer):
    """
    the delivery agent change status serializer
    """
    status = serializers.ChoiceField(
        choices=AgentStatusEnums.choices()
    )


class CourierUpdateSerializer(serializers.ModelSerializer):
    """
    the delivery agent update serializer by name, passport, address
    """
    class Meta:
        """
        the meta fields
        """
        model = Courier
        fields = [
            "name",
            "passport",
            "address",
        ]

    def validate_passport(self, value):
        """
        Uzbekistan passport validator
        """
        passport_pattern = r'^[A-Z]{2}\d{7}$'

        if not re.match(passport_pattern, value):
            raise serializers.ValidationError(
                "Invalid Uzbekistan passport format. It should be two uppercase letters followed by 7 digits \n"
                "(e.g., *********)."
            )

        # Check for uniqueness
        if Courier.objects.filter(passport=value).exclude(pk=self.instance.pk).exists():
            raise serializers.ValidationError("This passport number is already in use.")

        return value
