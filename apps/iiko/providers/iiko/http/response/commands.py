"""
This module defines models for handling the response structure returned by the iiko system
when checking the status of a specific command.

The `CommandsStatusResponseBody` class represents the main response body, including the
current command state and, if applicable, any associated exception message.
"""

from typing import Optional
from pydantic import BaseModel


class ExceptionMessage(BaseModel):
    """
    ExceptionMessage represents an error message that may accompany a failed command status.

    Attributes:
        message (str): A string containing the detailed error message.
    """
    message: str


class CommandsStatusResponseBody(BaseModel):
    """
    CommandsStatusResponseBody represents the structure of the response body for
    checking the status of a command in the iiko system.

    Attributes:
        state (str): A string indicating the current state of the command.
                     Possible values might include "Inprocess", "Error", etc.
        exception (Optional[ExceptionMessage]): An optional field that contains additional error
                                               information if the command is in an error state.
    """
    state: str
    exception: Optional[ExceptionMessage] = None
