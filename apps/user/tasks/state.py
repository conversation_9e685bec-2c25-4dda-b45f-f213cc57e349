"""
implementation of state tasks
"""
from celery import Task

from telebot import apihelper

from master_kebab import celery_app

from apps.bot.client import bot
from apps.bot.models.telegram import TelegramUser


PENDING_MESSAGE = {
    "on_start": {
        "uz": "👩‍🍳 Davom eting",
        "ru": "👩‍🍳 Давайте начнём",
    },
    "get_contact": {
        "uz": "👩‍🍳 Iltimos contact ma'lumotlarini yuboring",
        "ru": "👩‍🍳 Пожалуйста, отправьте свои контактные данные",
    },
    "get_location": {
        "uz": "👩‍🍳 Iltimos manzilni yuboring",
        "ru": "👩‍🍳 Пожалуйста, отправьте своё местоположение",
    },
}


@celery_app.task(bind=True, acks_late=False, task_acks_on_failure_or_timeout=False)
def send_notification_for_state_task(self: Task):
    """
    print message
    """
    users = TelegramUser.get_pending_states()

    for user in users:
        try:
            bot.send_message(
                chat_id=user.id,
                text=PENDING_MESSAGE[user.state][user.lang]
            )

        except apihelper.ApiTelegramException as exc:
            if exc.error_code == 403:
                TelegramUser.delete_by_chat_id(chat_id=user.id)
                return

        user.is_state_notified = True
        user.save()
