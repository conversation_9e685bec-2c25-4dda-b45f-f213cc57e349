"""
Yandex Geosuggestion API Serializers

This module provides serializers for the Yandex Geosuggestion API
endpoints, including request validation and response formatting.
"""

from rest_framework import serializers


class YandexGeosuggestionRequestSerializer(serializers.Serializer):
    """
    Serializer for Yandex Geosuggestion API request parameters.
    
    Validates the input parameters for location suggestion requests.
    """
    
    name = serializers.CharField(
        max_length=255,
        required=True,
        help_text="Search text for location suggestions (e.g., 'Aeroport', 'Restaurant')"
    )
    
    coordinates = serializers.CharField(
        max_length=50,
        required=False,
        help_text="Coordinates in format 'longitude,latitude' (e.g., '65.38055,40.09075')"
    )
    
    results = serializers.IntegerField(
        min_value=1,
        max_value=10,
        default=5,
        required=False,
        help_text="Number of results to return (1-10, default: 5)"
    )
    
    def validate_name(self, value):
        """
        Validate the name parameter.
        
        Args:
            value (str): The search text.
            
        Returns:
            str: Cleaned search text.
            
        Raises:
            serializers.ValidationError: If name is invalid.
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Name cannot be empty")
        
        # Remove extra whitespace
        cleaned_value = value.strip()
        
        # Check minimum length
        if len(cleaned_value) < 2:
            raise serializers.ValidationError("Name must be at least 2 characters long")
        
        return cleaned_value
    
    def validate_coordinates(self, value):
        """
        Validate the coordinates parameter.
        
        Args:
            value (str): Coordinates string.
            
        Returns:
            str: Validated coordinates string.
            
        Raises:
            serializers.ValidationError: If coordinates format is invalid.
        """
        if not value:
            return value
        
        try:
            parts = value.split(',')
            if len(parts) != 2:
                raise ValueError("Invalid format")
            
            longitude = float(parts[0].strip())
            latitude = float(parts[1].strip())
            
            # Basic coordinate validation
            if not (-180 <= longitude <= 180):
                raise ValueError("Longitude must be between -180 and 180")
            
            if not (-90 <= latitude <= 90):
                raise ValueError("Latitude must be between -90 and 90")
            
            return f"{longitude},{latitude}"
            
        except (ValueError, IndexError):
            raise serializers.ValidationError(
                "Coordinates must be in format 'longitude,latitude' (e.g., '65.38055,40.09075')"
            )


class CoordinatesSerializer(serializers.Serializer):
    """
    Serializer for coordinate data.
    """
    longitude = serializers.FloatField()
    latitude = serializers.FloatField()


class SuggestionSerializer(serializers.Serializer):
    """
    Serializer for individual location suggestions.
    """
    title = serializers.CharField()
    subtitle = serializers.CharField(allow_blank=True)
    uri = serializers.CharField(allow_blank=True)
    distance = serializers.CharField(allow_blank=True)
    tags = serializers.ListField(child=serializers.CharField(), default=list)
    coordinates = CoordinatesSerializer(required=False)


class YandexGeosuggestionResponseSerializer(serializers.Serializer):
    """
    Serializer for Yandex Geosuggestion API response.
    
    Formats the response data in a consistent structure.
    """
    suggestions = SuggestionSerializer(many=True)
    total_results = serializers.IntegerField()
    remaining_requests = serializers.IntegerField()
    status = serializers.CharField()
    
    
class ApiAvailabilitySerializer(serializers.Serializer):
    """
    Serializer for API availability status.
    """
    available = serializers.BooleanField()
    message = serializers.CharField()
    remaining_requests = serializers.IntegerField(required=False)
