"""
implementation of state tasks
"""
from celery import Task

from eskiz.exception import TokenExpired

from master_kebab import settings
from master_kebab import celery_app

from apps.core import core
from apps.sms.provider import sms_client


@celery_app.task(bind=True)
def send_sms_message_task(self: Task, phone, message):
    """
    print message.
    """
    if settings.IS_TEST_MODE:  # in the development environment we don't use sms client.
        from apps.bot.tasks import send_message_task
        send_message_task.delay(message)
        return

    balance = sms_client.check_balance()
    threshold = getattr(settings, 'BALANCE_THRESHOLD', 10000)
    if balance < threshold:
        alert_message = f"⚠️ Balance Low: {balance} credits - needs to be at least {threshold}!"
        from apps.bot.services.telegram_service import TelegramService
        TelegramService.send_text_message(
            chat_id=settings.ESKIZ_NOTIFY_TELEGRAM_CHANNEL_ID,
            title=None,
            message=alert_message
        )

    try:
        print("phone", phone)
        print("message", message)
        resp = sms_client.send_sms(phone, message)
        send_message_task.delay(f"message has been sent {message}")

    except TokenExpired as exc:
        log = f"sms provider token expired: {exc}"
        core.log("error", log)
        self.retry(exc=exc, countdown=3)
        return

    log = f"response from sms provider: {resp} phone: {phone} message: {message}"
    core.log("info", log)
