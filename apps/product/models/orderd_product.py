"""
the product model
"""
from django.db import models, transaction

from apps.core.models.base import BaseModel
from apps.product.enums.type import ProductType as ProductTypeEnum
from apps.product.models.product import Product, ProductAttribute
from apps.product.models.modifier import ModifierItems


class OrderedProduct(BaseModel):
    """
    Ordered product model.
    """
    class Meta:
        """
        Meta fields.
        """
        db_table = 'ordered_products'

    title = models.CharField(max_length=150)
    image_url = models.CharField(max_length=255, null=True)
    product_type = models.CharField(
        max_length=50,
        choices=ProductTypeEnum.choices(),
        default=ProductTypeEnum.DISH.value,
    )
    external_id = models.CharField(null=True, blank=True)
    comment = models.TextField(null=True, blank=True)

    @property
    def get_price(self):
        return self.attribute.price

    @property
    def attributes(self):
        return self.orderedproductattribute_set.first()

    @property
    def attributes_all(self):
        return self.orderedproductattribute_set.all()

    def __str__(self):
        return f"Ordered Product: {self.title}"


class OrderedProductAttribute(BaseModel):
    """
    Product attributes model.
    """
    description = models.TextField(null=True, blank=True)
    price = models.FloatField()
    size = models.CharField()
    external_id = models.CharField(null=True, blank=True)
    product = models.ForeignKey(OrderedProduct, on_delete=models.CASCADE, null=True)
    size_id = models.CharField(null=True, blank=True)

    def __str__(self) -> str:
        return f"{self.size} - {self.price} UZS"


class OrderedProductModifier(BaseModel):
    """
    Ordered product modifier model.
    """
    ordered_product = models.ForeignKey(OrderedProduct, on_delete=models.CASCADE, related_name="product_modifier")
    modifier_item = models.ForeignKey(ModifierItems, on_delete=models.CASCADE)
    quantity = models.IntegerField(default=1)

    def __str__(self):
        return f"{self.modifier_item.name} - {self.quantity}"

    class Meta:
        """
        Meta fields
        """
        db_table = 'ordered_product_modifiers'

    @classmethod
    def get_by_ordered_product_id(cls, product_id) -> list["OrderedProductModifier"]:
        return cls.objects.filter(ordered_product_id=product_id)


def init_ordered_product(
    product: Product,
    attribute: ProductAttribute,
    product_comment: str = None,
    modifiers: list = None
) -> OrderedProduct:
    """
    Initialize the ordered product model with its attribute and modifiers.

    Parameters:
    - product: The original product instance.
    - attribute: The attribute associated with the ordered product.
    - product_comment: Comment for the ordered product.
    - modifiers: List of modifiers associated with the ordered product.

    Returns:
    - OrderedProduct instance
    """
    with transaction.atomic():
        # Create the OrderedProduct instance
        ordered_product = OrderedProduct.objects.create(
            title=product.title,
            title_uz=product.title_uz,
            title_en=product.title_en,
            title_ru=product.title_ru,
            image_url=product.image_url,
            external_id=product.external_id,
            product_type=product.product_type,
            comment=product_comment
        )

        # Create and associate the OrderedProductAttribute instance
        OrderedProductAttribute.objects.create(
            description=attribute.description,
            price=attribute.price,
            size=attribute.size,
            external_id=attribute.external_id,
            size_id=attribute.size_id,
            product=ordered_product
        )

        # Create and associate the OrderedProductModifier instances
        if modifiers is not None:
            for modifier in modifiers:
                OrderedProductModifier.objects.create(
                    ordered_product=ordered_product,
                    modifier_item_id=modifier.id,
                    quantity=modifier.quantity
                )

        return ordered_product
