"""
user's managers
"""
from django.contrib.auth.models import BaseUserManager


class UserManager(BaseUserManager):
    """
    Custom manager for User model.
    Defines methods for creating regular users and superusers.
    """
    def create_user(self, phone, role, password=None, **extra_fields):
        """
        Create and return a regular user with a phone number, role, and password.

        Parameters:
        phone (str): The user's phone number, used as the unique identifier.
        role (str): The user's role (e.g., admin, customer).
        password (str, optional): The user's password.
        extra_fields (dict): Any additional fields to include in the user model.

        Returns:
        User: The created user instance.

        Raises:
        ValueError: If the phone number is not provided.
        """
        if not phone:
            raise ValueError('The Phone number must be set')
        user = self.model(phone=phone, role=role, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, phone, role, password=None, **extra_fields):
        """
        Create and return a superuser with a phone number, role, and password.

        Parameters:
        phone (str): The superuser's phone number, used as the unique identifier.
        role (str): The superuser's role.
        password (str, optional): The superuser's password.
        extra_fields (dict): Any additional fields to include in the user model.

        Returns:
        User: The created superuser instance.
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        return self.create_user(phone, role, password, **extra_fields)
