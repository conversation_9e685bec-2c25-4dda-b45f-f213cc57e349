from apps.organization.models import Organization
from rest_framework.exceptions import NotFound


class OrganizationService:
    """
    Organization service interfaces
    """
    @classmethod
    def update_organization(cls, organization_id, validated_data):
        """
        Update an organization's details.

        Args:
            organization_id (int): The ID of the organization to update.
            validated_data (dict): The validated data to update the organization with.

        Returns:
            Organization: The updated organization instance.

        Raises:
            NotFound: If the organization does not exist.
        """
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            raise NotFound('Organization not found.')

        for field, value in validated_data.items():
            setattr(organization, field, value)
        organization.save()
        return organization
