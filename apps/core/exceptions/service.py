"""
custom exceptions for service
"""
import logging

from rest_framework import status
from rest_framework.exceptions import APIException

logger = logging.getLogger(__name__)


class ServiceAPIException(APIException):
    """
    ServiceAPIException
    """
    def __init__(
        self,
        error_type,
        message,
        status_code=status.HTTP_400_BAD_REQUEST
    ):
        self.status_code = status_code
        self.default_detail = {
            "error": {
                "type": error_type,
                "message": message
            },
        }
        logger.error(self.default_detail, exc_info=True)
        super().__init__()


class PromoCodeAlreadyExists(Exception):
    pass
