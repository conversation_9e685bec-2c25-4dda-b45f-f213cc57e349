"""
Softphone admin configuration
"""
from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from apps.softphone.models.call_log import CallLog
from apps.softphone.models.softphone_config import SoftphoneConfig


@admin.register(CallLog)
class CallLogAdmin(admin.ModelAdmin):
    """
    Admin interface for CallLog model
    """
    list_display = [
        'id',
        'caller_id',
        'called_number',
        'direction',
        'status',
        'formatted_duration',
        'start_time',
        'user_link',
        'operator_link',
        'has_recording',
        'play_recording'
    ]
    
    list_filter = [
        'direction',
        'status',
        'start_time',
        'created_at'
    ]
    
    search_fields = [
        'caller_id',
        'called_number',
        'external_id',
        'user__name',
        'user__phone',
        'operator__name',
        'notes'
    ]
    
    readonly_fields = [
        'id',
        'formatted_duration',
        'created_at',
        'updated_at',
        'play_recording'
    ]
    
    fieldsets = (
        ('Call Information', {
            'fields': (
                'caller_id',
                'called_number',
                'direction',
                'status',
                'duration',
                'formatted_duration',
                'start_time',
                'end_time'
            )
        }),
        ('Associations', {
            'fields': (
                'user',
                'operator',
                'order'
            )
        }),
        ('Recording & External Data', {
            'fields': (
                'recording_url',
                'recording_file',
                'play_recording',
                'external_id',
                'did_number',
                'sip_account'
            )
        }),
        ('Additional Information', {
            'fields': (
                'notes',
            )
        }),
        ('Timestamps', {
            'fields': (
                'created_at',
                'updated_at'
            ),
            'classes': ('collapse',)
        })
    )
    
    def user_link(self, obj):
        """Create link to user admin page"""
        if obj.user:
            url = reverse('admin:user_users_change', args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.name or obj.user.phone)
        return '-'
    user_link.short_description = 'Customer'
    
    def operator_link(self, obj):
        """Create link to operator admin page"""
        if obj.operator:
            url = reverse('admin:user_users_change', args=[obj.operator.id])
            return format_html('<a href="{}">{}</a>', url, obj.operator.name or obj.operator.phone)
        return '-'
    operator_link.short_description = 'Operator'
    
    def has_recording(self, obj):
        """Show if call has recording"""
        if obj.recording_file or obj.recording_url:
            return format_html('<span style="color: green;">✓</span>')
        return format_html('<span style="color: red;">✗</span>')
    has_recording.short_description = 'Recording'
    
    def play_recording(self, obj):
        """Show audio player for recording"""
        if obj.recording_file:
            return format_html(
                '<audio controls style="width: 200px;"><source src="{}" type="audio/wav">Your browser does not support the audio element.</audio>',
                obj.recording_file.url
            )
        elif obj.recording_url:
            return format_html(
                '<audio controls style="width: 200px;"><source src="{}" type="audio/wav">Your browser does not support the audio element.</audio>',
                obj.recording_url
            )
        return '-'
    play_recording.short_description = 'Play Recording'
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('user', 'operator', 'order')


@admin.register(SoftphoneConfig)
class SoftphoneConfigAdmin(admin.ModelAdmin):
    """
    Admin interface for SoftphoneConfig model
    """
    list_display = [
        'name',
        'is_active',
        'enable_screen_popup',
        'enable_call_logging',
        'enable_recording_upload',
        'click_to_call_enabled',
        'created_at'
    ]
    
    list_filter = [
        'is_active',
        'enable_screen_popup',
        'enable_call_logging',
        'enable_recording_upload',
        'click_to_call_enabled',
        'created_at'
    ]
    
    search_fields = [
        'name',
        'screen_popup_url',
        'customer_data_url'
    ]
    
    readonly_fields = [
        'created_at',
        'updated_at'
    ]
    
    fieldsets = (
        ('Basic Configuration', {
            'fields': (
                'name',
                'is_active',
                'default_sip_account'
            )
        }),
        ('Integration URLs', {
            'fields': (
                'screen_popup_url',
                'customer_data_url',
                'call_log_url',
                'recording_upload_url'
            )
        }),
        ('Feature Toggles', {
            'fields': (
                'enable_screen_popup',
                'enable_call_logging',
                'enable_recording_upload',
                'click_to_call_enabled'
            )
        }),
        ('Advanced Settings', {
            'fields': (
                'caller_id_regex',
            ),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': (
                'created_at',
                'updated_at'
            ),
            'classes': ('collapse',)
        })
    )
    
    def save_model(self, request, obj, form, change):
        """Ensure only one active configuration"""
        if obj.is_active:
            # Deactivate all other configurations
            SoftphoneConfig.objects.filter(is_active=True).update(is_active=False)
        super().save_model(request, obj, form, change)
