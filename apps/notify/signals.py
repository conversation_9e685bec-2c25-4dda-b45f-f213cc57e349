"""from django.dispatch import receiver
from django.db.models.signals import post_delete

from apps.notify.models import NotificationAccepters
from apps.bot.services.telegram_service import TelegramService


@receiver(post_delete, sender=NotificationAccepters)
def post_delete_notification_accepter(sender, instance: NotificationAccepters, **kwargs):
    if instance.notification.provider == "telegram":
        TelegramService.delete_message(
            chat_id=instance.user_id,
            message_id=instance.notification_identity
        )
"""