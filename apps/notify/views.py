"""
Notification views
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from django.db import models
from django_filters import rest_framework as filters

from apps.notify.models import Notification
from apps.notify.filters import NotificationFilter
from apps.notify.service import notification_service
from apps.operations.permissions import IsMarketolog
from apps.bot.views.message import SendBotMessageView
from apps.notify.paginations import NotificationPagination
from apps.bot.services.telegram_service import TelegramService


class SendAPIView(APIView):
    """
    Notification view.
    """
    permission_classes = [IsAuthenticated, IsMarketolog]

    def post(self, request):
        """
        Create a notification
        """
        provider = request.data.get("provider")
        media_file = request.data.get("media_file")

        if media_file:
            valid_extensions = ['.jpg', '.jpeg', '.png', '.mp4', '.gif']
            if not any(media_file.name.lower().endswith(ext) for ext in valid_extensions):
                return Response(
                    {"error": "Unsupported file type. Supported formats: JPG, JPEG, PNG, MP4, GIF"},
                    status=400
                )

            file_ext = media_file.name.lower().split('.')[-1]

            if file_ext in ['mp4', 'mov', 'avi', 'webm', 'flv']:
                max_size = 10 * 1024 * 1024  # 10MB
            else:
                max_size = 1 * 1024 * 1024  # 1MB

            if media_file.size > max_size:
                return Response(
                    {"error": f"File size exceeds {max_size // (1024 * 1024)}MB limit for {file_ext} files"},
                    status=400
                )

        if provider not in ["telegram"]:
            return Response({"error": "Invalid notification provider"}, status=400)

        elif provider == "telegram":
            notification = notification_service.create_notification(
                provider=provider,
                title=request.data.get("title"),
                body=request.data.get("message")
            )
            SendBotMessageView().process(
                title=notification.title,
                media_file=media_file,
                message=request.data.get("message"),
                notification_id=notification.id
            )

        return Response()


class NotificationsAPIView(APIView):
    """
    Notifications view
    """
    filter_backends = [filters.DjangoFilterBackend]
    filterset_class = NotificationFilter
    permission_classes = [IsAuthenticated, IsMarketolog]
    pagination_class = NotificationPagination

    def get(self, request):
        """
        Get notifications grouped by title with stats
        """
        notifications = Notification.objects.all().order_by('-created_at')

        for backend in list(self.filter_backends):
            notifications = backend().filter_queryset(request, notifications, self)

        notifications = notifications.values('id', 'title', 'body').annotate(
            total=models.Count('id'),
            sent=models.Count(
                'notificationaccepters',
                filter=models.Q(notificationaccepters__status='sent')
            ),
            failed=models.Count(
                'notificationaccepters',
                filter=models.Q(notificationaccepters__status='failed')
            ),
            processing=models.Count(
                'notificationaccepters',
                filter=models.Q(notificationaccepters__status='processing')
            ),
            latest_created=models.Max('created_at'),
        ).order_by('-latest_created')

        paginator = self.pagination_class()
        paginated_notifications = paginator.paginate_queryset(notifications, request, view=self)
        return paginator.get_paginated_response(paginated_notifications)

    def delete(self, request):
        """
        Delete notifications
        """
        ids = request.data.get("ids", None)
        if not ids:
            notifications = Notification.objects.all()
            for notification in notifications:
                accepters = notification.notificationaccepters.all()
                for accepter in accepters:
                    TelegramService.delete_message(
                        chat_id=accepter.user_id,
                        message_id=accepter.notification_identity
                    )
                    accepter.delete()

                notification.delete()

        notifications = Notification.objects.filter(id__in=ids)
        if not notifications.exists():
            return Response()

        for notification in notifications:
            accepters = notification.notificationaccepters.all()
            for accepter in accepters:
                TelegramService.delete_message(
                    chat_id=accepter.user_id,
                    message_id=accepter.notification_identity
                )
                accepter.delete()

            notification.delete()
