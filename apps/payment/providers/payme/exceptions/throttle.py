"""
Transaction Already Exists Exception

This module defines the TransactionAlreadyExists exception that is raised
when an attempt is made to create a transaction that already exists in the
system. This exception inherits from the base exception class for the
Payme payment provider, ensuring consistency in error handling.

Attributes:
    status_code (int): HTTP status code to be returned with the error response.
    error_code (int): Unique error code representing this specific error.
    message (dict): A dictionary containing localized error messages for
                    different languages, including Uzbek, Russian, and English.

Usage:
    Raise this exception when a transaction with the given identifier already
    exists during the creation process.
"""

from apps.payment.providers.payme.exceptions.base import BasePaymeException  # noqa


class TransactionAlreadyExists(BasePaymeException):
    """
    TransactionAlreadyExists Exception

    Raised when a transaction already exists in the system, preventing
    the creation of a new transaction with the same identifier.

    Attributes:
        status_code (int): The HTTP status code for the response.
        error_code (int): The specific error code for this exception.
        message (dict): A dictionary containing localized error messages.
    """
    status_code = 200
    error_code = -31099
    message = {
        "uz": "Buyurtma tolovni amalga oshi<PERSON>h jarayonida",
        "ru": "Транзакция в очереди",
        "en": "Order payment status is queued"
    }
