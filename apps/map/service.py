"""
The map service is responsible for map interface communication
"""
from apps.map.models import MapApiKey


class MapService:
    """
    The map service class
    """
    @staticmethod
    def get_active_api_key() -> MapApiKey:
        """
        Retrieve the active API key for the map service
        """
        return MapApiKey.get_active_api_key()

    @staticmethod
    def mark_as_expired_api_key(api_key) -> MapApiKey:
        """
        Mark the given API key as expired
        """
        return MapApiKey.mark_as_limit_expired(api_key)
