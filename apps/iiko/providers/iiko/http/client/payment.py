"""
init iiko nomenclature class
"""
from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import PaymentTypesRequestBody
from apps.iiko.providers.iiko.http.response import PaymentTypesResponseBody


class IIKOPaymentTypes:
    """
    IIKO getting payment types API client
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def get_payment_types(self, organization_id: str, headers: dict):
        """
        Get payment types for an organization
        """
        endpoint = "/api/1/payment_types"
        body = PaymentTypesRequestBody(organizationId=organization_id).as_request_body()
        response = self.http.post(endpoint, headers=headers, json=body)
        response = PaymentTypesResponseBody(**response)
        return response


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
payment_types = IIKOPaymentTypes(http=http_base_client)
