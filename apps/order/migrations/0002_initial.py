# Generated by Django 5.0.6 on 2024-08-20 11:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('courier', '0002_initial'),
        ('operations', '0001_initial'),
        ('order', '0001_initial'),
        ('organization', '0001_initial'),
        ('product', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='bookmark',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='order',
            name='courier_shift',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='orders', to='courier.couriershifts'),
        ),
        migrations.AddField(
            model_name='order',
            name='delivery_agent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='courier.courier'),
        ),
        migrations.AddField(
            model_name='order',
            name='operator',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='operations.operations'),
        ),
        migrations.AddField(
            model_name='order',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='organization.organization'),
        ),
        migrations.AddField(
            model_name='order',
            name='sent_to_couriers',
            field=models.ManyToManyField(blank=True, related_name='sent_orders', to='courier.courier'),
        ),
        migrations.AddField(
            model_name='order',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='bookmark',
            name='orders',
            field=models.ManyToManyField(blank=True, related_name='bookmarked_orders', to='order.order'),
        ),
        migrations.AddField(
            model_name='order',
            name='order_detail',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='order', to='order.orderdetail'),
        ),
        migrations.AddField(
            model_name='orderitem',
            name='order',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='order.order'),
        ),
        migrations.AddField(
            model_name='orderitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='product.orderedproduct', verbose_name='Ordered Product'),
        ),
        migrations.AlterUniqueTogether(
            name='bookmark',
            unique_together={('user', 'address', 'lat', 'long')},
        ),
    ]
