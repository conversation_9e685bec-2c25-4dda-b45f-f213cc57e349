"""
Custom Django management command for initializing the database with
initial `SystemParameter` and `PaymentMethod` data.

This command creates default entries in the `SystemParameter` and `PaymentMethod`
models if they do not already exist. The data inserted includes system configuration
parameters and available payment methods.
"""

from django.core.management.base import BaseCommand

from apps.core.models import SystemParameter
from apps.core.enums import SystemParamsEnum


class Command(BaseCommand):
    """
    Django management command to initialize the database with
    initial `SystemParameter` and `PaymentMethod` data.

    This command ensures that specific system parameters and payment methods
    are present in the database. If any of the predefined parameters or methods
    do not exist, they are created. Otherwise, a message is shown indicating
    that they already exist.
    """

    help = 'Initialize the database with initial system parameters and payment methods'

    def handle(self, *args, **kwargs):
        """
        Command execution handler. Inserts initial data into the `SystemParameter`
        and `PaymentMethod` models if they do not already exist.

        The method checks for predefined parameters and payment methods,
        inserting them into the database if they are not found.

        :param args: Additional positional arguments
        :param kwargs: Additional keyword arguments
        """

        # Define the initial system parameters data to be inserted
        initial_data = [
            {
                'name': SystemParamsEnum.IIKO.value,
                'defaults': {
                    'value': '',
                    'is_enabled': False,
                    'description': 'Initial parameter for iiko'
                }
            },
            {
                'name': SystemParamsEnum.SEARCH_COURIER_SCHEDULER.value,
                'defaults': {
                    'value': 15 * 60,  # seconds
                    'is_enabled': False,
                    'description': 'Initial parameter for search courier scheduler'
                }
            },
            {
                'name': SystemParamsEnum.MAX_ORDERS_PER_DAY.value,
                'defaults': {
                    'value': 10,  # count
                    'is_enabled': False,
                    'description': 'Maximal orders per day for customers'
                }
            },
            {
                'name': SystemParamsEnum.IS_MINI_APP_ENABLED.value,
                'defaults': {
                    'value': False,
                    'is_enabled': False,
                    'description': 'Enable or disable the mini app'
                }
            }
        ]

        # Insert initial system parameters data into the database
        for data in initial_data:
            obj, created = SystemParameter.objects.get_or_create(**data)
            if created:
                self.stdout.write(self.style.SUCCESS(f'Successfully created: {obj.name}'))
            else:
                self.stdout.write(self.style.WARNING(f'Already exists: {obj.name}'))

        # Final message indicating the completion of database initialization
        self.stdout.write(self.style.SUCCESS('Database initialized with initial data'))
