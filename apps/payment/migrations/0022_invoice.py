# Generated by Django 5.0.6 on 2024-12-15 17:24

import apps.payment.utils
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0030_bookmark_is_client_bookmarked"),
        ("payment", "0021_alter_providercredentials_organization"),
    ]

    operations = [
        migrations.CreateModel(
            name="Invoice",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "invoice",
                    models.TextField(default=apps.payment.utils.generate_invoice),
                ),
                ("paylink", models.TextField()),
                (
                    "order",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="order.order",
                    ),
                ),
            ],
            options={
                "verbose_name": "Invoice",
                "verbose_name_plural": "invoices",
                "db_table": "invoices",
                "ordering": ["-created_at"],
            },
        ),
    ]
