"""
the most ordered list of bookmarks
"""
from rest_framework import viewsets
from rest_framework.response import Response

from apps.user.models.user import Users
from apps.core.views import ServiceBaseView
from apps.order.service import OrderService
from apps.order.models.bookmarks import Bookmark
from apps.bot.models.telegram import TelegramUser
from apps.order.serializers.bookmarks import BookmarkSerializer, BookMarkCreateSerializer


class MostOrderedBookmarksView(
    viewsets.ModelViewSet,
    ServiceBaseView
):
    """
    A viewset for listing a user's bookmarks ordered
    by how many times they have been included in orders
    """
    serializer_class = BookmarkSerializer

    def list(self, request,  *args, **kwargs):
        chat_id = request.query_params.get("chat_id")
        user_id = request.query_params.get('user_id')

        if chat_id and user_id:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Only one of user_id or chat_id must be provided.",
                status_code=400
            )

        user = self.get_user_from_request(chat_id, user_id)

        if not user:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Either user_id or chat_id must be provided.",
                status_code=400
            )

        bookmarks = Bookmark.get_most_ordered_bookmarks(user)
        serializer = BookmarkSerializer(bookmarks, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Saves a new bookmark for a user and returns the distance to the nearest existing bookmark
        """
        serializer = BookMarkCreateSerializer(
            data=self.request.data,
            context={'request': self.request}
        )
        serializer.is_valid(raise_exception=True)

        valid_data = serializer.validated_data
        chat_id = valid_data.get("chat_id")
        user_id = valid_data.get("user_id")
        name = valid_data.get("name")
        lat = valid_data.get("lat")
        long = valid_data.get("long")
        user = self.get_user_from_request(chat_id, user_id)

        try:
            bookmark, created, distance = OrderService.add_bookmark(
                lat=lat,
                long=long,
                address=valid_data.get("address"),
                subaddress=valid_data.get("subaddress"),
                user=user,
                name=name,
                is_client_bookmarked=True
            )
            response_data = serializer.data
            response_data['bookmark_id'] = bookmark.id
            response_data['created'] = created

            if distance:
                response_data["distance"] = distance

            return Response(response_data)
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message="Error while saving new bookmark"
            ) from exc

    def get_user_from_request(self, chat_id, user_id):
        if chat_id:
            try:
                telegram_user = TelegramUser.get_user_by_chat_id(chat_id)
                if not telegram_user:
                    raise TelegramUser.DoesNotExist
                return telegram_user.user
            except Exception as exc:
                raise self.call_service_exception(
                    error_type="not_found",
                    message="Telegram user not found",
                    status_code=400
                ) from exc
        elif user_id:
            try:
                return Users.objects.get(pk=user_id)
            except Exception as exc:
                raise self.call_service_exception(
                    error_type="not_found",
                    message="User not found",
                    status_code=400
                ) from exc
        return None

    def destroy(self, request, *args, pk=None, **kwargs):
        chat_id = request.query_params.get("chat_id")
        user_id = request.query_params.get("user_id")

        user = self.get_user_from_request(chat_id, user_id)

        if not user:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Either user_id or chat_id must be provided",
                status_code=400
            )

        try:
            bookmark = Bookmark.objects.get(pk=pk)

            if bookmark.user != user:
                raise self.call_service_exception(
                    error_type="forbidden",
                    message="You don't have permission to delete this bookmark",
                    status_code=403
                )

            bookmark.delete()
            return Response(status=204)

        except Bookmark.DoesNotExist as exc:
            raise self.call_service_exception(
                error_type="not_found",
                message="Bookmark not found",
                status_code=404
            ) from exc
