[{"model": "admin.logentry", "pk": 1, "fields": {"action_time": "2024-12-13T02:01:06.494", "user": 1, "content_type": 31, "object_id": "1", "object_repr": "iiko", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Value\", \"Is enabled\"]}}]"}}, {"model": "admin.logentry", "pk": 2, "fields": {"action_time": "2024-12-13T02:01:14.285", "user": 1, "content_type": 31, "object_id": "2", "object_repr": "search_courier_scheduler", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is enabled\"]}}]"}}, {"model": "admin.logentry", "pk": 3, "fields": {"action_time": "2024-12-13T02:01:21.566", "user": 1, "content_type": 31, "object_id": "3", "object_repr": "max_orders_per_day", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is enabled\"]}}]"}}, {"model": "admin.logentry", "pk": 4, "fields": {"action_time": "2024-12-13T02:01:26.370", "user": 1, "content_type": 31, "object_id": "4", "object_repr": "is_mini_app_enabled", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is enabled\"]}}]"}}, {"model": "admin.logentry", "pk": 5, "fields": {"action_time": "2024-12-13T02:07:47.782", "user": 1, "content_type": 32, "object_id": "3", "object_repr": "ЦУМ new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Address\", \"Latitude\", \"Longitude\", \"In use\", \"Is alive\", \"Opening Time\", \"Closing Time\"]}}]"}}, {"model": "admin.logentry", "pk": 6, "fields": {"action_time": "2024-12-13T02:08:28.404", "user": 1, "content_type": 32, "object_id": "2", "object_repr": "ВОСХОД new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Address\", \"Latitude\", \"Longitude\", \"In use\", \"Is alive\", \"Is default\", \"Opening Time\", \"Closing Time\"]}}]"}}, {"model": "admin.logentry", "pk": 7, "fields": {"action_time": "2024-12-13T02:10:07.902", "user": 1, "content_type": 33, "object_id": "2", "object_repr": "Delivery Цум", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"In use\"]}}]"}}, {"model": "admin.logentry", "pk": 8, "fields": {"action_time": "2024-12-13T02:10:13.597", "user": 1, "content_type": 33, "object_id": "1", "object_repr": "Восход", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name\", \"In use\"]}}]"}}, {"model": "admin.logentry", "pk": 9, "fields": {"action_time": "2024-12-13T02:14:08.332", "user": 1, "content_type": 38, "object_id": "98", "object_repr": "Tandir lavash NEW Большой GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 10, "fields": {"action_time": "2024-12-13T02:14:08.340", "user": 1, "content_type": 38, "object_id": "97", "object_repr": "Tandir lavash NEW Большой GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 11, "fields": {"action_time": "2024-12-13T02:14:08.348", "user": 1, "content_type": 38, "object_id": "96", "object_repr": "<PERSON><PERSON>sh NEW Средний GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 12, "fields": {"action_time": "2024-12-13T02:14:08.355", "user": 1, "content_type": 38, "object_id": "95", "object_repr": "<PERSON><PERSON>sh NEW Средний GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 13, "fields": {"action_time": "2024-12-13T02:14:08.362", "user": 1, "content_type": 38, "object_id": "94", "object_repr": "Lavash NEW Большой GRAM 13.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 14, "fields": {"action_time": "2024-12-13T02:14:08.369", "user": 1, "content_type": 38, "object_id": "93", "object_repr": "Lavash NEW Большой GRAM 13.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 15, "fields": {"action_time": "2024-12-13T02:14:08.377", "user": 1, "content_type": 38, "object_id": "92", "object_repr": "Lavash NEW Средний GRAM 11.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 16, "fields": {"action_time": "2024-12-13T02:14:08.384", "user": 1, "content_type": 38, "object_id": "91", "object_repr": "Lavash NEW Средний GRAM 11.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 17, "fields": {"action_time": "2024-12-13T02:14:08.391", "user": 1, "content_type": 38, "object_id": "90", "object_repr": "Lavash pishloqli NEW Большой GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 18, "fields": {"action_time": "2024-12-13T02:14:08.399", "user": 1, "content_type": 38, "object_id": "89", "object_repr": "Lavash pishloqli NEW Большой GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 19, "fields": {"action_time": "2024-12-13T02:14:08.407", "user": 1, "content_type": 38, "object_id": "88", "object_repr": "Lavash pishloqli NEW Средний GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 20, "fields": {"action_time": "2024-12-13T02:14:08.414", "user": 1, "content_type": 38, "object_id": "87", "object_repr": "Lavash pishloqli NEW Средний GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 21, "fields": {"action_time": "2024-12-13T02:14:08.421", "user": 1, "content_type": 38, "object_id": "86", "object_repr": "Tandir lavash pishloqli NEW Большой GRAM 16.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 22, "fields": {"action_time": "2024-12-13T02:14:08.428", "user": 1, "content_type": 38, "object_id": "85", "object_repr": "Tandir lavash pishloqli NEW Большой GRAM 16.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 23, "fields": {"action_time": "2024-12-13T02:14:08.435", "user": 1, "content_type": 38, "object_id": "84", "object_repr": "<PERSON><PERSON> lavash pishloqli NEW Средний GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 24, "fields": {"action_time": "2024-12-13T02:14:08.442", "user": 1, "content_type": 38, "object_id": "83", "object_repr": "<PERSON><PERSON> lavash pishloqli NEW Средний GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 25, "fields": {"action_time": "2024-12-13T02:14:08.449", "user": 1, "content_type": 38, "object_id": "82", "object_repr": "Gamburger NEW Default GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 26, "fields": {"action_time": "2024-12-13T02:14:08.456", "user": 1, "content_type": 38, "object_id": "81", "object_repr": "Gamburger NEW Default GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 27, "fields": {"action_time": "2024-12-13T02:14:08.463", "user": 1, "content_type": 38, "object_id": "80", "object_repr": "Да<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Default GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 28, "fields": {"action_time": "2024-12-13T02:14:08.471", "user": 1, "content_type": 38, "object_id": "79", "object_repr": "Да<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Default GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 29, "fields": {"action_time": "2024-12-13T02:14:08.478", "user": 1, "content_type": 38, "object_id": "78", "object_repr": "Чизбур<PERSON><PERSON><PERSON> Default GRAM 10.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 30, "fields": {"action_time": "2024-12-13T02:14:08.485", "user": 1, "content_type": 38, "object_id": "77", "object_repr": "Чизбур<PERSON><PERSON><PERSON> Default GRAM 10.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 31, "fields": {"action_time": "2024-12-13T02:14:08.492", "user": 1, "content_type": 38, "object_id": "76", "object_repr": "комбо янги Default GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 32, "fields": {"action_time": "2024-12-13T02:14:08.500", "user": 1, "content_type": 38, "object_id": "75", "object_repr": "комбо янги Default GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 33, "fields": {"action_time": "2024-12-13T02:14:08.508", "user": 1, "content_type": 38, "object_id": "74", "object_repr": "alfredo <PERSON>ьшая GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 34, "fields": {"action_time": "2024-12-13T02:14:08.515", "user": 1, "content_type": 38, "object_id": "73", "object_repr": "alfredo <PERSON>ьшая GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 35, "fields": {"action_time": "2024-12-13T02:14:08.522", "user": 1, "content_type": 38, "object_id": "72", "object_repr": "alfredo Средняя GRAM 13.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 36, "fields": {"action_time": "2024-12-13T02:14:08.529", "user": 1, "content_type": 38, "object_id": "71", "object_repr": "alfredo Средняя GRAM 13.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 37, "fields": {"action_time": "2024-12-13T02:14:08.536", "user": 1, "content_type": 38, "object_id": "70", "object_repr": "alfredo <PERSON>енькая GRAM 11.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 38, "fields": {"action_time": "2024-12-13T02:14:08.544", "user": 1, "content_type": 38, "object_id": "69", "object_repr": "alfredo <PERSON>енькая GRAM 11.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 39, "fields": {"action_time": "2024-12-13T02:14:08.551", "user": 1, "content_type": 38, "object_id": "68", "object_repr": "Pepperoni NEW Большая GRAM 28.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 40, "fields": {"action_time": "2024-12-13T02:14:08.558", "user": 1, "content_type": 38, "object_id": "67", "object_repr": "Pepperoni NEW Большая GRAM 28.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 41, "fields": {"action_time": "2024-12-13T02:14:08.565", "user": 1, "content_type": 38, "object_id": "66", "object_repr": "Pepperoni NEW Средняя GRAM 24.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 42, "fields": {"action_time": "2024-12-13T02:14:08.572", "user": 1, "content_type": 38, "object_id": "65", "object_repr": "Pepperoni NEW Средняя GRAM 24.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 43, "fields": {"action_time": "2024-12-13T02:14:08.579", "user": 1, "content_type": 38, "object_id": "64", "object_repr": "Pepperoni NEW Маленькая GRAM 20.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 44, "fields": {"action_time": "2024-12-13T02:14:08.591", "user": 1, "content_type": 38, "object_id": "63", "object_repr": "Pepperoni NEW Маленькая GRAM 20.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 45, "fields": {"action_time": "2024-12-13T02:14:08.600", "user": 1, "content_type": 38, "object_id": "62", "object_repr": "alfredo2 Большая GRAM 16.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 46, "fields": {"action_time": "2024-12-13T02:14:08.608", "user": 1, "content_type": 38, "object_id": "61", "object_repr": "alfredo2 Большая GRAM 16.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 47, "fields": {"action_time": "2024-12-13T02:14:08.616", "user": 1, "content_type": 38, "object_id": "60", "object_repr": "alfredo2 Средняя GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 48, "fields": {"action_time": "2024-12-13T02:14:08.624", "user": 1, "content_type": 38, "object_id": "59", "object_repr": "alfredo2 Средняя GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 49, "fields": {"action_time": "2024-12-13T02:14:08.632", "user": 1, "content_type": 38, "object_id": "58", "object_repr": "alfredo2 Маленькая GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 50, "fields": {"action_time": "2024-12-13T02:14:08.639", "user": 1, "content_type": 38, "object_id": "57", "object_repr": "alfredo2 Маленькая GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 51, "fields": {"action_time": "2024-12-13T02:14:08.646", "user": 1, "content_type": 38, "object_id": "56", "object_repr": "Margarita NEW Большая GRAM 25.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 52, "fields": {"action_time": "2024-12-13T02:14:08.652", "user": 1, "content_type": 38, "object_id": "55", "object_repr": "Margarita NEW Большая GRAM 25.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 53, "fields": {"action_time": "2024-12-13T02:14:08.659", "user": 1, "content_type": 38, "object_id": "54", "object_repr": "Margarita NEW Средняя GRAM 22.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 54, "fields": {"action_time": "2024-12-13T02:14:08.665", "user": 1, "content_type": 38, "object_id": "53", "object_repr": "Margarita NEW Средняя GRAM 22.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 55, "fields": {"action_time": "2024-12-13T02:14:08.676", "user": 1, "content_type": 38, "object_id": "52", "object_repr": "Margarita NEW Маленькая GRAM 18.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 56, "fields": {"action_time": "2024-12-13T02:14:08.683", "user": 1, "content_type": 38, "object_id": "51", "object_repr": "Margarita NEW Маленькая GRAM 18.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 57, "fields": {"action_time": "2024-12-13T02:14:08.691", "user": 1, "content_type": 38, "object_id": "50", "object_repr": "Hot-Dog Default GRAM 16.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 58, "fields": {"action_time": "2024-12-13T02:14:08.698", "user": 1, "content_type": 38, "object_id": "49", "object_repr": "Hot-Dog Default GRAM 16.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 59, "fields": {"action_time": "2024-12-13T02:14:08.705", "user": 1, "content_type": 38, "object_id": "48", "object_repr": "hot-dog odd<PERSON><PERSON> GRAM 8.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 60, "fields": {"action_time": "2024-12-13T02:14:08.712", "user": 1, "content_type": 38, "object_id": "47", "object_repr": "hot-dog odd<PERSON><PERSON> GRAM 8.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 61, "fields": {"action_time": "2024-12-13T02:14:08.720", "user": 1, "content_type": 38, "object_id": "20", "object_repr": "Choy NEW Default GRAM 4.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 62, "fields": {"action_time": "2024-12-13T02:14:08.727", "user": 1, "content_type": 38, "object_id": "19", "object_repr": "Choy NEW Default GRAM 4.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 63, "fields": {"action_time": "2024-12-13T02:14:08.734", "user": 1, "content_type": 38, "object_id": "18", "object_repr": "Pepsi NEW 1.5 л GRAM 18.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 64, "fields": {"action_time": "2024-12-13T02:14:08.741", "user": 1, "content_type": 38, "object_id": "17", "object_repr": "Pepsi NEW 1.5 л GRAM 18.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 65, "fields": {"action_time": "2024-12-13T02:14:08.748", "user": 1, "content_type": 38, "object_id": "16", "object_repr": "Pepsi NEW 1 л GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 66, "fields": {"action_time": "2024-12-13T02:14:08.755", "user": 1, "content_type": 38, "object_id": "15", "object_repr": "Pepsi NEW 1 л GRAM 12.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 67, "fields": {"action_time": "2024-12-13T02:14:08.762", "user": 1, "content_type": 38, "object_id": "14", "object_repr": "Pepsi NEW 0.5 л GRAM 9.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 68, "fields": {"action_time": "2024-12-13T02:14:08.769", "user": 1, "content_type": 38, "object_id": "13", "object_repr": "Pepsi NEW 0.5 л GRAM 9.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 69, "fields": {"action_time": "2024-12-13T02:14:08.777", "user": 1, "content_type": 38, "object_id": "12", "object_repr": "Pepsi razliv NEW 0,5л GRAM 11.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 70, "fields": {"action_time": "2024-12-13T02:14:08.785", "user": 1, "content_type": 38, "object_id": "11", "object_repr": "Pepsi razliv NEW 0,5л GRAM 11.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 71, "fields": {"action_time": "2024-12-13T02:14:08.792", "user": 1, "content_type": 38, "object_id": "10", "object_repr": "Pepsi razliv NEW 0,4л GRAM 9.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 72, "fields": {"action_time": "2024-12-13T02:14:08.799", "user": 1, "content_type": 38, "object_id": "9", "object_repr": "Pepsi razliv NEW 0,4л GRAM 9.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 73, "fields": {"action_time": "2024-12-13T02:14:08.807", "user": 1, "content_type": 38, "object_id": "8", "object_repr": "Pita NEW Большой GRAM 13.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 74, "fields": {"action_time": "2024-12-13T02:14:08.814", "user": 1, "content_type": 38, "object_id": "7", "object_repr": "Pita NEW Большой GRAM 13.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 75, "fields": {"action_time": "2024-12-13T02:14:08.822", "user": 1, "content_type": 38, "object_id": "6", "object_repr": "Pita NEW Средний GRAM 9.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 76, "fields": {"action_time": "2024-12-13T02:14:08.829", "user": 1, "content_type": 38, "object_id": "5", "object_repr": "Pita NEW Средний GRAM 9.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 77, "fields": {"action_time": "2024-12-13T02:14:08.836", "user": 1, "content_type": 38, "object_id": "4", "object_repr": "Pita (mol go'shti) NEW Большой GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 78, "fields": {"action_time": "2024-12-13T02:14:08.843", "user": 1, "content_type": 38, "object_id": "3", "object_repr": "Pita (mol go'shti) NEW Большой GRAM 14.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 79, "fields": {"action_time": "2024-12-13T02:14:08.850", "user": 1, "content_type": 38, "object_id": "2", "object_repr": "Pita (mol go'shti) NEW Средний GRAM 10.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 80, "fields": {"action_time": "2024-12-13T02:14:08.857", "user": 1, "content_type": 38, "object_id": "1", "object_repr": "Pita (mol go'shti) NEW Средний GRAM 10.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 81, "fields": {"action_time": "2024-12-13T02:17:42.516", "user": 1, "content_type": 34, "object_id": "8", "object_repr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 82, "fields": {"action_time": "2024-12-13T02:17:42.522", "user": 1, "content_type": 34, "object_id": "7", "object_repr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 83, "fields": {"action_time": "2024-12-13T02:17:42.527", "user": 1, "content_type": 34, "object_id": "6", "object_repr": "Combo", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 84, "fields": {"action_time": "2024-12-13T02:17:42.532", "user": 1, "content_type": 34, "object_id": "5", "object_repr": "Pizza", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 85, "fields": {"action_time": "2024-12-13T02:17:42.537", "user": 1, "content_type": 34, "object_id": "4", "object_repr": "Hot-Dog", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 86, "fields": {"action_time": "2024-12-13T02:17:42.542", "user": 1, "content_type": 34, "object_id": "2", "object_repr": "Напитки", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 87, "fields": {"action_time": "2024-12-13T02:17:42.547", "user": 1, "content_type": 34, "object_id": "1", "object_repr": "Пита", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 88, "fields": {"action_time": "2024-12-13T02:22:11.593", "user": 1, "content_type": 37, "object_id": "32", "object_repr": "<PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Title [en]\", \"Image url\", \"Description [uz]\", \"Description [ru]\", \"Description [en]\"]}}]"}}, {"model": "admin.logentry", "pk": 89, "fields": {"action_time": "2024-12-13T02:31:38.011", "user": 1, "content_type": 37, "object_id": "32", "object_repr": "Tandir lavash NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Description [en]\"]}}]"}}, {"model": "admin.logentry", "pk": 90, "fields": {"action_time": "2024-12-13T02:32:16.426", "user": 1, "content_type": 37, "object_id": "32", "object_repr": "Tandir lavash NEW", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 91, "fields": {"action_time": "2024-12-14T20:19:10.526", "user": 1, "content_type": 44, "object_id": "1", "object_repr": "ad1", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 92, "fields": {"action_time": "2024-12-14T20:20:13.386", "user": 1, "content_type": 44, "object_id": "2", "object_repr": "ad2", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 93, "fields": {"action_time": "2024-12-14T20:21:08.926", "user": 1, "content_type": 44, "object_id": "2", "object_repr": "ad2", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 94, "fields": {"action_time": "2024-12-16T16:32:01.267", "user": 1, "content_type": 37, "object_id": "32", "object_repr": "Tandir lavash NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\", \"Description [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 95, "fields": {"action_time": "2024-12-16T16:37:40.147", "user": 1, "content_type": 37, "object_id": "31", "object_repr": "Lavash NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\", \"Description [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 96, "fields": {"action_time": "2024-12-17T01:06:04.670", "user": 1, "content_type": 20, "object_id": "1", "object_repr": "Very Nearby (0-3 km) - Active", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 97, "fields": {"action_time": "2024-12-24T15:59:16.858", "user": 1, "content_type": 6, "object_id": "482569855", "object_repr": "Aktamov_Shahzod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is active\"]}}]"}}, {"model": "admin.logentry", "pk": 98, "fields": {"action_time": "2024-12-24T15:59:53.537", "user": 1, "content_type": 6, "object_id": "2105729169", "object_repr": "muh<PERSON><PERSON><PERSON>_me", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 99, "fields": {"action_time": "2024-12-24T19:46:55.302", "user": 1, "content_type": 6, "object_id": "6231040880", "object_repr": "jumamuradv", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is active\"]}}]"}}, {"model": "admin.logentry", "pk": 100, "fields": {"action_time": "2024-12-25T20:39:41.582", "user": 1, "content_type": 6, "object_id": "6231040880", "object_repr": "jumamuradv", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 101, "fields": {"action_time": "2024-12-26T20:54:29.879", "user": 1, "content_type": 37, "object_id": "32", "object_repr": "Tandir lavash NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image url\"]}}]"}}, {"model": "admin.logentry", "pk": 102, "fields": {"action_time": "2024-12-26T20:54:52.968", "user": 1, "content_type": 37, "object_id": "31", "object_repr": "Lavash NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image url\"]}}]"}}, {"model": "admin.logentry", "pk": 103, "fields": {"action_time": "2024-12-26T20:55:51.178", "user": 1, "content_type": 37, "object_id": "30", "object_repr": "Lavash pishloqli NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 104, "fields": {"action_time": "2024-12-26T20:57:04.982", "user": 1, "content_type": 37, "object_id": "29", "object_repr": "Tandir lavash pishloqli NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 105, "fields": {"action_time": "2024-12-26T20:58:05.906", "user": 1, "content_type": 37, "object_id": "28", "object_repr": "Gamburger NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 106, "fields": {"action_time": "2024-12-26T20:59:43.271", "user": 1, "content_type": 37, "object_id": "27", "object_repr": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 107, "fields": {"action_time": "2024-12-26T21:00:24.888", "user": 1, "content_type": 37, "object_id": "26", "object_repr": "Чизбургер", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 108, "fields": {"action_time": "2024-12-26T21:00:39.332", "user": 1, "content_type": 37, "object_id": "25", "object_repr": "комбо янги", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 109, "fields": {"action_time": "2024-12-26T21:01:13.588", "user": 1, "content_type": 37, "object_id": "33", "object_repr": "комбо янги", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 110, "fields": {"action_time": "2024-12-26T21:02:58.233", "user": 1, "content_type": 37, "object_id": "24", "object_repr": "<PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 111, "fields": {"action_time": "2024-12-26T21:04:40.932", "user": 1, "content_type": 37, "object_id": "23", "object_repr": "Pepperoni NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 112, "fields": {"action_time": "2024-12-26T21:06:31.857", "user": 1, "content_type": 37, "object_id": "22", "object_repr": "alfredo2", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 113, "fields": {"action_time": "2024-12-26T21:07:04.328", "user": 1, "content_type": 37, "object_id": "21", "object_repr": "Margarita NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 114, "fields": {"action_time": "2024-12-26T21:07:48.931", "user": 1, "content_type": 37, "object_id": "20", "object_repr": "Hot-Dog", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 115, "fields": {"action_time": "2024-12-26T21:08:28.649", "user": 1, "content_type": 37, "object_id": "19", "object_repr": "hot-dog oddiy", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 116, "fields": {"action_time": "2024-12-26T21:08:58.235", "user": 1, "content_type": 37, "object_id": "5", "object_repr": "Choy NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 117, "fields": {"action_time": "2024-12-26T21:09:27.967", "user": 1, "content_type": 37, "object_id": "4", "object_repr": "Pepsi NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 118, "fields": {"action_time": "2024-12-26T21:10:06.966", "user": 1, "content_type": 37, "object_id": "3", "object_repr": "Pepsi razliv NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 119, "fields": {"action_time": "2024-12-26T21:10:48.448", "user": 1, "content_type": 37, "object_id": "2", "object_repr": "Pita NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 120, "fields": {"action_time": "2024-12-26T21:11:30.871", "user": 1, "content_type": 37, "object_id": "1", "object_repr": "Pita (mol go'shti) NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [uz]\", \"Title [ru]\", \"Image url\", \"Description [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 121, "fields": {"action_time": "2024-12-26T21:11:59.689", "user": 1, "content_type": 41, "object_id": "23", "object_repr": "ItemModifierGroups object (23)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 122, "fields": {"action_time": "2024-12-26T21:12:18.673", "user": 1, "content_type": 41, "object_id": "22", "object_repr": "ItemModifierGroups object (22)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 123, "fields": {"action_time": "2024-12-26T21:12:30.971", "user": 1, "content_type": 41, "object_id": "21", "object_repr": "ItemModifierGroups object (21)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 124, "fields": {"action_time": "2024-12-26T21:12:40.220", "user": 1, "content_type": 41, "object_id": "20", "object_repr": "ItemModifierGroups object (20)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 125, "fields": {"action_time": "2024-12-26T21:12:45.087", "user": 1, "content_type": 41, "object_id": "20", "object_repr": "ItemModifierGroups object (20)", "action_flag": 2, "change_message": "[]"}}, {"model": "admin.logentry", "pk": 126, "fields": {"action_time": "2024-12-26T21:12:55.990", "user": 1, "content_type": 41, "object_id": "18", "object_repr": "ItemModifierGroups object (18)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 127, "fields": {"action_time": "2024-12-26T21:13:05.973", "user": 1, "content_type": 41, "object_id": "17", "object_repr": "ItemModifierGroups object (17)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 128, "fields": {"action_time": "2024-12-26T21:13:23.606", "user": 1, "content_type": 41, "object_id": "16", "object_repr": "ItemModifierGroups object (16)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 129, "fields": {"action_time": "2024-12-26T21:13:44.198", "user": 1, "content_type": 41, "object_id": "15", "object_repr": "ItemModifierGroups object (15)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 130, "fields": {"action_time": "2024-12-26T21:14:00.126", "user": 1, "content_type": 41, "object_id": "14", "object_repr": "ItemModifierGroups object (14)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 131, "fields": {"action_time": "2024-12-26T21:14:28.390", "user": 1, "content_type": 41, "object_id": "13", "object_repr": "ItemModifierGroups object (13)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 132, "fields": {"action_time": "2024-12-26T21:14:37.499", "user": 1, "content_type": 41, "object_id": "7", "object_repr": "ItemModifierGroups object (7)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 133, "fields": {"action_time": "2024-12-26T21:20:18.360", "user": 1, "content_type": 41, "object_id": "12", "object_repr": "ItemModifierGroups object (12)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 134, "fields": {"action_time": "2024-12-26T21:20:31.976", "user": 1, "content_type": 41, "object_id": "11", "object_repr": "ItemModifierGroups object (11)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 135, "fields": {"action_time": "2024-12-26T21:20:49.433", "user": 1, "content_type": 41, "object_id": "10", "object_repr": "ItemModifierGroups object (10)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 136, "fields": {"action_time": "2024-12-26T21:21:15.386", "user": 1, "content_type": 41, "object_id": "9", "object_repr": "ItemModifierGroups object (9)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 137, "fields": {"action_time": "2024-12-26T21:21:25.001", "user": 1, "content_type": 41, "object_id": "8", "object_repr": "ItemModifierGroups object (8)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 138, "fields": {"action_time": "2024-12-26T21:21:52.816", "user": 1, "content_type": 41, "object_id": "6", "object_repr": "ItemModifierGroups object (6)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 139, "fields": {"action_time": "2024-12-26T21:22:08.674", "user": 1, "content_type": 41, "object_id": "5", "object_repr": "ItemModifierGroups object (5)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 140, "fields": {"action_time": "2024-12-26T21:22:20.095", "user": 1, "content_type": 41, "object_id": "5", "object_repr": "ItemModifierGroups object (5)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 141, "fields": {"action_time": "2024-12-26T21:22:32.151", "user": 1, "content_type": 41, "object_id": "4", "object_repr": "ItemModifierGroups object (4)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 142, "fields": {"action_time": "2024-12-26T21:22:41.546", "user": 1, "content_type": 41, "object_id": "3", "object_repr": "ItemModifierGroups object (3)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 143, "fields": {"action_time": "2024-12-26T21:22:51.737", "user": 1, "content_type": 41, "object_id": "2", "object_repr": "ItemModifierGroups object (2)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 144, "fields": {"action_time": "2024-12-26T21:23:01.101", "user": 1, "content_type": 41, "object_id": "1", "object_repr": "ItemModifierGroups object (1)", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 145, "fields": {"action_time": "2024-12-26T21:23:44.645", "user": 1, "content_type": 38, "object_id": "100", "object_repr": "комбо янги Default GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 146, "fields": {"action_time": "2024-12-26T21:23:44.648", "user": 1, "content_type": 38, "object_id": "99", "object_repr": "комбо янги Default GRAM 15.0 - UZS", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is available\"]}}]"}}, {"model": "admin.logentry", "pk": 147, "fields": {"action_time": "2024-12-26T21:25:04.961", "user": 1, "content_type": 40, "object_id": "27", "object_repr": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 148, "fields": {"action_time": "2024-12-26T21:25:28.874", "user": 1, "content_type": 40, "object_id": "26", "object_repr": "халапеньо", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 149, "fields": {"action_time": "2024-12-26T21:25:41.265", "user": 1, "content_type": 40, "object_id": "25", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 150, "fields": {"action_time": "2024-12-26T21:25:55.445", "user": 1, "content_type": 40, "object_id": "24", "object_repr": "халапеньо", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 151, "fields": {"action_time": "2024-12-26T21:26:12.299", "user": 1, "content_type": 40, "object_id": "23", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 152, "fields": {"action_time": "2024-12-26T21:26:29.134", "user": 1, "content_type": 40, "object_id": "22", "object_repr": "халапеньо", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 153, "fields": {"action_time": "2024-12-26T21:26:59.911", "user": 1, "content_type": 40, "object_id": "21", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 154, "fields": {"action_time": "2024-12-26T22:44:00.651", "user": 1, "content_type": 40, "object_id": "1", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 155, "fields": {"action_time": "2024-12-26T22:44:10.077", "user": 1, "content_type": 40, "object_id": "2", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 156, "fields": {"action_time": "2024-12-26T22:44:19.794", "user": 1, "content_type": 40, "object_id": "3", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 157, "fields": {"action_time": "2024-12-26T22:44:28.270", "user": 1, "content_type": 40, "object_id": "4", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 158, "fields": {"action_time": "2024-12-26T22:44:51.889", "user": 1, "content_type": 40, "object_id": "5", "object_repr": "зеленый", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 159, "fields": {"action_time": "2024-12-26T22:45:07.208", "user": 1, "content_type": 40, "object_id": "6", "object_repr": "черный", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 160, "fields": {"action_time": "2024-12-26T22:45:20.250", "user": 1, "content_type": 40, "object_id": "7", "object_repr": "сыр", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 161, "fields": {"action_time": "2024-12-26T22:45:32.447", "user": 1, "content_type": 40, "object_id": "8", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 162, "fields": {"action_time": "2024-12-26T22:45:44.388", "user": 1, "content_type": 40, "object_id": "9", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 163, "fields": {"action_time": "2024-12-26T22:45:55.088", "user": 1, "content_type": 40, "object_id": "10", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 164, "fields": {"action_time": "2024-12-26T22:46:10.251", "user": 1, "content_type": 40, "object_id": "11", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 165, "fields": {"action_time": "2024-12-26T22:46:24.156", "user": 1, "content_type": 40, "object_id": "12", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 166, "fields": {"action_time": "2024-12-26T22:46:38.555", "user": 1, "content_type": 40, "object_id": "18", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 167, "fields": {"action_time": "2024-12-26T22:47:08.683", "user": 1, "content_type": 40, "object_id": "20", "object_repr": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 168, "fields": {"action_time": "2024-12-26T22:47:23.097", "user": 1, "content_type": 40, "object_id": "19", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 169, "fields": {"action_time": "2024-12-26T22:47:38.957", "user": 1, "content_type": 40, "object_id": "17", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 170, "fields": {"action_time": "2024-12-26T22:47:49.925", "user": 1, "content_type": 40, "object_id": "16", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 171, "fields": {"action_time": "2024-12-26T22:48:00.578", "user": 1, "content_type": 40, "object_id": "15", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 172, "fields": {"action_time": "2024-12-26T22:48:11.242", "user": 1, "content_type": 40, "object_id": "14", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 173, "fields": {"action_time": "2024-12-26T22:48:31.516", "user": 1, "content_type": 40, "object_id": "13", "object_repr": "pizza mod", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Name [uz]\", \"Name [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 174, "fields": {"action_time": "2024-12-27T00:11:09.614", "user": 1, "content_type": 44, "object_id": "1", "object_repr": "ad1", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Image url\"]}}]"}}, {"model": "admin.logentry", "pk": 175, "fields": {"action_time": "2024-12-27T00:11:33.415", "user": 1, "content_type": 44, "object_id": "3", "object_repr": "ad2", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 176, "fields": {"action_time": "2024-12-29T21:11:00.744", "user": 1, "content_type": 46, "object_id": "1", "object_repr": "<PERSON><PERSON><PERSON>", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 177, "fields": {"action_time": "2024-12-30T00:19:34.661", "user": 1, "content_type": 6, "object_id": "552563440", "object_repr": "jasur_murt<PERSON><PERSON>v", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is active\"]}}]"}}, {"model": "admin.logentry", "pk": 178, "fields": {"action_time": "2024-12-30T00:20:01.322", "user": 1, "content_type": 6, "object_id": "552563440", "object_repr": "jasur_murt<PERSON><PERSON>v", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 179, "fields": {"action_time": "2024-12-30T01:36:56.167", "user": 1, "content_type": 34, "object_id": "7", "object_repr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 180, "fields": {"action_time": "2024-12-30T01:37:32.126", "user": 1, "content_type": 34, "object_id": "3", "object_repr": "Доставка", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 181, "fields": {"action_time": "2024-12-30T01:38:33.009", "user": 1, "content_type": 34, "object_id": "8", "object_repr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 182, "fields": {"action_time": "2024-12-30T01:38:33.011", "user": 1, "content_type": 34, "object_id": "6", "object_repr": "Combo", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 183, "fields": {"action_time": "2024-12-30T01:38:33.013", "user": 1, "content_type": 34, "object_id": "5", "object_repr": "Pizza", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 184, "fields": {"action_time": "2024-12-30T01:38:33.016", "user": 1, "content_type": 34, "object_id": "4", "object_repr": "Hot-Dog", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\", \"Title [uz]\"]}}]"}}, {"model": "admin.logentry", "pk": 185, "fields": {"action_time": "2024-12-30T01:38:33.018", "user": 1, "content_type": 34, "object_id": "2", "object_repr": "Напитки", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 186, "fields": {"action_time": "2024-12-30T01:38:33.020", "user": 1, "content_type": 34, "object_id": "1", "object_repr": "Пита", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Title [ru]\"]}}]"}}, {"model": "admin.logentry", "pk": 187, "fields": {"action_time": "2024-12-30T01:49:19.578", "user": 1, "content_type": 34, "object_id": "8", "object_repr": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Position id\"]}}]"}}, {"model": "admin.logentry", "pk": 188, "fields": {"action_time": "2024-12-30T01:49:19.581", "user": 1, "content_type": 34, "object_id": "7", "object_repr": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Position id\"]}}]"}}, {"model": "admin.logentry", "pk": 189, "fields": {"action_time": "2024-12-30T01:49:19.583", "user": 1, "content_type": 34, "object_id": "6", "object_repr": "Combo", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Position id\"]}}]"}}, {"model": "admin.logentry", "pk": 190, "fields": {"action_time": "2024-12-30T01:49:19.585", "user": 1, "content_type": 34, "object_id": "5", "object_repr": "Pizza", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Position id\"]}}]"}}, {"model": "admin.logentry", "pk": 191, "fields": {"action_time": "2024-12-30T01:49:19.587", "user": 1, "content_type": 34, "object_id": "4", "object_repr": "Hot-Dog", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Position id\"]}}]"}}, {"model": "admin.logentry", "pk": 192, "fields": {"action_time": "2024-12-30T01:49:19.589", "user": 1, "content_type": 34, "object_id": "2", "object_repr": "Напитки", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Position id\"]}}]"}}, {"model": "admin.logentry", "pk": 193, "fields": {"action_time": "2024-12-30T01:49:19.591", "user": 1, "content_type": 34, "object_id": "1", "object_repr": "Пита", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Position id\"]}}]"}}, {"model": "admin.logentry", "pk": 194, "fields": {"action_time": "2025-01-02T16:03:25.586", "user": 1, "content_type": 48, "object_id": "1", "object_repr": "aaaa", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 195, "fields": {"action_time": "2025-01-02T16:03:35.108", "user": 1, "content_type": 48, "object_id": "1", "object_repr": "aaaa", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 196, "fields": {"action_time": "2025-01-07T21:58:51.557", "user": 1, "content_type": 46, "object_id": "2", "object_repr": "<PERSON><PERSON><PERSON>", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 197, "fields": {"action_time": "2025-01-08T01:16:47.638", "user": 1, "content_type": 32, "object_id": "3", "object_repr": "ЦУМ new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Opening Time\"]}}]"}}, {"model": "admin.logentry", "pk": 198, "fields": {"action_time": "2025-01-08T01:17:23.961", "user": 1, "content_type": 32, "object_id": "3", "object_repr": "ЦУМ new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Opening Time\"]}}]"}}, {"model": "admin.logentry", "pk": 199, "fields": {"action_time": "2025-01-08T01:18:25.389", "user": 1, "content_type": 32, "object_id": "3", "object_repr": "ЦУМ new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Closing Time\"]}}]"}}, {"model": "admin.logentry", "pk": 200, "fields": {"action_time": "2025-01-08T01:19:45.100", "user": 1, "content_type": 32, "object_id": "3", "object_repr": "ЦУМ new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Closing Time\"]}}]"}}, {"model": "admin.logentry", "pk": 201, "fields": {"action_time": "2025-01-08T12:58:39.251", "user": 1, "content_type": 32, "object_id": "3", "object_repr": "ЦУМ new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Opening Time\", \"Closing Time\"]}}]"}}, {"model": "admin.logentry", "pk": 202, "fields": {"action_time": "2025-01-08T13:00:52.340", "user": 1, "content_type": 32, "object_id": "3", "object_repr": "ЦУМ new", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Opening Time\", \"Closing Time\"]}}]"}}, {"model": "admin.logentry", "pk": 203, "fields": {"action_time": "2025-01-09T14:01:14.592", "user": 1, "content_type": 6, "object_id": "6231040880", "object_repr": "jumamuradv", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 204, "fields": {"action_time": "2025-01-09T19:06:00.935", "user": 1, "content_type": 6, "object_id": "6231040880", "object_repr": "jumamuradv", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 205, "fields": {"action_time": "2025-01-09T19:12:11.852", "user": 1, "content_type": 6, "object_id": "6231040880", "object_repr": "jumamuradv", "action_flag": 3, "change_message": ""}}, {"model": "admin.logentry", "pk": 206, "fields": {"action_time": "2025-01-09T19:12:39.210", "user": 1, "content_type": 6, "object_id": "6231040880", "object_repr": "jumamuradv", "action_flag": 1, "change_message": "[{\"added\": {}}]"}}, {"model": "admin.logentry", "pk": 207, "fields": {"action_time": "2025-01-14T10:35:55.915", "user": 1, "content_type": 37, "object_id": "26", "object_repr": "Чизбургер", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 208, "fields": {"action_time": "2025-01-15T00:39:26.411", "user": 1, "content_type": 37, "object_id": "33", "object_repr": "комбо янги", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 209, "fields": {"action_time": "2025-01-15T18:08:00.144", "user": 1, "content_type": 37, "object_id": "2", "object_repr": "Pita NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 210, "fields": {"action_time": "2025-01-16T19:43:12.319", "user": 1, "content_type": 6, "object_id": "6231040880", "object_repr": "jumamuradv", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Is active\"]}}]"}}, {"model": "admin.logentry", "pk": 211, "fields": {"action_time": "2025-01-17T22:07:03.383", "user": 1, "content_type": 37, "object_id": "33", "object_repr": "комбо янги", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 212, "fields": {"action_time": "2025-01-17T22:07:14.675", "user": 1, "content_type": 37, "object_id": "26", "object_repr": "Чизбургер", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 213, "fields": {"action_time": "2025-01-17T22:07:58.146", "user": 1, "content_type": 37, "object_id": "32", "object_repr": "Tandir lavash NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 214, "fields": {"action_time": "2025-01-17T22:08:26.933", "user": 1, "content_type": 37, "object_id": "24", "object_repr": "<PERSON><PERSON><PERSON>", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 215, "fields": {"action_time": "2025-01-17T22:09:41.881", "user": 1, "content_type": 37, "object_id": "21", "object_repr": "Margarita NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 216, "fields": {"action_time": "2025-01-17T22:10:12.721", "user": 1, "content_type": 37, "object_id": "2", "object_repr": "Pita NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "admin.logentry", "pk": 217, "fields": {"action_time": "2025-01-18T18:50:03.024", "user": 1, "content_type": 37, "object_id": "2", "object_repr": "Pita NEW", "action_flag": 2, "change_message": "[{\"changed\": {\"fields\": [\"Recommended Products\"]}}]"}}, {"model": "sessions.session", "pk": "00wvxl9wa66i95p7k16m8ou9ipb7gbuw", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tObpD:0e1tVwuZzGa1_DNqpNE0R4TpffeLD60Mx675jkzde8Q", "expire_date": "2025-01-03T17:09:47.613"}}, {"model": "sessions.session", "pk": "2844l8pbj7rez8e27gylsm2tb319slo3", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tOhoM:e7qY7NmeJFgBGld1eVdDaUjrAa3DFTExurrs9qQ1jiw", "expire_date": "2025-01-03T23:33:18.038"}}, {"model": "sessions.session", "pk": "2afrmjz20jy68xg6j7zw16pkm1of74d2", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tUir8:P_ywNdEbP5FJL-ldjziJpgX0TqFdaq3oFK1yyC75X6c", "expire_date": "2025-01-20T13:53:02.876"}}, {"model": "sessions.session", "pk": "366bj43drsdpti09hx60eisebg86w99t", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tWlBQ:XJ6kVGH5YsZ1g5jLrebP20Y64CQg6I3Aq6-HqbPaMV8", "expire_date": "2025-01-26T04:46:24.640"}}, {"model": "sessions.session", "pk": "60dcckwmgl0pydv73sdxe1aafflk49q8", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tMTtz:3exn0k3I4iSkxwf6kEzZVU4f8n5Ehy3Dx4nlq9fey20", "expire_date": "2024-12-28T20:17:55.422"}}, {"model": "sessions.session", "pk": "cmj8075lx9qwe5jt6ic6vlg1slk9hlqf", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1taCB5:ViZOO60kVO1lip1AtWG51Re-K7v1wJDmB8sP-22WdFQ", "expire_date": "2025-02-04T16:12:15.604"}}, {"model": "sessions.session", "pk": "il12m9yhm0wlwbveq1ious9b9c5p80do", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tQqBD:6dyVzpPelBLeiGupFu7p6eXXjjP8ayc25QuiJMTn9_k", "expire_date": "2025-01-09T20:53:43.370"}}, {"model": "sessions.session", "pk": "krsna58851yyiqf7cnnhqmmbrf46r07j", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tRGTS:N8uFnrt1S2rR7zZstDf928d8vXD0Kkd1D8oTjKMkZTw", "expire_date": "2025-01-11T00:58:18.192"}}, {"model": "sessions.session", "pk": "nhmtd7ws5yos25sb88s13v42j9qr7xsj", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tOLf6:uAblyNIEnYsL9yGqABFGh1YjSY1M7teHn3BYkMFtpLs", "expire_date": "2025-01-02T23:54:16.115"}}, {"model": "sessions.session", "pk": "s44kzjlp1tjxu2mumiqny6tv8zploqex", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tLqIj:ptG2ZRckl3BxHc87SuZdREgaiZQQMM_Od_jyLEVfNbk", "expire_date": "2024-12-27T02:00:49.643"}}, {"model": "sessions.session", "pk": "tkslsutsd6r93p9slp8s7ha3p8xebolh", "fields": {"session_data": ".eJxVjMsOwiAQRf-FtSEwPEZcuu83EGCmUjU0Ke3K-O_apAvd3nPOfYmYtrXGrfMSJxIXocXpd8upPLjtgO6p3WZZ5rYuU5a7Ig_a5TATP6-H-3dQU6_fmk1AID2iIVRgR0forULOZ2W9Y6QEWhnjC1pLhoIjHXwG0CZ4yArF-wO-ZDaF:1tWJim:qqNZwn-hOOofOiuWLmzLPiuwmZfxAc-aryLS1MShDPs", "expire_date": "2025-01-24T23:27:00.880"}}, {"model": "bot.telegramuser", "pk": *********, "fields": {"created_at": "2024-12-29T14:45:21.329", "updated_at": "2024-12-29T14:45:37.849", "user": 9, "username": "to<PERSON><PERSON>", "first_name": "Ｔａｋｈｉｒ", "last_name": null, "is_active": true, "lang": "ru", "phone": "998990920711", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "bot.telegramuser", "pk": 482569855, "fields": {"created_at": "2024-12-16T22:12:36.843", "updated_at": "2025-01-17T16:17:20.039", "user": 3, "username": "Aktamov_Shahzod", "first_name": "Shahzod", "last_name": "<PERSON><PERSON><PERSON><PERSON>", "is_active": true, "lang": "ru", "phone": "99**********", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "bot.telegramuser", "pk": 552563440, "fields": {"created_at": "2024-12-30T00:24:30.105", "updated_at": "2025-01-02T20:48:52.492", "user": 4, "username": "jasur_murt<PERSON><PERSON>v", "first_name": "<PERSON><PERSON><PERSON>", "last_name": null, "is_active": true, "lang": "uz", "phone": "998998769608", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "bot.telegramuser", "pk": 1275009699, "fields": {"created_at": "2024-12-30T18:30:25.259", "updated_at": "2024-12-30T18:46:44.628", "user": 10, "username": "name_727", "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "last_name": null, "is_active": true, "lang": "ru", "phone": "998978287227", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "bot.telegramuser", "pk": 2105729169, "fields": {"created_at": "2025-01-12T02:53:34.352", "updated_at": "2025-01-12T02:53:52.219", "user": 18, "username": "muh<PERSON><PERSON><PERSON>_me", "first_name": "<PERSON><PERSON> ", "last_name": null, "is_active": true, "lang": "uz", "phone": "998888351717", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "bot.telegramuser", "pk": 6231040880, "fields": {"created_at": "2025-01-09T19:12:39.209", "updated_at": "2025-01-17T21:46:03.525", "user": 5, "username": "jumamuradv", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "is_active": true, "lang": "ru", "phone": "99**********", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "bot.telegramuser", "pk": 6437316410, "fields": {"created_at": "2024-12-18T22:34:08.312", "updated_at": "2024-12-18T22:34:35.441", "user": 8, "username": null, "first_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_name": null, "is_active": false, "lang": "uz", "phone": "998933250144", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "bot.telegramuser", "pk": 6873479930, "fields": {"created_at": "2025-01-16T18:49:16.536", "updated_at": "2025-01-16T18:49:28.368", "user": 20, "username": "muh<PERSON><PERSON><PERSON><PERSON>", "first_name": "<PERSON><PERSON>", "last_name": null, "is_active": true, "lang": "uz", "phone": "998937040863", "latitude": null, "longitude": null, "is_state_notified": false, "state": "on_menu"}}, {"model": "user.users", "pk": 1, "fields": {"password": "pbkdf2_sha256$720000$LRmVl0VZatByeX1E2j8SvN$ZnOw9qm/uUnM7akMFrF6EMPENjaCxObUqIXYMUhJfZc=", "last_login": "2025-01-21T16:12:15.574", "is_superuser": true, "created_at": "2024-12-13T02:00:38.223", "updated_at": "2024-12-13T02:00:38.223", "phone": "998900000000", "role": "admin", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": true, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 3, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-14T14:42:48.579", "updated_at": "2025-01-08T14:30:06.713", "phone": "99**********", "role": "client", "name": "<PERSON><PERSON><PERSON>", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 4, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-14T18:35:50.976", "updated_at": "2025-01-02T13:00:01.050", "phone": "998998769608", "role": "client", "name": "<PERSON><PERSON><PERSON>", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 5, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-14T19:00:24.456", "updated_at": "2025-01-17T21:38:52.374", "phone": "99**********", "role": "client", "name": "<PERSON><PERSON><PERSON>", "email": null, "gender": "male", "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 6, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-18T21:43:24.319", "updated_at": "2024-12-18T21:43:24.319", "phone": "998090000000", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 7, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-18T22:00:39.277", "updated_at": "2024-12-18T22:00:39.277", "phone": "998110001111", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 8, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-18T22:34:35.372", "updated_at": "2024-12-18T22:34:35.372", "phone": "998933250144", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 9, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-29T14:45:37.817", "updated_at": "2024-12-29T14:45:37.817", "phone": "998990920711", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 10, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2024-12-30T18:30:59.342", "updated_at": "2024-12-30T18:30:59.343", "phone": "998978287227", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 16, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2025-01-08T14:32:52.694", "updated_at": "2025-01-08T14:32:59.292", "phone": "998565656565", "role": "client", "name": "MasterKebab Client", "email": "", "gender": "", "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 17, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2025-01-08T14:39:58.598", "updated_at": "2025-01-08T14:40:08.253", "phone": "998112121112", "role": "client", "name": "MasterKebab Client", "email": "", "gender": "", "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 18, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2025-01-12T02:53:52.177", "updated_at": "2025-01-12T02:53:52.177", "phone": "998888351717", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 19, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2025-01-12T11:32:28.501", "updated_at": "2025-01-12T16:09:40.139", "phone": "998915655359", "role": "client", "name": "<PERSON><PERSON><PERSON><PERSON>", "email": "", "gender": "F", "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 20, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2025-01-16T18:49:28.332", "updated_at": "2025-01-16T18:49:28.332", "phone": "998937040863", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 21, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2025-01-20T14:21:22.672", "updated_at": "2025-01-20T14:21:22.672", "phone": "998951221119", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "user.users", "pk": 22, "fields": {"password": "", "last_login": null, "is_superuser": false, "created_at": "2025-01-23T17:41:23.774", "updated_at": "2025-01-23T17:41:23.774", "phone": "998998880000", "role": "client", "name": "MasterKebab Client", "email": null, "gender": null, "date_of_birth": null, "lang": "uz", "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "order.orderitem", "pk": 1, "fields": {"created_at": "2024-12-17T01:00:58.901", "updated_at": "2024-12-17T01:00:58.901", "order": null, "product": 1, "quantity": 1}}, {"model": "order.orderitem", "pk": 2, "fields": {"created_at": "2024-12-17T01:00:58.918", "updated_at": "2024-12-17T01:00:58.918", "order": null, "product": 2, "quantity": 1}}, {"model": "order.orderitem", "pk": 3, "fields": {"created_at": "2024-12-25T22:37:59.495", "updated_at": "2024-12-25T22:37:59.495", "order": null, "product": 3, "quantity": 3}}, {"model": "order.orderitem", "pk": 4, "fields": {"created_at": "2024-12-25T22:39:02.186", "updated_at": "2024-12-25T22:39:02.186", "order": null, "product": 4, "quantity": 2}}, {"model": "order.orderitem", "pk": 5, "fields": {"created_at": "2024-12-26T14:12:03.140", "updated_at": "2024-12-26T14:12:03.162", "order": 4, "product": 5, "quantity": 1}}, {"model": "order.orderitem", "pk": 6, "fields": {"created_at": "2024-12-26T14:12:03.147", "updated_at": "2024-12-26T14:12:03.167", "order": 4, "product": 6, "quantity": 1}}, {"model": "order.orderitem", "pk": 7, "fields": {"created_at": "2024-12-26T14:16:57.242", "updated_at": "2024-12-26T14:16:57.264", "order": 5, "product": 7, "quantity": 1}}, {"model": "order.orderitem", "pk": 8, "fields": {"created_at": "2024-12-26T14:16:57.249", "updated_at": "2024-12-26T14:16:57.269", "order": 5, "product": 8, "quantity": 1}}, {"model": "order.orderitem", "pk": 9, "fields": {"created_at": "2024-12-30T18:42:43.186", "updated_at": "2024-12-30T18:42:43.186", "order": null, "product": 9, "quantity": 1}}, {"model": "order.orderitem", "pk": 10, "fields": {"created_at": "2024-12-30T18:42:43.193", "updated_at": "2024-12-30T18:42:43.193", "order": null, "product": 10, "quantity": 1}}, {"model": "order.orderitem", "pk": 11, "fields": {"created_at": "2024-12-30T18:42:52.603", "updated_at": "2024-12-30T18:42:52.603", "order": null, "product": 11, "quantity": 1}}, {"model": "order.orderitem", "pk": 12, "fields": {"created_at": "2024-12-30T18:42:52.610", "updated_at": "2024-12-30T18:42:52.610", "order": null, "product": 12, "quantity": 1}}, {"model": "order.orderitem", "pk": 13, "fields": {"created_at": "2025-01-02T13:00:01.003", "updated_at": "2025-01-02T13:00:01.026", "order": 8, "product": 13, "quantity": 1}}, {"model": "order.orderitem", "pk": 14, "fields": {"created_at": "2025-01-02T13:00:01.011", "updated_at": "2025-01-02T13:00:01.033", "order": 8, "product": 14, "quantity": 1}}, {"model": "order.orderitem", "pk": 15, "fields": {"created_at": "2025-01-07T01:17:00.898", "updated_at": "2025-01-07T01:17:00.929", "order": 9, "product": 15, "quantity": 1}}, {"model": "order.orderitem", "pk": 16, "fields": {"created_at": "2025-01-07T01:17:00.905", "updated_at": "2025-01-07T01:17:00.934", "order": 9, "product": 16, "quantity": 1}}, {"model": "order.orderitem", "pk": 17, "fields": {"created_at": "2025-01-07T01:17:00.912", "updated_at": "2025-01-07T01:17:00.938", "order": 9, "product": 17, "quantity": 1}}, {"model": "order.orderitem", "pk": 18, "fields": {"created_at": "2025-01-07T01:38:09.083", "updated_at": "2025-01-07T01:38:09.083", "order": null, "product": 18, "quantity": 1}}, {"model": "order.orderitem", "pk": 19, "fields": {"created_at": "2025-01-07T01:38:09.090", "updated_at": "2025-01-07T01:38:09.090", "order": null, "product": 19, "quantity": 1}}, {"model": "order.orderitem", "pk": 20, "fields": {"created_at": "2025-01-07T01:39:14.906", "updated_at": "2025-01-07T01:39:14.928", "order": 11, "product": 20, "quantity": 1}}, {"model": "order.orderitem", "pk": 21, "fields": {"created_at": "2025-01-07T01:39:14.913", "updated_at": "2025-01-07T01:39:14.932", "order": 11, "product": 21, "quantity": 1}}, {"model": "order.orderitem", "pk": 22, "fields": {"created_at": "2025-01-07T01:48:14.522", "updated_at": "2025-01-07T01:48:14.552", "order": 12, "product": 22, "quantity": 1}}, {"model": "order.orderitem", "pk": 23, "fields": {"created_at": "2025-01-07T01:48:14.530", "updated_at": "2025-01-07T01:48:14.557", "order": 12, "product": 23, "quantity": 1}}, {"model": "order.orderitem", "pk": 24, "fields": {"created_at": "2025-01-07T01:48:14.536", "updated_at": "2025-01-07T01:48:14.561", "order": 12, "product": 24, "quantity": 1}}, {"model": "order.orderitem", "pk": 25, "fields": {"created_at": "2025-01-07T01:56:40.088", "updated_at": "2025-01-07T01:56:40.117", "order": 13, "product": 25, "quantity": 1}}, {"model": "order.orderitem", "pk": 26, "fields": {"created_at": "2025-01-07T01:56:40.095", "updated_at": "2025-01-07T01:56:40.122", "order": 13, "product": 26, "quantity": 1}}, {"model": "order.orderitem", "pk": 27, "fields": {"created_at": "2025-01-07T01:56:40.102", "updated_at": "2025-01-07T01:56:40.126", "order": 13, "product": 27, "quantity": 1}}, {"model": "order.orderitem", "pk": 28, "fields": {"created_at": "2025-01-08T14:30:06.686", "updated_at": "2025-01-08T14:30:06.703", "order": 14, "product": 28, "quantity": 1}}, {"model": "order.orderitem", "pk": 29, "fields": {"created_at": "2025-01-08T14:30:30.452", "updated_at": "2025-01-08T14:30:30.469", "order": 15, "product": 29, "quantity": 1}}, {"model": "order.orderitem", "pk": 30, "fields": {"created_at": "2025-01-08T14:31:53.474", "updated_at": "2025-01-08T14:31:53.474", "order": null, "product": 30, "quantity": 1}}, {"model": "order.orderitem", "pk": 31, "fields": {"created_at": "2025-01-09T14:39:59.449", "updated_at": "2025-01-09T14:39:59.469", "order": 17, "product": 31, "quantity": 1}}, {"model": "order.orderitem", "pk": 32, "fields": {"created_at": "2025-01-09T14:42:16.229", "updated_at": "2025-01-09T14:42:16.229", "order": null, "product": 32, "quantity": 4}}, {"model": "order.orderitem", "pk": 33, "fields": {"created_at": "2025-01-09T14:44:51.445", "updated_at": "2025-01-09T14:44:51.445", "order": null, "product": 33, "quantity": 4}}, {"model": "order.orderitem", "pk": 34, "fields": {"created_at": "2025-01-09T19:08:54.665", "updated_at": "2025-01-09T19:08:54.684", "order": 20, "product": 34, "quantity": 1}}, {"model": "order.historicalorder", "pk": 4, "fields": {"id": 4, "created_at": "2024-12-26T14:12:03.155", "updated_at": "2024-12-26T14:12:03.155", "status": "Created", "history_date": "2024-12-26T14:12:03.156", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 5, "fields": {"id": 5, "created_at": "2024-12-26T14:16:57.258", "updated_at": "2024-12-26T14:16:57.258", "status": "Created", "history_date": "2024-12-26T14:16:57.259", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 6, "fields": {"id": 4, "created_at": "2024-12-26T14:12:03.155", "updated_at": "2024-12-26T14:22:09.885", "status": "Timeout", "history_date": "2024-12-26T14:22:09.888", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 7, "fields": {"id": 5, "created_at": "2024-12-26T14:16:57.258", "updated_at": "2024-12-26T14:32:09.886", "status": "Timeout", "history_date": "2024-12-26T14:32:09.888", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 10, "fields": {"id": 8, "created_at": "2025-01-02T13:00:01.019", "updated_at": "2025-01-02T13:00:01.019", "status": "Created", "history_date": "2025-01-02T13:00:01.020", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 11, "fields": {"id": 8, "created_at": "2025-01-02T13:00:01.019", "updated_at": "2025-01-02T13:16:54.325", "status": "Timeout", "history_date": "2025-01-02T13:16:54.327", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 12, "fields": {"id": 9, "created_at": "2025-01-07T01:17:00.921", "updated_at": "2025-01-07T01:17:00.921", "status": "Created", "history_date": "2025-01-07T01:17:00.922", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 13, "fields": {"id": 9, "created_at": "2025-01-07T01:17:00.921", "updated_at": "2025-01-07T01:31:19.376", "status": "Timeout", "history_date": "2025-01-07T01:31:19.378", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 15, "fields": {"id": 11, "created_at": "2025-01-07T01:39:14.921", "updated_at": "2025-01-07T01:39:14.921", "status": "Created", "history_date": "2025-01-07T01:39:14.922", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 16, "fields": {"id": 12, "created_at": "2025-01-07T01:48:14.545", "updated_at": "2025-01-07T01:48:14.545", "status": "Created", "history_date": "2025-01-07T01:48:14.546", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 17, "fields": {"id": 11, "created_at": "2025-01-07T01:39:14.921", "updated_at": "2025-01-07T01:51:19.374", "status": "Timeout", "history_date": "2025-01-07T01:51:19.376", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 18, "fields": {"id": 13, "created_at": "2025-01-07T01:56:40.111", "updated_at": "2025-01-07T01:56:40.111", "status": "Created", "history_date": "2025-01-07T01:56:40.112", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 19, "fields": {"id": 12, "created_at": "2025-01-07T01:48:14.545", "updated_at": "2025-01-07T02:01:19.373", "status": "Timeout", "history_date": "2025-01-07T02:01:19.376", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 20, "fields": {"id": 13, "created_at": "2025-01-07T01:56:40.111", "updated_at": "2025-01-07T02:11:19.374", "status": "Timeout", "history_date": "2025-01-07T02:11:19.377", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 21, "fields": {"id": 14, "created_at": "2025-01-08T14:30:06.695", "updated_at": "2025-01-08T14:30:06.695", "status": "Created", "history_date": "2025-01-08T14:30:06.696", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 22, "fields": {"id": 15, "created_at": "2025-01-08T14:30:30.463", "updated_at": "2025-01-08T14:30:30.463", "status": "Created", "history_date": "2025-01-08T14:30:30.464", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 24, "fields": {"id": 15, "created_at": "2025-01-08T14:30:30.463", "updated_at": "2025-01-08T14:44:51.956", "status": "Timeout", "history_date": "2025-01-08T14:44:51.959", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 25, "fields": {"id": 14, "created_at": "2025-01-08T14:30:06.695", "updated_at": "2025-01-08T14:44:51.962", "status": "Timeout", "history_date": "2025-01-08T14:44:51.965", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 26, "fields": {"id": 17, "created_at": "2025-01-09T14:39:59.461", "updated_at": "2025-01-09T14:39:59.461", "status": "Created", "history_date": "2025-01-09T14:39:59.462", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 29, "fields": {"id": 17, "created_at": "2025-01-09T14:39:59.461", "updated_at": "2025-01-09T14:58:42.825", "status": "Timeout", "history_date": "2025-01-09T14:58:42.827", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.historicalorder", "pk": 30, "fields": {"id": 20, "created_at": "2025-01-09T19:08:54.677", "updated_at": "2025-01-09T19:08:54.677", "status": "Created", "history_date": "2025-01-09T19:08:54.678", "history_change_reason": null, "history_type": "+", "history_user": null}}, {"model": "order.historicalorder", "pk": 31, "fields": {"id": 20, "created_at": "2025-01-09T19:08:54.677", "updated_at": "2025-01-09T19:28:42.825", "status": "Timeout", "history_date": "2025-01-09T19:28:42.827", "history_change_reason": null, "history_type": "~", "history_user": null}}, {"model": "order.order", "pk": 4, "fields": {"created_at": "2024-12-26T14:12:03.155", "updated_at": "2024-12-26T14:22:09.885", "user": 3, "delivery_agent": null, "operator": null, "organization": 3, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 5, "fields": {"created_at": "2024-12-26T14:16:57.258", "updated_at": "2024-12-26T14:32:09.886", "user": 3, "delivery_agent": null, "operator": null, "organization": 3, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 8, "fields": {"created_at": "2025-01-02T13:00:01.019", "updated_at": "2025-01-02T13:16:54.325", "user": 4, "delivery_agent": null, "operator": null, "organization": 3, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 9, "fields": {"created_at": "2025-01-07T01:17:00.921", "updated_at": "2025-01-07T01:31:19.376", "user": 3, "delivery_agent": null, "operator": null, "organization": 3, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 11, "fields": {"created_at": "2025-01-07T01:39:14.921", "updated_at": "2025-01-07T01:51:19.374", "user": 3, "delivery_agent": null, "operator": null, "organization": 3, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 12, "fields": {"created_at": "2025-01-07T01:48:14.545", "updated_at": "2025-01-07T02:01:19.373", "user": 3, "delivery_agent": null, "operator": null, "organization": 3, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 13, "fields": {"created_at": "2025-01-07T01:56:40.111", "updated_at": "2025-01-07T02:11:19.374", "user": 3, "delivery_agent": null, "operator": null, "organization": 3, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 14, "fields": {"created_at": "2025-01-08T14:30:06.695", "updated_at": "2025-01-08T14:44:51.962", "user": 3, "delivery_agent": null, "operator": null, "organization": 2, "status": "Timeout", "initiator": "web", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 15, "fields": {"created_at": "2025-01-08T14:30:30.463", "updated_at": "2025-01-08T14:44:51.956", "user": 3, "delivery_agent": null, "operator": null, "organization": 2, "status": "Timeout", "initiator": "web", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 17, "fields": {"created_at": "2025-01-09T14:39:59.461", "updated_at": "2025-01-09T14:58:42.825", "user": 5, "delivery_agent": null, "operator": null, "organization": 2, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.order", "pk": 20, "fields": {"created_at": "2025-01-09T19:08:54.677", "updated_at": "2025-01-09T19:28:42.825", "user": 5, "delivery_agent": null, "operator": null, "organization": 2, "status": "Timeout", "initiator": "bot", "courier_shift": null, "external_id": null, "courier_search_task_id": null, "external_number": null, "courier_finding_status": "NotStarted", "attempt_count": 0, "is_paid": false, "log": null, "sent_to_couriers": []}}, {"model": "order.orderdetail", "pk": 1, "fields": {"created_at": "2024-12-17T01:00:58.929", "updated_at": "2024-12-17T01:00:58.929", "order": null, "name": "Shahzod", "phone": "99**********", "delivery_type": "Delivery", "delivery_address": "Lev Tolstoy ko‘chasi 9", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.092242, "longitude": 65.375488, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 2, "fields": {"created_at": "2024-12-25T22:37:59.506", "updated_at": "2024-12-25T22:37:59.506", "order": null, "name": "Shahzod", "phone": "99**********", "delivery_type": "Delivery", "delivery_address": "Lev Tolstoy ko‘chasi 9", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.092242, "longitude": 65.375488, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 3, "fields": {"created_at": "2024-12-25T22:39:02.191", "updated_at": "2024-12-25T22:39:02.191", "order": null, "name": "Shahzod", "phone": "99**********", "delivery_type": "Delivery", "delivery_address": "<PERSON><PERSON> ko'chasi 9", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.100513, "longitude": 65.370772, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 4, "fields": {"created_at": "2024-12-26T14:12:03.152", "updated_at": "2024-12-26T14:12:03.182", "order": 4, "name": "Shahzod", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "Yoshlik koʻchasi 12", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 19.0, "delivery_cost": 0.0, "distance": 0.38326902614871333, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.098713, "longitude": 65.371149, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 5, "fields": {"created_at": "2024-12-26T14:16:57.255", "updated_at": "2024-12-26T14:16:57.283", "order": 5, "name": "Shahzod", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "<PERSON><PERSON> ko'chasi 9", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 19.0, "delivery_cost": 0.0, "distance": 0.3570723400483571, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.100513, "longitude": 65.370772, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 6, "fields": {"created_at": "2024-12-30T18:42:43.199", "updated_at": "2024-12-30T18:42:43.199", "order": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "phone": "998978287227", "delivery_type": "Delivery", "delivery_address": "O'zbekiston ko'chasi 1", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": " nnnvuongu", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09654, "longitude": 65.367798, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 7, "fields": {"created_at": "2024-12-30T18:42:52.615", "updated_at": "2024-12-30T18:42:52.615", "order": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "phone": "998978287227", "delivery_type": "Delivery", "delivery_address": "O'zbekiston ko'chasi 1", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": " nnnvuongu", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09654, "longitude": 65.367798, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 8, "fields": {"created_at": "2025-01-02T13:00:01.016", "updated_at": "2025-01-02T13:00:01.047", "order": 8, "name": "<PERSON><PERSON><PERSON>", "phone": "998998769608", "delivery_type": "SelfCall", "delivery_address": "O'zbekiston ko'chasi 8", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 28.0, "delivery_cost": 0.0, "distance": 0.4425276676964166, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.097202, "longitude": 65.372137, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 9, "fields": {"created_at": "2025-01-07T01:17:00.918", "updated_at": "2025-01-07T01:17:00.982", "order": 9, "name": "Shahzod", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 28", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 37.0, "delivery_cost": 0.0, "distance": 0.46890432394563697, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.092898786869, "longitude": 65.37580704670513, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 10, "fields": {"created_at": "2025-01-07T01:38:09.095", "updated_at": "2025-01-07T01:38:09.095", "order": null, "name": "Shahzod", "phone": "99**********", "delivery_type": "Delivery", "delivery_address": "<PERSON>ak k<PERSON>ʻ<PERSON>si 551", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09981, "longitude": 65.362175, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 11, "fields": {"created_at": "2025-01-07T01:39:14.918", "updated_at": "2025-01-07T01:39:14.945", "order": 11, "name": "Shahzod", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "<PERSON>ak k<PERSON>ʻ<PERSON>si 551", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 24.0, "delivery_cost": 0.0, "distance": 1.091303801510729, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09981, "longitude": 65.362175, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 12, "fields": {"created_at": "2025-01-07T01:48:14.542", "updated_at": "2025-01-07T01:48:14.574", "order": 12, "name": "Shahzod", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "<PERSON><PERSON> k<PERSON>'chasi 19/2", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 38.0, "delivery_cost": 0.0, "distance": 0.45134322592449877, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.098023, "longitude": 65.370807, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 13, "fields": {"created_at": "2025-01-07T01:56:40.108", "updated_at": "2025-01-07T01:56:40.139", "order": 13, "name": "Shahzod", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "O'zbekiston ko'chasi 2", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 36.0, "delivery_cost": 0.0, "distance": 0.7467075462407572, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.096181, "longitude": 65.368292, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 14, "fields": {"created_at": "2025-01-08T14:30:06.692", "updated_at": "2025-01-08T14:30:06.711", "order": 14, "name": "<PERSON><PERSON><PERSON>", "phone": "+123456789", "delivery_type": "SelfCall", "delivery_address": "123 Main St, Apartment 4B", "subaddress": null, "entrance": "Main entrance", "door_code": "1234", "floor": "4", "comment": "Leave at the door", "promo_code": null, "payment_method": "CASH", "total_cost": 10.0, "delivery_cost": 0.0, "distance": null, "street_id": null, "delivery_cost_id": null, "latitude": 40.12, "longitude": 30.12, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 15, "fields": {"created_at": "2025-01-08T14:30:30.459", "updated_at": "2025-01-08T14:30:30.476", "order": 15, "name": "<PERSON><PERSON><PERSON>", "phone": "+123456789", "delivery_type": "SelfCall", "delivery_address": "123 Main St, Apartment 4B", "subaddress": null, "entrance": "Main entrance", "door_code": "1234", "floor": "4", "comment": "Leave at the door", "promo_code": null, "payment_method": "CASH", "total_cost": 10.0, "delivery_cost": 0.0, "distance": null, "street_id": null, "delivery_cost_id": null, "latitude": 40.12, "longitude": 30.12, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 16, "fields": {"created_at": "2025-01-08T14:31:53.477", "updated_at": "2025-01-08T14:31:53.477", "order": null, "name": "MasterKebab Client", "phone": "998915655359", "delivery_type": "Delivery", "delivery_address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 89", "subaddress": "test", "entrance": null, "door_code": null, "floor": null, "comment": "", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": null, "delivery_cost_id": "", "latitude": 40.20615007225971, "longitude": 65.38453452082553, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 17, "fields": {"created_at": "2025-01-09T14:39:59.458", "updated_at": "2025-01-09T14:39:59.516", "order": 17, "name": "<PERSON><PERSON><PERSON>", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "Uzbekistan str", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "Hello it's me!", "promo_code": null, "payment_method": "CASH", "total_cost": 10.0, "delivery_cost": 0.0, "distance": 0.035097929362757505, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09106047532488, "longitude": 65.38047564467858, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 18, "fields": {"created_at": "2025-01-09T14:42:16.237", "updated_at": "2025-01-09T14:42:16.237", "order": null, "name": "<PERSON><PERSON><PERSON>", "phone": "99**********", "delivery_type": "Delivery", "delivery_address": "Uzbekistan str", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "Hello it's me!", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09106047532488, "longitude": 65.38047564467858, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 19, "fields": {"created_at": "2025-01-09T14:44:51.453", "updated_at": "2025-01-09T14:44:51.453", "order": null, "name": "<PERSON><PERSON><PERSON>", "phone": "99**********", "delivery_type": "Delivery", "delivery_address": "Uzbekistan str", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "Hello it's me!", "promo_code": null, "payment_method": "CASH", "total_cost": 0.0, "delivery_cost": 0.0, "distance": 0.0, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09106047532488, "longitude": 65.38047564467858, "delivery_duration": null, "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.orderdetail", "pk": 20, "fields": {"created_at": "2025-01-09T19:08:54.674", "updated_at": "2025-01-09T19:08:54.697", "order": 20, "name": "<PERSON><PERSON><PERSON>", "phone": "99**********", "delivery_type": "SelfCall", "delivery_address": "Uzbekistan str", "subaddress": null, "entrance": null, "door_code": null, "floor": null, "comment": "Hello it's me!", "promo_code": null, "payment_method": "CASH", "total_cost": 10.0, "delivery_cost": 0.0, "distance": 0.035097929362757505, "street_id": "fd70666b-b71a-435e-adc7-36778542f882", "delivery_cost_id": null, "latitude": 40.09106047532488, "longitude": 65.38047564467858, "delivery_duration": "15", "house": null, "payment_phone": null, "complete_before": null}}, {"model": "order.bookmark", "pk": 2, "fields": {"created_at": "2024-12-14T14:43:17.571", "updated_at": "2025-01-07T01:16:17.288", "user": 3, "address": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 28", "subaddress": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>, 28", "lat": 40.092898786869, "long": 65.37580704670513, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 3, "fields": {"created_at": "2024-12-14T18:36:14.451", "updated_at": "2025-01-07T01:31:59.050", "user": 4, "address": "<PERSON><PERSON><PERSON><PERSON>ist<PERSON> ko'chasi 13", "subaddress": null, "name": "<PERSON><PERSON><PERSON><PERSON>ist<PERSON> ko'chasi 13", "lat": 40.09714, "long": 65.369972, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 4, "fields": {"created_at": "2024-12-16T21:33:49.292", "updated_at": "2025-01-08T17:47:09.785", "user": 4, "address": "O'zbekiston ko'chasi 1", "subaddress": null, "name": "O'zbekiston ko'chasi 1", "lat": 40.09654, "long": 65.367798, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 5, "fields": {"created_at": "2024-12-16T22:13:30.994", "updated_at": "2024-12-29T14:46:11.383", "user": 3, "address": "Lev Tolstoy ko‘chasi 9", "subaddress": null, "name": "Lev Tolstoy ko‘chasi 9", "lat": 40.092242, "long": 65.375488, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 6, "fields": {"created_at": "2024-12-17T13:45:31.374", "updated_at": "2025-01-22T15:00:56.343", "user": 4, "address": "Spitamen ko'chasi 5", "subaddress": null, "name": "O'zbekiston ko'chasi 8", "lat": 40.097202, "long": 65.372137, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 7, "fields": {"created_at": "2024-12-17T14:35:13.001", "updated_at": "2025-01-07T01:23:49.544", "user": 3, "address": "Parvoz koʻchasi 2", "subaddress": null, "name": "Parvoz koʻchasi 4", "lat": 40.098306, "long": 65.371957, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 8, "fields": {"created_at": "2024-12-18T11:53:14.463", "updated_at": "2025-01-06T13:43:46.387", "user": 3, "address": "<PERSON><PERSON> 17", "subaddress": null, "name": "<PERSON><PERSON> 17", "lat": 40.093573, "long": 65.364268, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 9, "fields": {"created_at": "2024-12-18T17:28:49.356", "updated_at": "2024-12-25T12:29:23.889", "user": 3, "address": "Yoshlik koʻchasi 12", "subaddress": null, "name": "Yoshlik koʻchasi 12", "lat": 40.098713, "long": 65.371149, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 10, "fields": {"created_at": "2024-12-18T17:32:26.703", "updated_at": "2025-01-17T15:28:59.523", "user": 3, "address": "Parvoz koʻchasi 9", "subaddress": null, "name": "Parvoz koʻchasi 9", "lat": 40.099492, "long": 65.373772, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 11, "fields": {"created_at": "2024-12-18T17:35:17.156", "updated_at": "2025-01-07T01:27:08.157", "user": 3, "address": "Spitamen ko'chasi 5", "subaddress": null, "name": "Spitamen ko'chasi 5", "lat": 40.096843, "long": 65.372299, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 12, "fields": {"created_at": "2024-12-18T17:37:07.528", "updated_at": "2024-12-18T17:37:07.529", "user": 3, "address": "O'z<PERSON>iston ko'chasi 14", "subaddress": null, "name": "O'z<PERSON>iston ko'chasi 14", "lat": 40.098051, "long": 65.375317, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 13, "fields": {"created_at": "2024-12-18T21:43:43.552", "updated_at": "2024-12-18T21:43:43.552", "user": 6, "address": "<PERSON><PERSON><PERSON><PERSON>ist<PERSON> ko'chasi 13", "subaddress": null, "name": "<PERSON><PERSON><PERSON><PERSON>ist<PERSON> ko'chasi 13", "lat": 40.09714, "long": 65.369972, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 14, "fields": {"created_at": "2024-12-18T21:51:59.146", "updated_at": "2024-12-31T12:56:10.578", "user": 3, "address": "Stroitelnaya ulitsa 4", "subaddress": null, "name": "Stroitelnaya ulitsa 4", "lat": 40.093918, "long": 65.370053, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 15, "fields": {"created_at": "2024-12-18T21:57:23.098", "updated_at": "2024-12-18T21:57:23.098", "user": 3, "address": "Gusev ko'chasi 3", "subaddress": null, "name": "Gusev ko'chasi 3", "lat": 40.101617, "long": 65.370206, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 16, "fields": {"created_at": "2024-12-18T22:00:46.420", "updated_at": "2024-12-18T22:00:46.420", "user": 7, "address": "O'zbekiston ko'chasi 21", "subaddress": null, "name": "O'zbekiston ko'chasi 21", "lat": 40.097637, "long": 65.371679, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 17, "fields": {"created_at": "2024-12-18T22:02:39.156", "updated_at": "2024-12-18T22:20:23.370", "user": 3, "address": "<PERSON> 10", "subaddress": null, "name": "<PERSON> 10", "lat": 40.100265, "long": 65.367403, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 18, "fields": {"created_at": "2024-12-18T22:03:40.542", "updated_at": "2024-12-26T22:51:44.521", "user": 3, "address": "<PERSON><PERSON> ko'chasi 9", "subaddress": null, "name": "<PERSON><PERSON> ko'chasi 9", "lat": 40.100513, "long": 65.370772, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 19, "fields": {"created_at": "2024-12-18T22:19:20.237", "updated_at": "2024-12-31T16:48:49.808", "user": 3, "address": "<PERSON><PERSON><PERSON> 1", "subaddress": null, "name": "<PERSON><PERSON><PERSON> 1", "lat": 40.098837, "long": 65.381399, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 20, "fields": {"created_at": "2024-12-18T22:36:06.234", "updated_at": "2024-12-18T22:36:06.234", "user": 8, "address": "Parvoz koʻchasi 2", "subaddress": null, "name": "Parvoz koʻchasi 2", "lat": 40.098154, "long": 65.371436, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 21, "fields": {"created_at": "2024-12-18T22:36:29.304", "updated_at": "2024-12-18T22:36:29.304", "user": 8, "address": "Zarapetyan ko'chasi 6", "subaddress": null, "name": "Zarapetyan ko'chasi 6", "lat": 40.096257, "long": 65.374122, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 22, "fields": {"created_at": "2024-12-18T22:38:45.883", "updated_at": "2024-12-18T22:48:47.050", "user": 8, "address": "<PERSON><PERSON><PERSON> 590", "subaddress": null, "name": "<PERSON><PERSON><PERSON> 590", "lat": 40.095533, "long": 65.371293, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 23, "fields": {"created_at": "2024-12-18T22:42:56.859", "updated_at": "2024-12-18T22:42:56.859", "user": 8, "address": "<PERSON><PERSON><PERSON> Shodlik c547", "subaddress": null, "name": "<PERSON><PERSON><PERSON> Shodlik c547", "lat": 40.096071, "long": 65.369217, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 24, "fields": {"created_at": "2024-12-18T22:51:45.714", "updated_at": "2024-12-18T22:51:45.714", "user": 8, "address": "<PERSON><PERSON> k<PERSON>'chasi 19/2", "subaddress": null, "name": "<PERSON><PERSON> k<PERSON>'chasi 19/2", "lat": 40.098023, "long": 65.370807, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 25, "fields": {"created_at": "2024-12-18T22:52:45.958", "updated_at": "2024-12-19T10:08:58.146", "user": 8, "address": "O'zbekiston ko'chasi 21", "subaddress": null, "name": "O'zbekiston ko'chasi 21", "lat": 40.097637, "long": 65.371679, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 26, "fields": {"created_at": "2024-12-18T22:53:30.607", "updated_at": "2024-12-18T22:53:30.607", "user": 8, "address": "<PERSON>sher Navoiy <PERSON> 14B", "subaddress": null, "name": "<PERSON>sher Navoiy <PERSON> 14B", "lat": 40.088074, "long": 65.37246, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 27, "fields": {"created_at": "2024-12-18T22:54:10.603", "updated_at": "2024-12-18T22:54:23.360", "user": 8, "address": "Yoshlik koʻchasi 2", "subaddress": null, "name": "Yoshlik koʻchasi 2", "lat": 40.099134, "long": 65.370798, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 29, "fields": {"created_at": "2024-12-24T16:14:27.048", "updated_at": "2025-01-17T17:36:50.674", "user": 3, "address": "<PERSON><PERSON> ko'chasi 21", "subaddress": null, "name": "<PERSON><PERSON> ko'chasi 21", "lat": 40.101065, "long": 65.372586, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 30, "fields": {"created_at": "2024-12-29T14:47:13.345", "updated_at": "2024-12-29T14:47:13.345", "user": 3, "address": "<PERSON><PERSON><PERSON> shoh ko'chasi 7", "subaddress": null, "name": "<PERSON><PERSON><PERSON> shoh ko'chasi 7", "lat": 40.100244, "long": 65.374832, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 31, "fields": {"created_at": "2024-12-30T00:44:00.430", "updated_at": "2024-12-30T00:44:00.430", "user": 3, "address": "O'zbekiston ko'chasi 1", "subaddress": null, "name": "O'zbekiston ko'chasi 1", "lat": 40.09654, "long": 65.367798, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 32, "fields": {"created_at": "2024-12-30T17:21:52.580", "updated_at": "2024-12-30T17:21:52.581", "user": 9, "address": "<PERSON><PERSON><PERSON> shoh ko'chasi 7", "subaddress": null, "name": "<PERSON><PERSON><PERSON> shoh ko'chasi 7", "lat": 40.100244, "long": 65.374832, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 33, "fields": {"created_at": "2024-12-30T18:31:15.707", "updated_at": "2024-12-30T18:31:15.707", "user": 10, "address": "O'zbekiston ko'chasi 1", "subaddress": null, "name": "O'zbekiston ko'chasi 1", "lat": 40.09654, "long": 65.367798, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 34, "fields": {"created_at": "2024-12-30T19:49:07.734", "updated_at": "2024-12-30T19:49:07.734", "user": 3, "address": "<PERSON><PERSON><PERSON> shoh ko'chasi 78", "subaddress": null, "name": "<PERSON><PERSON><PERSON> shoh ko'chasi 78", "lat": 40.100893, "long": 65.376736, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 35, "fields": {"created_at": "2025-01-02T20:16:03.209", "updated_at": "2025-01-06T04:43:04.504", "user": 3, "address": "<PERSON><PERSON><PERSON>ch<PERSON> 100A", "subaddress": null, "name": "<PERSON><PERSON><PERSON>ch<PERSON> 100A", "lat": 40.103487, "long": 65.384318, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 36, "fields": {"created_at": "2025-01-07T01:11:00.556", "updated_at": "2025-01-07T01:11:00.556", "user": 3, "address": "<PERSON><PERSON> 139", "subaddress": null, "name": "<PERSON><PERSON> 139", "lat": 40.096071, "long": 65.365813, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 37, "fields": {"created_at": "2025-01-07T01:24:33.272", "updated_at": "2025-01-07T01:24:33.272", "user": 3, "address": "<PERSON>ak k<PERSON>ʻ<PERSON>si 551", "subaddress": null, "name": "<PERSON>ak k<PERSON>ʻ<PERSON>si 551", "lat": 40.09981, "long": 65.362175, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 38, "fields": {"created_at": "2025-01-07T01:24:58.200", "updated_at": "2025-01-07T01:24:58.200", "user": 3, "address": "<PERSON><PERSON> 398E", "subaddress": null, "name": "<PERSON><PERSON> 398E", "lat": 40.097319, "long": 65.366019, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 39, "fields": {"created_at": "2025-01-07T01:27:16.381", "updated_at": "2025-01-07T01:27:16.381", "user": 3, "address": "Nafosat koʻchasi 362", "subaddress": null, "name": "Nafosat koʻchasi 362", "lat": 40.090075, "long": 65.365975, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 40, "fields": {"created_at": "2025-01-07T01:30:59.238", "updated_at": "2025-01-07T01:56:10.894", "user": 3, "address": "O'zbekiston ko'chasi 2", "subaddress": null, "name": "O'zbekiston ko'chasi 2", "lat": 40.096181, "long": 65.368292, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 41, "fields": {"created_at": "2025-01-07T01:47:29.663", "updated_at": "2025-01-07T01:47:29.663", "user": 3, "address": "<PERSON><PERSON> k<PERSON>'chasi 19/2", "subaddress": null, "name": "<PERSON><PERSON> k<PERSON>'chasi 19/2", "lat": 40.098023, "long": 65.370807, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 42, "fields": {"created_at": "2025-01-08T01:17:34.312", "updated_at": "2025-01-08T01:25:26.641", "user": 5, "address": "<PERSON><PERSON>un<PERSON><PERSON><PERSON> ko‘chasi 12", "subaddress": null, "name": "<PERSON><PERSON>un<PERSON><PERSON><PERSON> ko‘chasi 12", "lat": 40.097906, "long": 65.369146, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 46, "fields": {"created_at": "2025-01-08T14:33:25.126", "updated_at": "2025-01-08T14:33:25.126", "user": 16, "address": "<PERSON><PERSON><PERSON>", "subaddress": null, "name": "<PERSON><PERSON><PERSON>", "lat": 40.12750565099774, "long": 65.34785802704218, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 47, "fields": {"created_at": "2025-01-08T17:37:57.967", "updated_at": "2025-01-08T17:37:57.967", "user": 5, "address": "Zarapetyan ko'chasi c144", "subaddress": null, "name": null, "lat": 40.096947, "long": 65.373817, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 48, "fields": {"created_at": "2025-01-12T11:32:44.050", "updated_at": "2025-01-12T11:32:44.050", "user": 19, "address": "Zarapetyan ko'chasi", "subaddress": null, "name": "Zarapetyan ko'chasi", "lat": 40.09733975639591, "long": 65.3740696755163, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 49, "fields": {"created_at": "2025-01-12T16:03:40.541", "updated_at": "2025-01-12T16:03:40.541", "user": 19, "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ko'chasi, 23", "subaddress": null, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ko'chasi, 23", "lat": 40.097842238298234, "long": 65.37204242995423, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 50, "fields": {"created_at": "2025-01-17T21:56:22.659", "updated_at": "2025-01-17T21:56:22.659", "user": 18, "address": "Yoshlik koʻchasi 4", "subaddress": null, "name": "Yoshlik koʻchasi 4", "lat": 40.099272, "long": 65.371257, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.bookmark", "pk": 51, "fields": {"created_at": "2025-01-23T17:41:53.802", "updated_at": "2025-01-23T17:41:53.802", "user": 22, "address": "<PERSON><PERSON>, 14B", "subaddress": null, "name": "<PERSON><PERSON>, 14B", "lat": 40.088082227557415, "long": 65.37208722241606, "street_id": null, "is_client_bookmarked": true, "orders": []}}, {"model": "order.deliveryprice", "pk": 1, "fields": {"created_at": "2024-12-17T01:06:04.664", "updated_at": "2024-12-17T01:06:04.664", "external_id": "3873a6d2-40ab-434e-bf35-8829f886f9c3", "distance_type": "VN", "display_name": "Доставка", "price": 10.0, "is_active": true}}, {"model": "order.historicalordermodel", "pk": 4, "fields": {"created_at": "2024-12-26T14:12:03.155", "updated_at": "2024-12-26T14:12:03.155", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 4, "fields": {"created_at": "2024-12-26T14:12:03.155", "updated_at": "2024-12-26T14:22:09.885", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 5, "fields": {"created_at": "2024-12-26T14:16:57.258", "updated_at": "2024-12-26T14:16:57.258", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 5, "fields": {"created_at": "2024-12-26T14:16:57.258", "updated_at": "2024-12-26T14:32:09.886", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 8, "fields": {"created_at": "2025-01-02T13:00:01.019", "updated_at": "2025-01-02T13:00:01.019", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 8, "fields": {"created_at": "2025-01-02T13:00:01.019", "updated_at": "2025-01-02T13:16:54.325", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 9, "fields": {"created_at": "2025-01-07T01:17:00.921", "updated_at": "2025-01-07T01:17:00.921", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 9, "fields": {"created_at": "2025-01-07T01:17:00.921", "updated_at": "2025-01-07T01:31:19.376", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 11, "fields": {"created_at": "2025-01-07T01:39:14.921", "updated_at": "2025-01-07T01:39:14.921", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 11, "fields": {"created_at": "2025-01-07T01:39:14.921", "updated_at": "2025-01-07T01:51:19.374", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 12, "fields": {"created_at": "2025-01-07T01:48:14.545", "updated_at": "2025-01-07T01:48:14.545", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 12, "fields": {"created_at": "2025-01-07T01:48:14.545", "updated_at": "2025-01-07T02:01:19.373", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 13, "fields": {"created_at": "2025-01-07T01:56:40.111", "updated_at": "2025-01-07T01:56:40.111", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 13, "fields": {"created_at": "2025-01-07T01:56:40.111", "updated_at": "2025-01-07T02:11:19.374", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 14, "fields": {"created_at": "2025-01-08T14:30:06.695", "updated_at": "2025-01-08T14:30:06.695", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 14, "fields": {"created_at": "2025-01-08T14:30:06.695", "updated_at": "2025-01-08T14:44:51.962", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 15, "fields": {"created_at": "2025-01-08T14:30:30.463", "updated_at": "2025-01-08T14:30:30.463", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 15, "fields": {"created_at": "2025-01-08T14:30:30.463", "updated_at": "2025-01-08T14:44:51.956", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 17, "fields": {"created_at": "2025-01-09T14:39:59.461", "updated_at": "2025-01-09T14:39:59.461", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 17, "fields": {"created_at": "2025-01-09T14:39:59.461", "updated_at": "2025-01-09T14:58:42.825", "status": "Timeout"}}, {"model": "order.historicalordermodel", "pk": 20, "fields": {"created_at": "2025-01-09T19:08:54.677", "updated_at": "2025-01-09T19:08:54.677", "status": "Created"}}, {"model": "order.historicalordermodel", "pk": 20, "fields": {"created_at": "2025-01-09T19:08:54.677", "updated_at": "2025-01-09T19:28:42.825", "status": "Timeout"}}, {"model": "sms.verificationcodes", "pk": 1, "fields": {"created_at": "2024-12-14T14:42:48.587", "updated_at": "2024-12-14T14:42:58.307", "user": 3, "code": 1059, "expires_at": "2024-12-14T14:44:48.586", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 4, "fields": {"created_at": "2025-01-06T02:47:14.347", "updated_at": "2025-01-06T02:47:19.064", "user": 4, "code": 1059, "expires_at": "2025-01-06T02:49:14.347", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 12, "fields": {"created_at": "2025-01-08T14:32:52.697", "updated_at": "2025-01-08T14:32:56.248", "user": 16, "code": 1059, "expires_at": "2025-01-08T14:34:52.697", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 13, "fields": {"created_at": "2025-01-08T14:39:58.601", "updated_at": "2025-01-08T14:40:05.174", "user": 17, "code": 1059, "expires_at": "2025-01-08T14:41:58.601", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 14, "fields": {"created_at": "2025-01-09T01:00:04.992", "updated_at": "2025-01-09T01:00:16.618", "user": 4, "code": 1059, "expires_at": "2025-01-09T01:02:04.992", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 15, "fields": {"created_at": "2025-01-12T11:32:28.506", "updated_at": "2025-01-12T11:32:35.408", "user": 19, "code": 1059, "expires_at": "2025-01-12T11:34:28.506", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 16, "fields": {"created_at": "2025-01-17T23:45:39.461", "updated_at": "2025-01-17T23:45:39.461", "user": 19, "code": 1059, "expires_at": "2025-01-17T23:47:39.460", "is_used": false}}, {"model": "sms.verificationcodes", "pk": 17, "fields": {"created_at": "2025-01-20T14:21:22.681", "updated_at": "2025-01-20T14:21:22.681", "user": 21, "code": 1059, "expires_at": "2025-01-20T14:23:22.680", "is_used": false}}, {"model": "sms.verificationcodes", "pk": 18, "fields": {"created_at": "2025-01-20T14:21:42.796", "updated_at": "2025-01-20T14:21:52.676", "user": 19, "code": 1059, "expires_at": "2025-01-20T14:23:42.796", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 19, "fields": {"created_at": "2025-01-21T11:02:46.547", "updated_at": "2025-01-21T11:03:01.216", "user": 3, "code": 1059, "expires_at": "2025-01-21T11:04:46.547", "is_used": true}}, {"model": "sms.verificationcodes", "pk": 20, "fields": {"created_at": "2025-01-23T17:41:23.786", "updated_at": "2025-01-23T17:41:28.738", "user": 22, "code": 1059, "expires_at": "2025-01-23T17:43:23.786", "is_used": true}}, {"model": "payment.paymentmethod", "pk": 1, "fields": {"created_at": "2024-12-13T02:10:14.554", "updated_at": "2025-01-18T17:01:36.828", "name": "Наличные", "display_name": null, "display_name_uz": null, "display_name_ru": null, "display_name_en": null, "is_enabled": false, "external_id": "09322f46-578a-d210-add7-eec222a08871", "payment_type_kind": "Cash", "is_processed_externally": false, "code": "CASH", "is_enabled_for_dashboard": false, "organization": 2}}, {"model": "payment.paymentmethod", "pk": 2, "fields": {"created_at": "2024-12-13T02:10:14.564", "updated_at": "2025-01-18T17:01:36.832", "name": "PAYME", "display_name": null, "display_name_uz": null, "display_name_ru": null, "display_name_en": null, "is_enabled": false, "external_id": "16253e15-3476-442c-8c68-f59c3b1b1030", "payment_type_kind": "Card", "is_processed_externally": false, "code": "PAYME", "is_enabled_for_dashboard": false, "organization": 2}}, {"model": "payment.paymentmethod", "pk": 3, "fields": {"created_at": "2024-12-13T02:10:14.569", "updated_at": "2025-01-18T17:01:36.835", "name": "CLICK", "display_name": null, "display_name_uz": null, "display_name_ru": null, "display_name_en": null, "is_enabled": false, "external_id": "49d35415-3e6f-46c4-afaa-de86a27b80b9", "payment_type_kind": "Card", "is_processed_externally": false, "code": "CLICK", "is_enabled_for_dashboard": false, "organization": 2}}, {"model": "payment.paymentmethod", "pk": 4, "fields": {"created_at": "2024-12-13T02:10:14.773", "updated_at": "2025-01-18T17:01:37.124", "name": "Наличные", "display_name": null, "display_name_uz": null, "display_name_ru": null, "display_name_en": null, "is_enabled": false, "external_id": "09322f46-578a-d210-add7-eec222a08871", "payment_type_kind": "Cash", "is_processed_externally": false, "code": "CASH", "is_enabled_for_dashboard": false, "organization": 3}}, {"model": "payment.paymentmethod", "pk": 5, "fields": {"created_at": "2024-12-13T02:10:14.779", "updated_at": "2025-01-18T17:01:37.128", "name": "PAYME", "display_name": null, "display_name_uz": null, "display_name_ru": null, "display_name_en": null, "is_enabled": false, "external_id": "16253e15-3476-442c-8c68-f59c3b1b1030", "payment_type_kind": "Card", "is_processed_externally": false, "code": "PAYME", "is_enabled_for_dashboard": false, "organization": 3}}, {"model": "payment.paymentmethod", "pk": 6, "fields": {"created_at": "2024-12-13T02:10:14.784", "updated_at": "2025-01-18T17:01:37.131", "name": "CLICK", "display_name": null, "display_name_uz": null, "display_name_ru": null, "display_name_en": null, "is_enabled": false, "external_id": "49d35415-3e6f-46c4-afaa-de86a27b80b9", "payment_type_kind": "Card", "is_processed_externally": false, "code": "CLICK", "is_enabled_for_dashboard": false, "organization": 3}}, {"model": "core.systemparameter", "pk": 1, "fields": {"name": "iiko", "value": "0", "is_enabled": true, "description": "Initial parameter for iiko"}}, {"model": "core.systemparameter", "pk": 2, "fields": {"name": "search_courier_scheduler", "value": "900", "is_enabled": true, "description": "Initial parameter for search courier scheduler"}}, {"model": "core.systemparameter", "pk": 3, "fields": {"name": "max_orders_per_day", "value": "10", "is_enabled": true, "description": "Maximal orders per day for customers"}}, {"model": "core.systemparameter", "pk": 4, "fields": {"name": "is_mini_app_enabled", "value": "False", "is_enabled": true, "description": "Enable or disable the mini app"}}, {"model": "organization.organization", "pk": 1, "fields": {"created_at": "2024-12-13T02:06:48.710", "updated_at": "2025-01-18T17:01:36.768", "name": "Колл-центр", "external_id": "51a25bf6-c919-41a0-99b8-902b50f900e5", "code": "5", "address": null, "latitude": null, "longitude": null, "in_use": false, "is_alive": false, "is_default": false, "delivery_duration": 30, "self_service_duration": 15, "default_min_sum": 10, "is_delivery_available": true, "is_pickup_available": true, "region": "N", "opens_at": null, "closes_at": null}}, {"model": "organization.organization", "pk": 2, "fields": {"created_at": "2024-12-13T02:06:48.716", "updated_at": "2025-01-18T17:01:36.771", "name": "ВОСХОД new", "external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "code": "4", "address": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "latitude": 40.09075, "longitude": 65.38055, "in_use": true, "is_alive": false, "is_default": true, "delivery_duration": 30, "self_service_duration": 15, "default_min_sum": 10, "is_delivery_available": true, "is_pickup_available": true, "region": "N", "opens_at": "00:00:00", "closes_at": "00:00:00"}}, {"model": "organization.organization", "pk": 3, "fields": {"created_at": "2024-12-13T02:06:48.721", "updated_at": "2025-01-18T17:01:36.775", "name": "ЦУМ new", "external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "code": "3", "address": "Xalqlar Do's<PERSON><PERSON> shoh k<PERSON>'chasi 63, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> v<PERSON>", "latitude": 40.10054, "longitude": 65.37497, "in_use": true, "is_alive": true, "is_default": false, "delivery_duration": 30, "self_service_duration": 15, "default_min_sum": 10, "is_delivery_available": true, "is_pickup_available": true, "region": "N", "opens_at": "00:00:00", "closes_at": "00:00:00"}}, {"model": "organization.terminalgroup", "pk": 1, "fields": {"created_at": "2024-12-13T02:09:59.455", "updated_at": "2025-01-18T17:01:38.976", "name": "Восход ", "external_id": "aea41d3c-20f9-498b-a64e-dd54d90255c5", "address": "", "organization": 2, "in_use": true}}, {"model": "organization.terminalgroup", "pk": 2, "fields": {"created_at": "2024-12-13T02:09:59.516", "updated_at": "2025-01-18T17:01:39.118", "name": "Delivery Цум", "external_id": "67c43d9f-2db1-4656-9a5a-4c93dd745780", "address": "", "organization": 3, "in_use": true}}, {"model": "product.category", "pk": 1, "fields": {"created_at": "2024-12-13T02:11:20.128", "updated_at": "2025-01-17T22:12:26.885", "title": "Пита", "title_uz": "Pita", "title_ru": "Пита", "title_en": "Пита", "image_url": null, "external_id": "13436a4c-b6e1-4ec6-a2de-25d2a1aece41", "position_id": 6}}, {"model": "product.category", "pk": 2, "fields": {"created_at": "2024-12-13T02:11:20.134", "updated_at": "2025-01-17T22:12:26.889", "title": "Напитки", "title_uz": "Na<PERSON><PERSON>", "title_ru": "Напитки", "title_en": "Напитки", "image_url": null, "external_id": "4e44a76f-25aa-44ac-9f9a-f27508149a56", "position_id": 7}}, {"model": "product.category", "pk": 4, "fields": {"created_at": "2024-12-13T02:11:20.144", "updated_at": "2025-01-17T22:12:26.894", "title": "Hot-Dog", "title_uz": "HotdogUZ", "title_ru": "HotdogRU", "title_en": "Hot-Dog", "image_url": null, "external_id": "89744f8b-a23b-469f-a2f3-8f92a367bdd0", "position_id": 5}}, {"model": "product.category", "pk": 5, "fields": {"created_at": "2024-12-13T02:11:20.148", "updated_at": "2025-01-17T22:12:26.896", "title": "Pizza", "title_uz": "PizzaUz", "title_ru": "PizzaRu", "title_en": "Pizza", "image_url": null, "external_id": "c186d556-9eef-42c7-a880-05a07b36f52e", "position_id": 4}}, {"model": "product.category", "pk": 6, "fields": {"created_at": "2024-12-13T02:11:20.153", "updated_at": "2025-01-17T22:12:26.897", "title": "Combo", "title_uz": "ComboUZ", "title_ru": "ComboRU", "title_en": "Combo", "image_url": null, "external_id": "d8221a97-db6e-4405-aadf-a45c69022f95", "position_id": 3}}, {"model": "product.category", "pk": 7, "fields": {"created_at": "2024-12-13T02:11:20.160", "updated_at": "2025-01-17T22:12:26.900", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title_uz": "Burger", "title_ru": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title_en": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "image_url": null, "external_id": "2ba2a462-3fc9-4f2f-96ec-dff0deb8ed06", "position_id": 1}}, {"model": "product.category", "pk": 8, "fields": {"created_at": "2024-12-13T02:11:20.165", "updated_at": "2025-01-17T22:12:26.901", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title_uz": "Lavash", "title_ru": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title_en": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image_url": null, "external_id": "6da93a45-3db6-42c5-9929-160f967ea7eb", "position_id": 2}}, {"model": "product.category", "pk": 9, "fields": {"created_at": "2024-12-30T01:50:01.545", "updated_at": "2025-01-17T22:12:26.891", "title": "Доставка", "title_uz": null, "title_ru": null, "title_en": "Доставка", "image_url": null, "external_id": "53da72b0-d455-420e-b4d3-197f18b17f74", "position_id": 0}}, {"model": "product.product", "pk": 1, "fields": {"created_at": "2024-12-13T02:11:20.181", "updated_at": "2025-01-17T22:12:26.906", "title": "Pita (mol go'shti) NEW", "title_uz": "o'<PERSON><PERSON>i", "title_ru": "Pita", "title_en": "Pita (mol go'shti) NEW", "image_url": "test/products/pita.png", "description": "", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": "", "category": 1, "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 2, "fields": {"created_at": "2024-12-13T02:11:20.212", "updated_at": "2025-01-18T18:50:03.018", "title": "Pita NEW", "title_uz": "Pita", "title_ru": "Pita", "title_en": "Pita NEW", "image_url": "test/products/pita.png", "description": "<PERSON><PERSON>i mol go'shti, tovuq, pomidor, bodring, oq va qizil sous", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": null, "category": 1, "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "product_type": "DISH", "is_top": false, "recommended_products": [1]}}, {"model": "product.product", "pk": 3, "fields": {"created_at": "2024-12-13T02:11:20.240", "updated_at": "2025-01-17T22:12:26.930", "title": "Pepsi razliv NEW", "title_uz": "Pepsi (quyma)", "title_ru": "Pepsi (разливной)", "title_en": "Pepsi razliv NEW", "image_url": "test/products/pepsi_razliv.jpg", "description": "", "description_uz": "<PERSON><PERSON><PERSON> quyma", "description_ru": null, "description_en": "", "category": 2, "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 4, "fields": {"created_at": "2024-12-13T02:11:20.270", "updated_at": "2025-01-17T22:12:26.941", "title": "Pepsi NEW", "title_uz": "Pepsi", "title_ru": "Pepsi", "title_en": "Pepsi NEW", "image_url": "test/products/pepsi.jpg", "description": "", "description_uz": "Pepsi", "description_ru": null, "description_en": "", "category": 2, "external_id": "7b4b1903-48a0-48e5-8587-bdc7e3b2a8a8", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 5, "fields": {"created_at": "2024-12-13T02:11:20.308", "updated_at": "2025-01-17T22:12:26.957", "title": "Choy NEW", "title_uz": "Choy", "title_ru": "<PERSON><PERSON>", "title_en": "Choy NEW", "image_url": "test/products/chay.png", "description": "", "description_uz": "Choy", "description_ru": null, "description_en": "", "category": 2, "external_id": "7ff853f5-f532-4970-a107-fdd2ae5c35d9", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 19, "fields": {"created_at": "2024-12-13T02:11:20.506", "updated_at": "2025-01-17T22:12:27.052", "title": "hot-dog oddiy", "title_uz": "Hot-Dog fransuzcha", "title_ru": "Hot-Dog fransuzkiy", "title_en": "hot-dog oddiy", "image_url": "test/products/4.png", "description": "", "description_uz": "<PERSON><PERSON><PERSON> x2, <PERSON><PERSON><PERSON> x2, <PERSON><PERSON><PERSON><PERSON> fri x2, <PERSON>epsi razliv x2, <PERSON><PERSON> Bliss 0,2L x2", "description_ru": null, "description_en": "", "category": 4, "external_id": "983bba5c-b451-4c24-b8f2-fc481a7cf3e9", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 20, "fields": {"created_at": "2024-12-13T02:11:20.519", "updated_at": "2025-01-17T22:12:27.058", "title": "Hot-Dog", "title_uz": "Hot-Dog", "title_ru": "Hot-Dog", "title_en": "Hot-Dog", "image_url": "test/products/3.png", "description": "", "description_uz": "<PERSON><PERSON><PERSON> x2, <PERSON><PERSON><PERSON> x2, <PERSON><PERSON><PERSON><PERSON> fri x2, <PERSON>epsi razliv x2, <PERSON><PERSON> Bliss 0,2L x2", "description_ru": null, "description_en": "", "category": 4, "external_id": "6bdce19a-0c83-464d-aa93-00b5cacd61a3", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 21, "fields": {"created_at": "2024-12-13T02:11:20.535", "updated_at": "2025-01-17T22:12:27.065", "title": "Margarita NEW", "title_uz": "<PERSON><PERSON><PERSON>", "title_ru": "<PERSON><PERSON><PERSON><PERSON>", "title_en": "Margarita NEW", "image_url": "test/products/13-min.png", "description": "", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pishloq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": "", "category": 5, "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "product_type": "DISH", "is_top": false, "recommended_products": [1, 2, 3, 4, 5]}}, {"model": "product.product", "pk": 22, "fields": {"created_at": "2024-12-13T02:11:20.571", "updated_at": "2025-01-17T22:12:27.084", "title": "alfredo2", "title_uz": "Alfredo2", "title_ru": "Alfredo2", "title_en": "alfredo2", "image_url": "test/products/10-min.png", "description": "", "description_uz": "Sutdan ta<PERSON>yorlangan mazali va salqin ichimlik", "description_ru": null, "description_en": "", "category": 5, "external_id": "c089a47a-513a-45cf-90c3-3812ada86c61", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 23, "fields": {"created_at": "2024-12-13T02:11:20.604", "updated_at": "2025-01-17T22:12:27.101", "title": "Pepperoni NEW", "title_uz": "<PERSON><PERSON>", "title_ru": "Пепперони", "title_en": "Pepperoni NEW", "image_url": "test/products/14-min.png", "description": "", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pishloq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": "", "category": 5, "external_id": "401abda0-329b-4d58-a96b-290f1d1dc001", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 24, "fields": {"created_at": "2024-12-13T02:11:20.633", "updated_at": "2025-01-17T22:12:27.118", "title": "<PERSON><PERSON><PERSON>", "title_uz": "<PERSON>", "title_ru": "Альфредо", "title_en": "<PERSON><PERSON><PERSON>", "image_url": "test/products/12-min.png", "description": "", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": "", "category": 5, "external_id": "fb1d3228-bb50-4ede-a70b-eef90d915bec", "product_type": "DISH", "is_top": false, "recommended_products": [1, 2, 3, 4, 5]}}, {"model": "product.product", "pk": 26, "fields": {"created_at": "2024-12-13T02:11:20.673", "updated_at": "2025-01-17T22:12:27.140", "title": "Чизбургер", "title_uz": "Chizburger", "title_ru": "Чизбургер", "title_en": "Чизбургер", "image_url": "test/products/15-min.png", "description": "", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pishloq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": "", "category": 7, "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "product_type": "DISH", "is_top": false, "recommended_products": [1]}}, {"model": "product.product", "pk": 27, "fields": {"created_at": "2024-12-13T02:11:20.685", "updated_at": "2025-01-17T22:12:27.156", "title": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "title_uz": "Dabl burger", "title_ru": "Даб<PERSON><PERSON><PERSON><PERSON>г<PERSON>р", "title_en": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "image_url": "test/products/16-min.png", "description": "Две сочные котлеты из говядины, круглые кусочки спелого свежего помидора и маринованного огурца, салат Айсберг\", кольца красного сладкого лука под ароматным соусом Гриль в мягкой круглой булочке", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pishloq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": "Две сочные котлеты из говядины, круглые кусочки спелого свежего помидора и маринованного огурца, салат Айсберг\", кольца красного сладкого лука под ароматным соусом Гриль в мягкой круглой булочке", "category": 7, "external_id": "6c499b31-3789-4f29-9c20-7127fcbda9a6", "product_type": "DISH", "is_top": true, "recommended_products": []}}, {"model": "product.product", "pk": 28, "fields": {"created_at": "2024-12-13T02:11:20.698", "updated_at": "2025-01-17T22:12:27.159", "title": "Gamburger NEW", "title_uz": "Gamburger", "title_ru": "Га<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "title_en": "Gamburger NEW", "image_url": "test/products/16-min.png", "description": "", "description_uz": "<PERSON><PERSON><PERSON> mol go'shti, <PERSON><PERSON><PERSON> bargi, p<PERSON>dor, oq sous", "description_ru": null, "description_en": "", "category": 7, "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 29, "fields": {"created_at": "2024-12-13T02:11:20.711", "updated_at": "2025-01-17T22:12:27.168", "title": "Tandir lavash pishloqli NEW", "title_uz": "<PERSON><PERSON> lavash pishloqli", "title_ru": "Тандыр лаваш с сыром", "title_en": "Tandir lavash pishloqli NEW", "image_url": "test/products/2.png", "description": "", "description_uz": "<PERSON><PERSON>i mol go'shti, tovuq, pishloq, pomidor, bodring, oq va qizil sous", "description_ru": null, "description_en": "", "category": 8, "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 30, "fields": {"created_at": "2024-12-13T02:11:20.735", "updated_at": "2025-01-17T22:12:27.180", "title": "Lavash pishloqli NEW", "title_uz": "<PERSON><PERSON><PERSON>", "title_ru": "Лаваш с сыром", "title_en": "Lavash pishloqli NEW", "image_url": "test/products/1.png", "description": "", "description_uz": "<PERSON><PERSON><PERSON> va to'yim<PERSON> lavash", "description_ru": null, "description_en": "", "category": 8, "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 31, "fields": {"created_at": "2024-12-13T02:11:20.758", "updated_at": "2025-01-17T22:12:27.191", "title": "Lavash NEW", "title_uz": "Lavash", "title_ru": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title_en": "Lavash NEW", "image_url": "test/products/1.png", "description": "", "description_uz": "<PERSON><PERSON><PERSON> va to'yim<PERSON> lavash", "description_ru": "Вкусный и питательный лаваш", "description_en": "", "category": 8, "external_id": "deb7c2d5-5e6c-46b2-8cbb-dfb4cd42afda", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 32, "fields": {"created_at": "2024-12-13T02:11:20.779", "updated_at": "2025-01-17T22:12:27.201", "title": "Tandir lavash NEW", "title_uz": "<PERSON><PERSON> lavash", "title_ru": "Тандыр лаваш", "title_en": "Tandir lavash NEW", "image_url": "test/products/2.png", "description": "", "description_uz": "<PERSON><PERSON><PERSON> pishi<PERSON>gan mazali lavash", "description_ru": "Вкусный лаваш, запеченный в тандыре", "description_en": "", "category": 8, "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "product_type": "DISH", "is_top": false, "recommended_products": [1]}}, {"model": "product.product", "pk": 33, "fields": {"created_at": "2024-12-26T21:00:44.885", "updated_at": "2025-01-17T22:12:27.133", "title": "комбо янги", "title_uz": "<PERSON><PERSON>", "title_ru": "Kombo", "title_en": "комбо янги", "image_url": "test/products/child_combo-min.png", "description": "", "description_uz": "Sutdan ta<PERSON>yorlangan mazali va salqin ichimlik", "description_ru": null, "description_en": "", "category": 6, "external_id": "7356c172-ea40-47f3-9c77-30e6ce22808e", "product_type": "DISH", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 34, "fields": {"created_at": "2024-12-30T01:50:01.609", "updated_at": "2025-01-17T22:12:26.968", "title": "Доставка 20", "title_uz": null, "title_ru": null, "title_en": "Доставка 20", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "33d682b6-da16-498c-a1c6-bbbe51727a64", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 35, "fields": {"created_at": "2024-12-30T01:50:01.618", "updated_at": "2025-01-17T22:12:26.976", "title": "Доставка 40", "title_uz": null, "title_ru": null, "title_en": "Доставка 40", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "fcf5eab4-c682-4606-8d9b-881a874f2d37", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 36, "fields": {"created_at": "2024-12-30T01:50:01.624", "updated_at": "2025-01-17T22:12:26.983", "title": "Доставка 30", "title_uz": null, "title_ru": null, "title_en": "Доставка 30", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "9b895bf9-4aef-49cf-b7c6-9331dc9d357b", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 37, "fields": {"created_at": "2024-12-30T01:50:01.631", "updated_at": "2025-01-17T22:12:26.990", "title": "Доставка 25", "title_uz": null, "title_ru": null, "title_en": "Доставка 25", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "f9679ed3-20f8-439b-b2c1-a8650d8c800d", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 38, "fields": {"created_at": "2024-12-30T01:50:01.637", "updated_at": "2025-01-17T22:12:26.997", "title": "Доставка 50", "title_uz": null, "title_ru": null, "title_en": "Доставка 50", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "592e6f80-7a1d-40cf-badc-20adbaec2a86", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 39, "fields": {"created_at": "2024-12-30T01:50:01.644", "updated_at": "2025-01-17T22:12:27.003", "title": "Доставка 70", "title_uz": null, "title_ru": null, "title_en": "Доставка 70", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "5bf0536f-8b83-49c0-b4fe-9ac5aa7a492c", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 40, "fields": {"created_at": "2024-12-30T01:50:01.650", "updated_at": "2025-01-17T22:12:27.009", "title": "Доставка 60", "title_uz": null, "title_ru": null, "title_en": "Доставка 60", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "be5205d9-2d3b-4aca-a3a7-a91d2c528117", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 41, "fields": {"created_at": "2024-12-30T01:50:01.657", "updated_at": "2025-01-17T22:12:27.015", "title": "Доставка 1", "title_uz": null, "title_ru": null, "title_en": "Доставка 1", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "b92cadda-2629-432f-88b1-509189c12a20", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 42, "fields": {"created_at": "2024-12-30T01:50:01.663", "updated_at": "2025-01-17T22:12:27.021", "title": "Доставка 10", "title_uz": null, "title_ru": null, "title_en": "Доставка 10", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "3873a6d2-40ab-434e-bf35-8829f886f9c3", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 43, "fields": {"created_at": "2024-12-30T01:50:01.669", "updated_at": "2025-01-17T22:12:27.027", "title": "Доставка 100", "title_uz": null, "title_ru": null, "title_en": "Доставка 100", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "350e87c5-8852-45d9-9aec-3d4e5a53da00", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 44, "fields": {"created_at": "2024-12-30T01:50:01.675", "updated_at": "2025-01-17T22:12:27.034", "title": "Доставка 15", "title_uz": null, "title_ru": null, "title_en": "Доставка 15", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "08ea39b7-cf93-4646-957c-b170c2364572", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 45, "fields": {"created_at": "2024-12-30T01:50:01.682", "updated_at": "2025-01-17T22:12:27.040", "title": "Доставка 80", "title_uz": null, "title_ru": null, "title_en": "Доставка 80", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "5623c1ab-eb80-4448-a3e2-9b1ffc8fe3c6", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.product", "pk": 46, "fields": {"created_at": "2024-12-30T01:50:01.689", "updated_at": "2025-01-17T22:12:27.046", "title": "Доставка 90", "title_uz": null, "title_ru": null, "title_en": "Доставка 90", "image_url": "", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "category": 9, "external_id": "e3e3b0cf-ea0c-4dfa-997c-0a8f727fdb5d", "product_type": "SERVICE", "is_top": false, "recommended_products": []}}, {"model": "product.label", "pk": 1, "fields": {"created_at": "2024-12-29T23:06:16.665", "updated_at": "2025-01-17T22:12:27.155", "product": 27, "code": "", "name": "top"}}, {"model": "product.productattribute", "pk": 1, "fields": {"created_at": "2024-12-13T02:11:20.190", "updated_at": "2025-01-17T22:12:26.909", "description": "Pita (mol go'shti) NEW", "description_uz": null, "description_ru": null, "description_en": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 1, "sku": "00055-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 2, "fields": {"created_at": "2024-12-13T02:11:20.197", "updated_at": "2025-01-17T22:12:26.912", "description": "Pita (mol go'shti) NEW", "description_uz": null, "description_ru": null, "description_en": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 1, "sku": "00055-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 3, "fields": {"created_at": "2024-12-13T02:11:20.201", "updated_at": "2025-01-17T22:12:26.914", "description": "Pita (mol go'shti) NEW", "description_uz": null, "description_ru": null, "description_en": "Pita (mol go'shti) NEW", "price": 14.0, "size": "Больш<PERSON>й", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 1, "sku": "00055-Б<PERSON><PERSON><PERSON><PERSON><PERSON>й", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 4, "fields": {"created_at": "2024-12-13T02:11:20.206", "updated_at": "2025-01-17T22:12:26.916", "description": "Pita (mol go'shti) NEW", "description_uz": null, "description_ru": null, "description_en": "Pita (mol go'shti) NEW", "price": 14.0, "size": "Больш<PERSON>й", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 1, "sku": "00055-Б<PERSON><PERSON><PERSON><PERSON><PERSON>й", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 5, "fields": {"created_at": "2024-12-13T02:11:20.217", "updated_at": "2025-01-17T22:12:26.921", "description": "Pita NEW", "description_uz": null, "description_ru": null, "description_en": "Pita NEW", "price": 9.0, "size": "Средний", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 2, "sku": "00054-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 6, "fields": {"created_at": "2024-12-13T02:11:20.223", "updated_at": "2025-01-17T22:12:26.923", "description": "Pita NEW", "description_uz": null, "description_ru": null, "description_en": "Pita NEW", "price": 9.0, "size": "Средний", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 2, "sku": "00054-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 7, "fields": {"created_at": "2024-12-13T02:11:20.229", "updated_at": "2025-01-17T22:12:26.925", "description": "Pita NEW", "description_uz": null, "description_ru": null, "description_en": "Pita NEW", "price": 13.0, "size": "Больш<PERSON>й", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 2, "sku": "00054-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 8, "fields": {"created_at": "2024-12-13T02:11:20.234", "updated_at": "2025-01-17T22:12:26.927", "description": "Pita NEW", "description_uz": null, "description_ru": null, "description_en": "Pita NEW", "price": 13.0, "size": "Больш<PERSON>й", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 2, "sku": "00054-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 9, "fields": {"created_at": "2024-12-13T02:11:20.246", "updated_at": "2025-01-17T22:12:26.932", "description": "Pepsi razliv NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi razliv NEW", "price": 9.0, "size": "0,4л", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 3, "sku": "00060-0,4л", "is_default": false, "size_id": "7166c0ca-c0b5-422d-8315-247b9c73a597", "measure_unit_type": "GRAM", "size_code": "0,4л", "is_available": true}}, {"model": "product.productattribute", "pk": 10, "fields": {"created_at": "2024-12-13T02:11:20.252", "updated_at": "2025-01-17T22:12:26.934", "description": "Pepsi razliv NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi razliv NEW", "price": 9.0, "size": "0,4л", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 3, "sku": "00060-0,4л", "is_default": false, "size_id": "7166c0ca-c0b5-422d-8315-247b9c73a597", "measure_unit_type": "GRAM", "size_code": "0,4л", "is_available": true}}, {"model": "product.productattribute", "pk": 11, "fields": {"created_at": "2024-12-13T02:11:20.258", "updated_at": "2025-01-17T22:12:26.936", "description": "Pepsi razliv NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi razliv NEW", "price": 11.0, "size": "0,5л", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 3, "sku": "00060-0,5л", "is_default": false, "size_id": "6938bb29-a212-444c-9c49-b4be626804d0", "measure_unit_type": "GRAM", "size_code": "0,5л", "is_available": true}}, {"model": "product.productattribute", "pk": 12, "fields": {"created_at": "2024-12-13T02:11:20.263", "updated_at": "2025-01-17T22:12:26.938", "description": "Pepsi razliv NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi razliv NEW", "price": 11.0, "size": "0,5л", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 3, "sku": "00060-0,5л", "is_default": false, "size_id": "6938bb29-a212-444c-9c49-b4be626804d0", "measure_unit_type": "GRAM", "size_code": "0,5л", "is_available": true}}, {"model": "product.productattribute", "pk": 13, "fields": {"created_at": "2024-12-13T02:11:20.276", "updated_at": "2025-01-17T22:12:26.944", "description": "Pepsi NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi NEW", "price": 9.0, "size": "0.5 л", "external_id": "7b4b1903-48a0-48e5-8587-bdc7e3b2a8a8", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 4, "sku": "00059-0.5 л", "is_default": false, "size_id": "da7de87a-c705-4a32-a2ff-880fd7fdab41", "measure_unit_type": "GRAM", "size_code": "0.5 л", "is_available": true}}, {"model": "product.productattribute", "pk": 14, "fields": {"created_at": "2024-12-13T02:11:20.281", "updated_at": "2025-01-17T22:12:26.946", "description": "Pepsi NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi NEW", "price": 9.0, "size": "0.5 л", "external_id": "7b4b1903-48a0-48e5-8587-bdc7e3b2a8a8", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 4, "sku": "00059-0.5 л", "is_default": false, "size_id": "da7de87a-c705-4a32-a2ff-880fd7fdab41", "measure_unit_type": "GRAM", "size_code": "0.5 л", "is_available": true}}, {"model": "product.productattribute", "pk": 15, "fields": {"created_at": "2024-12-13T02:11:20.286", "updated_at": "2025-01-17T22:12:26.948", "description": "Pepsi NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi NEW", "price": 12.0, "size": "1 л", "external_id": "7b4b1903-48a0-48e5-8587-bdc7e3b2a8a8", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 4, "sku": "00059-1 л", "is_default": true, "size_id": "8984179b-e6e2-42d2-af37-8ff9967af71e", "measure_unit_type": "GRAM", "size_code": "1 л", "is_available": true}}, {"model": "product.productattribute", "pk": 16, "fields": {"created_at": "2024-12-13T02:11:20.292", "updated_at": "2025-01-17T22:12:26.950", "description": "Pepsi NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi NEW", "price": 12.0, "size": "1 л", "external_id": "7b4b1903-48a0-48e5-8587-bdc7e3b2a8a8", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 4, "sku": "00059-1 л", "is_default": true, "size_id": "8984179b-e6e2-42d2-af37-8ff9967af71e", "measure_unit_type": "GRAM", "size_code": "1 л", "is_available": true}}, {"model": "product.productattribute", "pk": 17, "fields": {"created_at": "2024-12-13T02:11:20.297", "updated_at": "2025-01-17T22:12:26.953", "description": "Pepsi NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi NEW", "price": 18.0, "size": "1.5 л", "external_id": "7b4b1903-48a0-48e5-8587-bdc7e3b2a8a8", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 4, "sku": "00059-1,5 л", "is_default": false, "size_id": "53b66859-8d14-4024-a26f-cf4a2ae46692", "measure_unit_type": "GRAM", "size_code": "1,5 л", "is_available": true}}, {"model": "product.productattribute", "pk": 18, "fields": {"created_at": "2024-12-13T02:11:20.302", "updated_at": "2025-01-17T22:12:26.955", "description": "Pepsi NEW", "description_uz": null, "description_ru": null, "description_en": "Pepsi NEW", "price": 18.0, "size": "1.5 л", "external_id": "7b4b1903-48a0-48e5-8587-bdc7e3b2a8a8", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 4, "sku": "00059-1,5 л", "is_default": false, "size_id": "53b66859-8d14-4024-a26f-cf4a2ae46692", "measure_unit_type": "GRAM", "size_code": "1,5 л", "is_available": true}}, {"model": "product.productattribute", "pk": 19, "fields": {"created_at": "2024-12-13T02:11:20.313", "updated_at": "2025-01-17T22:12:26.961", "description": "Choy NEW", "description_uz": null, "description_ru": null, "description_en": "Choy NEW", "price": 4.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "7ff853f5-f532-4970-a107-fdd2ae5c35d9", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 5, "sku": "00061", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 20, "fields": {"created_at": "2024-12-13T02:11:20.319", "updated_at": "2025-01-17T22:12:26.963", "description": "Choy NEW", "description_uz": null, "description_ru": null, "description_en": "Choy NEW", "price": 4.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "7ff853f5-f532-4970-a107-fdd2ae5c35d9", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 5, "sku": "00061", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 47, "fields": {"created_at": "2024-12-13T02:11:20.511", "updated_at": "2025-01-17T22:12:27.053", "description": "hot-dog oddiy", "description_uz": null, "description_ru": null, "description_en": "hot-dog oddiy", "price": 8.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "983bba5c-b451-4c24-b8f2-fc481a7cf3e9", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 19, "sku": "00086", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 48, "fields": {"created_at": "2024-12-13T02:11:20.515", "updated_at": "2025-01-17T22:12:27.055", "description": "hot-dog oddiy", "description_uz": null, "description_ru": null, "description_en": "hot-dog oddiy", "price": 8.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "983bba5c-b451-4c24-b8f2-fc481a7cf3e9", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 19, "sku": "00086", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 49, "fields": {"created_at": "2024-12-13T02:11:20.524", "updated_at": "2025-01-17T22:12:27.060", "description": "Hot-Dog", "description_uz": null, "description_ru": null, "description_en": "Hot-Dog", "price": 16.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "6bdce19a-0c83-464d-aa93-00b5cacd61a3", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 20, "sku": "00047", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 50, "fields": {"created_at": "2024-12-13T02:11:20.528", "updated_at": "2025-01-17T22:12:27.062", "description": "Hot-Dog", "description_uz": null, "description_ru": null, "description_en": "Hot-Dog", "price": 16.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "6bdce19a-0c83-464d-aa93-00b5cacd61a3", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 20, "sku": "00047", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 51, "fields": {"created_at": "2024-12-13T02:11:20.540", "updated_at": "2025-01-17T22:12:27.067", "description": "Margarita NEW", "description_uz": null, "description_ru": null, "description_en": "Margarita NEW", "price": 18.0, "size": "Маленькая", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 21, "sku": "00064-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 52, "fields": {"created_at": "2024-12-13T02:11:20.546", "updated_at": "2025-01-17T22:12:27.071", "description": "Margarita NEW", "description_uz": null, "description_ru": null, "description_en": "Margarita NEW", "price": 18.0, "size": "Маленькая", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 21, "sku": "00064-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 53, "fields": {"created_at": "2024-12-13T02:11:20.551", "updated_at": "2025-01-17T22:12:27.074", "description": "Margarita NEW", "description_uz": null, "description_ru": null, "description_en": "Margarita NEW", "price": 22.0, "size": "Средняя", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 21, "sku": "00064-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 54, "fields": {"created_at": "2024-12-13T02:11:20.556", "updated_at": "2025-01-17T22:12:27.077", "description": "Margarita NEW", "description_uz": null, "description_ru": null, "description_en": "Margarita NEW", "price": 22.0, "size": "Средняя", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 21, "sku": "00064-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 55, "fields": {"created_at": "2024-12-13T02:11:20.561", "updated_at": "2025-01-17T22:12:27.079", "description": "Margarita NEW", "description_uz": null, "description_ru": null, "description_en": "Margarita NEW", "price": 25.0, "size": "Большая", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 21, "sku": "00064-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 56, "fields": {"created_at": "2024-12-13T02:11:20.565", "updated_at": "2025-01-17T22:12:27.081", "description": "Margarita NEW", "description_uz": null, "description_ru": null, "description_en": "Margarita NEW", "price": 25.0, "size": "Большая", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 21, "sku": "00064-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 57, "fields": {"created_at": "2024-12-13T02:11:20.576", "updated_at": "2025-01-17T22:12:27.086", "description": "alfredo2", "description_uz": null, "description_ru": null, "description_en": "alfredo2", "price": 12.0, "size": "Маленькая", "external_id": "c089a47a-513a-45cf-90c3-3812ada86c61", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 22, "sku": "00085-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 58, "fields": {"created_at": "2024-12-13T02:11:20.581", "updated_at": "2025-01-17T22:12:27.089", "description": "alfredo2", "description_uz": null, "description_ru": null, "description_en": "alfredo2", "price": 12.0, "size": "Маленькая", "external_id": "c089a47a-513a-45cf-90c3-3812ada86c61", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 22, "sku": "00085-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 59, "fields": {"created_at": "2024-12-13T02:11:20.587", "updated_at": "2025-01-17T22:12:27.091", "description": "alfredo2", "description_uz": null, "description_ru": null, "description_en": "alfredo2", "price": 14.0, "size": "Средняя", "external_id": "c089a47a-513a-45cf-90c3-3812ada86c61", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 22, "sku": "00085-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 60, "fields": {"created_at": "2024-12-13T02:11:20.592", "updated_at": "2025-01-17T22:12:27.093", "description": "alfredo2", "description_uz": null, "description_ru": null, "description_en": "alfredo2", "price": 14.0, "size": "Средняя", "external_id": "c089a47a-513a-45cf-90c3-3812ada86c61", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 22, "sku": "00085-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 61, "fields": {"created_at": "2024-12-13T02:11:20.596", "updated_at": "2025-01-17T22:12:27.095", "description": "alfredo2", "description_uz": null, "description_ru": null, "description_en": "alfredo2", "price": 16.0, "size": "Большая", "external_id": "c089a47a-513a-45cf-90c3-3812ada86c61", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 22, "sku": "00085-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 62, "fields": {"created_at": "2024-12-13T02:11:20.600", "updated_at": "2025-01-17T22:12:27.098", "description": "alfredo2", "description_uz": null, "description_ru": null, "description_en": "alfredo2", "price": 16.0, "size": "Большая", "external_id": "c089a47a-513a-45cf-90c3-3812ada86c61", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 22, "sku": "00085-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 63, "fields": {"created_at": "2024-12-13T02:11:20.608", "updated_at": "2025-01-17T22:12:27.104", "description": "Pepperoni NEW", "description_uz": null, "description_ru": null, "description_en": "Pepperoni NEW", "price": 20.0, "size": "Маленькая", "external_id": "401abda0-329b-4d58-a96b-290f1d1dc001", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 23, "sku": "00063-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 64, "fields": {"created_at": "2024-12-13T02:11:20.612", "updated_at": "2025-01-17T22:12:27.106", "description": "Pepperoni NEW", "description_uz": null, "description_ru": null, "description_en": "Pepperoni NEW", "price": 20.0, "size": "Маленькая", "external_id": "401abda0-329b-4d58-a96b-290f1d1dc001", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 23, "sku": "00063-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 65, "fields": {"created_at": "2024-12-13T02:11:20.616", "updated_at": "2025-01-17T22:12:27.109", "description": "Pepperoni NEW", "description_uz": null, "description_ru": null, "description_en": "Pepperoni NEW", "price": 24.0, "size": "Средняя", "external_id": "401abda0-329b-4d58-a96b-290f1d1dc001", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 23, "sku": "00063-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 66, "fields": {"created_at": "2024-12-13T02:11:20.620", "updated_at": "2025-01-17T22:12:27.111", "description": "Pepperoni NEW", "description_uz": null, "description_ru": null, "description_en": "Pepperoni NEW", "price": 24.0, "size": "Средняя", "external_id": "401abda0-329b-4d58-a96b-290f1d1dc001", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 23, "sku": "00063-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 67, "fields": {"created_at": "2024-12-13T02:11:20.624", "updated_at": "2025-01-17T22:12:27.113", "description": "Pepperoni NEW", "description_uz": null, "description_ru": null, "description_en": "Pepperoni NEW", "price": 28.0, "size": "Большая", "external_id": "401abda0-329b-4d58-a96b-290f1d1dc001", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 23, "sku": "00063-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 68, "fields": {"created_at": "2024-12-13T02:11:20.628", "updated_at": "2025-01-17T22:12:27.116", "description": "Pepperoni NEW", "description_uz": null, "description_ru": null, "description_en": "Pepperoni NEW", "price": 28.0, "size": "Большая", "external_id": "401abda0-329b-4d58-a96b-290f1d1dc001", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 23, "sku": "00063-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 69, "fields": {"created_at": "2024-12-13T02:11:20.637", "updated_at": "2025-01-17T22:12:27.120", "description": "<PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON>", "price": 11.0, "size": "Маленькая", "external_id": "fb1d3228-bb50-4ede-a70b-eef90d915bec", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 24, "sku": "00082-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 70, "fields": {"created_at": "2024-12-13T02:11:20.641", "updated_at": "2025-01-17T22:12:27.122", "description": "<PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON>", "price": 11.0, "size": "Маленькая", "external_id": "fb1d3228-bb50-4ede-a70b-eef90d915bec", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 24, "sku": "00082-25см", "is_default": false, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "measure_unit_type": "GRAM", "size_code": "25см", "is_available": true}}, {"model": "product.productattribute", "pk": 71, "fields": {"created_at": "2024-12-13T02:11:20.645", "updated_at": "2025-01-17T22:12:27.124", "description": "<PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON>", "price": 13.0, "size": "Средняя", "external_id": "fb1d3228-bb50-4ede-a70b-eef90d915bec", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 24, "sku": "00082-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 72, "fields": {"created_at": "2024-12-13T02:11:20.648", "updated_at": "2025-01-17T22:12:27.126", "description": "<PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON>", "price": 13.0, "size": "Средняя", "external_id": "fb1d3228-bb50-4ede-a70b-eef90d915bec", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 24, "sku": "00082-35см", "is_default": true, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "measure_unit_type": "GRAM", "size_code": "35см", "is_available": true}}, {"model": "product.productattribute", "pk": 73, "fields": {"created_at": "2024-12-13T02:11:20.653", "updated_at": "2025-01-17T22:12:27.128", "description": "<PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON>", "price": 15.0, "size": "Большая", "external_id": "fb1d3228-bb50-4ede-a70b-eef90d915bec", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 24, "sku": "00082-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 74, "fields": {"created_at": "2024-12-13T02:11:20.657", "updated_at": "2025-01-17T22:12:27.130", "description": "<PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON>", "price": 15.0, "size": "Большая", "external_id": "fb1d3228-bb50-4ede-a70b-eef90d915bec", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 24, "sku": "00082-45см", "is_default": false, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "measure_unit_type": "GRAM", "size_code": "45см", "is_available": true}}, {"model": "product.productattribute", "pk": 77, "fields": {"created_at": "2024-12-13T02:11:20.677", "updated_at": "2025-01-17T22:12:27.142", "description": "Чизбургер", "description_uz": null, "description_ru": null, "description_en": "Чизбургер", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 26, "sku": "00003", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 78, "fields": {"created_at": "2024-12-13T02:11:20.681", "updated_at": "2025-01-17T22:12:27.144", "description": "Чизбургер", "description_uz": null, "description_ru": null, "description_en": "Чизбургер", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 26, "sku": "00003", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 79, "fields": {"created_at": "2024-12-13T02:11:20.689", "updated_at": "2025-01-17T22:12:27.149", "description": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "price": 15.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "6c499b31-3789-4f29-9c20-7127fcbda9a6", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 27, "sku": "00004", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 80, "fields": {"created_at": "2024-12-13T02:11:20.694", "updated_at": "2025-01-17T22:12:27.151", "description": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description_uz": null, "description_ru": null, "description_en": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "price": 15.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "6c499b31-3789-4f29-9c20-7127fcbda9a6", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 27, "sku": "00004", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 81, "fields": {"created_at": "2024-12-13T02:11:20.702", "updated_at": "2025-01-17T22:12:27.161", "description": "Gamburger NEW", "description_uz": null, "description_ru": null, "description_en": "Gamburger NEW", "price": 14.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 28, "sku": "00062", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 82, "fields": {"created_at": "2024-12-13T02:11:20.707", "updated_at": "2025-01-17T22:12:27.165", "description": "Gamburger NEW", "description_uz": null, "description_ru": null, "description_en": "Gamburger NEW", "price": 14.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 28, "sku": "00062", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 83, "fields": {"created_at": "2024-12-13T02:11:20.716", "updated_at": "2025-01-17T22:12:27.171", "description": "Tandir lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash pishloqli NEW", "price": 14.0, "size": "Средний", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 29, "sku": "00058-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 84, "fields": {"created_at": "2024-12-13T02:11:20.721", "updated_at": "2025-01-17T22:12:27.173", "description": "Tandir lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash pishloqli NEW", "price": 14.0, "size": "Средний", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 29, "sku": "00058-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 85, "fields": {"created_at": "2024-12-13T02:11:20.725", "updated_at": "2025-01-17T22:12:27.176", "description": "Tandir lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash pishloqli NEW", "price": 16.0, "size": "Больш<PERSON>й", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 29, "sku": "00058-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 86, "fields": {"created_at": "2024-12-13T02:11:20.730", "updated_at": "2025-01-17T22:12:27.178", "description": "Tandir lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash pishloqli NEW", "price": 16.0, "size": "Больш<PERSON>й", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 29, "sku": "00058-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 87, "fields": {"created_at": "2024-12-13T02:11:20.739", "updated_at": "2025-01-17T22:12:27.182", "description": "Lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash pishloqli NEW", "price": 12.0, "size": "Средний", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 30, "sku": "00056-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 88, "fields": {"created_at": "2024-12-13T02:11:20.744", "updated_at": "2025-01-17T22:12:27.184", "description": "Lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash pishloqli NEW", "price": 12.0, "size": "Средний", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 30, "sku": "00056-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 89, "fields": {"created_at": "2024-12-13T02:11:20.748", "updated_at": "2025-01-17T22:12:27.186", "description": "Lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash pishloqli NEW", "price": 14.0, "size": "Больш<PERSON>й", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 30, "sku": "00056-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 90, "fields": {"created_at": "2024-12-13T02:11:20.754", "updated_at": "2025-01-17T22:12:27.188", "description": "Lavash pishloqli NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash pishloqli NEW", "price": 14.0, "size": "Больш<PERSON>й", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 30, "sku": "00056-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 91, "fields": {"created_at": "2024-12-13T02:11:20.762", "updated_at": "2025-01-17T22:12:27.193", "description": "Lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash NEW", "price": 11.0, "size": "Средний", "external_id": "deb7c2d5-5e6c-46b2-8cbb-dfb4cd42afda", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 31, "sku": "00053-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 92, "fields": {"created_at": "2024-12-13T02:11:20.766", "updated_at": "2025-01-17T22:12:27.195", "description": "Lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash NEW", "price": 11.0, "size": "Средний", "external_id": "deb7c2d5-5e6c-46b2-8cbb-dfb4cd42afda", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 31, "sku": "00053-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 93, "fields": {"created_at": "2024-12-13T02:11:20.771", "updated_at": "2025-01-17T22:12:27.197", "description": "Lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash NEW", "price": 13.0, "size": "Больш<PERSON>й", "external_id": "deb7c2d5-5e6c-46b2-8cbb-dfb4cd42afda", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 31, "sku": "00053-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 94, "fields": {"created_at": "2024-12-13T02:11:20.775", "updated_at": "2025-01-17T22:12:27.199", "description": "Lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Lavash NEW", "price": 13.0, "size": "Больш<PERSON>й", "external_id": "deb7c2d5-5e6c-46b2-8cbb-dfb4cd42afda", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 31, "sku": "00053-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 95, "fields": {"created_at": "2024-12-13T02:11:20.784", "updated_at": "2025-01-17T22:12:27.203", "description": "Tandir lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash NEW", "price": 12.0, "size": "Средний", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 32, "sku": "00057-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 96, "fields": {"created_at": "2024-12-13T02:11:20.788", "updated_at": "2025-01-17T22:12:27.205", "description": "Tandir lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash NEW", "price": 12.0, "size": "Средний", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 32, "sku": "00057-Средний", "is_default": true, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "measure_unit_type": "GRAM", "size_code": "Средний", "is_available": true}}, {"model": "product.productattribute", "pk": 97, "fields": {"created_at": "2024-12-13T02:11:20.792", "updated_at": "2025-01-17T22:12:27.208", "description": "Tandir lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash NEW", "price": 14.0, "size": "Больш<PERSON>й", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 32, "sku": "00057-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 98, "fields": {"created_at": "2024-12-13T02:11:20.797", "updated_at": "2025-01-17T22:12:27.210", "description": "Tandir lavash NEW", "description_uz": null, "description_ru": null, "description_en": "Tandir lavash NEW", "price": 14.0, "size": "Больш<PERSON>й", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 32, "sku": "00057-<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "is_default": false, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "measure_unit_type": "GRAM", "size_code": "Больш<PERSON>й", "is_available": true}}, {"model": "product.productattribute", "pk": 99, "fields": {"created_at": "2024-12-26T21:00:44.888", "updated_at": "2025-01-17T22:12:27.136", "description": "комбо янги", "description_uz": null, "description_ru": null, "description_en": "комбо янги", "price": 15.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "7356c172-ea40-47f3-9c77-30e6ce22808e", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 33, "sku": "00050", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 100, "fields": {"created_at": "2024-12-26T21:00:44.891", "updated_at": "2025-01-17T22:12:27.138", "description": "комбо янги", "description_uz": null, "description_ru": null, "description_en": "комбо янги", "price": 15.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "7356c172-ea40-47f3-9c77-30e6ce22808e", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 33, "sku": "00050", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": true}}, {"model": "product.productattribute", "pk": 101, "fields": {"created_at": "2024-12-30T01:50:01.613", "updated_at": "2025-01-17T22:12:26.971", "description": "Доставка 20", "description_uz": null, "description_ru": null, "description_en": "Доставка 20", "price": 20.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "33d682b6-da16-498c-a1c6-bbbe51727a64", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 34, "sku": "00016", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 102, "fields": {"created_at": "2024-12-30T01:50:01.616", "updated_at": "2025-01-17T22:12:26.974", "description": "Доставка 20", "description_uz": null, "description_ru": null, "description_en": "Доставка 20", "price": 20.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "33d682b6-da16-498c-a1c6-bbbe51727a64", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 34, "sku": "00016", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 103, "fields": {"created_at": "2024-12-30T01:50:01.620", "updated_at": "2025-01-17T22:12:26.979", "description": "Доставка 40", "description_uz": null, "description_ru": null, "description_en": "Доставка 40", "price": 40.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "fcf5eab4-c682-4606-8d9b-881a874f2d37", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 35, "sku": "00031", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 104, "fields": {"created_at": "2024-12-30T01:50:01.622", "updated_at": "2025-01-17T22:12:26.981", "description": "Доставка 40", "description_uz": null, "description_ru": null, "description_en": "Доставка 40", "price": 40.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "fcf5eab4-c682-4606-8d9b-881a874f2d37", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 35, "sku": "00031", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 105, "fields": {"created_at": "2024-12-30T01:50:01.627", "updated_at": "2025-01-17T22:12:26.985", "description": "Доставка 30", "description_uz": null, "description_ru": null, "description_en": "Доставка 30", "price": 30.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "9b895bf9-4aef-49cf-b7c6-9331dc9d357b", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 36, "sku": "00030", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 106, "fields": {"created_at": "2024-12-30T01:50:01.629", "updated_at": "2025-01-17T22:12:26.987", "description": "Доставка 30", "description_uz": null, "description_ru": null, "description_en": "Доставка 30", "price": 30.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "9b895bf9-4aef-49cf-b7c6-9331dc9d357b", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 36, "sku": "00030", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 107, "fields": {"created_at": "2024-12-30T01:50:01.633", "updated_at": "2025-01-17T22:12:26.992", "description": "Доставка 25", "description_uz": null, "description_ru": null, "description_en": "Доставка 25", "price": 25.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "f9679ed3-20f8-439b-b2c1-a8650d8c800d", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 37, "sku": "00028", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 108, "fields": {"created_at": "2024-12-30T01:50:01.635", "updated_at": "2025-01-17T22:12:26.994", "description": "Доставка 25", "description_uz": null, "description_ru": null, "description_en": "Доставка 25", "price": 25.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "f9679ed3-20f8-439b-b2c1-a8650d8c800d", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 37, "sku": "00028", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 109, "fields": {"created_at": "2024-12-30T01:50:01.640", "updated_at": "2025-01-17T22:12:26.999", "description": "Доставка 50", "description_uz": null, "description_ru": null, "description_en": "Доставка 50", "price": 50.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "592e6f80-7a1d-40cf-badc-20adbaec2a86", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 38, "sku": "00032", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 110, "fields": {"created_at": "2024-12-30T01:50:01.642", "updated_at": "2025-01-17T22:12:27.001", "description": "Доставка 50", "description_uz": null, "description_ru": null, "description_en": "Доставка 50", "price": 50.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "592e6f80-7a1d-40cf-badc-20adbaec2a86", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 38, "sku": "00032", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 111, "fields": {"created_at": "2024-12-30T01:50:01.646", "updated_at": "2025-01-17T22:12:27.005", "description": "Доставка 70", "description_uz": null, "description_ru": null, "description_en": "Доставка 70", "price": 70.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "5bf0536f-8b83-49c0-b4fe-9ac5aa7a492c", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 39, "sku": "00034", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 112, "fields": {"created_at": "2024-12-30T01:50:01.648", "updated_at": "2025-01-17T22:12:27.007", "description": "Доставка 70", "description_uz": null, "description_ru": null, "description_en": "Доставка 70", "price": 70.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "5bf0536f-8b83-49c0-b4fe-9ac5aa7a492c", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 39, "sku": "00034", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 113, "fields": {"created_at": "2024-12-30T01:50:01.652", "updated_at": "2025-01-17T22:12:27.011", "description": "Доставка 60", "description_uz": null, "description_ru": null, "description_en": "Доставка 60", "price": 60.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "be5205d9-2d3b-4aca-a3a7-a91d2c528117", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 40, "sku": "00033", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 114, "fields": {"created_at": "2024-12-30T01:50:01.654", "updated_at": "2025-01-17T22:12:27.013", "description": "Доставка 60", "description_uz": null, "description_ru": null, "description_en": "Доставка 60", "price": 60.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "be5205d9-2d3b-4aca-a3a7-a91d2c528117", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 40, "sku": "00033", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 115, "fields": {"created_at": "2024-12-30T01:50:01.659", "updated_at": "2025-01-17T22:12:27.017", "description": "Доставка 1", "description_uz": null, "description_ru": null, "description_en": "Доставка 1", "price": 1.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "b92cadda-2629-432f-88b1-509189c12a20", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 41, "sku": "00029", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 116, "fields": {"created_at": "2024-12-30T01:50:01.661", "updated_at": "2025-01-17T22:12:27.019", "description": "Доставка 1", "description_uz": null, "description_ru": null, "description_en": "Доставка 1", "price": 1.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "b92cadda-2629-432f-88b1-509189c12a20", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 41, "sku": "00029", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 117, "fields": {"created_at": "2024-12-30T01:50:01.665", "updated_at": "2025-01-17T22:12:27.023", "description": "Доставка 10", "description_uz": null, "description_ru": null, "description_en": "Доставка 10", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "3873a6d2-40ab-434e-bf35-8829f886f9c3", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 42, "sku": "00015", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 118, "fields": {"created_at": "2024-12-30T01:50:01.667", "updated_at": "2025-01-17T22:12:27.025", "description": "Доставка 10", "description_uz": null, "description_ru": null, "description_en": "Доставка 10", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "3873a6d2-40ab-434e-bf35-8829f886f9c3", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 42, "sku": "00015", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 119, "fields": {"created_at": "2024-12-30T01:50:01.671", "updated_at": "2025-01-17T22:12:27.030", "description": "Доставка 100", "description_uz": null, "description_ru": null, "description_en": "Доставка 100", "price": 100.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "350e87c5-8852-45d9-9aec-3d4e5a53da00", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 43, "sku": "00037", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 120, "fields": {"created_at": "2024-12-30T01:50:01.673", "updated_at": "2025-01-17T22:12:27.032", "description": "Доставка 100", "description_uz": null, "description_ru": null, "description_en": "Доставка 100", "price": 100.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "350e87c5-8852-45d9-9aec-3d4e5a53da00", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 43, "sku": "00037", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 121, "fields": {"created_at": "2024-12-30T01:50:01.678", "updated_at": "2025-01-17T22:12:27.036", "description": "Доставка 15", "description_uz": null, "description_ru": null, "description_en": "Доставка 15", "price": 15.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "08ea39b7-cf93-4646-957c-b170c2364572", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 44, "sku": "00017", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 122, "fields": {"created_at": "2024-12-30T01:50:01.680", "updated_at": "2025-01-17T22:12:27.038", "description": "Доставка 15", "description_uz": null, "description_ru": null, "description_en": "Доставка 15", "price": 15.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "08ea39b7-cf93-4646-957c-b170c2364572", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 44, "sku": "00017", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 123, "fields": {"created_at": "2024-12-30T01:50:01.684", "updated_at": "2025-01-17T22:12:27.042", "description": "Доставка 80", "description_uz": null, "description_ru": null, "description_en": "Доставка 80", "price": 80.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "5623c1ab-eb80-4448-a3e2-9b1ffc8fe3c6", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 45, "sku": "00035", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 124, "fields": {"created_at": "2024-12-30T01:50:01.686", "updated_at": "2025-01-17T22:12:27.043", "description": "Доставка 80", "description_uz": null, "description_ru": null, "description_en": "Доставка 80", "price": 80.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "5623c1ab-eb80-4448-a3e2-9b1ffc8fe3c6", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 45, "sku": "00035", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 125, "fields": {"created_at": "2024-12-30T01:50:01.691", "updated_at": "2025-01-17T22:12:27.048", "description": "Доставка 90", "description_uz": null, "description_ru": null, "description_en": "Доставка 90", "price": 0.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "e3e3b0cf-ea0c-4dfa-997c-0a8f727fdb5d", "organization_external_id": "ef0fcb2a-ff10-41f4-9655-64def0c1a122", "product": 46, "sku": "00036", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.productattribute", "pk": 126, "fields": {"created_at": "2024-12-30T01:50:01.693", "updated_at": "2025-01-17T22:12:27.049", "description": "Доставка 90", "description_uz": null, "description_ru": null, "description_en": "Доставка 90", "price": 0.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "e3e3b0cf-ea0c-4dfa-997c-0a8f727fdb5d", "organization_external_id": "f8da2658-3127-4ce3-9d29-24c314a39d89", "product": 46, "sku": "00036", "is_default": true, "size_id": null, "measure_unit_type": "GRAM", "size_code": "", "is_available": false}}, {"model": "product.modifieritems", "pk": 1, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 23, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 2, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 24, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 3, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 25, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 4, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 26, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 5, "fields": {"sku": "00023", "name": "зеленый", "name_uz": "<PERSON><PERSON><PERSON> choy", "name_ru": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "name_en": "зеленый", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 27, "portion_weight_grams": 0, "is_hidden": false, "item_id": "048954e8-e557-4409-802c-f4c7cdb749c4", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 6, "fields": {"sku": "00024", "name": "черный", "name_uz": "<PERSON><PERSON> choy", "name_ru": "<PERSON><PERSON><PERSON><PERSON>", "name_en": "черный", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 28, "portion_weight_grams": 0, "is_hidden": false, "item_id": "1b0df34c-ce96-4c77-b52a-ae2bf23a82f7", "position": 1, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 7, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 29, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 8, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 30, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 9, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 31, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 10, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 32, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 11, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 33, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 12, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 34, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 13, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 35, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 14, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 36, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 15, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 37, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 16, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 38, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 17, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 39, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 18, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 40, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 19, "fields": {"sku": "00084", "name": "pizza mod", "name_uz": "Pishloq", "name_ru": "Добавка сыр", "name_en": "pizza mod", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 41, "portion_weight_grams": 0, "is_hidden": false, "item_id": "f7fbf66a-4d31-4da3-b50e-fa17e87d73eb", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 20, "fields": {"sku": "00004", "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_uz": "Dablburger", "name_ru": "Dablburger", "name_en": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": null, "description_uz": null, "description_ru": null, "description_en": null, "restrictions": 42, "portion_weight_grams": 0, "is_hidden": false, "item_id": "6c499b31-3789-4f29-9c20-7127fcbda9a6", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 21, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 43, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 22, "fields": {"sku": "00022", "name": "халапеньо", "name_uz": "Qalampir", "name_ru": "Сыр", "name_en": "халапеньо", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 44, "portion_weight_grams": 0, "is_hidden": false, "item_id": "48a87b17-3bfb-498d-a9d7-2acf81f664b6", "position": 1, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 23, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 45, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 24, "fields": {"sku": "00022", "name": "халапеньо", "name_uz": "Pishloq", "name_ru": "Сыр", "name_en": "халапеньо", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 46, "portion_weight_grams": 0, "is_hidden": false, "item_id": "48a87b17-3bfb-498d-a9d7-2acf81f664b6", "position": 1, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 25, "fields": {"sku": "00021", "name": "сыр", "name_uz": "Pishloq Hochland", "name_ru": "Сыр <PERSON>land", "name_en": "сыр", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 47, "portion_weight_grams": 0, "is_hidden": false, "item_id": "8a0d87d5-2f5d-4077-b423-70b310f9a810", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 26, "fields": {"sku": "00022", "name": "халапеньо", "name_uz": "Qalampir", "name_ru": "Сыр", "name_en": "халапеньо", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 48, "portion_weight_grams": 0, "is_hidden": false, "item_id": "48a87b17-3bfb-498d-a9d7-2acf81f664b6", "position": 1, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.modifieritems", "pk": 27, "fields": {"sku": "00004", "name": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_uz": "Pishloq Hochland", "name_ru": "Сыр <PERSON>land", "name_en": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 50, "portion_weight_grams": 0, "is_hidden": false, "item_id": "6c499b31-3789-4f29-9c20-7127fcbda9a6", "position": 0, "independent_quantity": false, "measure_unit_type": "GRAM", "button_image_url": null}}, {"model": "product.restrictions", "pk": 1, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 2, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 3, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 4, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 5, "fields": {"min_quantity": 1, "max_quantity": 1, "free_quantity": 1, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 6, "fields": {"min_quantity": 0, "max_quantity": 3, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 7, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 8, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 9, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 10, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 11, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 12, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 13, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 14, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 15, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 16, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 17, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 18, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 19, "fields": {"min_quantity": 4, "max_quantity": 4, "free_quantity": 4, "by_default": 4, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 20, "fields": {"min_quantity": 0, "max_quantity": 2, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 21, "fields": {"min_quantity": 0, "max_quantity": 2, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 22, "fields": {"min_quantity": 0, "max_quantity": 2, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 23, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 24, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 25, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 26, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 27, "fields": {"min_quantity": 0, "max_quantity": 0, "free_quantity": 1, "by_default": 1, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 28, "fields": {"min_quantity": 0, "max_quantity": 0, "free_quantity": 1, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 29, "fields": {"min_quantity": 0, "max_quantity": 3, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 30, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 31, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 32, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 33, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 34, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 35, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 36, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 37, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 38, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 39, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 40, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 41, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 42, "fields": {"min_quantity": 4, "max_quantity": 4, "free_quantity": 4, "by_default": 4, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 43, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 44, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 45, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 46, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 47, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 48, "fields": {"min_quantity": 0, "max_quantity": 1, "free_quantity": 0, "by_default": 0, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 49, "fields": {"min_quantity": 4, "max_quantity": 4, "free_quantity": 4, "by_default": 4, "hide_if_default_quantity": false}}, {"model": "product.restrictions", "pk": 50, "fields": {"min_quantity": 4, "max_quantity": 4, "free_quantity": 4, "by_default": 4, "hide_if_default_quantity": false}}, {"model": "product.modiferproductprices", "pk": 1, "fields": {"organization": 2, "price": 3.0, "modifer_items": 1}}, {"model": "product.modiferproductprices", "pk": 2, "fields": {"organization": 3, "price": 3.0, "modifer_items": 1}}, {"model": "product.modiferproductprices", "pk": 3, "fields": {"organization": 2, "price": 3.0, "modifer_items": 2}}, {"model": "product.modiferproductprices", "pk": 4, "fields": {"organization": 3, "price": 3.0, "modifer_items": 2}}, {"model": "product.modiferproductprices", "pk": 5, "fields": {"organization": 2, "price": 3.0, "modifer_items": 3}}, {"model": "product.modiferproductprices", "pk": 6, "fields": {"organization": 3, "price": 3.0, "modifer_items": 3}}, {"model": "product.modiferproductprices", "pk": 7, "fields": {"organization": 2, "price": 3.0, "modifer_items": 4}}, {"model": "product.modiferproductprices", "pk": 8, "fields": {"organization": 3, "price": 3.0, "modifer_items": 4}}, {"model": "product.modiferproductprices", "pk": 9, "fields": {"organization": 2, "price": 0.0, "modifer_items": 5}}, {"model": "product.modiferproductprices", "pk": 10, "fields": {"organization": 3, "price": 0.0, "modifer_items": 5}}, {"model": "product.modiferproductprices", "pk": 11, "fields": {"organization": 2, "price": 0.0, "modifer_items": 6}}, {"model": "product.modiferproductprices", "pk": 12, "fields": {"organization": 3, "price": 0.0, "modifer_items": 6}}, {"model": "product.modiferproductprices", "pk": 13, "fields": {"organization": 2, "price": 3.0, "modifer_items": 7}}, {"model": "product.modiferproductprices", "pk": 14, "fields": {"organization": 3, "price": 3.0, "modifer_items": 7}}, {"model": "product.modiferproductprices", "pk": 15, "fields": {"organization": 2, "price": 2.0, "modifer_items": 8}}, {"model": "product.modiferproductprices", "pk": 16, "fields": {"organization": 3, "price": 2.0, "modifer_items": 8}}, {"model": "product.modiferproductprices", "pk": 17, "fields": {"organization": 2, "price": 4.0, "modifer_items": 9}}, {"model": "product.modiferproductprices", "pk": 18, "fields": {"organization": 3, "price": 4.0, "modifer_items": 9}}, {"model": "product.modiferproductprices", "pk": 19, "fields": {"organization": 2, "price": 6.0, "modifer_items": 10}}, {"model": "product.modiferproductprices", "pk": 20, "fields": {"organization": 3, "price": 6.0, "modifer_items": 10}}, {"model": "product.modiferproductprices", "pk": 21, "fields": {"organization": 2, "price": 2.0, "modifer_items": 11}}, {"model": "product.modiferproductprices", "pk": 22, "fields": {"organization": 3, "price": 2.0, "modifer_items": 11}}, {"model": "product.modiferproductprices", "pk": 23, "fields": {"organization": 2, "price": 4.0, "modifer_items": 12}}, {"model": "product.modiferproductprices", "pk": 24, "fields": {"organization": 3, "price": 4.0, "modifer_items": 12}}, {"model": "product.modiferproductprices", "pk": 25, "fields": {"organization": 2, "price": 6.0, "modifer_items": 13}}, {"model": "product.modiferproductprices", "pk": 26, "fields": {"organization": 3, "price": 6.0, "modifer_items": 13}}, {"model": "product.modiferproductprices", "pk": 27, "fields": {"organization": 2, "price": 2.0, "modifer_items": 14}}, {"model": "product.modiferproductprices", "pk": 28, "fields": {"organization": 3, "price": 2.0, "modifer_items": 14}}, {"model": "product.modiferproductprices", "pk": 29, "fields": {"organization": 2, "price": 4.0, "modifer_items": 15}}, {"model": "product.modiferproductprices", "pk": 30, "fields": {"organization": 3, "price": 4.0, "modifer_items": 15}}, {"model": "product.modiferproductprices", "pk": 31, "fields": {"organization": 2, "price": 6.0, "modifer_items": 16}}, {"model": "product.modiferproductprices", "pk": 32, "fields": {"organization": 3, "price": 6.0, "modifer_items": 16}}, {"model": "product.modiferproductprices", "pk": 33, "fields": {"organization": 2, "price": 2.0, "modifer_items": 17}}, {"model": "product.modiferproductprices", "pk": 34, "fields": {"organization": 3, "price": 2.0, "modifer_items": 17}}, {"model": "product.modiferproductprices", "pk": 35, "fields": {"organization": 2, "price": 4.0, "modifer_items": 18}}, {"model": "product.modiferproductprices", "pk": 36, "fields": {"organization": 3, "price": 4.0, "modifer_items": 18}}, {"model": "product.modiferproductprices", "pk": 37, "fields": {"organization": 2, "price": 6.0, "modifer_items": 19}}, {"model": "product.modiferproductprices", "pk": 38, "fields": {"organization": 3, "price": 6.0, "modifer_items": 19}}, {"model": "product.modiferproductprices", "pk": 39, "fields": {"organization": 2, "price": 15.0, "modifer_items": 20}}, {"model": "product.modiferproductprices", "pk": 40, "fields": {"organization": 3, "price": 15.0, "modifer_items": 20}}, {"model": "product.modiferproductprices", "pk": 41, "fields": {"organization": 2, "price": 3.0, "modifer_items": 21}}, {"model": "product.modiferproductprices", "pk": 42, "fields": {"organization": 3, "price": 3.0, "modifer_items": 21}}, {"model": "product.modiferproductprices", "pk": 43, "fields": {"organization": 2, "price": 2.0, "modifer_items": 22}}, {"model": "product.modiferproductprices", "pk": 44, "fields": {"organization": 3, "price": 2.0, "modifer_items": 22}}, {"model": "product.modiferproductprices", "pk": 45, "fields": {"organization": 2, "price": 3.0, "modifer_items": 23}}, {"model": "product.modiferproductprices", "pk": 46, "fields": {"organization": 3, "price": 3.0, "modifer_items": 23}}, {"model": "product.modiferproductprices", "pk": 47, "fields": {"organization": 2, "price": 2.0, "modifer_items": 24}}, {"model": "product.modiferproductprices", "pk": 48, "fields": {"organization": 3, "price": 2.0, "modifer_items": 24}}, {"model": "product.modiferproductprices", "pk": 49, "fields": {"organization": 2, "price": 3.0, "modifer_items": 25}}, {"model": "product.modiferproductprices", "pk": 50, "fields": {"organization": 3, "price": 3.0, "modifer_items": 25}}, {"model": "product.modiferproductprices", "pk": 51, "fields": {"organization": 2, "price": 2.0, "modifer_items": 26}}, {"model": "product.modiferproductprices", "pk": 52, "fields": {"organization": 3, "price": 2.0, "modifer_items": 26}}, {"model": "product.modiferproductprices", "pk": 53, "fields": {"organization": 2, "price": 15.0, "modifer_items": 27}}, {"model": "product.modiferproductprices", "pk": 54, "fields": {"organization": 3, "price": 15.0, "modifer_items": 27}}, {"model": "product.itemmodifiergroups", "pk": 1, "fields": {"product": 1, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 1, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [1]}}, {"model": "product.itemmodifiergroups", "pk": 2, "fields": {"product": 1, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 2, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [2]}}, {"model": "product.itemmodifiergroups", "pk": 3, "fields": {"product": 2, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 3, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [3]}}, {"model": "product.itemmodifiergroups", "pk": 4, "fields": {"product": 2, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 4, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [4]}}, {"model": "product.itemmodifiergroups", "pk": 5, "fields": {"product": 5, "size_id": null, "name": "чай на выбор", "name_uz": "<PERSON>y turini tanlang", "name_ru": "Дополнительно", "name_en": "чай на выбор", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 5, "can_be_divided": false, "item_group_id": "c0b71bda-0d8c-4693-adf5-2e7fbcfc3450", "is_hidden": false, "child_modifier_have_min_max_restrictions": false, "sku": "0012", "items": [5, 6]}}, {"model": "product.itemmodifiergroups", "pk": 6, "fields": {"product": 20, "size_id": null, "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 6, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [7]}}, {"model": "product.itemmodifiergroups", "pk": 7, "fields": {"product": 21, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 7, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [8]}}, {"model": "product.itemmodifiergroups", "pk": 8, "fields": {"product": 21, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 8, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [9]}}, {"model": "product.itemmodifiergroups", "pk": 9, "fields": {"product": 21, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 9, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [10]}}, {"model": "product.itemmodifiergroups", "pk": 10, "fields": {"product": 22, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 10, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [11]}}, {"model": "product.itemmodifiergroups", "pk": 11, "fields": {"product": 22, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 11, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [12]}}, {"model": "product.itemmodifiergroups", "pk": 12, "fields": {"product": 22, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 12, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [13]}}, {"model": "product.itemmodifiergroups", "pk": 13, "fields": {"product": 23, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 13, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [14]}}, {"model": "product.itemmodifiergroups", "pk": 14, "fields": {"product": 23, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 14, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [15]}}, {"model": "product.itemmodifiergroups", "pk": 15, "fields": {"product": 23, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 15, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [16]}}, {"model": "product.itemmodifiergroups", "pk": 16, "fields": {"product": 24, "size_id": "3632ab93-78ed-419a-9914-494164a71fd9", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 16, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [17]}}, {"model": "product.itemmodifiergroups", "pk": 17, "fields": {"product": 24, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 17, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [18]}}, {"model": "product.itemmodifiergroups", "pk": 18, "fields": {"product": 24, "size_id": "dbbf055f-56c5-4b9b-9de4-c99a5b037e1e", "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 18, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [19]}}, {"model": "product.itemmodifiergroups", "pk": 20, "fields": {"product": 26, "size_id": null, "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 20, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [21, 22]}}, {"model": "product.itemmodifiergroups", "pk": 21, "fields": {"product": 27, "size_id": null, "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 21, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [24, 23]}}, {"model": "product.itemmodifiergroups", "pk": 22, "fields": {"product": 28, "size_id": null, "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 22, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [25, 26]}}, {"model": "product.itemmodifiergroups", "pk": 23, "fields": {"product": 33, "size_id": null, "name": "Дополнительно", "name_uz": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name_ru": "Дополнительно", "name_en": "Дополнительно", "description": "", "description_uz": null, "description_ru": null, "description_en": "", "restrictions": 49, "can_be_divided": false, "item_group_id": null, "is_hidden": false, "child_modifier_have_min_max_restrictions": true, "sku": "", "items": [27]}}, {"model": "product.orderedproduct", "pk": 1, "fields": {"created_at": "2024-12-17T01:00:58.887", "updated_at": "2024-12-17T01:00:58.887", "title": "Pita (mol go'shti) NEW", "title_uz": null, "title_ru": null, "title_en": "Pita (mol go'shti) NEW", "image_url": "", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": ""}}, {"model": "product.orderedproduct", "pk": 2, "fields": {"created_at": "2024-12-17T01:00:58.912", "updated_at": "2024-12-17T01:00:58.912", "title": "Pepsi razliv NEW", "title_uz": null, "title_ru": null, "title_en": "Pepsi razliv NEW", "image_url": "", "product_type": "DISH", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "comment": ""}}, {"model": "product.orderedproduct", "pk": 3, "fields": {"created_at": "2024-12-25T22:37:59.479", "updated_at": "2024-12-25T22:37:59.479", "title": "Pita (mol go'shti) NEW", "title_uz": null, "title_ru": null, "title_en": "Pita (mol go'shti) NEW", "image_url": "", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": ""}}, {"model": "product.orderedproduct", "pk": 4, "fields": {"created_at": "2024-12-25T22:39:02.183", "updated_at": "2024-12-25T22:39:02.183", "title": "Pepsi razliv NEW", "title_uz": null, "title_ru": null, "title_en": "Pepsi razliv NEW", "image_url": "", "product_type": "DISH", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "comment": ""}}, {"model": "product.orderedproduct", "pk": 5, "fields": {"created_at": "2024-12-26T14:12:03.137", "updated_at": "2024-12-26T14:12:03.137", "title": "Pita (mol go'shti) NEW", "title_uz": null, "title_ru": null, "title_en": "Pita (mol go'shti) NEW", "image_url": "", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": ""}}, {"model": "product.orderedproduct", "pk": 6, "fields": {"created_at": "2024-12-26T14:12:03.145", "updated_at": "2024-12-26T14:12:03.145", "title": "Pita NEW", "title_uz": null, "title_ru": null, "title_en": "Pita NEW", "image_url": "", "product_type": "DISH", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "comment": ""}}, {"model": "product.orderedproduct", "pk": 7, "fields": {"created_at": "2024-12-26T14:16:57.239", "updated_at": "2024-12-26T14:16:57.239", "title": "Pita NEW", "title_uz": null, "title_ru": null, "title_en": "Pita NEW", "image_url": "", "product_type": "DISH", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "comment": ""}}, {"model": "product.orderedproduct", "pk": 8, "fields": {"created_at": "2024-12-26T14:16:57.247", "updated_at": "2024-12-26T14:16:57.247", "title": "Pita (mol go'shti) NEW", "title_uz": null, "title_ru": null, "title_en": "Pita (mol go'shti) NEW", "image_url": "", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": ""}}, {"model": "product.orderedproduct", "pk": 9, "fields": {"created_at": "2024-12-30T18:42:43.182", "updated_at": "2024-12-30T18:42:43.182", "title": "Tandir lavash pishloqli NEW", "title_uz": "<PERSON><PERSON> lavash pishloqli", "title_ru": "Тандыр лаваш с сыром", "title_en": "Tandir lavash pishloqli NEW", "image_url": "test/products/2.png", "product_type": "DISH", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "comment": ""}}, {"model": "product.orderedproduct", "pk": 10, "fields": {"created_at": "2024-12-30T18:42:43.191", "updated_at": "2024-12-30T18:42:43.191", "title": "Tandir lavash NEW", "title_uz": "<PERSON><PERSON> lavash", "title_ru": "Тандыр лаваш", "title_en": "Tandir lavash NEW", "image_url": "test/products/2.png", "product_type": "DISH", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "comment": ""}}, {"model": "product.orderedproduct", "pk": 11, "fields": {"created_at": "2024-12-30T18:42:52.600", "updated_at": "2024-12-30T18:42:52.600", "title": "Tandir lavash pishloqli NEW", "title_uz": "<PERSON><PERSON> lavash pishloqli", "title_ru": "Тандыр лаваш с сыром", "title_en": "Tandir lavash pishloqli NEW", "image_url": "test/products/2.png", "product_type": "DISH", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "comment": ""}}, {"model": "product.orderedproduct", "pk": 12, "fields": {"created_at": "2024-12-30T18:42:52.608", "updated_at": "2024-12-30T18:42:52.608", "title": "Tandir lavash NEW", "title_uz": "<PERSON><PERSON> lavash", "title_ru": "Тандыр лаваш", "title_en": "Tandir lavash NEW", "image_url": "test/products/2.png", "product_type": "DISH", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "comment": ""}}, {"model": "product.orderedproduct", "pk": 13, "fields": {"created_at": "2025-01-02T13:00:01.000", "updated_at": "2025-01-02T13:00:01.000", "title": "Gamburger NEW", "title_uz": "Gamburger", "title_ru": "Га<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "title_en": "Gamburger NEW", "image_url": "test/products/16-min.png", "product_type": "DISH", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "comment": ""}}, {"model": "product.orderedproduct", "pk": 14, "fields": {"created_at": "2025-01-02T13:00:01.008", "updated_at": "2025-01-02T13:00:01.008", "title": "Tandir lavash pishloqli NEW", "title_uz": "<PERSON><PERSON> lavash pishloqli", "title_ru": "Тандыр лаваш с сыром", "title_en": "Tandir lavash pishloqli NEW", "image_url": "test/products/2.png", "product_type": "DISH", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "comment": ""}}, {"model": "product.orderedproduct", "pk": 15, "fields": {"created_at": "2025-01-07T01:17:00.895", "updated_at": "2025-01-07T01:17:00.895", "title": "Tandir lavash pishloqli NEW", "title_uz": "<PERSON><PERSON> lavash pishloqli", "title_ru": "Тандыр лаваш с сыром", "title_en": "Tandir lavash pishloqli NEW", "image_url": "test/products/2.png", "product_type": "DISH", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "comment": ""}}, {"model": "product.orderedproduct", "pk": 16, "fields": {"created_at": "2025-01-07T01:17:00.903", "updated_at": "2025-01-07T01:17:00.903", "title": "Lavash pishloqli NEW", "title_uz": "<PERSON><PERSON><PERSON>", "title_ru": "Лаваш с сыром", "title_en": "Lavash pishloqli NEW", "image_url": "test/products/1.png", "product_type": "DISH", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "comment": ""}}, {"model": "product.orderedproduct", "pk": 17, "fields": {"created_at": "2025-01-07T01:17:00.910", "updated_at": "2025-01-07T01:17:00.910", "title": "Lavash NEW", "title_uz": "Lavash", "title_ru": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "title_en": "Lavash NEW", "image_url": "test/products/1.png", "product_type": "DISH", "external_id": "deb7c2d5-5e6c-46b2-8cbb-dfb4cd42afda", "comment": ""}}, {"model": "product.orderedproduct", "pk": 18, "fields": {"created_at": "2025-01-07T01:38:09.080", "updated_at": "2025-01-07T01:38:09.080", "title": "Чизбургер", "title_uz": "Chizburger", "title_ru": "Чизбургер", "title_en": "Чизбургер", "image_url": "test/products/15-min.png", "product_type": "DISH", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "comment": ""}}, {"model": "product.orderedproduct", "pk": 19, "fields": {"created_at": "2025-01-07T01:38:09.087", "updated_at": "2025-01-07T01:38:09.087", "title": "Gamburger NEW", "title_uz": "Gamburger", "title_ru": "Га<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "title_en": "Gamburger NEW", "image_url": "test/products/16-min.png", "product_type": "DISH", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "comment": ""}}, {"model": "product.orderedproduct", "pk": 20, "fields": {"created_at": "2025-01-07T01:39:14.903", "updated_at": "2025-01-07T01:39:14.903", "title": "Чизбургер", "title_uz": "Chizburger", "title_ru": "Чизбургер", "title_en": "Чизбургер", "image_url": "test/products/15-min.png", "product_type": "DISH", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "comment": ""}}, {"model": "product.orderedproduct", "pk": 21, "fields": {"created_at": "2025-01-07T01:39:14.911", "updated_at": "2025-01-07T01:39:14.911", "title": "Gamburger NEW", "title_uz": "Gamburger", "title_ru": "Га<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "title_en": "Gamburger NEW", "image_url": "test/products/16-min.png", "product_type": "DISH", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "comment": ""}}, {"model": "product.orderedproduct", "pk": 22, "fields": {"created_at": "2025-01-07T01:48:14.519", "updated_at": "2025-01-07T01:48:14.519", "title": "Gamburger NEW", "title_uz": "Gamburger", "title_ru": "Га<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "title_en": "Gamburger NEW", "image_url": "test/products/16-min.png", "product_type": "DISH", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "comment": ""}}, {"model": "product.orderedproduct", "pk": 23, "fields": {"created_at": "2025-01-07T01:48:14.527", "updated_at": "2025-01-07T01:48:14.527", "title": "Чизбургер", "title_uz": "Chizburger", "title_ru": "Чизбургер", "title_en": "Чизбургер", "image_url": "test/products/15-min.png", "product_type": "DISH", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "comment": ""}}, {"model": "product.orderedproduct", "pk": 24, "fields": {"created_at": "2025-01-07T01:48:14.534", "updated_at": "2025-01-07T01:48:14.534", "title": "Tandir lavash pishloqli NEW", "title_uz": "<PERSON><PERSON> lavash pishloqli", "title_ru": "Тандыр лаваш с сыром", "title_en": "Tandir lavash pishloqli NEW", "image_url": "test/products/2.png", "product_type": "DISH", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "comment": ""}}, {"model": "product.orderedproduct", "pk": 25, "fields": {"created_at": "2025-01-07T01:56:40.085", "updated_at": "2025-01-07T01:56:40.085", "title": "Чизбургер", "title_uz": "Chizburger", "title_ru": "Чизбургер", "title_en": "Чизбургер", "image_url": "test/products/15-min.png", "product_type": "DISH", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "comment": ""}}, {"model": "product.orderedproduct", "pk": 26, "fields": {"created_at": "2025-01-07T01:56:40.093", "updated_at": "2025-01-07T01:56:40.093", "title": "Gamburger NEW", "title_uz": "Gamburger", "title_ru": "Га<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>р", "title_en": "Gamburger NEW", "image_url": "test/products/16-min.png", "product_type": "DISH", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "comment": ""}}, {"model": "product.orderedproduct", "pk": 27, "fields": {"created_at": "2025-01-07T01:56:40.100", "updated_at": "2025-01-07T01:56:40.100", "title": "Lavash pishloqli NEW", "title_uz": "<PERSON><PERSON><PERSON>", "title_ru": "Лаваш с сыром", "title_en": "Lavash pishloqli NEW", "image_url": "test/products/1.png", "product_type": "DISH", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "comment": ""}}, {"model": "product.orderedproduct", "pk": 28, "fields": {"created_at": "2025-01-08T14:30:06.683", "updated_at": "2025-01-08T14:30:06.683", "title": "Pita (mol go'shti) NEW", "title_uz": "Pita", "title_ru": "Pita", "title_en": "Pita (mol go'shti) NEW", "image_url": "test/products/pita.png", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": null}}, {"model": "product.orderedproduct", "pk": 29, "fields": {"created_at": "2025-01-08T14:30:30.449", "updated_at": "2025-01-08T14:30:30.449", "title": "Pita (mol go'shti) NEW", "title_uz": "Pita", "title_ru": "Pita", "title_en": "Pita (mol go'shti) NEW", "image_url": "test/products/pita.png", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": null}}, {"model": "product.orderedproduct", "pk": 30, "fields": {"created_at": "2025-01-08T14:31:53.468", "updated_at": "2025-01-08T14:31:53.468", "title": "Margarita NEW", "title_uz": "<PERSON><PERSON><PERSON>", "title_ru": "<PERSON><PERSON><PERSON><PERSON>", "title_en": "Margarita NEW", "image_url": "test/products/13-min.png", "product_type": "DISH", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "comment": ""}}, {"model": "product.orderedproduct", "pk": 31, "fields": {"created_at": "2025-01-09T14:39:59.446", "updated_at": "2025-01-09T14:39:59.446", "title": "Pita (mol go'shti) NEW", "title_uz": "Pita", "title_ru": "Pita", "title_en": "Pita (mol go'shti) NEW", "image_url": "test/products/pita.png", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": "TESTCOMMENT"}}, {"model": "product.orderedproduct", "pk": 32, "fields": {"created_at": "2025-01-09T14:42:16.225", "updated_at": "2025-01-09T14:42:16.225", "title": "Pita (mol go'shti) NEW", "title_uz": "Pita", "title_ru": "Pita", "title_en": "Pita (mol go'shti) NEW", "image_url": "test/products/pita.png", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": "TESTCOMMENT"}}, {"model": "product.orderedproduct", "pk": 33, "fields": {"created_at": "2025-01-09T14:44:51.441", "updated_at": "2025-01-09T14:44:51.441", "title": "Pita (mol go'shti) NEW", "title_uz": "Pita", "title_ru": "Pita", "title_en": "Pita (mol go'shti) NEW", "image_url": "test/products/pita.png", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": "TESTCOMMENT"}}, {"model": "product.orderedproduct", "pk": 34, "fields": {"created_at": "2025-01-09T19:08:54.663", "updated_at": "2025-01-09T19:08:54.663", "title": "Pita (mol go'shti) NEW", "title_uz": "Pita", "title_ru": "Pita", "title_en": "Pita (mol go'shti) NEW", "image_url": "test/products/pita.png", "product_type": "DISH", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "comment": "TESTCOMMENT"}}, {"model": "product.orderedproductattribute", "pk": 1, "fields": {"created_at": "2024-12-17T01:00:58.890", "updated_at": "2024-12-17T01:00:58.891", "description": null, "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 1, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 2, "fields": {"created_at": "2024-12-17T01:00:58.914", "updated_at": "2024-12-17T01:00:58.914", "description": null, "price": 11.0, "size": "0,5л", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "product": 2, "size_id": "6938bb29-a212-444c-9c49-b4be626804d0"}}, {"model": "product.orderedproductattribute", "pk": 3, "fields": {"created_at": "2024-12-25T22:37:59.484", "updated_at": "2024-12-25T22:37:59.484", "description": null, "price": 14.0, "size": "Больш<PERSON>й", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 3, "size_id": "c95da914-04b6-4d8e-b40c-803b53d7c910"}}, {"model": "product.orderedproductattribute", "pk": 4, "fields": {"created_at": "2024-12-25T22:39:02.184", "updated_at": "2024-12-25T22:39:02.184", "description": "Pepsi razliv NEW", "price": 11.0, "size": "0,5л", "external_id": "408e8bf6-5ff9-4acd-97c3-ea8dc6dd3c71", "product": 4, "size_id": "6938bb29-a212-444c-9c49-b4be626804d0"}}, {"model": "product.orderedproductattribute", "pk": 5, "fields": {"created_at": "2024-12-26T14:12:03.139", "updated_at": "2024-12-26T14:12:03.139", "description": null, "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 5, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 6, "fields": {"created_at": "2024-12-26T14:12:03.146", "updated_at": "2024-12-26T14:12:03.146", "description": null, "price": 9.0, "size": "Средний", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "product": 6, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 7, "fields": {"created_at": "2024-12-26T14:16:57.240", "updated_at": "2024-12-26T14:16:57.240", "description": null, "price": 9.0, "size": "Средний", "external_id": "6335ae38-eed8-4890-90ab-57f719b9cb3f", "product": 7, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 8, "fields": {"created_at": "2024-12-26T14:16:57.248", "updated_at": "2024-12-26T14:16:57.248", "description": null, "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 8, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 9, "fields": {"created_at": "2024-12-30T18:42:43.184", "updated_at": "2024-12-30T18:42:43.184", "description": null, "price": 14.0, "size": "Средний", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "product": 9, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 10, "fields": {"created_at": "2024-12-30T18:42:43.192", "updated_at": "2024-12-30T18:42:43.192", "description": null, "price": 12.0, "size": "Средний", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "product": 10, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 11, "fields": {"created_at": "2024-12-30T18:42:52.601", "updated_at": "2024-12-30T18:42:52.601", "description": null, "price": 14.0, "size": "Средний", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "product": 11, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 12, "fields": {"created_at": "2024-12-30T18:42:52.609", "updated_at": "2024-12-30T18:42:52.609", "description": null, "price": 12.0, "size": "Средний", "external_id": "2909f169-b45f-44cd-875e-4e22025bebde", "product": 12, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 13, "fields": {"created_at": "2025-01-02T13:00:01.001", "updated_at": "2025-01-02T13:00:01.001", "description": null, "price": 14.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "product": 13, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 14, "fields": {"created_at": "2025-01-02T13:00:01.009", "updated_at": "2025-01-02T13:00:01.009", "description": null, "price": 14.0, "size": "Средний", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "product": 14, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 15, "fields": {"created_at": "2025-01-07T01:17:00.896", "updated_at": "2025-01-07T01:17:00.896", "description": null, "price": 14.0, "size": "Средний", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "product": 15, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 16, "fields": {"created_at": "2025-01-07T01:17:00.904", "updated_at": "2025-01-07T01:17:00.904", "description": null, "price": 12.0, "size": "Средний", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "product": 16, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 17, "fields": {"created_at": "2025-01-07T01:17:00.911", "updated_at": "2025-01-07T01:17:00.911", "description": null, "price": 11.0, "size": "Средний", "external_id": "deb7c2d5-5e6c-46b2-8cbb-dfb4cd42afda", "product": 17, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 18, "fields": {"created_at": "2025-01-07T01:38:09.081", "updated_at": "2025-01-07T01:38:09.081", "description": "Чизбургер", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "product": 18, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 19, "fields": {"created_at": "2025-01-07T01:38:09.088", "updated_at": "2025-01-07T01:38:09.088", "description": "Gamburger NEW", "price": 14.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "product": 19, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 20, "fields": {"created_at": "2025-01-07T01:39:14.904", "updated_at": "2025-01-07T01:39:14.904", "description": "Чизбургер", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "product": 20, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 21, "fields": {"created_at": "2025-01-07T01:39:14.911", "updated_at": "2025-01-07T01:39:14.911", "description": "Gamburger NEW", "price": 14.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "product": 21, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 22, "fields": {"created_at": "2025-01-07T01:48:14.521", "updated_at": "2025-01-07T01:48:14.521", "description": "Gamburger NEW", "price": 14.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "product": 22, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 23, "fields": {"created_at": "2025-01-07T01:48:14.528", "updated_at": "2025-01-07T01:48:14.528", "description": "Чизбургер", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "product": 23, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 24, "fields": {"created_at": "2025-01-07T01:48:14.535", "updated_at": "2025-01-07T01:48:14.535", "description": "Tandir lavash pishloqli NEW", "price": 14.0, "size": "Средний", "external_id": "d0eda366-0057-446d-a5d5-6d97a82b687d", "product": 24, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 25, "fields": {"created_at": "2025-01-07T01:56:40.086", "updated_at": "2025-01-07T01:56:40.086", "description": "Чизбургер", "price": 10.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "237c2464-b8ed-4d64-b461-8c67e7f46a80", "product": 25, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 26, "fields": {"created_at": "2025-01-07T01:56:40.094", "updated_at": "2025-01-07T01:56:40.094", "description": "Gamburger NEW", "price": 14.0, "size": "<PERSON><PERSON><PERSON>", "external_id": "dd5f8efd-fa32-4d45-af71-1d7ecc823558", "product": 26, "size_id": null}}, {"model": "product.orderedproductattribute", "pk": 27, "fields": {"created_at": "2025-01-07T01:56:40.101", "updated_at": "2025-01-07T01:56:40.101", "description": "Lavash pishloqli NEW", "price": 12.0, "size": "Средний", "external_id": "3b5341db-8217-4f6e-99df-bba0d6e06e20", "product": 27, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 28, "fields": {"created_at": "2025-01-08T14:30:06.685", "updated_at": "2025-01-08T14:30:06.685", "description": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 28, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 29, "fields": {"created_at": "2025-01-08T14:30:30.451", "updated_at": "2025-01-08T14:30:30.451", "description": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 29, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 30, "fields": {"created_at": "2025-01-08T14:31:53.470", "updated_at": "2025-01-08T14:31:53.470", "description": null, "price": 22.0, "size": "Средняя", "external_id": "5b88852e-1174-40ed-9f1a-9b948b494251", "product": 30, "size_id": "d56142b3-eedb-4fff-a78f-3dc032ede637"}}, {"model": "product.orderedproductattribute", "pk": 31, "fields": {"created_at": "2025-01-09T14:39:59.448", "updated_at": "2025-01-09T14:39:59.448", "description": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 31, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 32, "fields": {"created_at": "2025-01-09T14:42:16.226", "updated_at": "2025-01-09T14:42:16.226", "description": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 32, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 33, "fields": {"created_at": "2025-01-09T14:44:51.442", "updated_at": "2025-01-09T14:44:51.442", "description": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 33, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductattribute", "pk": 34, "fields": {"created_at": "2025-01-09T19:08:54.664", "updated_at": "2025-01-09T19:08:54.664", "description": "Pita (mol go'shti) NEW", "price": 10.0, "size": "Средний", "external_id": "56acf816-d3ad-48a6-b53f-21b46d689eb5", "product": 34, "size_id": "34cabecb-a604-42b7-865d-ea9a39c04d7b"}}, {"model": "product.orderedproductmodifier", "pk": 1, "fields": {"created_at": "2024-12-25T22:37:59.488", "updated_at": "2024-12-25T22:37:59.488", "ordered_product": 3, "modifier_item": 2, "quantity": 1}}, {"model": "product.orderedproductmodifier", "pk": 2, "fields": {"created_at": "2025-01-08T14:31:53.471", "updated_at": "2025-01-08T14:31:53.471", "ordered_product": 30, "modifier_item": 9, "quantity": 1}}, {"model": "product.orderedproductmodifier", "pk": 3, "fields": {"created_at": "2025-01-09T14:42:16.227", "updated_at": "2025-01-09T14:42:16.227", "ordered_product": 32, "modifier_item": 1, "quantity": 1}}, {"model": "product.orderedproductmodifier", "pk": 4, "fields": {"created_at": "2025-01-09T14:44:51.443", "updated_at": "2025-01-09T14:44:51.443", "ordered_product": 33, "modifier_item": 1, "quantity": 1}}, {"model": "product.banner", "pk": 1, "fields": {"created_at": "2024-12-14T20:19:09.861", "updated_at": "2024-12-27T00:11:08.563", "name": "ad1", "image_url": "test/banners/photo_2024-12-14_20.18.49.jpeg", "ad_link": null, "position": 1}}, {"model": "product.banner", "pk": 3, "fields": {"created_at": "2024-12-27T00:11:33.397", "updated_at": "2024-12-27T00:11:33.397", "name": "ad2", "image_url": "test/banners/photo_2024-12-14_20.19.56.jpeg", "ad_link": null, "position": 2}}, {"model": "notify.notification", "pk": 91, "fields": {"created_at": "2025-01-20T03:01:34.492", "updated_at": "2025-01-20T03:01:34.492", "provider": "telegram", "title": "BYD kerakmi?)", "body": "<b>BYD avtomobili yoki ko‘plab qimmatbaho sovg‘alardan birini yutib olish imkoniyatini boy bermang! 🎉</b>\n<p>2024-yil 20-dekabrdan 2025-yil 15-fevralgacha <b>MaxWay</b> restoranlarida yoki yetkazib berish xizmati or<PERSON><PERSON> x<PERSON> <b>Maxi Box</b> ni buyurtma qiling va sovg'alik stiker oling:</p>\n<ul>\n  <li>🍔 <b>MaxWay menyusidan tezkor mazali sovg‘alar</b></li>\n  <li>🎁 <b>Qimmatbaho sovg‘alar uchun promokodlar:</b> smartfon<PERSON>, aqlli soatlar, o‘yin pristavkalari</li>\n  <li>🚗 <b>Final o‘yinda BYD E2 avtomobilini yutib olish imkoniyati!</b></li>\n</ul>\n<p>Har bir promokod g‘alaba qozonish imkoniyatingizni oshiradi! Bosh sovrinning final o‘yini <b>2025-yil 20-fevral</b>da MaxWay ning 20 yilligiga bag‘ishlangan bayramda bo‘lib o‘tadi 🤩</p>\n<b>📌 Qanday ishtirok etish mumkin:</b>\n<ol>\n  <li>Maxi Box buyurtma qiling</li>\n  <li>Stikerni oching va sovg‘angizni bilib oling</li>\n  <li>Promokodni <a href=\"https://t.me/maxway_promo_bot\">@maxway_promo_bot</a> orqali ro‘yxatdan o‘tkazing</li>\n</ol>\n<p>Aksiyada ishtirok eting va MaxWay ning 20 yilligini sovg‘alar va yorqin hissiyotlar bilan nishonlang! Batafsil ma’lumot: <a href=\"https://maxway.uz\">maxway.uz</a></p>\n"}}, {"model": "notify.notification", "pk": 92, "fields": {"created_at": "2025-01-20T03:26:19.831", "updated_at": "2025-01-20T03:26:19.831", "provider": "telegram", "title": "BYD kerakmi?)", "body": "<p><PERSON><PERSON><PERSON><PERSON><PERSON> qishgi aksiya haliyam davom etmoqda</p><p><PERSON><PERSON> qator</p><p><br></p><p><PERSON><PERSON> bitta joy tashlab ketilgan qator</p>"}}, {"model": "notify.notification", "pk": 93, "fields": {"created_at": "2025-01-20T11:21:48.991", "updated_at": "2025-01-20T11:21:48.991", "provider": "telegram", "title": "<PERSON><PERSON>", "body": "<p>Master kebab - b<PERSON><PERSON>ga bo’l<PERSON> muhab<PERSON> bilan</p><p><br></p><p>• <PERSON><PERSON><PERSON><PERSON> taomingizni shahar ichida va tashqa<PERSON>ida, kunu tun har qanday vaqtda yetkazib berish xizmati</p><p><br></p><p>📞1059</p><p><br></p><p>📍Tsum, Navo<PERSON>y sh.</p><p><br></p><p>📍Islom <PERSON><PERSON>ov ko‘chasi, 109 (Perfectum ofisi), Navoiy sh.</p><p><br></p><p>📍Vosxod, Navoiy sh.</p><p><br></p><p>📍Toshkent ko‘chasi, 28/1 (Markaziy poliklinika), Karmana</p><p><br></p><p>_______________________________________</p><p><br></p><p>Master kebab - с любовью к детям</p><p><br></p><p>• Доставка любимого блюда до любой точки города и за его пределами , в любое время суток</p><p><br></p><p>📞1059</p><p><br></p><p>📍Цум, г.Навои&nbsp;</p><p><br></p><p>📍ул. Ислама Каримова, 109 (офис Perfectum), г. Навои</p><p><br></p><p>📍Восход , г.Навои</p><p><br></p><p>📍ул. Ташкентская, 28/1 (Центральная поликлиника), Кармана</p>"}}, {"model": "notify.notification", "pk": 94, "fields": {"created_at": "2025-01-20T11:29:59.599", "updated_at": "2025-01-20T11:29:59.599", "provider": "telegram", "title": "<PERSON><PERSON>", "body": "<p style=\"margin:0; padding:0\">Master kebab - b<PERSON><PERSON><PERSON> bo’l<PERSON> muhab<PERSON> bilan</p><p style=\"margin:0; padding:0\"><br></p><p style=\"margin:0; padding:0\">• <PERSON><PERSON><PERSON><PERSON> taomingizni shahar ichida va ta<PERSON>, kunu tun har qanday vaqtda yetka<PERSON>b berish xizmati</p><p style=\"margin:0; padding:0\"><br></p><p style=\"margin:0; padding:0\">📞1059</p><p style=\"margin:0; padding:0\"><br></p><p style=\"margin:0; padding:0\"><br></p><p style=\"margin:0; padding:0\">📍Tsum, Navoiy sh.</p><p style=\"margin:0; padding:0\"><br></p><p style=\"margin:0; padding:0\">📍Islom <PERSON><PERSON><PERSON> ko‘chasi, 109 (Perfectum ofisi), <PERSON>vo<PERSON>y sh.</p><p style=\"margin:0; padding:0\"><br></p><p style=\"margin:0; padding:0\">📍Vosxod, <PERSON><PERSON><PERSON>y sh.</p><p style=\"margin:0; padding:0\"><br></p><p style=\"margin:0; padding:0\">📍Toshkent ko‘chasi, 28/1 (Markaziy poliklinika), Karmana</p>"}}, {"model": "notify.notification", "pk": 95, "fields": {"created_at": "2025-01-20T11:33:38.800", "updated_at": "2025-01-20T11:33:38.800", "provider": "telegram", "title": "<PERSON><PERSON>", "body": "<p style=\"margin:0; padding:0\">Master k<PERSON><PERSON> - b<PERSON><PERSON><PERSON> bo<PERSON><PERSON><PERSON> muh<PERSON><PERSON> bilan</p><p style=\"margin:0; padding:0\"><br style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">• <PERSON><PERSON><PERSON><PERSON> taomingizni shahar ichida va tashqa<PERSON>, kunu tun har qanday vaqtda yetkazib berish xizmati</p><p style=\"margin:0; padding:0\"><br style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📞1059</p><p style=\"margin:0; padding:0\"><br style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Tsum, Navoiy sh.</p><p style=\"margin:0; padding:0\"><br style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Islom <PERSON><PERSON>ov ko‘chasi, 109 (<PERSON><PERSON> ofisi), <PERSON><PERSON><PERSON><PERSON> sh.</p><p style=\"margin:0; padding:0\"><br style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Vosxod, Navoiy sh.</p><p style=\"margin:0; padding:0\"><br style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Toshkent ko‘chasi, 28/1 (Markaziy poliklinika), Karmana</p>"}}, {"model": "notify.notification", "pk": 96, "fields": {"created_at": "2025-01-20T11:35:58.902", "updated_at": "2025-01-20T11:35:58.902", "provider": "telegram", "title": "yangi", "body": "<p style=\"margin:0; padding:0\">Master k<PERSON><PERSON> - b<PERSON><PERSON><PERSON> bo’<PERSON><PERSON> muhab<PERSON> bilan</p><p style=\"margin:0; padding:0\"><br style=\"display:inline; margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">• <PERSON><PERSON><PERSON><PERSON> taomingizni shahar ichida va tashqa<PERSON>, kunu tun har qanday vaqtda yetkazib berish xizmati</p><p style=\"margin:0; padding:0\"><br style=\"display:inline; margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📞1059</p><p style=\"margin:0; padding:0\"><br style=\"display:inline; margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Tsum, Navoiy sh</p><p style=\"margin:0; padding:0\"><br style=\"display:inline; margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Islom <PERSON><PERSON><PERSON> ko‘chasi, 109 (<PERSON><PERSON> ofisi), <PERSON><PERSON><PERSON><PERSON> sh.</p><p style=\"margin:0; padding:0\"><br style=\"display:inline; margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Vosxod, Navoiy sh.</p><p style=\"margin:0; padding:0\"><br style=\"display:inline; margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Toshkent ko‘chasi, 28/1 (Markaziy poliklinika), Karmana</p>"}}, {"model": "notify.notification", "pk": 97, "fields": {"created_at": "2025-01-20T11:40:07.819", "updated_at": "2025-01-20T11:40:07.819", "provider": "telegram", "title": "Yangi test", "body": "<p style=\\\"margin:0; padding:0\\\">Master kebab - bolalar<PERSON> bo’<PERSON>gan muhabbat bilan</p><p style=\\\"margin:0; padding:0\\\"><br style=\\\"display:inline; margin:0; padding:0\\\"></p><p style=\\\"margin:0; padding:0\\\">• <PERSON><PERSON><PERSON><PERSON> taomingizni shahar ichida va tashqa<PERSON>ida, kunu tun har qanday vaqtda yetkazib berish xizmati</p><p style=\\\"margin:0; padding:0\\\"><br style=\\\"display:inline; margin:0; padding:0\\\"></p><p style=\\\"margin:0; padding:0\\\">📞1059</p><p style=\\\"margin:0; padding:0\\\"><br style=\\\"display:inline; margin:0; padding:0\\\"></p><p style=\\\"margin:0; padding:0\\\">📍Tsum, Navoiy sh</p><p style=\\\"margin:0; padding:0\\\"><br style=\\\"display:inline; margin:0; padding:0\\\"></p><p style=\\\"margin:0; padding:0\\\">📍Islom <PERSON><PERSON><PERSON> ko‘chasi, 109 (Perfectum ofisi), Navoiy sh.</p><p style=\\\"margin:0; padding:0\\\"><br style=\\\"display:inline; margin:0; padding:0\\\"></p><p style=\\\"margin:0; padding:0\\\">📍Vosxod, Navoiy sh.</p><p style=\\\"margin:0; padding:0\\\"><br style=\\\"display:inline; margin:0; padding:0\\\"></p><p style=\\\"margin:0; padding:0\\\">📍Toshkent ko‘chasi, 28/1 (Markaziy poliklinika), Karmana</p>"}}, {"model": "notify.notification", "pk": 98, "fields": {"created_at": "2025-01-20T12:04:08.865", "updated_at": "2025-01-20T12:04:08.865", "provider": "telegram", "title": "BYD kerakmi?)", "body": "<b>BYD avtomobili yoki ko‘plab qimmatbaho sovg‘alardan birini yutib olish imkoniyatini boy bermang! 🎉</b>\n\n2024-yil 20-dekabrdan 2025-yil 15-fevralgacha MaxWay restoranlarida yoki yetkazib berish xizmati or<PERSON><PERSON> x<PERSON> <b>Maxi Box</b> ni buyurtma qiling va sovg'alik stiker oling:\n\n• 🍔 MaxWay menyusidan tezkor mazali sovg‘alar  \n• 🎁 Qimmatbaho sovg‘alar uchun promokodlar: smartfonlar, aqlli soatlar, o‘yin pristavkalari  \n• 🚗 Final o‘yinda <b>BYD E2 avtomobilini</b> yutib olish imkoniyati!\n\nHar bir promokod g‘alaba qozonish imkoniyatingizni oshiradi! Bosh sovrinning final o‘yini <b>2025-yil 20-fevralda</b> MaxWay ning 20 yilligiga bag‘ishlangan bayramda bo‘lib o‘tadi 🤩\n\n<b>📌 <PERSON><PERSON><PERSON> ishtirok etish mumkin:</b>  \n1. Maxi Box buyurtma qiling  \n2. Stikerni oching va sovg‘angizni bilib oling  \n3. Promokodni <a href=\"https://t.me/maxway_promo_bot\">@maxway_promo_bot</a> orqali ro‘yxatdan o‘tkazing  \n\nAksiyada ishtirok eting va MaxWay ning 20 yilligini sovg‘alar va yorqin hissiyotlar bilan nishonlang! Batafsil ma’lumot: <a href=\"https://maxway.uz\">maxway.uz</a>\n"}}, {"model": "notify.notification", "pk": 99, "fields": {"created_at": "2025-01-20T12:05:31.892", "updated_at": "2025-01-20T12:05:31.892", "provider": "telegram", "title": "BYD kerakmi?)", "body": "<b>BYD avtomobili yoki ko‘plab qimmatbaho sovg‘alardan birini yutib olish imkoniyatini boy bermang! 🎉</b>\\n<p>2024-yil 20-dekabrdan 2025-yil 15-fevralgacha <b>MaxWay</b> restoranlarida yoki yetkazib berish xizmati or<PERSON>li xohlagan <b>Maxi Box</b> ni buyurtma qiling va sovg'alik stiker oling:</p>\\n<ul>\\n  <li>🍔 <b>MaxWay menyusidan tezkor mazali sovg‘alar</b></li>\\n  <li>🎁 <b>Qimmatbaho sovg‘alar uchun promokodlar:</b> smartfonlar, aqlli soatlar, o‘yin pristavkalari</li>\\n  <li>🚗 <b>Final o‘yinda BYD E2 avtomobilini yutib olish imkoniyati!</b></li>\\n</ul>\\n<p>Har bir promokod g‘alaba qozonish imkoniyatingizni oshiradi! Bosh sovrinning final o‘yini <b>2025-yil 20-fevral</b>da MaxWay ning 20 yilligiga bag‘ishlangan bayramda bo‘lib o‘tadi 🤩</p>\\n<b>📌 Qanday ishtirok etish mumkin:</b>\\n<ol>\\n  <li>Maxi Box buyurtma qiling</li>\\n  <li>Stikerni oching va sovg‘angizni bilib oling</li>\\n  <li>Promokodni <a href=\\\"https://t.me/maxway_promo_bot\\\">@maxway_promo_bot</a> orqali ro‘yxatdan o‘tkazing</li>\\n</ol>\\n<p>Aksiyada ishtirok eting va MaxWay ning 20 yilligini sovg‘alar va yorqin hissiyotlar bilan nishonlang! Batafsil ma’lumot: <a href=\\\"https://maxway.uz\\\">maxway.uz</a></p>\\n"}}, {"model": "notify.notification", "pk": 100, "fields": {"created_at": "2025-01-20T12:05:51.374", "updated_at": "2025-01-20T12:05:51.374", "provider": "telegram", "title": "BYD kerakmi?)", "body": "<b>BYD avtomobili yoki ko‘plab qimmatbaho sovg‘alardan birini yutib olish imkoniyatini boy bermang! 🎉</b>\\n<p>2024-yil 20-dekabrdan 2025-yil 15-fevralgacha <b>MaxWay</b> restoranlarida yoki yetkazib berish xizmati or<PERSON>li xohlagan <b>Maxi Box</b> ni buyurtma qiling va sovg'alik stiker oling:</p>\\n<ul>\\n  <li>🍔 <b>MaxWay menyusidan tezkor mazali sovg‘alar</b></li>\\n  <li>🎁 <b>Qimmatbaho sovg‘alar uchun promokodlar:</b> smartfonlar, aqlli soatlar, o‘yin pristavkalari</li>\\n  <li>🚗 <b>Final o‘yinda BYD E2 avtomobilini yutib olish imkoniyati!</b></li>\\n</ul>\\n<p>Har bir promokod g‘alaba qozonish imkoniyatingizni oshiradi! Bosh sovrinning final o‘yini <b>2025-yil 20-fevral</b>da MaxWay ning 20 yilligiga bag‘ishlangan bayramda bo‘lib o‘tadi 🤩</p>\\n<b>📌 Qanday ishtirok etish mumkin:</b>\\n<ol>\\n  <li>Maxi Box buyurtma qiling</li>\\n  <li>Stikerni oching va sovg‘angizni bilib oling</li>\\n  <li>Promokodni <a href=\\\"https://t.me/maxway_promo_bot\\\">@maxway_promo_bot</a> orqali ro‘yxatdan o‘tkazing</li>\\n</ol>\\n<p>Aksiyada ishtirok eting va MaxWay ning 20 yilligini sovg‘alar va yorqin hissiyotlar bilan nishonlang! Batafsil ma’lumot: <a href=\\\"https://maxway.uz\\\">maxway.uz</a></p>\\n"}}, {"model": "notify.notification", "pk": 101, "fields": {"created_at": "2025-01-20T12:06:01.077", "updated_at": "2025-01-20T12:06:01.077", "provider": "telegram", "title": "BYD kerakmi?)", "body": "<b>BYD avtomobili yoki ko‘plab qimmatbaho sovg‘alardan birini yutib olish imkoniyatini boy bermang! 🎉</b>\\n<p>2024-yil 20-dekabrdan 2025-yil 15-fevralgacha <b>MaxWay</b> restoranlarida yoki yetkazib berish xizmati or<PERSON>li xohlagan <b>Maxi Box</b> ni buyurtma qiling va sovg'alik stiker oling:</p>\\n<ul>\\n  <li>🍔 <b>MaxWay menyusidan tezkor mazali sovg‘alar</b></li>\\n  <li>🎁 <b>Qimmatbaho sovg‘alar uchun promokodlar:</b> smartfonlar, aqlli soatlar, o‘yin pristavkalari</li>\\n  <li>🚗 <b>Final o‘yinda BYD E2 avtomobilini yutib olish imkoniyati!</b></li>\\n</ul>\\n<p>Har bir promokod g‘alaba qozonish imkoniyatingizni oshiradi! Bosh sovrinning final o‘yini <b>2025-yil 20-fevral</b>da MaxWay ning 20 yilligiga bag‘ishlangan bayramda bo‘lib o‘tadi 🤩</p>\\n<b>📌 Qanday ishtirok etish mumkin:</b>\\n<ol>\\n  <li>Maxi Box buyurtma qiling</li>\\n  <li>Stikerni oching va sovg‘angizni bilib oling</li>\\n  <li>Promokodni <a href=\\\"https://t.me/maxway_promo_bot\\\">@maxway_promo_bot</a> orqali ro‘yxatdan o‘tkazing</li>\\n</ol>\\n<p>Aksiyada ishtirok eting va MaxWay ning 20 yilligini sovg‘alar va yorqin hissiyotlar bilan nishonlang! Batafsil ma’lumot: <a href=\\\"https://maxway.uz\\\">maxway.uz</a></p>\\n"}}, {"model": "notify.notification", "pk": 102, "fields": {"created_at": "2025-01-20T12:06:47.821", "updated_at": "2025-01-20T12:06:47.821", "provider": "telegram", "title": "BYD kerakmi?)", "body": "Hellov"}}, {"model": "notify.notification", "pk": 103, "fields": {"created_at": "2025-01-20T12:07:04.805", "updated_at": "2025-01-20T12:07:04.805", "provider": "telegram", "title": "BYD kerakmi?)", "body": ""}}, {"model": "notify.notification", "pk": 104, "fields": {"created_at": "2025-01-20T12:24:20.752", "updated_at": "2025-01-20T12:24:20.752", "provider": "telegram", "title": "Yangi 2", "body": "<p style=\"margin:0; padding:0\">Master kebab - b<PERSON><PERSON><PERSON> bo’l<PERSON> muh<PERSON><PERSON> bilan</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">• <PERSON><PERSON><PERSON><PERSON> taomingizni shahar ichida va ta<PERSON>, kunu tun har qanday vaqtda yetkazib berish xizmati</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📞1059</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Tsum, Navoiy sh.</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Islom <PERSON><PERSON><PERSON> ko‘chasi, 109 (Perfectum ofisi), Navoiy sh.</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Vosxod, Navoiy sh.</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Toshkent ko‘chasi, 28/1 (Markaziy poliklinika), <PERSON><PERSON><PERSON></p><p style=\"margin:0; padding:0\">_______________________________________</p><p style=\"margin:0; padding:0\">Master kebab - с любовью к детям</p><p style=\"margin:0; padding:0\">• Доставка любимого блюда до любой точки города и за его пределами , в любое время суток</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📞1059</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Цум, г.Навои&nbsp;</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍ул. Ислама Каримова, 109 (офис Perfectum), г. Навои</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍Восход , г.Навои</p><p style=\"margin:0; padding:0\"></p><p style=\"margin:0; padding:0\">📍ул. Ташкентская, 28/1 (Центральная поликлиника), Кармана</p>"}}, {"model": "notify.notificationaccepters", "pk": 150, "fields": {"notification": 91, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 151, "fields": {"notification": 91, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 152, "fields": {"notification": 91, "notification_identity": 2172, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 153, "fields": {"notification": 91, "notification_identity": 2171, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 154, "fields": {"notification": 91, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 155, "fields": {"notification": 91, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 156, "fields": {"notification": 91, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 157, "fields": {"notification": 92, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 158, "fields": {"notification": 92, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 159, "fields": {"notification": 92, "notification_identity": 2173, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 160, "fields": {"notification": 92, "notification_identity": 2174, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 161, "fields": {"notification": 92, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 162, "fields": {"notification": 92, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 163, "fields": {"notification": 92, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 164, "fields": {"notification": 93, "notification_identity": 1037, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 165, "fields": {"notification": 93, "notification_identity": 1042, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 166, "fields": {"notification": 93, "notification_identity": 1036, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 167, "fields": {"notification": 93, "notification_identity": 1040, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 168, "fields": {"notification": 93, "notification_identity": 1041, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 169, "fields": {"notification": 93, "notification_identity": 1038, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 170, "fields": {"notification": 93, "notification_identity": 1039, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 171, "fields": {"notification": 94, "notification_identity": 1043, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 172, "fields": {"notification": 94, "notification_identity": 1049, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 173, "fields": {"notification": 94, "notification_identity": 1044, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 174, "fields": {"notification": 94, "notification_identity": 1046, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 175, "fields": {"notification": 94, "notification_identity": 1048, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 176, "fields": {"notification": 94, "notification_identity": 1045, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 177, "fields": {"notification": 94, "notification_identity": 1047, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 178, "fields": {"notification": 95, "notification_identity": 1050, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 179, "fields": {"notification": 95, "notification_identity": 1052, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 180, "fields": {"notification": 95, "notification_identity": 1053, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 181, "fields": {"notification": 95, "notification_identity": 1056, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 182, "fields": {"notification": 95, "notification_identity": 1054, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 183, "fields": {"notification": 95, "notification_identity": 1051, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 184, "fields": {"notification": 95, "notification_identity": 1055, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 185, "fields": {"notification": 96, "notification_identity": 1057, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 186, "fields": {"notification": 96, "notification_identity": 1058, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 187, "fields": {"notification": 96, "notification_identity": 1063, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 188, "fields": {"notification": 96, "notification_identity": 1059, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 189, "fields": {"notification": 96, "notification_identity": 1061, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 190, "fields": {"notification": 96, "notification_identity": 1060, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 191, "fields": {"notification": 96, "notification_identity": 1062, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 192, "fields": {"notification": 97, "notification_identity": 1065, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 193, "fields": {"notification": 97, "notification_identity": 1068, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 194, "fields": {"notification": 97, "notification_identity": 1067, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 195, "fields": {"notification": 97, "notification_identity": 1069, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 196, "fields": {"notification": 97, "notification_identity": 1070, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 197, "fields": {"notification": 97, "notification_identity": 1064, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 198, "fields": {"notification": 97, "notification_identity": 1066, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 199, "fields": {"notification": 98, "notification_identity": 1073, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 200, "fields": {"notification": 98, "notification_identity": 1071, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 201, "fields": {"notification": 98, "notification_identity": 1072, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 202, "fields": {"notification": 98, "notification_identity": 1076, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 203, "fields": {"notification": 98, "notification_identity": 1074, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 204, "fields": {"notification": 98, "notification_identity": 1075, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 205, "fields": {"notification": 98, "notification_identity": 1077, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 206, "fields": {"notification": 99, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 207, "fields": {"notification": 99, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 208, "fields": {"notification": 99, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 209, "fields": {"notification": 99, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 210, "fields": {"notification": 99, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 211, "fields": {"notification": 99, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 212, "fields": {"notification": 99, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 213, "fields": {"notification": 99, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 214, "fields": {"notification": 99, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 215, "fields": {"notification": 99, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 216, "fields": {"notification": 99, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 217, "fields": {"notification": 99, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 218, "fields": {"notification": 99, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 219, "fields": {"notification": 99, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 220, "fields": {"notification": 99, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 221, "fields": {"notification": 99, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 222, "fields": {"notification": 99, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 223, "fields": {"notification": 99, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 224, "fields": {"notification": 99, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 225, "fields": {"notification": 99, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 226, "fields": {"notification": 99, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 227, "fields": {"notification": 99, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 228, "fields": {"notification": 99, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 229, "fields": {"notification": 99, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 230, "fields": {"notification": 99, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 231, "fields": {"notification": 99, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 232, "fields": {"notification": 99, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 233, "fields": {"notification": 99, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 234, "fields": {"notification": 100, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 235, "fields": {"notification": 100, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 236, "fields": {"notification": 100, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 237, "fields": {"notification": 100, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 238, "fields": {"notification": 100, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 239, "fields": {"notification": 100, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 240, "fields": {"notification": 100, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 241, "fields": {"notification": 100, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 242, "fields": {"notification": 100, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 243, "fields": {"notification": 100, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 244, "fields": {"notification": 100, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 245, "fields": {"notification": 100, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 246, "fields": {"notification": 100, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 247, "fields": {"notification": 100, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 248, "fields": {"notification": 101, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 249, "fields": {"notification": 101, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 250, "fields": {"notification": 101, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 251, "fields": {"notification": 101, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 252, "fields": {"notification": 101, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 253, "fields": {"notification": 101, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 254, "fields": {"notification": 101, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 255, "fields": {"notification": 100, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 256, "fields": {"notification": 100, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 257, "fields": {"notification": 100, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 258, "fields": {"notification": 100, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 259, "fields": {"notification": 100, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 260, "fields": {"notification": 100, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 261, "fields": {"notification": 100, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 262, "fields": {"notification": 101, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 263, "fields": {"notification": 101, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 264, "fields": {"notification": 101, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 265, "fields": {"notification": 101, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 266, "fields": {"notification": 101, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 267, "fields": {"notification": 101, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 268, "fields": {"notification": 101, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 269, "fields": {"notification": 100, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 270, "fields": {"notification": 100, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 271, "fields": {"notification": 100, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 272, "fields": {"notification": 100, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 273, "fields": {"notification": 100, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 274, "fields": {"notification": 100, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 275, "fields": {"notification": 100, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 276, "fields": {"notification": 101, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 277, "fields": {"notification": 101, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 278, "fields": {"notification": 101, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 279, "fields": {"notification": 101, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 280, "fields": {"notification": 101, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 281, "fields": {"notification": 101, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 282, "fields": {"notification": 101, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 283, "fields": {"notification": 101, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 284, "fields": {"notification": 101, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 285, "fields": {"notification": 101, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 286, "fields": {"notification": 101, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 287, "fields": {"notification": 101, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 288, "fields": {"notification": 101, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 289, "fields": {"notification": 101, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "processing"}}, {"model": "notify.notificationaccepters", "pk": 290, "fields": {"notification": 102, "notification_identity": 1079, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 291, "fields": {"notification": 102, "notification_identity": 1078, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 292, "fields": {"notification": 102, "notification_identity": 1080, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 293, "fields": {"notification": 102, "notification_identity": 1081, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 294, "fields": {"notification": 102, "notification_identity": 1082, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 295, "fields": {"notification": 102, "notification_identity": 1083, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 296, "fields": {"notification": 102, "notification_identity": 1084, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 297, "fields": {"notification": 103, "notification_identity": null, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 298, "fields": {"notification": 103, "notification_identity": null, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 299, "fields": {"notification": 103, "notification_identity": null, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 300, "fields": {"notification": 103, "notification_identity": null, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 301, "fields": {"notification": 103, "notification_identity": null, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 302, "fields": {"notification": 103, "notification_identity": null, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 303, "fields": {"notification": 103, "notification_identity": null, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 304, "fields": {"notification": 104, "notification_identity": 1087, "user_id": 6231040880, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 305, "fields": {"notification": 104, "notification_identity": 1089, "user_id": 482569855, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 306, "fields": {"notification": 104, "notification_identity": 1088, "user_id": 6873479930, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 307, "fields": {"notification": 104, "notification_identity": 1085, "user_id": 2105729169, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 308, "fields": {"notification": 104, "notification_identity": 1090, "user_id": 552563440, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 309, "fields": {"notification": 104, "notification_identity": 1091, "user_id": 1275009699, "user_type": "telegram", "status": "sent"}}, {"model": "notify.notificationaccepters", "pk": 310, "fields": {"notification": 104, "notification_identity": 1086, "user_id": *********, "user_type": "telegram", "status": "sent"}}, {"model": "operations.operations", "pk": 1, "fields": {"last_login": null, "is_superuser": true, "created_at": "2024-12-13T02:34:18.239", "updated_at": "2024-12-13T02:34:18.239", "phone": "998888351717", "name": "", "password": "pbkdf2_sha256$720000$4fHFK6A2YKn8EUuTom9QRF$as7JTiKAzDf3ar4WLBTFsu63HgJvbFo5nXqB3aIJt0g=", "passport": null, "external_id": null, "organization": null, "role": "admin", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": true, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 2, "fields": {"last_login": null, "is_superuser": false, "created_at": "2024-12-21T10:49:07.761", "updated_at": "2024-12-21T10:49:08.016", "phone": "998901234570", "name": "API Test Marketolog", "password": "pbkdf2_sha256$720000$YPggd6E9bcQumWyEykinMP$KjRBoXUqKNbECYQJ5X965gjT/N/CqfHv90DZW8sOtwo=", "passport": null, "external_id": null, "organization": null, "role": "marketolog", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 3, "fields": {"last_login": null, "is_superuser": false, "created_at": "2024-12-21T11:00:58.350", "updated_at": "2024-12-21T11:02:14.798", "phone": "998888351700", "name": "<PERSON> Updated successfully", "password": "pbkdf2_sha256$720000$m6armyYV0qXae0ERDVNuXH$1o2vuvDPCJEcxFQ3DFu2G4cI08XcJEHHRfu2mNXyiAg=", "passport": "*********", "external_id": null, "organization": 2, "role": "marketolog", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 4, "fields": {"last_login": null, "is_superuser": false, "created_at": "2024-12-22T15:47:23.057", "updated_at": "2024-12-22T15:47:23.280", "phone": "998888351739", "name": "<PERSON>tor", "password": "pbkdf2_sha256$720000$UoBoZ8f37XL4xSr9ZaZ9CK$+U5/VFPdvBHTMoDqglsMTRI39Yek8YX+XaXp8LeETeo=", "passport": "*********", "external_id": null, "organization": 2, "role": "operator", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 5, "fields": {"last_login": null, "is_superuser": false, "created_at": "2024-12-23T12:32:53.297", "updated_at": "2024-12-23T12:32:53.519", "phone": "998888351729", "name": "<PERSON>tor", "password": "pbkdf2_sha256$720000$TMWQCH4vt5GTLHNLWsi6GC$8jvOpohfI2ACgatDd5SVp4iiRjJdkCqLZBcGz8vr26E=", "passport": "*********", "external_id": null, "organization": 2, "role": "operator", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 6, "fields": {"last_login": null, "is_superuser": false, "created_at": "2024-12-24T16:01:34.365", "updated_at": "2024-12-24T16:01:34.585", "phone": "998888351799", "name": "<PERSON> (Vosxod)", "password": "pbkdf2_sha256$720000$JEjo704iGblxhCNmSB4jIE$7ojgCn4ra6M7Y0ybhiaMKkH1hqik5dYQ8pzV6KSb13g=", "passport": "*********", "external_id": null, "organization": 2, "role": "marketolog", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 7, "fields": {"last_login": null, "is_superuser": false, "created_at": "2025-01-08T12:51:20.181", "updated_at": "2025-01-08T12:51:20.406", "phone": "998888352021", "name": "<PERSON> Manager V<PERSON><PERSON><PERSON>", "password": "pbkdf2_sha256$720000$Q33Pt2FIYeBzSuWziENd6K$RzWR1OCrhIyqbwonTksOgtcJlWOcbZo7XyeaIB5oSAM=", "passport": "*********", "external_id": null, "organization": 2, "role": "managers", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 8, "fields": {"last_login": null, "is_superuser": false, "created_at": "2025-01-10T18:47:14.483", "updated_at": "2025-01-10T18:47:14.705", "phone": "998888352022", "name": "<PERSON><PERSON><PERSON> pro max manager", "password": "pbkdf2_sha256$720000$NAPmB3J1bHObFmIP8z8bAG$Y3DcrDy3Cy8EpmTCe6COw/XmpiIyq7uhE9XB0cHJOAI=", "passport": "*********", "external_id": null, "organization": 2, "role": "managers", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "operations.operations", "pk": 9, "fields": {"last_login": null, "is_superuser": false, "created_at": "2025-01-25T16:35:41.785", "updated_at": "2025-01-25T16:35:42.017", "phone": "998888352027", "name": "<PERSON> Manager V<PERSON><PERSON><PERSON>", "password": "pbkdf2_sha256$720000$21V1D0HaH4pkdxMG4zDmAl$UIqO+ovUZPYSiEuiIwEcj1xF6KHTNq7ahbifvCvX5Kc=", "passport": "*********", "external_id": null, "organization": 2, "role": "managers", "is_blocked": false, "is_deleted": false, "is_active": true, "is_verified": false, "is_staff": false, "groups": [], "user_permissions": []}}, {"model": "hr.jobapplication", "pk": 1, "fields": {"created_at": "2024-12-29T21:10:59.436", "updated_at": "2024-12-29T21:10:59.436", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "**********", "job_position": "<PERSON><PERSON><PERSON>", "working_hours": "Kunduzgi smena (07:00 - 19:00)", "birth_date": "2024-11-30", "address": "Marifatchi 54", "education": "no", "language_uz": "uz", "language_ru": "ru", "previous_experience": "", "health_issues": false, "photo": "applicant_photos/__3.jpeg", "gender": "Uz", "status": "APPROVED"}}, {"model": "hr.jobapplication", "pk": 2, "fields": {"created_at": "2025-01-07T21:58:50.471", "updated_at": "2025-01-07T21:58:50.471", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "99**********", "job_position": "Oshpaz", "working_hours": "Kunduzgi smena (07:00 - 19:00)", "birth_date": "2025-01-07", "address": "Xonqa, Uzbekistan", "education": "no", "language_uz": "O'rta", "language_ru": "Past", "previous_experience": "<PERSON><PERSON> tajriba yo'q", "health_issues": true, "photo": "applicant_photos/__3.jpeg", "gender": "Erkak", "status": "IN_REVIEW"}}, {"model": "hr.jobapplication", "pk": 3, "fields": {"created_at": "2025-01-20T14:16:48.837", "updated_at": "2025-01-20T14:16:48.837", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "**********", "job_position": "CHEF", "working_hours": "Dushanba-Yakshanba 10:00-18:00", "birth_date": "1999-06-20", "address": "<PERSON><PERSON><PERSON><PERSON> shahri", "education": "masters", "language_uz": "advanced", "language_ru": "intermediate", "previous_experience": "<PERSON><PERSON><PERSON><PERSON><PERSON> yo'q", "health_issues": false, "photo": "applicant_photos/photo_2025-01-19_23-56-43.jpg", "gender": "male", "status": "pending"}}, {"model": "hr.jobapplication", "pk": 4, "fields": {"created_at": "2025-01-20T18:58:53.507", "updated_at": "2025-01-20T18:58:53.507", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "**********", "job_position": "Oshpaz", "working_hours": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e", "birth_date": "2009-01-09", "address": "Marifatchi 54", "education": "masters", "language_uz": "none", "language_ru": "beginner", "previous_experience": "yo'q", "health_issues": false, "photo": "applicant_photos/_.jpeg", "gender": "male", "status": "pending"}}, {"model": "hr.jobapplication", "pk": 5, "fields": {"created_at": "2025-01-20T18:58:54.360", "updated_at": "2025-01-20T18:58:54.360", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "**********", "job_position": "Oshpaz", "working_hours": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>e", "birth_date": "2009-01-09", "address": "Marifatchi 54", "education": "masters", "language_uz": "none", "language_ru": "beginner", "previous_experience": "yo'q", "health_issues": false, "photo": "applicant_photos/_.jpeg", "gender": "male", "status": "pending"}}, {"model": "hr.jobapplication", "pk": 6, "fields": {"created_at": "2025-01-24T08:20:08.470", "updated_at": "2025-01-24T08:20:08.470", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "+99**********", "job_position": "Oshpaz", "working_hours": "Dushanba-Juma 8:00-19:00", "birth_date": "2009-01-11", "address": "Uzbekistan", "education": "bachelors", "language_uz": "intermediate", "language_ru": "none", "previous_experience": "ok", "health_issues": false, "photo": "applicant_photos/image_2024-12-30_16-11-13.png", "gender": "male", "status": "pending"}}, {"model": "hr.jobapplication", "pk": 7, "fields": {"created_at": "2025-01-24T08:25:24.591", "updated_at": "2025-01-24T08:25:24.591", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "944536020", "job_position": "Oshpaz", "working_hours": "Dushanba-Juma 9:00-19:00", "birth_date": "2009-02-22", "address": "Marifatchi 54", "education": "high_school", "language_uz": "none", "language_ru": "beginner", "previous_experience": "<PERSON><PERSON> par<PERSON>h", "health_issues": false, "photo": "applicant_photos/_.jpeg", "gender": "male", "status": "pending"}}, {"model": "hr.jobapplication", "pk": 8, "fields": {"created_at": "2025-01-24T08:25:37.694", "updated_at": "2025-01-24T08:25:37.694", "full_name": "<PERSON><PERSON><PERSON>", "phone_number": "944536020", "job_position": "Oshpaz", "working_hours": "Dushanba-Juma 9:00-19:00", "birth_date": "2009-02-22", "address": "Marifatchi 54", "education": "high_school", "language_uz": "none", "language_ru": "beginner", "previous_experience": "<PERSON><PERSON> par<PERSON>h", "health_issues": false, "photo": "applicant_photos/_.jpeg", "gender": "male", "status": "pending"}}, {"model": "hr.jobapplication", "pk": 9, "fields": {"created_at": "2025-01-24T18:49:23.793", "updated_at": "2025-01-24T18:49:23.793", "full_name": "A", "phone_number": "944536020", "job_position": "2", "working_hours": "day", "birth_date": "2009-02-01", "address": "Marifatchi 54", "education": "higher", "language_uz": "none", "language_ru": "none", "previous_experience": "AA", "health_issues": false, "photo": "applicant_photos/_.jpeg", "gender": "male", "status": "pending"}}, {"model": "hr.job<PERSON>", "pk": 2, "fields": {"created_at": "2025-01-20T09:48:59.399", "updated_at": "2025-01-20T09:48:59.399", "name": "Oshpaz", "name_uz": "Oshpaz", "name_ru": "<PERSON><PERSON><PERSON><PERSON>", "name_en": "Chef", "description": null}}, {"model": "token_blacklist.outstandingtoken", "pk": 1, "fields": {"user": 3, "jti": "9f3b45497dd14aeba8717c496239faf3", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************.Ph9_3Yvjn0gbmxWVEnAbiEz4Yl-NkIEMRk61w1tjPE4", "created_at": "2024-12-14T09:42:58.311", "expires_at": "2025-12-14T09:42:58"}}, {"model": "token_blacklist.outstandingtoken", "pk": 2, "fields": {"user": null, "jti": "4ed6fbdef898449dbd72e3989b4eb2cc", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************._nEvbk-fenSetqprAaURsyDDahDpkcw0tyJ8akmtyt0", "created_at": "2024-12-22T10:47:01.291", "expires_at": "2025-12-22T10:47:01"}}, {"model": "token_blacklist.outstandingtoken", "pk": 3, "fields": {"user": null, "jti": "ca683622dbd6477f9479cefb70a7c287", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2NjQ3NTA1NSwiaWF0IjoxNzM0OTM5MDU1LCJqdGkiOiJjYTY4MzYyMmRiZDY0NzdmOTQ3OWNlZmI3MGE3YzI4NyIsInVzZXJfaWQiOjJ9.tkg9Bq8WQ4O65vH9R36FNv4jLaVaChc_gdXJH9_KCDY", "created_at": "2024-12-23T07:30:55.796", "expires_at": "2025-12-23T07:30:55"}}, {"model": "token_blacklist.outstandingtoken", "pk": 4, "fields": {"user": 4, "jti": "17c86f17c81f4bc8bf34c56df0aceb38", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2NzY0OTYzOSwiaWF0IjoxNzM2MTEzNjM5LCJqdGkiOiIxN2M4NmYxN2M4MWY0YmM4YmYzNGM1NmRmMGFjZWIzOCIsInVzZXJfaWQiOjR9.W1AO5w7koF_tJCA8RXZgf90wBWAyYQbgkFzg-MO2MFE", "created_at": "2025-01-05T21:47:19.067", "expires_at": "2026-01-05T21:47:19"}}, {"model": "token_blacklist.outstandingtoken", "pk": 5, "fields": {"user": null, "jti": "68781564533c4e6f85854047b11f0a03", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2NzgzNTIzMiwiaWF0IjoxNzM2Mjk5MjMyLCJqdGkiOiI2ODc4MTU2NDUzM2M0ZTZmODU4NTQwNDdiMTFmMGEwMyIsInVzZXJfaWQiOjJ9.Z_UzekrdDNzsevyTavC2oaZP2VdO-mIPHg6cmkFp5nY", "created_at": "2025-01-08T01:20:32.914", "expires_at": "2026-01-08T01:20:32"}}, {"model": "token_blacklist.outstandingtoken", "pk": 6, "fields": {"user": null, "jti": "2813eced7eae4724ab02eeff5a1dd2fd", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2NzgzNTQ0MSwiaWF0IjoxNzM2Mjk5NDQxLCJqdGkiOiIyODEzZWNlZDdlYWU0NzI0YWIwMmVlZmY1YTFkZDJmZCIsInVzZXJfaWQiOjJ9.CRc99Nyka7S_F_33lsOhOuwofylUqn0a7h7OtzGIo1c", "created_at": "2025-01-08T01:24:01.039", "expires_at": "2026-01-08T01:24:01"}}, {"model": "token_blacklist.outstandingtoken", "pk": 7, "fields": {"user": null, "jti": "10491e8d8f964666a416d1ceaf43ccad", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2NzgzNTcxMSwiaWF0IjoxNzM2Mjk5NzExLCJqdGkiOiIxMDQ5MWU4ZDhmOTY0NjY2YTQxNmQxY2VhZjQzY2NhZCIsInVzZXJfaWQiOjExfQ.q7KY24WO7Kat6Po7lmsGIoWy4TyeGkb2iwvlb8lrZK8", "created_at": "2025-01-08T01:28:31.648", "expires_at": "2026-01-08T01:28:31"}}, {"model": "token_blacklist.outstandingtoken", "pk": 8, "fields": {"user": null, "jti": "84b97188f8e5497c8ec460e34c663d23", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2NzgzNjA4MCwiaWF0IjoxNzM2MzAwMDgwLCJqdGkiOiI4NGI5NzE4OGY4ZTU0OTdjOGVjNDYwZTM0YzY2M2QyMyIsInVzZXJfaWQiOjEyfQ.rys_PJnTdl0ou7lc0QO9pY4N77_uxv3e0P9E7Jt4fu4", "created_at": "2025-01-08T01:34:40.714", "expires_at": "2026-01-08T01:34:40"}}, {"model": "token_blacklist.outstandingtoken", "pk": 9, "fields": {"user": null, "jti": "a8766830a3474260875a1122a938afb2", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2Nzg2NDM5MywiaWF0IjoxNzM2MzI4MzkzLCJqdGkiOiJhODc2NjgzMGEzNDc0MjYwODc1YTExMjJhOTM4YWZiMiIsInVzZXJfaWQiOjEzfQ.K6Pqj3ddklQU1AGerGERZGEDLnVX0zOBDAdpA98GuLI", "created_at": "2025-01-08T09:26:33.687", "expires_at": "2026-01-08T09:26:33"}}, {"model": "token_blacklist.outstandingtoken", "pk": 10, "fields": {"user": null, "jti": "9629ba353be74115b931e52b63a77885", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2Nzg2NDU1MCwiaWF0IjoxNzM2MzI4NTUwLCJqdGkiOiI5NjI5YmEzNTNiZTc0MTE1YjkzMWU1MmI2M2E3Nzg4NSIsInVzZXJfaWQiOjE0fQ.aXGQDhQZvAk0DiXmSifo0Wvsx8TgonFD-jzTuR0tT0Q", "created_at": "2025-01-08T09:29:10.650", "expires_at": "2026-01-08T09:29:10"}}, {"model": "token_blacklist.outstandingtoken", "pk": 11, "fields": {"user": null, "jti": "05530b3984f14bc597aaee85f293299a", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2Nzg2NDYxNywiaWF0IjoxNzM2MzI4NjE3LCJqdGkiOiIwNTUzMGIzOTg0ZjE0YmM1OTdhYWVlODVmMjkzMjk5YSIsInVzZXJfaWQiOjE1fQ.TgDVnlce9oaIXLJYJJeXAixWeii-rvjtLQZjjrlsHXU", "created_at": "2025-01-08T09:30:17.096", "expires_at": "2026-01-08T09:30:17"}}, {"model": "token_blacklist.outstandingtoken", "pk": 12, "fields": {"user": 16, "jti": "a673ac7daadb4d2e8d34c7a1edc306ea", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2Nzg2NDc3NiwiaWF0IjoxNzM2MzI4Nzc2LCJqdGkiOiJhNjczYWM3ZGFhZGI0ZDJlOGQzNGM3YTFlZGMzMDZlYSIsInVzZXJfaWQiOjE2fQ.4rorrfPEc6LeLmqwoPuzjt9xRUnUjsLT0ptxwecPPO8", "created_at": "2025-01-08T09:32:56.253", "expires_at": "2026-01-08T09:32:56"}}, {"model": "token_blacklist.outstandingtoken", "pk": 13, "fields": {"user": 17, "jti": "ca1d4512f6974fa88c4af71428303ae2", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2Nzg2NTIwNSwiaWF0IjoxNzM2MzI5MjA1LCJqdGkiOiJjYTFkNDUxMmY2OTc0ZmE4OGM0YWY3MTQyODMwM2FlMiIsInVzZXJfaWQiOjE3fQ.Q80mbqUqMk6vbkahrBZBLIsy0sjb99fOkdQQ9z2lLNQ", "created_at": "2025-01-08T09:40:05.177", "expires_at": "2026-01-08T09:40:05"}}, {"model": "token_blacklist.outstandingtoken", "pk": 14, "fields": {"user": 4, "jti": "d1bc42cc77314479aa9bd21543aa8b00", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2NzkwMjQxNiwiaWF0IjoxNzM2MzY2NDE2LCJqdGkiOiJkMWJjNDJjYzc3MzE0NDc5YWE5YmQyMTU0M2FhOGIwMCIsInVzZXJfaWQiOjR9.d3UscbdkM6ILDKOU1gLwUpE_hiTtLHVoz0WM7HbZOeI", "created_at": "2025-01-08T20:00:16.621", "expires_at": "2026-01-08T20:00:16"}}, {"model": "token_blacklist.outstandingtoken", "pk": 15, "fields": {"user": 19, "jti": "d8bcbc98a39c42a89cfc356ffb013fe6", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2ODE5OTU1NSwiaWF0IjoxNzM2NjYzNTU1LCJqdGkiOiJkOGJjYmM5OGEzOWM0MmE4OWNmYzM1NmZmYjAxM2ZlNiIsInVzZXJfaWQiOjE5fQ.VjXgRXb4AN1EYryflpxYhvc5ZHCwSbdxiA9QBFgOi7I", "created_at": "2025-01-12T06:32:35.411", "expires_at": "2026-01-12T06:32:35"}}, {"model": "token_blacklist.outstandingtoken", "pk": 16, "fields": {"user": 19, "jti": "4aae297772924a70b64eea3cf129b02e", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2ODkwMDkxMiwiaWF0IjoxNzM3MzY0OTEyLCJqdGkiOiI0YWFlMjk3NzcyOTI0YTcwYjY0ZWVhM2NmMTI5YjAyZSIsInVzZXJfaWQiOjE5fQ.NSvvdaFo09UmSM0c1q7iCUMYGj_v2fiemuBMAYuI2yk", "created_at": "2025-01-20T09:21:52.679", "expires_at": "2026-01-20T09:21:52"}}, {"model": "token_blacklist.outstandingtoken", "pk": 17, "fields": {"user": 3, "jti": "ba5e0e00e9ef45c88c176af661574ea1", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2ODk3NTM4MSwiaWF0IjoxNzM3NDM5MzgxLCJqdGkiOiJiYTVlMGUwMGU5ZWY0NWM4OGMxNzZhZjY2MTU3NGVhMSIsInVzZXJfaWQiOjN9.EjK-Sr1dt_ZkqjSA2AFSL7q3kpUlA7fM4C1hCo_LL3Y", "created_at": "2025-01-21T06:03:01.220", "expires_at": "2026-01-21T06:03:01"}}, {"model": "token_blacklist.outstandingtoken", "pk": 18, "fields": {"user": 22, "jti": "7fe23ca8fc304c339297d19d9cc6ddb5", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTc2OTE3MjA4OCwiaWF0IjoxNzM3NjM2MDg4LCJqdGkiOiI3ZmUyM2NhOGZjMzA0YzMzOTI5N2QxOWQ5Y2M2ZGRiNSIsInVzZXJfaWQiOjIyfQ.iXjrw29Ut5dKIWY-oujyZjd9IA2atXBb8vnwBIEJhgU", "created_at": "2025-01-23T12:41:28.742", "expires_at": "2026-01-23T12:41:28"}}]