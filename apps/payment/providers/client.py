"""
This module provides the implementation of the provider client using the factory pattern.
It supports the creation of different payment providers based on the specified payment method.
"""

from apps.payment.enums.method import PaymentMethodEnum
from apps.payment.models.credentials import ProviderCredentials

from apps.payment.providers.interface import IProvider
from apps.payment.providers.click.client import ClickClient
from apps.payment.providers.payme.client import PaymeClient


class ProviderFactory:
    """
    The ProviderFactory class is responsible for creating instances of payment providers.
    It uses the factory pattern to instantiate the appropriate provider based on the given payment method.
    """
    @classmethod
    def create_provider(cls, provider_type, organization_id) -> IProvider:
        """
        Create and return an instance of the appropriate payment provider.

        Args:
            provider_type (str): The type of the payment provider.
            organization_id (int): The ID of the organization.

        Returns:
            IProvider: An instance of the payment provider.

        Raises:
            NotImplementedError: If the specified provider type is not implemented.
        """
        if provider_type not in [
            PaymentMethodEnum.PAYME.value,
            PaymentMethodEnum.CLICK.value,
        ]:
            raise NotImplementedError(f"Provider: {provider_type} not implemented yet")

        credentials = ProviderCredentials.get_by_payment_method(
            payment_method=provider_type,
            organization_id=organization_id
        )
        credentials.decrypt_credentials()

        if provider_type == PaymentMethodEnum.PAYME:
            return PaymeClient(
                payme_id=credentials.identification,
                payme_key=credentials.key,
                payme_url=credentials.url,
                payme_return_url=credentials.return_url,
                payme_account=credentials.transaction_key
            )

        elif provider_type == PaymentMethodEnum.CLICK:
            return ClickClient(
                service_id=credentials.service_id,
                merchant_id=credentials.identification
            )
