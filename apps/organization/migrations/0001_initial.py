# Generated by Django 5.0.6 on 2024-08-20 11:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('external_id', models.CharField(db_index=True, unique=True)),
                ('code', models.CharField(blank=True, max_length=255, null=True)),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('in_use', models.BooleanField(default=False)),
                ('is_closed', models.BooleanField(default=False)),
                ('is_default', models.BooleanField(default=False)),
                ('delivery_duration', models.IntegerField(blank=True, null=True)),
                ('self_service_duration', models.IntegerField(blank=True, null=True)),
                ('default_min_sum', models.IntegerField(blank=True, null=True)),
                ('opens_at', models.TimeField(blank=True, null=True, verbose_name='Opening Time')),
                ('closes_at', models.TimeField(blank=True, null=True, verbose_name='Closing Time')),
            ],
            options={
                'db_table': 'organization',
            },
        ),
        migrations.CreateModel(
            name='TerminalGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(max_length=255)),
                ('external_id', models.CharField(db_index=True, max_length=255, unique=True)),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('in_use', models.BooleanField(default=False)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='terminal_groups', to='organization.organization')),
            ],
            options={
                'db_table': 'terminal_group',
            },
        ),
    ]
