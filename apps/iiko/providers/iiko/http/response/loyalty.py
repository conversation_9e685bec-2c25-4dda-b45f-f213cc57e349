from pydantic import BaseModel
from typing import Optional, List, Dict


class CustomerCreateOrUpdateResponseBody(BaseModel):
    """Response body for customer create/update operation"""
    id: str


class CustomerAddToProgramResponseBody(BaseModel):
    """Response body for adding customer to loyalty program"""
    userWalletId: str


class LoyaltyDiscount(BaseModel):
    """Discount details in loyalty program results"""
    code: int
    orderItemId: str
    positionId: str
    discountSum: float
    amount: float
    comment: Optional[str] = None


class LoyaltyProgramResult(BaseModel):
    """Individual loyalty program result"""
    marketingCampaignId: str
    name: str
    discounts: List[LoyaltyDiscount]
    upsales: List[Dict] = []
    freeProducts: List[Dict] = []
    availableComboSpecifications: List[Dict] = []
    availableCombos: List[Dict] = []
    needToActivateCertificate: bool = False


class LoyaltyCalculationResponseBody(BaseModel):
    """Response body for loyalty calculation"""
    loyaltyProgramResults: List[LoyaltyProgramResult]
    availablePayments: List[Dict] = []
    validationWarnings: List[str] = []
    Warnings: List[str] = []
    loyaltyTrace: Optional[Dict] = None
