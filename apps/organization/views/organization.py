"""
Module for handling the organization-related API view.

This module defines an API view for retrieving active organizations, calculating the distance
from the user's location to the organization, checking the organization's terminal group status,
and determining the appropriate delivery cost based on the distance.
"""

from rest_framework import views, response, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import NotFound, ValidationError

from apps.order.service import OrderService
from apps.order.utility.find_courier import haversine
from apps.iiko.providers.iiko.http.client import client
from apps.organization.models import Organization, TerminalGroup
from apps.organization.helper.organization import organization_helper
from apps.organization.serializers.organization import OrganizationSerializer
from apps.organization.permissions import IsMarketologOrManager
from apps.organization.service import OrganizationService
from apps.core.views import ServiceBaseView


class OrganizationAPIView(views.APIView):
    """
    API view for retrieving a list of active organizations.

    This view handles the GET request to retrieve organizations, sort them by their distance
    to the user's location if provided, check if the organizations' terminal groups are alive,
    and determine the appropriate delivery cost based on the distance.
    """

    def get(self, request, *args, **kwargs):
        """
        Handles GET requests to retrieve and return a list of organizations.

        The list of organizations can be sorted by their distance to the user's location,
        and includes information about whether the organization's terminal group is alive
        and the applicable delivery cost based on the distance.

        Args:
            request: The HTTP request object containing query parameters for latitude and longitude.
            *args: Additional positional arguments.
            **kwargs: Additional keyword arguments.

        Returns:
            Response: A JSON response containing the list of organizations with additional information.
        """
        organization_helper.check_organization_is_alive()
        lat, lon = self._get_coordinates(request)
        # region = self.get_region_by_lat_lon(lat, lon)

        organizations = Organization.get_active_organizations()

        if lat and lon:
            organizations = self._sort_organizations_by_distance(organizations, lat, lon)

        serialized_data = self._serialize_organizations(organizations, lat, lon)
        return response.Response(serialized_data)

    def _get_coordinates(self, request):
        """
        Extracts latitude and longitude from the request's query parameters.

        Args:
            request: The HTTP request object.

        Returns:
            tuple: A tuple containing latitude and longitude as floats, or (None, None) if not provided.
        """
        lat = request.query_params.get('lat')
        lon = request.query_params.get('lon')
        if lat and lon:
            return float(lat), float(lon)
        return None, None

    @staticmethod
    def get_region_by_lat_lon(latitude, longitude):
        """
        Determine the region based on latitude and longitude.
        Returns 'Bukhoro' or 'Navoi' if coordinates fall within those cities, else None.
        """
        cities_bounds = {
            'B': {
                'lat_min': 39.767, 'lat_max': 39.827,
                'lon_min': 64.400, 'lon_max': 64.460
            },
            'N': {
                'lat_min': 40.080, 'lat_max': 40.120,
                'lon_min': 65.350, 'lon_max': 65.410
            }
        }

        for city, bounds in cities_bounds.items():
            if (bounds['lat_min'] <= latitude <= bounds['lat_max'] and
                    bounds['lon_min'] <= longitude <= bounds['lon_max']):
                return city

        return None

    def _sort_organizations_by_distance(self, organizations, lat, lon):
        """
        Sorts the list of organizations based on their distance from the user's location.

        Args:
            organizations (list): A list of active organizations.
            lat (float): Latitude of the user's location.
            lon (float): Longitude of the user's location.

        Returns:
            list: The sorted list of organizations by distance if within 20 km, else an empty list.
        """
        def is_within_20km(org):
            distance = self._calculate_distance(lat, lon, org)
            return distance <= 20

        nearby_organizations = filter(is_within_20km, organizations)
        return sorted(
            nearby_organizations,
            key=lambda org: self._calculate_distance(lat, lon, org)
        )

    def _serialize_organizations(self, organizations, lat, lon):
        """
        Serializes the list of organizations and adds additional information like distance,
        is_alive status, and delivery cost.

        Args:
            organizations (list): A list of active organizations.
            lat (float): Latitude of the user's location.
            lon (float): Longitude of the user's location.

        Returns:
            list: A list of serialized organization data with additional fields.
        """
        delivery_price = None
        serialized_data = []

        for organization in organizations:
            terminal_group = TerminalGroup.get_active_terminal_group(organization.id)
            is_alive = self._check_is_alive(organization, terminal_group) if terminal_group is not None else False

            organization_data = OrganizationSerializer(organization).data
            organization_data['is_alive'] = is_alive

            if lat and lon:
                distance = self._calculate_distance(lat, lon, organization)
                delivery_price = self._get_delivery_cost_id(distance)
                organization_data['distance'] = distance
                organization_data['delivery'] = delivery_price.to_dict() if delivery_price else None

            serialized_data.append(organization_data)
        return serialized_data

    def _calculate_distance(self, user_lat, user_lon, organization):
        """
        Calculates the distance between the user's location and the organization's location.

        Args:
            user_lat (float): Latitude of the user's location.
            user_lon (float): Longitude of the user's location.
            organization (Organization): The organization object.

        Returns:
            float: The distance between the user's location and the organization.
        """
        return haversine(user_lat, user_lon, organization.latitude, organization.longitude)

    def _get_delivery_cost_id(self, distance):
        """
        Determines the delivery cost ID based on the distance.

        Args:
            distance (float): The distance between the user and the organization.

        Returns:
            int: The ID of the applicable delivery cost.
        """
        return OrderService.calculate_delivery_price(distance)

    @classmethod
    def _check_is_alive(cls, organization, terminal_group):
        """
        Checks if the organization's terminal group is alive.

        Args:
            organization (Organization): The organization object.
            terminal_group (TerminalGroup): The terminal group object associated with the organization.

        Returns:
            bool: True if the terminal group is alive, False otherwise.
        """
        results = client.terminal_group.is_alive(
            organization_id=str(organization.external_id),
            terminal_group_id=str(terminal_group.external_id),
            headers=client.variables.get_headers()
        )
        alive_list = results.isAliveStatus

        for alive in alive_list:
            if alive.organizationId == organization.external_id and alive.terminalGroupId == terminal_group.external_id:
                return alive.isAlive

        return False


class OrganizationUpdateAPIView(views.APIView, ServiceBaseView):
    """
    API view to update an organization's details.
    """
    permission_classes = [IsAuthenticated, IsMarketologOrManager]

    def put(self, request, organization_id, *args, **kwargs):
        """
        Handle PUT request to update an organization's details.
        """
        try:
            # Use the serializer to validate the data
            serializer = OrganizationSerializer(data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)

            # Update the organization using the service
            updated_organization = OrganizationService.update_organization(organization_id, serializer.validated_data)
            return response.Response(OrganizationSerializer(updated_organization).data, status=status.HTTP_200_OK)
        except NotFound as exc:
            raise self.call_service_exception(
                error_type="not_found",
                message=str(exc),
                status_code=status.HTTP_404_NOT_FOUND
            )

        except ValidationError as exc:
            raise self.call_service_exception(
                error_type="bad_request",
                message=str(exc),
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=str(exc),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
