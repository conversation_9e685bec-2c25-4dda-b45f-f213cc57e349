"""
The payment method views.

This module defines the views for retrieving payment methods, with support for dynamic
filtering based on query parameters. Specifically, it provides a list of payment methods
that can be filtered by an `organization_id`. The request must include this parameter
to retrieve relevant payment methods for the given organization.

The view utilizes Django REST Framework's generic class-based views and integrates with
custom filtering logic on the `PaymentMethod` model.
"""

from rest_framework import generics, status, views, response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import NotFound, ValidationError

from apps.core.views import ServiceBaseView
from apps.payment.models import PaymentMethod
from apps.payment.serializers import PaymentMethodSerializer
from apps.payment.service import PaymentService


class PaymentMethodListView(generics.ListAPIView, ServiceBaseView):
    """
    Payment method list view.

    This view provides a list of payment methods that can be filtered based on the
    `organization_id` query parameter. If the `organization_id` is not provided, it
    returns all available payment methods.

    Query Parameters:
        - organization_id (int, optional): The ID of the organization to filter payment methods.

    Example Request:
        GET /payment-methods/?organization_id=5

    Response:
        - 200 OK: A list of payment methods for the given organization or all payment methods.
        - 400 Bad Request: If `organization_id` is invalid.
    """
    serializer_class = PaymentMethodSerializer

    def get_queryset(self):
        """
        Customize the queryset based on the organization_id provided in the request.

        If `organization_id` is provided, it will retrieve the corresponding payment methods
        for that organization. Otherwise, it returns all payment methods.

        Returns:
            QuerySet: A queryset filtered by organization ID or all payment methods.
        """
        organization_id = self.request.query_params.get('organization_id')

        if organization_id:
            return PaymentMethod.get_by_organization_id(organization_id=organization_id)

        return PaymentMethod.objects.all()


class PaymentMethodUpdateView(views.APIView, ServiceBaseView):
    """
    API view to update a payment method by its ID.
    """
    permission_classes = [IsAuthenticated]

    def put(self, request, payment_method_id, *args, **kwargs):
        """
        Handle PUT request to update a payment method.
        """
        is_enabled = request.data.get('is_enabled')

        if is_enabled is None:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Missing 'is_enabled' in request data.",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        try:
            updated_payment_method = PaymentService.update_payment_method_status(payment_method_id, is_enabled)
            return response.Response(PaymentMethodSerializer(updated_payment_method).data, status=status.HTTP_200_OK)
        except NotFound as exc:
            raise self.call_service_exception(
                error_type="not_found",
                message=str(exc),
                status_code=status.HTTP_404_NOT_FOUND
            )
        except ValidationError as exc:
            raise self.call_service_exception(
                error_type="validation_error",
                message=str(exc),
                status_code=status.HTTP_400_BAD_REQUEST
            )
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to update payment method: {str(exc)}",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
