"""
Admin interface for Click SuperApp models.
"""
from django.utils.html import format_html
from django.urls import reverse
from django.contrib import admin

from unfold.admin import ModelAdmin

from apps.payment.models.superapp import ClickSuperAppUser, ClickSuperAppSession


class ClickSuperAppSessionInline(admin.TabularInline):
    """
    Inline admin for ClickSuperAppSession model.
    """
    model = ClickSuperAppSession
    extra = 0
    fields = ('session_id', 'is_active', 'expires_at')
    readonly_fields = ('session_id', 'expires_at')
    can_delete = False
    show_change_link = True
    max_num = 5
    verbose_name = "Session"
    verbose_name_plural = "Recent Sessions"

    def has_add_permission(self, _request=None, _obj=None):
        """
        Don't allow adding sessions through the admin.
        """
        return False

    def get_queryset(self, request):
        """
        Limit queryset to the most recent sessions.
        """
        queryset = super().get_queryset(request)
        return queryset.order_by('-created_at')

    def get_extra(self, _request=None, obj=None, **_kwargs):
        """
        Don't show any extra forms if we already have sessions.
        """
        if obj and obj.sessions.exists():
            return 0
        return self.extra


@admin.register(ClickSuperAppUser)
class ClickSuperAppUserAdmin(ModelAdmin):
    """
    Admin interface for ClickSuperAppUser model using Unfold.
    """
    list_display = (
        'id', 'click_user_id', 'merchant_user_id', 'phone',
        'user_link', 'last_login', 'created_at'
    )
    list_filter = ('created_at', 'last_login')
    search_fields = ('click_user_id', 'merchant_user_id', 'phone', 'user__name', 'user__phone')
    readonly_fields = ('created_at', 'updated_at', 'last_login')
    inlines = [ClickSuperAppSessionInline]

    fieldsets = (
        (None, {
            'fields': (
                ('user', 'phone'),
                ('click_user_id', 'merchant_user_id'),
            )
        }),
        ('Timestamps', {
            'fields': (
                ('last_login', 'created_at', 'updated_at'),
            ),
            'classes': ('collapse',)
        }),
    )

    def user_link(self, obj):
        """
        Return a link to the user admin page.
        """
        if obj.user:
            url = reverse('admin:user_users_change', args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.name or obj.user.phone)
        return '-'
    user_link.short_description = 'User'

    def get_queryset(self, request):
        """
        Optimize queryset by prefetching related objects.
        """
        return super().get_queryset(request).select_related('user')


@admin.register(ClickSuperAppSession)
class ClickSuperAppSessionAdmin(ModelAdmin):
    """
    Admin interface for ClickSuperAppSession model using Unfold.
    """
    list_display = (
        'id', 'session_id', 'click_user_link', 'is_active',
        'expires_at', 'created_at'
    )
    list_filter = ('is_active', 'created_at', 'expires_at')
    search_fields = ('session_id', 'click_user__click_user_id', 'click_user__phone')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        (None, {
            'fields': (
                ('click_user', 'session_id'),
                ('is_active', 'expires_at'),
            )
        }),
        ('Token', {
            'fields': ('token',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': (
                ('created_at', 'updated_at'),
            ),
            'classes': ('collapse',)
        }),
    )

    def click_user_link(self, obj):
        """
        Return a link to the Click SuperApp user admin page.
        """
        if obj.click_user:
            url = reverse('admin:payment_clicksuperappuser_change', args=[obj.click_user.id])
            return format_html('<a href="{}">{}</a>', url, obj.click_user.click_user_id)
        return '-'
    click_user_link.short_description = 'Click User'

    def get_queryset(self, request):
        """
        Optimize queryset by prefetching related objects.
        """
        return super().get_queryset(request).select_related('click_user')
