"""
iiko updates for shift systems
"""
from datetime import datetime

from pydantic import BaseModel


class EventInfo(BaseModel):
    """
    Represents the detailed information about a specific event.

    Attributes:
        id (str): Unique identifier for the event.
        roleId (str): Identifier for the role associated with the event.
        opened (bool): A boolean flag indicating whether the shift is opened.
        terminalGroupId (str): Identifier for the terminal group associated with the event.
    """
    id: str
    roleId: str
    opened: bool
    terminalGroupId: str


class ShiftEvent(BaseModel):
    """
    Represents a shift event with its associated details.

    Attributes:
        eventType (str): Type of the event, such as "PersonalShift".
        eventTime (datetime): Timestamp when the event occurred.
        organizationId (str): Identifier for the organization related to the event.
        correlationId (str): Correlation identifier for tracing the event.
        eventInfo (EventInfo): Detailed information related to the event.
    """
    eventType: str
    eventTime: datetime
    organizationId: str
    correlationId: str
    eventInfo: EventInfo


class PersonalShiftUpdateEvent(BaseModel):
    """
    Represents a collection of shift events.

    Attributes:
        event_body (List[ShiftEvent]): A list of shift events.
    """
    event_body: list[ShiftEvent]
