"""
the courier status view
"""
from rest_framework import views
from rest_framework import response

from apps.user.helper.agent import agent_helper
from apps.core.exceptions import ServiceAPIException
from apps.courier.serializers.courier import CourierChangeStatusSerializer


class CourierStatusAPIView(views.APIView):
    """
    the courier status apiview
    """
    def get(self, request, *args, **kwargs):
        """
        the post method for courier.
        """
        courier_id = request.user.id

        if not courier_id:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Missing required parameters: agent_id",
                status_code=400
            )

        try:
            agent = agent_helper.get_by_id(courier_id)

        except Exception as exc:
            msg = f"get courier-id order failed exc: {exc}"
            raise ServiceAPIException(
                error_type="internal error",
                message=msg
            ) from exc

        return response.Response({
            "status": agent.status,
        })

    def post(self, request, *args, **kwargs):
        """
        the post method for courier.
        """
        courier_id = request.user.id

        if not courier_id:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Courier Not Found",
                status_code=400
            )

        serializer = CourierChangeStatusSerializer(data=request.data)
        if not serializer.is_valid():
            raise ServiceAPIException(
                error_type="bad_request",
                message=serializer.errors,
                status_code=400
            )

        status = serializer.validated_data["status"]

        try:
            agent_helper.change_agent_status(courier_id, status)
        except Exception as exc:
            msg = f"change agent status failed exc: {exc}"
            raise ServiceAPIException(
                error_type="internal error",
                message=msg
            ) from exc

        return response.Response()
