"""
implementation of state tasks
"""
from celery import Task

from master_kebab import celery_app

from apps.core import core
from apps.iiko.providers.iiko.enum import OrderStatus
from apps.iiko.providers.iiko.http.client import client
from apps.order.models.order import get_order_by_external_id


@celery_app.task(bind=True, max_retries=5)
def confirm_task(self: Task, external_id: str):
    """
    order confirm task
    """
    try:
        order = get_order_by_external_id(external_id)

        if order.status != OrderStatus.UNCONFIRMED:
            print("stautus is not unconfirmed")
            return

    except Exception as exc:
        core.log('error', f"An error occurred while confirming order {external_id}: {exc}")
        self.retry(exc=exc, countdown=5)

    try:
        resp = client.delivery.confirm(
            order_id=external_id,
            organization_id=order.organization.external_id,
            headers=client.variables.get_headers()
        )
        print(resp)

    except Exception as exc:
        core.log('error', f"An error occurred while confirming order {external_id}: {exc}")
        self.retry(exc=exc, countdown=5)
