"""
Job Position Admin Interface
"""

from django.contrib import admin
from apps.core.admin.modeladmin import ModelAdmin, TabularInline
from apps.hr.models.job_position import JobPosition, JobWorkingHour


class JobWorkingHourInline(TabularInline):
    """
    Inline admin for adding working hours directly from JobPosition
    """
    model = JobWorkingHour
    extra = 1


class JobPositionUI(ModelAdmin):
    """
    The job position admin page.
    """
    list_display = [
        "id",
        "name",
        "created_at",
        "updated_at",
    ]
    search_fields = ["name", "name_uz", "name_ru", "name_en"]
    list_filter = ["created_at", "updated_at"]

    inlines = [JobWorkingHourInline]


admin.site.register(JobPosition, JobPositionUI)


class WorkingHoursUI(ModelAdmin):
    """
    The working hours admin page.
    """
    list_display = [
        "id",
        "job_position",
        "working_hours",
        "created_at",
        "updated_at",
    ]
    search_fields = ["working_hours", "working_hours_uz", "working_hours_ru", "working_hours_en"]
    list_filter = ["created_at", "updated_at"]


admin.site.register(JobWorkingHour, WorkingHoursUI)
