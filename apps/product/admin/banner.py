"""
Admin module for managing banners.
"""
from django.contrib import admin
from django.utils.html import format_html
from apps.core.admin.modeladmin import ModelAdmin
from apps.product.models.banner import Banner


class BannerAdmin(ModelAdmin):
    """
    Admin interface for managing banners.
    """
    list_display = ('id', 'name', 'display_image', 'ad_link', 'position')
    list_filter = ('position',)
    search_fields = ('name', 'ad_link')
    ordering = ('position',)
    fields = ('name', 'image_url', 'ad_link', 'position')

    def display_image(self, obj):
        """
        Display thumbnail of banner image in admin list view
        """
        if obj.image_url:
            return format_html(
                '<img src="{}" width="50" height="50" style="object-fit: cover;" />',
                obj.image_url.url
            )
        return "-"
    display_image.short_description = 'Image'


admin.site.register(Banner, BannerAdmin)
