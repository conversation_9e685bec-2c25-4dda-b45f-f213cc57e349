# Generated by Django 5.0.6 on 2024-12-15 17:24

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0016_alter_banner_image_url"),
    ]

    operations = [
        migrations.AlterField(
            model_name="banner",
            name="image_url",
            field=models.ImageField(
                null=True,
                upload_to="test/banners",
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "png", "webp"]
                    )
                ],
            ),
        ),
        migrations.AlterField(
            model_name="product",
            name="image_url",
            field=models.ImageField(
                null=True,
                upload_to="test/products",
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "png", "webp"]
                    )
                ],
            ),
        ),
    ]
