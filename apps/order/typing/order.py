"""
the order serialization
"""
from datetime import datetime
from typing import List, Optional, Any

from pydantic import field_validator
from pydantic import BaseModel, Field, validator

from apps.map.utility import is_within_circle
from apps.payment.enums.method import PaymentMethodEnum
from apps.order.enums.delivery import DeliveryType as DeliveryTypeEnum


class Attribute(BaseModel):
    """
    the attribute serialization
    """
    id: Any
    comment: Optional[str] = None


class ModifierAttribute(BaseModel):
    """
    the attribute serialization
    """
    id: Any
    quantity: int = Field(alias="quantity")
    comment: Optional[str] = None


class OrderItems(BaseModel):
    """
    the product serialization
    """
    id: Any = Field(alias="id")
    quantity: int = Field(alias="quantity")
    comment: Optional[str] = None
    attribute: Attribute
    modifiers: Optional[List[ModifierAttribute]] = None


class Location(BaseModel):
    """
    the location serialization
    """
    lat: float
    lng: float


class OrderDetail(BaseModel):
    """
    The Pydantic model for order detail.
    """
    user_id: Optional[int] = None
    subaddress: Optional[str] = None
    name: Optional[str] = Field(None, max_length=200)
    phone: Optional[str] = Field(None, max_length=20)
    delivery_type: DeliveryTypeEnum = Field()
    delivery_address: Optional[str] = None
    entrance: Optional[str] = Field(None, max_length=300)
    floor: Optional[str] = Field(None, max_length=50)
    door_code: str = Field(None, max_length=255)
    floor: Optional[str] = Field(None, max_length=50)
    comment: Optional[str] = Field(None)
    promo_code: Optional[str] = Field(None, max_length=50)
    payment_method: PaymentMethodEnum = Field()
    location: Optional[Location] = None
    complete_before: Optional[str] = None
    street_id: Optional[str] = None
    delivery_cost_id: Optional[str] = None
    house: Optional[str] = None
    organization_id: Optional[int] = None
    payment_phone: Optional[str] = None

    @validator('payment_method', pre=True, always=True)
    def validate_payment_method(cls, value):
        """
        payment method should be always uppercase
        """
        # TODO: remove
        if value == "Наличные":
            value = "CASH"

        return value.upper()

    @validator('complete_before', pre=True, always=True)
    def validate_complete_before(cls, value):
        if value:
            try:
                # Normalize the input datetime string
                normalized_value = cls.time_normalize(value)

                # Parse the normalized string to a datetime object
                complete_before_dt = datetime.strptime(normalized_value, '%Y-%m-%d %H:%M:%S.%f')

            except ValueError:
                raise ValueError("Incorrect date format, should be YYYY-MM-DD HH:MM:SS.sss")

            if complete_before_dt <= datetime.now():
                raise ValueError("Complete before date should be in the future")

            return normalized_value

        return value

    @classmethod
    def time_normalize(cls, input_datetime):
        # Parse the input string to a datetime object
        dt_object = datetime.strptime(input_datetime, "%Y-%m-%dT%H:%M:%S.%fZ")

        # Format the datetime object into the desired output format
        output_datetime = dt_object.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]

        return output_datetime


class OrderCreateSerializer(BaseModel):
    """
    the product serialization
    """
    items: List[OrderItems]
    order_detail: OrderDetail

    @classmethod
    def from_request(cls, request_data):
        """
        Create an instance from request data, handling ID type conversions.

        This method manually processes the request data to handle both integer and string IDs.
        """
        # Process items
        processed_items = []
        for item in request_data.get('items', []):
            # Process attribute
            attribute = item.get('attribute', {})
            processed_attribute = {'id': attribute.get('id')}

            # Process modifiers
            processed_modifiers = []
            for modifier in item.get('modifiers', []):
                processed_modifiers.append({
                    'id': modifier.get('id'),
                    'quantity': modifier.get('quantity', 1)
                })

            processed_items.append({
                'id': item.get('id'),
                'quantity': item.get('quantity'),
                'comment': item.get('comment', ''),
                'attribute': processed_attribute,
                'modifiers': processed_modifiers
            })

        # Process order_detail
        order_detail = request_data.get('order_detail', {})

        # Create the processed data
        processed_data = {
            'items': processed_items,
            'order_detail': order_detail
        }

        return cls(**processed_data)


class OrderUserInfo(BaseModel):
    """
    The Pydantic model for order detail.
    """
    lat: float
    long: float


class OrderCreateForTelegramSerializer(BaseModel):
    """
    the product serialization
    """
    items: List[OrderItems]
    order_detail: OrderDetail

    @field_validator("order_detail")
    @classmethod
    def validate_user_info(cls, value: OrderDetail):
        """
        The validator for user info.
        """
        if not value.location.lat or not value.location.lng:
            raise ValueError("lat and long are required")

        if not is_within_circle(value.location.lat, value.location.lng):
            raise ValueError("User is not within the circle")

        return value
