"""
API views for user orders (active, history, and detail)
"""
from rest_framework import views, status
from rest_framework.response import Response
from rest_framework.permissions import AllowAny

from apps.core.exceptions import ServiceAPIException
from apps.core.views import ServiceBaseView
from apps.order.models.order import Order
from apps.order.enums.order import OrderStatus
from apps.order.serializers.order import OrderSerializer, OrderListSerializer
from apps.bot.models.telegram import TelegramUser
from apps.user.models.user import Users


class UserActiveOrdersAPIView(views.APIView, ServiceBaseView):
    """
    API view for retrieving active orders of a Telegram user by chat_id
    """
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        """
        Get active orders for a user identified by chat_id
        """
        try:
            chat_id = request.query_params.get('chat_id')

            if not chat_id:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message="chat_id parameter is required",
                    status_code=400
                )

            try:
                chat_id = int(chat_id)
            except ValueError:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message="chat_id must be an integer",
                    status_code=400
                )

            # Get user from TelegramUser model
            telegram_user = TelegramUser.objects.filter(id=chat_id).first()

            if not telegram_user:
                return Response({
                    "count": 0,
                    "results": []
                }, status=status.HTTP_200_OK)

            user = telegram_user.user

            if not user:
                return Response({
                    "count": 0,
                    "results": []
                }, status=status.HTTP_200_OK)

            # Define active order statuses
            active_statuses = [
                OrderStatus.CREATED.value,
                OrderStatus.APPROVED.value,
                OrderStatus.IN_PROGRESS.value,
                OrderStatus.COOKING_STARTED.value,
                OrderStatus.COOKING_COMPLETED.value,
                OrderStatus.ON_WAY.value,
                OrderStatus.ARRIVED.value,
                OrderStatus.WAITING.value,
            ]

            # Get active orders
            active_orders = Order.objects.filter(
                user_id=user.id,
                status__in=active_statuses
            ).select_related(
                'user',
                'order_detail',
                'organization'
            ).prefetch_related(
                'order_items',
                'order_items__product'
            ).order_by('-created_at')

            serializer = OrderListSerializer(active_orders, many=True)

            return Response({
                "count": len(serializer.data),
                "results": serializer.data
            }, status=status.HTTP_200_OK)

        except ServiceAPIException as exc:
            raise exc
        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_error",
                message=f"Failed to retrieve active orders: {str(exc)}",
                status_code=500
            ) from exc


class UserOrderHistoryAPIView(views.APIView, ServiceBaseView):
    """
    API view for retrieving order history of a Telegram user by chat_id
    """
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        """
        Get order history for a user identified by chat_id
        """
        try:
            chat_id = request.query_params.get('chat_id')

            if not chat_id:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message="chat_id parameter is required",
                    status_code=400
                )

            try:
                chat_id = int(chat_id)
            except ValueError:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message="chat_id must be an integer",
                    status_code=400
                )

            # Get user from TelegramUser model
            telegram_user = TelegramUser.objects.filter(id=chat_id).first()

            if not telegram_user:
                return Response({
                    "count": 0,
                    "results": [],
                    "page": 1,
                    "page_size": 10,
                    "total_pages": 0
                }, status=status.HTTP_200_OK)

            user = telegram_user.user

            if not user:
                return Response({
                    "count": 0,
                    "results": [],
                    "page": 1,
                    "page_size": 10,
                    "total_pages": 0
                }, status=status.HTTP_200_OK)

            # Define completed/archived order statuses
            completed_statuses = [
                OrderStatus.DELIVERED.value,
                OrderStatus.CLOSED.value,
                OrderStatus.TIMEOUT.value,
                OrderStatus.CANCELLED.value,
            ]

            # Get order history
            order_history = Order.objects.filter(
                user_id=user.id,
                status__in=completed_statuses
            ).select_related(
                'user',
                'order_detail',
                'organization'
            ).prefetch_related(
                'order_items',
                'order_items__product'
            ).order_by('-created_at')

            # Apply pagination if needed
            page_size = int(request.query_params.get('page_size', 10))
            page = int(request.query_params.get('page', 1))

            start = (page - 1) * page_size
            end = start + page_size

            paginated_orders = order_history[start:end]

            serializer = OrderListSerializer(paginated_orders, many=True)

            return Response({
                "count": order_history.count(),
                "results": serializer.data,
                "page": page,
                "page_size": page_size,
                "total_pages": (order_history.count() + page_size - 1) // page_size
            }, status=status.HTTP_200_OK)

        except ServiceAPIException as exc:
            raise exc
        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_error",
                message=f"Failed to retrieve order history: {str(exc)}",
                status_code=500
            ) from exc


class UserOrderDetailAPIView(views.APIView, ServiceBaseView):
    """
    API view for retrieving a specific order detail of a Telegram user
    by chat_id and order_id
    """
    permission_classes = [AllowAny]

    def get(self, request, order_id):
        """
        Get a specific order detail for a user identified by chat_id
        """
        try:
            chat_id = request.query_params.get('chat_id')

            if not chat_id:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message="chat_id parameter is required",
                    status_code=400
                )

            try:
                chat_id = int(chat_id)
            except ValueError as exc:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message="chat_id must be an integer",
                    status_code=400
                ) from exc

            # Get user from TelegramUser model
            telegram_user = TelegramUser.objects.filter(id=chat_id).first()

            if not telegram_user:
                raise ServiceAPIException(
                    error_type="not_found",
                    message="Telegram user not found",
                    status_code=404
                )

            user = telegram_user.user

            if not user:
                raise ServiceAPIException(
                    error_type="not_found",
                    message="User not found",
                    status_code=404
                )

            # Get the order and check if it belongs to the user
            order = Order.objects.filter(
                id=order_id,
                user_id=user.id
            ).select_related(
                'user',
                'order_detail',
                'organization'
            ).prefetch_related(
                'order_items',
                'order_items__product'
            ).first()

            if not order:
                raise ServiceAPIException(
                    error_type="not_found",
                    message="Order not found",
                    status_code=404
                )

            serializer = OrderSerializer(order)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except ServiceAPIException as exc:
            raise exc
        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_error",
                message=f"Failed to retrieve order detail: {str(exc)}",
                status_code=500
            ) from exc
