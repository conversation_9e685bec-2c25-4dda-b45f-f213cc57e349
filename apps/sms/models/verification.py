"""
init models
"""
import random

from datetime import timedelta

from django.db import models
from django.utils import timezone

from master_kebab import settings
from apps.user.models.user import Users
from apps.sms.models.base import BaseModel


def default_expiration():
    """
    returns default expiration
    """
    return timezone.now() + timedelta(minutes=2)


class VerificationCodes(BaseModel):
    """
    verification code model
    """
    class Meta:
        """
        the meta fields
        """
        db_table = 'verification_codes'

    user = models.ForeignKey(Users, on_delete=models.CASCADE)
    code = models.IntegerField(editable=False, unique=False)
    expires_at = models.DateTimeField(default=default_expiration)
    is_used = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.phone} - {self.code}"

    def save(self, *args, **kwargs):
        """
        save a verification code
        """
        if not self.code:
            if settings.IS_TEST_MODE or self.user.phone in [
                "915655359",
                "998915655359",
                "+998915655359"
            ]:
                self.code = '888888'
            else:
                self.code = str(random.randint(100000, 999999))

        super().save(*args, **kwargs)


def create_verification_code(
    user: Users,
    code: int,
    expires_at: timedelta,
    is_verified: bool = False,
):
    """
    create verification code
    """
    VerificationCodes.objects.create(
        user=user,
        code=code,
        expires_at=expires_at,
        is_verified=is_verified,
    )


def create_verify_code(user: Users):
    """
    create verify code
    """
    code = random.randint(100000, 999999)
    expires_at = timezone.now() + timedelta(minutes=2)

    create_verification_code(user, code, expires_at, is_verified=True)


def get_verification_code(user: Users, code: int):
    """
    get verification code
    """
    return VerificationCodes.objects.filter(
        user=user, code=code, is_used=False
    ).last()
