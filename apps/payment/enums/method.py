"""
the enumeration of order status
"""
from enum import StrEnum


class PaymentMethodEnum(StrEnum):
    """
    The enumeration of order status.
    """
    CLICK = "CLICK"
    PAYME = "PAYME"
    CASH = "CASH"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]

    @classmethod
    def allowed_methods(cls):
        """
        Returns a list of allowed values.
        """
        return [member.value for member in cls]
