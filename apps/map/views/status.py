"""
init status apiview
"""
from rest_framework import views
from rest_framework import response

from apps.map.utility import is_within_circle
from apps.core.exceptions import ServiceAPIException


class CheckGeoLocationAPIView(views.APIView):
    """
    the check geo location api view
    """
    def get(self, request, *args, **kwargs):
        """
        the get method for getting the health check.
        """
        lat = request.query_params.get('lat', None)
        long = request.query_params.get('long', None)

        if not lat or not long:
            return response.Response({
                "message": "No query parameter provided"
            })

        status = is_within_circle(float(lat), float(long))

        if status is False:
            raise ServiceAPIException(
                error_type="unsupported_geozone",
                message={
                    "ru": "Указанное местоположение не находится в пределах города Навои",
                    "uz": "Taqdim etilgan joylashuv Navoi shahri hududida emas"
                }
            )

        return response.Response({
            "status": status
        })
