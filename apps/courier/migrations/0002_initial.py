# Generated by Django 5.0.6 on 2024-08-20 11:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('courier', '0001_initial'),
        ('order', '0001_initial'),
        ('organization', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='couriermissedorders',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='missed_orders', to='order.order'),
        ),
        migrations.AddField(
            model_name='courierpoint',
            name='courier',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='courier.courier'),
        ),
        migrations.AddField(
            model_name='couriershifts',
            name='courier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='courier.courier'),
        ),
        migrations.AddField(
            model_name='couriershifts',
            name='organization',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='organization.organization'),
        ),
        migrations.AddField(
            model_name='fcmdevice',
            name='courier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='courier.courier'),
        ),
        migrations.AlterUniqueTogether(
            name='jwtcouriertokenblocklist',
            unique_together={('token',)},
        ),
        migrations.AddField(
            model_name='jwtcouriertokenlist',
            name='courier',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='courier.courier'),
        ),
    ]
