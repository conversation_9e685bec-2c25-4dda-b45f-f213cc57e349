"""
logging monitoring
"""
import json

from django.db import models


class RequestResponseLog(models.Model):
    """
    A model to store detailed logs of HTTP requests and responses
    processed by the Django application. This includes the HTTP method,
    request path, headers, body, client IP address, response status,
    response body, and the time it took to handle the request.

    Fields:
        - method (str): The HTTP method used for the request (e.g., GET, POST).
        - path (str): The full URL path of the request.
        - headers (str): A JSON-encoded string of request headers.
        - body (str): The request body, which can be JSON or plain text.
        - client_ip (str): The IP address of the client making the request.
        - response_status (int): The HTTP status code of the response.
        - response_body (str): The content of the response body, JSON or plain text.
        - duration (float): Time in seconds that it took to process the request.
        - timestamp (datetime): The timestamp of when the request was processed.

    Methods:
        - __str__: Returns a string representation of the log entry for easy identification.
    """

    method = models.CharField(max_length=10, help_text="HTTP method used (e.g., GET, POST).")
    path = models.CharField(max_length=255, help_text="The URL path of the request.")
    headers = models.TextField(help_text="JSON-encoded string of the request headers.")
    body = models.TextField(null=True, blank=True, help_text="The request body (optional).")
    client_ip = models.GenericIPAddressField(help_text="IP address of the client making the request.")
    response_status = models.IntegerField(help_text="HTTP status code returned in the response.")
    response_body = models.TextField(null=True, blank=True, help_text="The body of the response (optional).")
    duration = models.FloatField(null=True, blank=True, help_text="Time in seconds taken to process the request.")
    timestamp = models.DateTimeField(auto_now_add=True, help_text="The time when the request was processed.")

    def __str__(self):
        """
        Returns a human-readable string representation of the log entry.
        Format: '<HTTP Method> <Request Path> - <Response Status>'
        """
        return f"{self.method} {self.path} - {self.response_status}"

    @staticmethod
    def save_log_to_db(log_data):
        log_entry = RequestResponseLog(
            method=log_data['method'],
            path=log_data['path'],
            headers=json.dumps(log_data['headers']),
            body=json.dumps(log_data['body']),
            client_ip=log_data['client_ip'],
            response_status=log_data['response_status'],
            response_body=json.dumps(log_data['response_body']),
            duration=log_data['duration']
        )
        log_entry.save()
