"""
The user orders view
"""
from rest_framework import views, response, status, generics, filters
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from apps.order.models.order import Order
from apps.order.serializers.order import OrderSerializer
from apps.order.serializers.order import OrderListSerializer
from apps.order.permissions import OrderHistoryPermissionClass
from apps.order.enums.order import OrderStatus as OrderStatusEnum
from apps.user.permissions.is_client import IsClientRole
from apps.core.exceptions.service import ServiceAPIException
from apps.core.views import ServiceBaseView


class ActiveOrdersAPIView(views.APIView, ServiceBaseView):
    """
    The user active orders
    """
    permission_classes = [IsAuthenticated, IsClientRole]

    def get(self, request, *args, **kwargs):
        try:
            return self.process(request)
        except ServiceAPIException as exc:
            raise ServiceAPIException(
                error_type="internal_error",
                message=f"unable to retrieve: {exc}",
                status_code=500,
            ) from exc

    def process(self, request):
        """
        Process the user active orders.
        """
        user_id = request.user.id
        orders = Order.active_orders(
            role="client",
            object_owner_id=user_id,
        )
        serialized_orders = OrderSerializer(orders, many=True)
        return response.Response(
            data=serialized_orders.data,
            status=status.HTTP_200_OK
        )


class OrderHistoryListView(generics.ListAPIView, ServiceBaseView):
    """
    API endpoint for retrieving authenticated user's order history
    """
    serializer_class = OrderSerializer
    permission_classes = [OrderHistoryPermissionClass]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]

    def get_queryset(self):
        """
        Returns orders for the authenticated user only.
        """
        completed_statuses = [
            OrderStatusEnum.DELIVERED.value,
            OrderStatusEnum.CLOSED.value,
            OrderStatusEnum.CANCELLED.value,
            OrderStatusEnum.TIMEOUT.value,
        ]

        return Order.objects.filter(
            user_id=self.request.user.id,
            status__in=completed_statuses
        ).select_related(
            'user',
            'order_detail',
            'operator'
        ).order_by('-created_at')

    def list(self, request, *args, **kwargs):
        """
        Return orders for the authenticated user with optimized response.
        """
        try:
            self.serializer_class = OrderListSerializer
            return super().list(request, *args, **kwargs)

        except Exception as e:
            raise self.call_service_exception(
                error_type="internal_error",
                message="An unexpected error occurred",
                status_code=500,
                notify_admin=True
            ) from e


class OrderHistoryDetailView(generics.RetrieveAPIView, ServiceBaseView):
    """
    API endpoint for retrieving authenticated user's order history
    """
    serializer_class = OrderSerializer
    permission_classes = [OrderHistoryPermissionClass]
    lookup_field = 'pk'

    def get_queryset(self):
        """
        Returns orders for the authenticated user only.
        """
        completed_statuses = [
            OrderStatusEnum.DELIVERED.value,
            OrderStatusEnum.CLOSED.value,
            OrderStatusEnum.CANCELLED.value,
            OrderStatusEnum.TIMEOUT.value,
        ]

        return Order.objects.filter(
            user_id=self.request.user.id,
            status__in=completed_statuses
        ).select_related(
            'user',
            'order_detail',
            'operator'
        ).order_by('-created_at')

    def retrieve(self, request, *args, **kwargs):
        """
        Return orders for the authenticated user with optimized response.
        """
        try:
            return super().retrieve(request, *args, **kwargs)

        except Exception as e:
            raise self.call_service_exception(
                error_type="internal_error",
                message="An unexpected error occurred",
                status_code=500,
                notify_admin=True
            ) from e
