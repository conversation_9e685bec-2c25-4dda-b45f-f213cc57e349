"""
the employees request body models
"""
import json

import pydantic


class CouriersRequestBody(pydantic.BaseModel):
    """
    the couriers request body
    """
    organization_id: list[str]

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationIds": self.organization_id,
        }


class EmployeeInfoRequestBody(pydantic.BaseModel):
    """
    the employee info request body
    """
    employee_id: str
    organization_id: str

    def as_request_body(self):
        """
        the as request body method
        """
        return json.dumps({
            "organizationId": str(self.organization_id),
            "id": str(self.employee_id)
        })


class EmployeesByRoleRequestBody(pydantic.BaseModel):
    """
    the employee by role request body
    """
    role: str
    organization_ids: list[str]

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "rolesToCheck": [self.role],
            "organizationIds": self.organization_ids,
        }


class EmployeesShiftClockInRequestBody(pydantic.BaseModel):
    """
    the employee shift clock in request body
    """
    organization_id: str
    terminal_group_id: str
    employee_id: str
    role_id: str = None

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organization_id,
            "terminalGroupId": self.terminal_group_id,
            "employeeId": self.employee_id,
            "roleId": self.role_id,
        }


class EmployeesShiftIsOpenRequestBody(pydantic.BaseModel):
    """
    the employee shift is open request body
    """
    organization_id: str
    terminal_group_id: str
    employee_id: str
    role_id: str = None

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organization_id,
            "terminalGroupId": self.terminal_group_id,
            "employeeId": self.employee_id,
            "roleId": self.role_id,
        }


class EmployeesShiftClockOutRequestBody(pydantic.BaseModel):
    """
    the shift clock out request body model
    """
    organization_id: str
    terminal_group_id: str
    employee_id: str
    role_id: str = None

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organization_id,
            "terminalGroupId": self.terminal_group_id,
            "employeeId": self.employee_id,
            "roleId": self.role_id,
        }
