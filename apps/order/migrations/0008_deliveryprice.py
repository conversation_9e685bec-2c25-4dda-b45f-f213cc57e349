# Generated by Django 5.0.6 on 2024-09-06 02:08

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0007_rename_delivery_cost_cofficient_orderdetail_distance_coefficient'),
    ]

    operations = [
        migrations.CreateModel(
            name='DeliveryPrice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('external_id', models.CharField(help_text='Unique identifier for the delivery price', max_length=255, unique=True)),
                ('distance_type', models.CharField(choices=[('NB', 'Nearby (0-5 km)'), ('LC', 'Local (5-10 km)'), ('DS', 'Distant (10-20 km)'), ('RM', 'Remote (20+ km)'), ('RM_PLUS', 'Remote plus (40+ km)')], default='NB', help_text='Type of delivery based on distance', max_length=8, unique=True)),
                ('is_active', models.<PERSON>oleanField(default=True, help_text='Whether this delivery price is currently active')),
            ],
            options={
                'verbose_name': 'Delivery Price',
                'verbose_name_plural': 'Delivery Prices',
                'db_table': 'delivery_prices',
                'ordering': ['distance_type'],
                'indexes': [models.Index(fields=['distance_type'], name='delivery_pr_distanc_ef2d53_idx'), models.Index(fields=['is_active'], name='delivery_pr_is_acti_5f8ba2_idx')],
            },
        ),
    ]
