"""
operations_views.py

This module contains viewset classes for managing `Manager`, `Operator`, `Dispatcher`, `Marketolog` and `SuperAdmin` operations within the `Operations` model. # noqa
Each viewset provides CRUD functionality, allowing entities to be listed, created, retrieved, updated, and deleted.
"""

from rest_framework import viewsets

from apps.operations.models import Operations
from apps.user.enum.role import UserRoleEnums
from apps.operations.serializers import OperationsSerializer
from apps.operations.permissions import IsAdminOrManager, IsAdminOrOperatorOrManager

from apps.product.paginations import LimitAllPagination


class ManagersViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing managers.

    This viewset provides the following actions:
    - list: List all managers.
    - create: Create a new manager.
    - retrieve: Retrieve a manager by ID.
    - update: Update a manager's details.
    - partial_update: Partially update a manager's details.
    - destroy: Delete a manager.

    Only authenticated users can perform these actions.
    """
    queryset = Operations.objects.managers()
    serializer_class = OperationsSerializer
    permission_classes = [IsAdminOrManager]

    def perform_create(self, serializer):
        """
        Automatically set the role to 'manager' before saving.
        """
        serializer.validated_data['role'] = UserRoleEnums.MANAGERS.value
        serializer.save()


class OperatorsViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing operators.

    This viewset provides the following actions:
    - list: List all operators.
    - create: Create a new operator.
    - retrieve: Retrieve an operator by ID.
    - update: Update an operator's details.
    - partial_update: Partially update an operator's details.
    - destroy: Delete an operator.

    Only authenticated users can perform these actions
    """
    queryset = Operations.objects.operators()
    serializer_class = OperationsSerializer
    permission_classes = [IsAdminOrOperatorOrManager]

    def list(self, request, *args, **kwargs):
        """
        List all operators with optional pagination control.
        """
        limit = request.query_params.get('limit', None)
        if limit == 'all':
            self.pagination_class = LimitAllPagination
        return super().list(request, *args, **kwargs)

    def perform_create(self, serializer):
        """
        Automatically set the role to 'operator' before saving.
        """
        serializer.validated_data['role'] = UserRoleEnums.OPERATOR.value
        serializer.save()


class DispatchersViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing dispatchers.

    This viewset provides the following actions:
    - list: List all dispatchers.
    - create: Create a new dispatcher.
    - retrieve: Retrieve a dispatcher by ID.
    - update: Update a dispatcher's details.
    - partial_update: Partially update a dispatcher's details.
    - destroy: Delete a dispatcher.

    Only authenticated users can perform these actions.
    """
    queryset = Operations.objects.dispatchers()
    serializer_class = OperationsSerializer
    permission_classes = [IsAdminOrManager]

    def perform_create(self, serializer):
        """
        Automatically set the role to 'dispatcher' before saving.
        """
        serializer.validated_data['role'] = UserRoleEnums.DISPATCHER.value
        serializer.save()


class MarketologViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing marketologs.

    This viewset provides the following actions:
    - list: List all marketologs.
    - create: Create a new marketolog.
    - retrieve: Retrieve a marketolog by ID.
    - update: Update a marketolog's details.
    - partial_update: Partially update a marketolog's details.
    - destroy: Delete a marketolog.

    Only authenticated users can perform these actions.
    """
    queryset = Operations.objects.marketologs()
    serializer_class = OperationsSerializer
    permission_classes = [IsAdminOrManager]

    def perform_create(self, serializer):
        """
        Automatically set the role to 'marketolog' before saving.
        """
        serializer.validated_data['role'] = UserRoleEnums.MARKETOLOG.value
        serializer.save()
