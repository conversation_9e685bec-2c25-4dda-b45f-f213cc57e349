"""
the create superuser command
"""
from django.contrib.auth import get_user_model
from django.core.management.base import BaseCommand

from master_kebab.settings.internal.user import DJANGO_SUPERUSER_PHONE
from master_kebab.settings.internal.user import DJANGO_SUPERUSER_PASSWORD


class Command(BaseCommand):
    """
    Create a default admin user
    """
    help = 'Create a default admin user'

    def handle(self, *args, **kwargs):
        User = get_user_model()
        if not User.objects.filter(phone=DJANGO_SUPERUSER_PHONE).exists():
            User.objects.create_superuser(
                phone=DJANGO_SUPERUSER_PHONE,
                role='admin',
                password=DJANGO_SUPERUSER_PASSWORD
            )
            self.stdout.write(self.style.SUCCESS('Successfully created default admin user'))
        else:
            self.stdout.write(self.style.WARNING('Default admin user already exists'))
