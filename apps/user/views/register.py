"""
init registration
"""
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.user.models import Users
from apps.core.views import ServiceBaseView
from apps.user.serializers.user import UserSerializer


class RegisterView(APIView, ServiceBaseView):
    """
    The user registration view.
    Handles user registration through POST requests.
    """

    def post(self, request):
        """
        Handle POST requests for user registration.
        """
        phone = request.data.get("phone")

        # Check if the user already exists
        existing_user = Users.check_user_exsist(phone=phone).last()
        if existing_user:
            return Response({
                "id": existing_user.id,
                "phone": existing_user.phone,
            }, status=status.HTTP_200_OK)

        try:
            # Validate and save the new user
            serializer = UserSerializer(data=request.data)
            if serializer.is_valid():
                user = serializer.save()
                return Response(UserSerializer(user).data, status=status.HTTP_201_CREATED)

        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to register user: {exc}",
                status_code=500,
                notify_admin=True,
            )

        # Handle invalid data
        raise self.call_service_exception(
            error_type="bad_request",
            message=serializer.errors,
            status_code=status.HTTP_400_BAD_REQUEST,
            notify_admin=True,
        )
