"""
the core functionality implementation
"""
import logging

from apps.core.icore import ICore


class Core(ICore):
    """
    The core functionality implementation.
    """
    # pylint: disable=W0621
    def __init__(self, logger: logging.Logger = None):
        self._logger = logger or logging.getLogger(__name__)

    def log(self, level, message):
        """
        Logs a message at the specified logging level.
        """
        if level == "debug":
            self._logger.debug(message)
        elif level == "info":
            self._logger.info(message)
        elif level == "warning":
            self._logger.warning(message)
        elif level == "error":
            self._logger.error(message)
        elif level == "critical":
            self._logger.critical(message)
        else:
            raise ValueError(f"Unknown logging level: {level}")


core = Core()
