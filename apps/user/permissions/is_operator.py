"""
the is owner or operator permission
"""
from rest_framework import permissions


from apps.user.enum.role import UserRoleEnums


class IsOperatorRole(permissions.BasePermission):
    """
    Custom permission to only allow admin users to edit objects.
    """
    def has_permission(self, request, view):
        is_operator = all([
            request.user,
            request.user.is_authenticated,
            request.user.role == UserRoleEnums.OPERATOR.value
        ])
        return is_operator
