import logging

from django.core.exceptions import ValidationError

from rest_framework import views
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny

from apps.bot.models.telegram import TelegramUser

from apps.core.views import ServiceBaseView
from apps.core.exceptions.service import ServiceAPIException

from apps.loyalty.serializers import (
    ApplyPromoSerializer,
    PromoCodeUsageSerializer,
    TelegramApplyPromoSerializer
)
from apps.loyalty.service import LoyaltyService


logger = logging.getLogger(__name__)


class ApplyPromoAPIView(views.APIView, ServiceBaseView):
    """
    API endpoint for validating promo codes
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        Validate promo code for authenticated user without applying it
        """
        serializer = ApplyPromoSerializer(data=request.data)
        accept_language = request.headers.get('Accept-Language', 'uz')
        if accept_language not in ['ru', 'uz', 'en']:
            accept_language = 'uz'

        if not serializer.is_valid():
            error_message = {
                "ru": "Неверный запрос",
                "uz": "Noto'g'ri so'rov",
                "en": "Invalid request"
            }
            raise ServiceAPIException(
                error_type="validation_error",
                message=error_message.get(accept_language, "Invalid request"),
                status_code=400
            )

        try:
            user_id = request.user.id
            promo_name = serializer.validated_data['promo_code']
            organization_id = serializer.validated_data['organization_id']

            # Get initiator from request headers
            initiator = request.headers.get('X-Initiator', 'web').lower()

            # Get order amount and items cost from request data if available
            order_amount = request.data.get('order_amount', 0)
            items_cost = request.data.get('items_cost')

            # Validate promo code without applying it
            validation_result = LoyaltyService.validate_promo_code(
                promo_name=promo_name,
                user_id=user_id,
                organization_id=organization_id,
                initiator=initiator,
                order_amount=order_amount,
                items_cost=items_cost
            )

            if validation_result["is_valid"]:
                # Return success with promo details
                promo = validation_result["promo"]
                return Response({
                    "status": "success",
                    "message": "Promo code is valid",
                    "promo": {
                        "name": promo.name,
                        "amount": promo.promo_amount,
                        "type": promo.promocode_type,
                        "min_order_amount": promo.min_order_amount
                    }
                })

            # Return validation error
            error_message = {
                "ru": validation_result["message"],
                "uz": validation_result["message"],
                "en": validation_result["message"]
            }

            # Check for specific error types
            error_type = validation_result.get("error_type", "validation_error")

            if error_type == "promo_code_already_exists":
                error_message = {
                    "ru": "Вы уже используете этот промокод",
                    "uz": "Siz allaqachon bu promo kodni ishlatdingiz",
                    "en": "You are already using this promo code"
                }
            elif error_type == "invalid_initiator":
                error_message = {
                    "ru": "Промокод не может быть использован с этим инициатором",
                    "uz": "Promo kod bu initiator bilan ishlatilishi mumkin emas",
                    "en": "Promo code cannot be used with this initiator"
                }

            # Use ServiceAPIException directly instead of call_service_exception
            raise ServiceAPIException(
                error_type=error_type,
                message=error_message.get(accept_language, validation_result["message"]),
                status_code=400
            )

        except ServiceAPIException:
            # Re-raise ServiceAPIException without wrapping it
            raise
        except Exception as e:
            error_message = {
                "ru": "Ошибка при проверке промокода",
                "uz": "Promo kodni tekshirishda xatolik",
                "en": "Error validating promo code"
            }
            logger.error(f"Error validating promo code: {e}")
            raise ServiceAPIException(
                error_type="internal_error",
                message=error_message[accept_language],
                status_code=500
            ) from e

    def get(self, request):
        """
        Get list of user's active promos
        """
        active_promos = LoyaltyService.get_active_promos(request.user.id)
        serializer = PromoCodeUsageSerializer(active_promos, many=True)
        return Response(serializer.data)


class TelegramApplyPromoAPIView(views.APIView, ServiceBaseView):
    """
    API endpoint for validating promo codes for Telegram users

    This endpoint allows Telegram users to validate promo codes using their chat_id
    instead of requiring authentication.
    """
    permission_classes = [AllowAny]  # No authentication required

    def post(self, request):
        """
        Validate promo code for Telegram user without applying it

        Request body should contain:
        - chat_id: Telegram chat ID
        - promo_code: Promo code to validate
        - organization_id: Organization ID
        """
        serializer = TelegramApplyPromoSerializer(data=request.data)
        accept_language = request.headers.get('Accept-Language', 'uz')
        if accept_language not in ['ru', 'uz', 'en']:
            accept_language = 'uz'

        if not serializer.is_valid():
            error_message = {
                "ru": "Неверный запрос",
                "uz": "Noto'g'ri so'rov",
                "en": "Invalid request"
            }
            raise ServiceAPIException(
                error_type="bad_request",
                message=error_message[accept_language],
                status_code=400
            )

        try:
            chat_id = serializer.validated_data['chat_id']
            promo_name = serializer.validated_data['promo_code']
            organization_id = serializer.validated_data['organization_id']

            # Get initiator from request headers
            initiator = request.headers.get('X-Initiator', 'bot').lower()

            # Get Telegram user
            try:
                telegram_user = TelegramUser.get_by_chat_id(chat_id)
                if not telegram_user.user:
                    error_message = {
                        "uz": "Telegram foydalanuvchisi tizimda ro'yxatdan o'tmagan",
                        "ru": "Пользователь Telegram не зарегистрирован в системе",
                        "en": "Telegram user is not registered in the system"
                    }
                    raise ServiceAPIException(
                        error_type="user_not_registered",
                        message=error_message[accept_language],
                        status_code=400
                    )

                user_id = telegram_user.user.id
            except TelegramUser.DoesNotExist as exc:
                error_message = {
                    "uz": "Telegram foydalanuvchisi topilmadi",
                    "ru": "Пользователь Telegram не найден",
                    "en": "Telegram user not found"
                }
                raise ServiceAPIException(
                    error_type="user_not_found",
                    message=error_message[accept_language],
                    status_code=400
                ) from exc

            # Get order amount and items cost from request data if available
            order_amount = request.data.get('order_amount', 0)
            items_cost = request.data.get('items_cost')

            # Validate promo code without applying it
            validation_result = LoyaltyService.validate_promo_code(
                promo_name=promo_name,
                user_id=user_id,
                organization_id=organization_id,
                initiator=initiator,
                order_amount=order_amount,
                items_cost=items_cost
            )

            if validation_result["is_valid"]:
                # Return success with promo details
                promo = validation_result["promo"]
                return Response({
                    "status": "success",
                    "message": "Promo code is valid",
                    "promo": {
                        "name": promo.name,
                        "amount": promo.promo_amount,
                        "type": promo.promocode_type,
                        "min_order_amount": promo.min_order_amount
                    }
                })

            # Return validation error
            error_message = {
                "ru": validation_result["message"],
                "uz": validation_result["message"],
                "en": validation_result["message"]
            }

            # Check for specific error types
            error_type = validation_result.get("error_type", "validation_error")

            if error_type == "promo_code_already_exists":
                error_message = {
                    "ru": "Вы уже используете этот промокод",
                    "uz": "Siz allaqachon bu promo kodni ishlatdingiz",
                    "en": "You are already using this promo code"
                }
            elif error_type == "invalid_initiator":
                error_message = {
                    "ru": "Промокод не может быть использован с этим инициатором",
                    "uz": "Promo kod bu initiator bilan ishlatilishi mumkin emas",
                    "en": "Promo code cannot be used with this initiator"
                }

            # Use ServiceAPIException directly instead of call_service_exception
            raise ServiceAPIException(
                error_type=error_type,
                message=error_message.get(accept_language, validation_result["message"]),
                status_code=400
            )

        except ServiceAPIException:
            # Re-raise ServiceAPIException without wrapping it
            raise
        except Exception as e:
            logger.error(f"Error validating promo code for Telegram user: {e}")
            error_message = {
                "uz": "Promo kodni tekshirishda tizim xatosi",
                "ru": "Ошибка при проверке промокода",
                "en": "Error validating promo code"
            }
            raise ServiceAPIException(
                error_type="internal_error",
                message=error_message[accept_language],
                status_code=500
            ) from e

    def get(self, request):
        """
        Get list of Telegram user's active promos

        Query parameters:
        - chat_id: Telegram chat ID
        """
        accept_language = request.headers.get('Accept-Language', 'uz')
        if accept_language not in ['ru', 'uz', 'en']:
            accept_language = 'uz'

        chat_id = request.query_params.get('chat_id')
        if not chat_id:
            error_message = {
                "ru": "Требуется chat_id",
                "uz": "chat_id talab qilinadi",
                "en": "chat_id is required"
            }
            raise ServiceAPIException(
                error_type="missing_parameter",
                message=error_message[accept_language],
                status_code=400
            )

        try:
            chat_id = int(chat_id)
        except ValueError as exc:
            error_message = {
                "ru": "chat_id должен быть целым числом",
                "uz": "chat_id butun son bo'lishi kerak",
                "en": "chat_id must be an integer"
            }
            raise ServiceAPIException(
                error_type="invalid_parameter",
                message=error_message[accept_language],
                status_code=400
            ) from exc

        try:
            # Get active promos for Telegram user
            active_promos = LoyaltyService.get_active_promos_for_telegram_user(chat_id)
            serializer = PromoCodeUsageSerializer(active_promos, many=True)
            return Response(serializer.data)

        except ValidationError as e:
            logger.error(f"Validation error getting promos for Telegram user: {e}")
            raise ServiceAPIException(
                error_type="validation_error",
                message=str(e),
                status_code=400
            ) from e

        except Exception as e:
            logger.error(f"Error getting promos for Telegram user: {e}")
            raise ServiceAPIException(
                error_type="internal_error",
                message=f"An unexpected error occurred: {str(e)}",
                status_code=500
            ) from e
