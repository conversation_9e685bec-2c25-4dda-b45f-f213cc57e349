"""
Screen popup functionality for incoming calls
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from apps.core.views import ServiceBaseView
from apps.user.models.user import Users
from apps.order.models.order import Order
from apps.softphone.models.call_log import CallLog


class ScreenPopupAPIView(APIView, ServiceBaseView):
    """
    API view for handling screen popup requests from Softphone.Pro
    This endpoint is called when an incoming call is received
    """
    
    def get(self, request):
        """
        Handle screen popup requests
        Expected parameters:
        - callerid: Phone number of the caller
        - did: DID number that was called
        - direction: 'inbound' or 'outbound'
        """
        try:
            caller_id = request.query_params.get('callerid')
            did = request.query_params.get('did')
            direction = request.query_params.get('direction', 'inbound')
            
            if not caller_id:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Caller ID is required",
                    status_code=400
                )
            
            # Clean caller ID (remove + if present)
            clean_caller_id = caller_id.replace('+', '')
            
            # Find customer by phone number
            customer_info = None
            customer_orders = []
            
            try:
                customer = Users.objects.get(phone=clean_caller_id)
                customer_info = {
                    "id": customer.id,
                    "name": customer.name,
                    "phone": customer.phone,
                    "email": customer.email,
                    "role": customer.role,
                    "is_verified": customer.is_verified,
                    "lang": customer.lang,
                    "gender": customer.gender,
                    "date_of_birth": customer.date_of_birth.isoformat() if customer.date_of_birth else None
                }
                
                # Get recent orders for this customer
                recent_orders = Order.objects.filter(
                    user=customer
                ).select_related('order_detail', 'organization').order_by('-created_at')[:5]
                
                for order in recent_orders:
                    order_data = {
                        "id": order.id,
                        "status": order.status,
                        "status_display": order.get_status_display() if hasattr(order, 'get_status_display') else order.status,
                        "created_at": order.created_at.isoformat(),
                        "external_id": order.external_id,
                        "total_cost": order.order_detail.total_cost if order.order_detail else 0,
                        "delivery_address": order.order_detail.delivery_address if order.order_detail else None,
                        "organization_name": order.organization.name if order.organization else None
                    }
                    customer_orders.append(order_data)
                    
            except Users.DoesNotExist:
                # Customer not found - this might be a new customer
                customer_info = {
                    "phone": clean_caller_id,
                    "is_new_customer": True,
                    "message": "New customer - not found in database"
                }
            
            # Get call history for this number
            call_history = []
            recent_calls = CallLog.objects.filter(
                caller_id=clean_caller_id
            ).order_by('-start_time')[:10]
            
            for call in recent_calls:
                call_data = {
                    "id": call.id,
                    "direction": call.direction,
                    "status": call.status,
                    "duration": call.duration,
                    "formatted_duration": call.formatted_duration,
                    "start_time": call.start_time.isoformat(),
                    "notes": call.notes,
                    "recording_url": call.recording_url
                }
                call_history.append(call_data)
            
            # Prepare response data
            popup_data = {
                "call_info": {
                    "caller_id": clean_caller_id,
                    "did": did,
                    "direction": direction,
                    "timestamp": request.META.get('HTTP_DATE', '')
                },
                "customer_info": customer_info,
                "recent_orders": customer_orders,
                "call_history": call_history,
                "actions": {
                    "create_order_url": f"/admin/order/order/add/?customer_id={customer_info.get('id', '')}" if customer_info and customer_info.get('id') else None,
                    "customer_profile_url": f"/admin/user/users/{customer_info.get('id', '')}/change/" if customer_info and customer_info.get('id') else None,
                    "answer_call_url": "softphone://answer",
                    "hangup_call_url": "softphone://hangup"
                }
            }
            
            return Response(popup_data, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to generate screen popup data: {str(exc)}",
                status_code=500,
                notify_admin=True
            )


class CallControlAPIView(APIView, ServiceBaseView):
    """
    API view for call control actions (answer, hangup, transfer)
    """
    
    def post(self, request):
        """
        Handle call control actions
        Expected payload:
        {
            "action": "answer|hangup|transfer",
            "caller_id": "+998901234567",
            "transfer_to": "+998907654321" (for transfer action)
        }
        """
        try:
            action = request.data.get('action')
            caller_id = request.data.get('caller_id')
            transfer_to = request.data.get('transfer_to')
            
            if not action or not caller_id:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Action and caller_id are required",
                    status_code=400
                )
            
            # Generate appropriate softphone URL based on action
            if action == 'answer':
                control_url = "softphone://answer"
            elif action == 'hangup':
                control_url = "softphone://hangup"
            elif action == 'transfer':
                if not transfer_to:
                    raise self.call_service_exception(
                        error_type="bad_request",
                        message="transfer_to is required for transfer action",
                        status_code=400
                    )
                clean_transfer_to = transfer_to.replace('+', '')
                control_url = f"softphone://transfer/{clean_transfer_to}"
            else:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Invalid action. Must be 'answer', 'hangup', or 'transfer'",
                    status_code=400
                )
            
            return Response({
                "success": True,
                "action": action,
                "control_url": control_url,
                "caller_id": caller_id,
                "transfer_to": transfer_to if action == 'transfer' else None,
                "message": f"Call {action} command generated successfully"
            }, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to generate call control command: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
