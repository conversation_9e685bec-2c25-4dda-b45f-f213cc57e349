"""
the hr bot user model
"""
from django.db import models
from apps.core.models.base import BaseModel


class User(BaseModel):
    LANGUAGE_CHOICES = [
        ('uz', 'Uzbek'),
        ('ru', 'Russian'),
    ]

    id = models.BigAutoField(primary_key=True)
    username = models.CharField(max_length=200,  null=True)
    first_name = models.CharField(max_length=200,  null=True)
    last_name = models.CharField(max_length=200, null=True, blank=True)
    language = models.CharField(
        max_length=2,
        choices=LANGUAGE_CHOICES,
        default='uz',
    )

    class Meta:
        """
        the meta class
        """
        db_table = 'hr_users'
        verbose_name = 'HR User'
        verbose_name_plural = 'HR Users'

    def __str__(self):
        return f"User {self.id} - {self.language}"

    @classmethod
    def update_user_language(cls, user_id, language):
        """
        Update the language preference for a specific user
        """
        try:
            user = cls.objects.get(id=user_id)
            user.language = language
            user.save()
        except cls.DoesNotExist:
            raise ValueError(f"User with ID {user_id} does not exist.")

    @classmethod
    def get_user_language(cls, user_id):
        """
        Retrieve the user's language preference
        """
        try:
            user = cls.objects.get(id=user_id)
            return user.language
        except cls.DoesNotExist:
            return 'uz'
