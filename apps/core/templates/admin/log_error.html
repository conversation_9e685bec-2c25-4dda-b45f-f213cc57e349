{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .error-container {
        margin: 20px 0;
        padding: 30px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
    }
    .error-message {
        color: #c00;
        font-weight: bold;
        background-color: #fff0f0;
        padding: 20px;
        border-radius: 4px;
        border-left: 4px solid #c00;
        margin-bottom: 20px;
    }
    .back-link {
        margin-top: 30px;
    }
    .back-link .button {
        background-color: #79aec8;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
        display: inline-flex;
        align-items: center;
        font-weight: 500;
        text-decoration: none;
    }
    .back-link .button:hover {
        background-color: #417690;
    }
    .back-link .button i {
        margin-right: 8px;
    }
    .card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }
    .card-header {
        background-color: #c00;
        color: white;
        padding: 15px 20px;
        font-weight: 500;
        font-size: 1.1em;
    }
    .card-body {
        padding: 20px;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:core_log_file' %}">{% trans 'Log File Management' %}</a>
    &rsaquo; {% trans 'Error' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <h1><i class="fas fa-exclamation-triangle"></i> {% trans 'Log File Error' %}</h1>

    <div class="error-container">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-exclamation-circle"></i> {% trans 'Error Details' %}
            </div>
            <div class="card-body">
                <div class="error-message">
                    <p>{{ error_message }}</p>
                </div>

                <div class="back-link">
                    <a href="{% url 'admin:core_log_file' %}" class="button">
                        <i class="fas fa-arrow-left"></i> {% trans 'Back to Log File Management' %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
