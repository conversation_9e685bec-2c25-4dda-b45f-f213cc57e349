"""
organization helper
"""
from apps.core import core
from apps.iiko.providers.iiko.http import client
from apps.organization.models.terminal import TerminalGroup
from apps.iiko.providers.iiko.http.client import IIKOClientAPI
from apps.iiko.providers.iiko.http.client import client as provider


from apps.organization.models.organtzation import Organization


class OrganizationHelper:
    """
    The organization helper class
    """
    def __init__(self, provider: IIKOClientAPI):
        self.client = provider

    def update_organization(self):
        """
        update organization
        """
        organization_ids = []
        result = self.client.variables.organizations(
            headers=self.client.variables.get_headers()
        )

        for organization in result.organizations:
            # updating organization from database
            organization_obj = Organization.update_or_create_organization(
                name=organization.name,
                external_id=organization.id,
                code=organization.code,
            )
            organization_ids.append(organization_obj.external_id)

        result = self.client.delivery.delivery_restrictions(
            headers=self.client.variables.get_headers(),
            organizations_ids=organization_ids
        )

        for restriction in result.deliveryRestrictions:
            # updating delivery restrictions from database
            Organization.update_restrictions(
                organization_id=restriction.organizationId,
                delivery_duration=restriction.defaultDeliveryDurationInMinutes,
                self_service_duration=restriction.defaultSelfServiceDurationInMinutes,
                default_min_sum=restriction.defaultMinSum,
            )

    def update_terminal_groups(self):
        """
        Updating terminal groups for a list of organizations
        """
        # Get the list of organizations
        organizations = Organization.get_active_organizations()

        for organization in organizations:
            try:
                # Fetch terminal groups from the API
                response = client.variables.variables.terminal_groups(
                    headers=client.variables.variables.get_headers(),
                    organization_id=organization.external_id  # Use the organization's external_id
                )

                for terminal_group in response.terminalGroups:
                    for item in terminal_group.items:
                        TerminalGroup.update_or_create_terminal_group(
                            external_id=item.id,
                            organization_external_id=organization.external_id,
                            name=item.name,
                            address=item.address
                        )

                core.log("info", f"Updated terminal groups for organization {organization.name}")

            except Exception as e:
                core.log("error", f"Error updating terminal groups for organization {organization.name}: {e}")

    def check_organization_is_alive(self):
        organizations = Organization.get_in_use_all()

        for organization in organizations:
            terminal_group = TerminalGroup.get_active_terminal_group(organization.id)
            results = self.client.terminal_group.is_alive(
                organization_id=str(organization.external_id),
                terminal_group_id=str(terminal_group.external_id),
                headers=self.client.variables.get_headers()
            )

            for result in results.isAliveStatus:
                if result.organizationId == organization.external_id and result.terminalGroupId == terminal_group.external_id: # noqa
                    if result.isAlive:
                        organization.activate()
                        return True

                    organization.deactivate()
                    return

        Organization.update_cache()


organization_helper = OrganizationHelper(
    provider=provider
)
