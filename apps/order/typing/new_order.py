"""
init order typing
"""
from typing import Literal, Optional

from pydantic import BaseModel


class OrderLocation(BaseModel):
    """
    new order message location field
    """
    latitude: float
    longitude: float


class OrderData(BaseModel):
    """
    new order message data field
    """
    order_id: int
    order_location: OrderLocation
    address: str
    client_phone: str
    delivery_cost: float = None
    complete_before: Optional[str] = None


class NewOrderMessage(BaseModel):
    """
    new order message model type
    """
    type: Literal['new_order_message']
    data: OrderData

    def to_firebase(self):
        """
        convert to firebase message
        """
        complete_before = None

        if self.data.complete_before:
            complete_before = str(self.data.complete_before)

        return {
            'type': str(self.type),
            'order_id': str(self.data.order_id),
            "latitude": str(self.data.order_location.latitude),
            "longitude": str(self.data.order_location.longitude),
            'address': str(self.data.address),
            'client_phone': str(self.data.client_phone),
            "delivery_cost": str(self.data.delivery_cost),
            'complete_before': complete_before
        }
