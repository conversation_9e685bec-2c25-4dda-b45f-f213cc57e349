"""
this permission helps to protect objects that means that only object owner can
edit or delete that object
"""
from rest_framework import permissions

from apps.user.enum.role import UserRoleEnums


class IsAdminOrOperatorOrManager(permissions.BasePermission):
    """
    Object-level permission to allow only admins, operators, or managers to edit objects.
    """

    def has_permission(self, request, view):
        # General-level permission check
        return request.user.is_authenticated and request.user.role in [
            UserRoleEnums.ADMIN.value,
            UserRoleEnums.OPERATOR.value,
            UserRoleEnums.MANAGERS.value
        ]

    def has_object_permission(self, request, view, obj):
        # Object-level permission check
        return self.has_permission(request, view)


class OrderHistoryPermissionClass(permissions.BasePermission):
    """
    Object-level permission to allow only admins, operators, or managers to edit objects.
    """

    def has_permission(self, request, view):
        # General-level permission check
        return request.user.is_authenticated and request.user.role in [
            UserRoleEnums.ADMIN.value,
            UserRoleEnums.OPERATOR.value,
            UserRoleEnums.MANAGERS.value,
            UserRoleEnums.CLIENT.value,
        ]

    def has_object_permission(self, request, view, obj):
        # Object-level permission check
        return self.has_permission(request, view)
