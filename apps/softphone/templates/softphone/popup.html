<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Incoming Call - Master Kebab</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .call-popup {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
            overflow: hidden;
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .call-header {
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .call-header h2 {
            margin: 0;
            font-size: 24px;
        }
        
        .caller-info {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .caller-phone {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .caller-name {
            font-size: 20px;
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        
        .customer-type {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .customer-type.new { background: #e74c3c; color: white; }
        .customer-type.regular { background: #3498db; color: white; }
        .customer-type.vip { background: #f39c12; color: white; }
        
        .customer-details {
            padding: 20px;
            background: #f8f9fa;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        
        .detail-value {
            color: #6c757d;
        }
        
        .recent-orders {
            padding: 20px;
        }
        
        .recent-orders h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        .order-item {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }
        
        .order-status {
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
        }
        
        .order-status.delivered { color: #27ae60; }
        .order-status.in-progress { color: #f39c12; }
        .order-status.cancelled { color: #e74c3c; }
        
        .call-actions {
            padding: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
            background: #f8f9fa;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-answer {
            background: #27ae60;
            color: white;
        }
        
        .btn-answer:hover {
            background: #229954;
            transform: translateY(-2px);
        }
        
        .btn-hangup {
            background: #e74c3c;
            color: white;
        }
        
        .btn-hangup:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        
        .btn-create-order {
            background: #3498db;
            color: white;
        }
        
        .btn-create-order:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="call-popup">
        <div class="call-header">
            <h2>📞 Incoming Call</h2>
        </div>
        
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>Loading customer information...</p>
        </div>
        
        <div id="call-content" style="display: none;">
            <div class="caller-info">
                <div class="caller-phone" id="caller-phone"></div>
                <div class="caller-name" id="caller-name"></div>
                <span class="customer-type" id="customer-type"></span>
            </div>
            
            <div class="customer-details" id="customer-details">
                <!-- Customer details will be populated here -->
            </div>
            
            <div class="recent-orders" id="recent-orders" style="display: none;">
                <h3>Recent Orders</h3>
                <div id="orders-list">
                    <!-- Orders will be populated here -->
                </div>
            </div>
            
            <div class="call-actions">
                <button class="btn btn-answer" onclick="answerCall()">Answer</button>
                <button class="btn btn-hangup" onclick="hangupCall()">Decline</button>
                <a class="btn btn-create-order" id="create-order-btn" href="#" target="_blank">Create Order</a>
            </div>
        </div>
    </div>

    <script>
        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const callerId = urlParams.get('callerid');
        const did = urlParams.get('did');
        const direction = urlParams.get('direction');
        
        // Load customer data when page loads
        window.onload = function() {
            if (callerId) {
                loadCustomerData(callerId);
            } else {
                showError('No caller ID provided');
            }
        };
        
        async function loadCustomerData(phone) {
            try {
                const response = await fetch(`/api/v1/softphone/customer-data/?phone=${phone}`);
                const data = await response.json();
                
                document.getElementById('loading').style.display = 'none';
                document.getElementById('call-content').style.display = 'block';
                
                displayCustomerInfo(data);
            } catch (error) {
                console.error('Error loading customer data:', error);
                showError('Failed to load customer information');
            }
        }
        
        function displayCustomerInfo(data) {
            // Display phone number
            document.getElementById('caller-phone').textContent = '+' + data.call_info?.caller_id || callerId;
            
            if (data.found && data.customer) {
                // Existing customer
                const customer = data.customer;
                document.getElementById('caller-name').textContent = customer.name || 'Unknown Customer';
                
                // Customer type
                const customerType = determineCustomerType(data.statistics);
                const typeElement = document.getElementById('customer-type');
                typeElement.textContent = customerType;
                typeElement.className = `customer-type ${customerType.toLowerCase()}`;
                
                // Customer details
                displayCustomerDetails(customer, data.statistics);
                
                // Recent orders
                if (data.recent_orders && data.recent_orders.length > 0) {
                    displayRecentOrders(data.recent_orders);
                }
                
                // Set create order link
                document.getElementById('create-order-btn').href = data.actions?.create_order || '#';
                
            } else {
                // New customer
                document.getElementById('caller-name').textContent = 'New Customer';
                document.getElementById('customer-type').textContent = 'NEW';
                document.getElementById('customer-type').className = 'customer-type new';
                
                // Show new customer message
                document.getElementById('customer-details').innerHTML = `
                    <div class="detail-row">
                        <span class="detail-label">Status:</span>
                        <span class="detail-value">New customer - not in database</span>
                    </div>
                `;
                
                // Set create customer link
                document.getElementById('create-order-btn').href = data.actions?.create_customer || '#';
                document.getElementById('create-order-btn').textContent = 'Create Customer';
            }
        }
        
        function displayCustomerDetails(customer, stats) {
            const detailsHtml = `
                <div class="detail-row">
                    <span class="detail-label">Email:</span>
                    <span class="detail-value">${customer.email || 'Not provided'}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Total Orders:</span>
                    <span class="detail-value">${stats?.total_orders || 0}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Total Spent:</span>
                    <span class="detail-value">${stats?.total_spent || 0} UZS</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Success Rate:</span>
                    <span class="detail-value">${Math.round(stats?.success_rate || 0)}%</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Language:</span>
                    <span class="detail-value">${customer.lang?.toUpperCase() || 'UZ'}</span>
                </div>
            `;
            
            document.getElementById('customer-details').innerHTML = detailsHtml;
        }
        
        function displayRecentOrders(orders) {
            const ordersHtml = orders.map(order => `
                <div class="order-item">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>Order #${order.id}</strong>
                            <div class="order-status ${order.status.toLowerCase()}">${order.status_display || order.status}</div>
                        </div>
                        <div style="text-align: right;">
                            <div><strong>${order.total_cost} UZS</strong></div>
                            <div style="font-size: 12px; color: #6c757d;">
                                ${new Date(order.created_at).toLocaleDateString()}
                            </div>
                        </div>
                    </div>
                    ${order.delivery_address ? `<div style="margin-top: 5px; font-size: 12px; color: #6c757d;">${order.delivery_address}</div>` : ''}
                </div>
            `).join('');
            
            document.getElementById('orders-list').innerHTML = ordersHtml;
            document.getElementById('recent-orders').style.display = 'block';
        }
        
        function determineCustomerType(stats) {
            if (!stats || stats.total_orders === 0) return 'NEW';
            if (stats.total_orders >= 20) return 'VIP';
            if (stats.total_orders >= 5) return 'REGULAR';
            return 'OCCASIONAL';
        }
        
        function answerCall() {
            // Send answer command to Softphone.Pro
            window.location.href = 'softphone://answer';
        }
        
        function hangupCall() {
            // Send hangup command to Softphone.Pro
            window.location.href = 'softphone://hangup';
            // Close popup window
            setTimeout(() => window.close(), 500);
        }
        
        function showError(message) {
            document.getElementById('loading').innerHTML = `
                <div style="color: #e74c3c; text-align: center;">
                    <h3>Error</h3>
                    <p>${message}</p>
                </div>
            `;
        }
        
        // Auto-close popup after 30 seconds if no action taken
        setTimeout(() => {
            if (confirm('Call popup will close automatically. Continue?')) {
                // Keep open
            } else {
                window.close();
            }
        }, 30000);
    </script>
</body>
</html>
