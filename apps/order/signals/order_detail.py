"""
Order signal handlers to manage post-save actions for the OrderDetail model.

This module listens for changes in the OrderDetail model and sends appropriate
notifications to Telegram users when an order requires a non-cash payment. The signal
sends a message to the user only once, using caching to avoid duplicate notifications
for the same order within a specific time frame.
"""

from django.core.cache import cache
from django.dispatch import receiver
from django.db.models.signals import post_save

from apps.bot.enum import OrderStates
from apps.order.models import OrderDetail
from apps.bot.tasks import send_message_task
from apps.core.enums.initiator import Initiator
from apps.payment.service import PaymentService
from apps.payment.enums.method import PaymentMethodEnum
from apps.order.enums.order import OrderStatus as OrderStatusEnum


ORDER_APPROVED_NON_CASH = {
    "uz": "🍔 Buyurtma {ID} yaratildi, buyurtmani tasdiqlash uchun quyidagi havola orqali To'lovni amalga oshiring.\n\n{payment_method} orqali to'lash\nto'lov qiymati: {amount} UZS\nTo'lovni amalga oshirish uchun 💳 To'lash knopkasini bosing.",  # noqa
    "ru": "🍔 Заказ № {ID} создан. Для подтверждения заказа вам нужно нажать кнопку 💳 для оплаты.\n\n{payment_method} оплата\nСумма: {amount} UZS\nНажмите кнопку 💳 для оплаты."  # noqa
}

ORDER_CREATED_TEXT = {
    "ru": "🥘 Для вас создан заказ № {ID}. Наши операторы свяжутся с вами в ближайшее время.",
    "uz": "🥘 Siz uchun № {ID} raqamli buyurtma yaratildi. Yaqin orada bizning operatorlarimiz siz bilan bog'lanishadi"  # noqa
}

PAY_BUTTON_TEXT = {
    "uz": "💳 To'lamoq",
    "ru": "💳 Оплатить"
}

CACHE_TIMEOUT = 3600


@receiver(post_save, sender=OrderDetail)
def handle_order_signal(sender, instance: OrderDetail, **kwargs):
    """
    Handle post-save signals for the OrderDetail model.

    This signal triggers when an OrderDetail object is saved and processes
    orders that require non-cash payments. The notification is sent only once
    per order within a set timeframe to avoid duplicates.

    The signal will:
    1. Validate the order and payment method.
    2. If valid, send a Telegram message to the user with payment instructions.

    Args:
        sender: The model class that sent the signal (OrderDetail in this case).
        instance: The instance of the OrderDetail being saved.
        kwargs: Additional arguments passed with the signal.
    """
    if not __validate_order(instance):
        return

    __handle_created_order(instance)


def __validate_order(instance: OrderDetail) -> bool:
    """
    Validate the order instance to determine if the signal should be processed.

    This includes checking if:
    1. The order exists and uses a non-cash payment method.
    2. The order was initiated by the bot.
    3. No duplicate message was already sent within the cache timeout period.

    Args:
        instance: The OrderDetail instance being validated.

    Returns:
        bool: True if the instance is valid for further processing, False otherwise.
    """
    if not instance.order or instance.order.operator:
        return False

    if instance.order.initiator != Initiator.BOT:
        return False

    if instance.order.total_cost <= 0:
        return False

    unique_key = f"order:{instance.order.status}:{instance.order.id}"
    if cache.get(unique_key):
        return False

    return True


def __handle_created_order(instance: OrderDetail):
    """
    Handle the creation of non-cash and cash orders by sending a message to the Telegram user.

    This function will:
    1. Check the payment method.
    2. Send a notification message with payment details.
    3. Ensure the message is sent only once using a cache to track sent notifications.

    Args:
        instance: The OrderDetail instance being processed.
    """
    if instance.order.status != OrderStatusEnum.CREATED:
        return

    telegram = instance.order.get_tg_user()
    chat_id = telegram.id
    unique_key = f"order:{instance.order.status}:{instance.order.id}"

    if instance.payment_method == PaymentMethodEnum.CASH:
        message = ORDER_CREATED_TEXT.get(telegram.lang).format(ID=instance.order.id)
        button_text = None
        button_url = None

    else:
        message = ORDER_APPROVED_NON_CASH.get(telegram.lang).format(
            ID=instance.order.id,
            payment_method=instance.payment_method,
            amount=instance.order.total_cost
        )
        button_text = PAY_BUTTON_TEXT[telegram.lang]
        button_url = PaymentService.create_payment(instance.order.id)

    state = OrderStates.ORDER_CREATED
    send_message_task.apply_async(
        kwargs={
            'chat_id': chat_id,
            'message': message,
            'state': state,
            'order_id': instance.id,
            'button_text': button_text,
            'button_url': button_url
        },
        task_id=unique_key,
    )

    cache.set(unique_key, True, CACHE_TIMEOUT)
