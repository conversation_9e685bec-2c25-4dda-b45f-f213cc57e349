"""
the order status helpers
"""
from django.db import transaction

from apps.courier.models.courier import Courier
from apps.user.enum.agent import AgentStatusEnums
from apps.order.models import order as order_model
from apps.core.exceptions import ServiceAPIException
from apps.operations.models.operations import Operations
from apps.iiko.providers.iiko.http.client import client
from apps.order.enums.order import OrderStatus as OrderStatusEnum


class OrderStatusHelper:
    """
    the order status helper class
    """
    @classmethod
    def check_order_existence(cls, order_id):
        """
        check order existence
        """
        if not order_model.Order.check_order_exists(order_id):
            raise ServiceAPIException(
                error_type="not_found",
                message=f"Order not found - {order_id}",
                status_code=404
            )

    @classmethod
    def get_order_by_id(cls, order_id):
        """
        check order existence
        """
        return order_model.get_order_by_id(order_id)

    @classmethod
    def get_orders_by_status(cls, status):
        """
        getting orders by status
        """
        return order_model.get_orders_by_status(status)

    @classmethod
    def get_and_validate_order(cls, order_id, status):
        """
        get and validate order
        """
        order = order_model.get_order_by_id(order_id)

        if status == OrderStatusEnum.ON_WAY:
            if order.status != OrderStatusEnum.APPROVED:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message=f"Order status is not APPROVED - {order_id}",
                    status_code=400
                )

        elif status == OrderStatusEnum.CANCELLED:
            if order.status != OrderStatusEnum.ON_WAY:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message=f"Order status is not ON DELIVERY - {order_id}",
                    status_code=400
                )

        elif status == OrderStatusEnum.ARRIVED:
            if order.status != OrderStatusEnum.ON_WAY:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message=f"Order status is not ON DELIVERY - {order_id}",
                    status_code=400
                )

        elif status == OrderStatusEnum.CLOSED:
            if order.status != OrderStatusEnum.ARRIVED:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message=f"Order status is not ARRIVED - {order_id}",
                    status_code=400
                )

        return order

    @classmethod
    def update_order_status(
        cls,
        order: order_model.Order,
        new_status=None,
        operator_user_id=None,
        delivery_agent_user_id=None
    ):
        """
        update order status
        """
        if new_status:
            order.status = new_status
            if delivery_agent_user_id:
                delivery_agent = Courier.get_by_id(delivery_agent_user_id)
                order.delivery_agent = delivery_agent

        if operator_user_id and new_status == OrderStatusEnum.CREATED:
            taker = Operations.get_by_id(operator_user_id)
            from apps.order.service import OrderService
            OrderService.bind_to_taker(
                order_id=order.id,
                taker_id=taker.id,
                taker_type=taker.role
            )

        elif order.status == OrderStatusEnum.ARRIVED:
            with transaction.atomic():
                order.mark_as_arrived()
                order.delivery_agent.mark_as_arrived()

        elif new_status == OrderStatusEnum.APPROVED:
            order.mark_as_approved()
            return

        order.save()

    @classmethod
    def bind_operator_for_order(cls, order_id, operator_user_id):
        """
        bind operator for order
        """
        order = cls.get_order_by_id(order_id)
        taker = Operations.get_by_id(operator_user_id)
        from apps.order.service import OrderService
        OrderService.bind_to_taker(
            order_id=order.id,
            taker_id=taker.id,
            taker_type=taker.role
        )

    @classmethod
    def update_agent_status(cls, delivery_agent_user_id, new_status):
        """
        update agent status
        """
        delivery_agent = Courier.get_by_id(delivery_agent_user_id)

        if new_status == AgentStatusEnums.ON_WAY:
            if delivery_agent.status != AgentStatusEnums.AVAILABLE:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message=f"Delivery agent status is not AVAILABLE - {delivery_agent_user_id}",
                    status_code=400
                )

        if new_status == AgentStatusEnums.ON_WAY:
            if delivery_agent.status != AgentStatusEnums.AVAILABLE:
                raise ServiceAPIException(
                    error_type="bad_request",
                    message=f"Delivery agent status is not AVAILABLE - {delivery_agent_user_id}",
                    status_code=400
                )

        delivery_agent.status = new_status
        delivery_agent.save(update_fields=['status'])

    @classmethod
    def get_user_active_order(cls, user_id, user_type):
        """
        getting active orders for a customer
        """
        return order_model.get_active_order(user_id, user_type)

    @classmethod
    def bind_delivery_agent_with_provider(cls, order_ext_id, delivery_agent_ext_id):
        order = order_model.get_order_by_external_id(order_ext_id)
        return client.delivery.update_order_courier(
            order_id=order_ext_id,
            employee_id=delivery_agent_ext_id,
            headers=client.variables.get_headers(),
            organization_id=order.organization.external_id
        )

    @classmethod
    def cancel_order_with_provider(cls, order_ext_id):
        """
        cancel order with provider
        """
        order = order_model.get_order_by_external_id(order_ext_id)
        return client.delivery.cancel_confirmation(
            order_id=order_ext_id,
            headers=client.variables.get_headers(),
            organization_id=order.organization.external_id
        )

    @classmethod
    def finish_order_with_provider(cls, order_ext_id):
        """
        confirm order with provider
        """
        order = order_model.get_order_by_external_id(order_ext_id)
        return client.delivery.update_order_delivery_status(
            order_id=order_ext_id,
            delivery_status=OrderStatusEnum.DELIVERED,
            headers=client.variables.get_headers(),
            organization_id=order.organization.external_id
        )


order_status_helper = OrderStatusHelper()
