"""
the customer serialization
"""
from rest_framework import serializers

from apps.user.models.customer import Customer


class CustomerSerializer(serializers.ModelSerializer):
    """
    the customer serialization
    """
    class Meta:
        """
        the meta fields
        """
        model = Customer
        fields = [
            'id',
            'user',
            'name',
            'gender',
            'birthday',
            'email',
            'created_at',
        ]
        read_only_fields = [
            'user',
            'created_at',
            'updated_at'
        ]

    def create(self, validated_data):
        password = validated_data.get('password')
        customer = Customer(**validated_data)
        customer.user.set_password(password)
        customer.save()
        return customer

    def update(self, instance, validated_data):
        password = validated_data.pop('password', None)
        _ = validated_data.pop('user', None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        if password:
            instance.user.set_password(password)
        instance.save()
        return instance
