# Generated by Django 5.0.6 on 2024-09-25 07:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("bot", "0006_alter_messages_chat_id"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="messages",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="messages",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="telegramuser",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name="telegramuser",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
    ]
