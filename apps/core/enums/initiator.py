"""
the initiator enumuration
"""
from enum import Enum


class Initiator(str, Enum):
    """
    the enumeration of initiator
    """
    BOT = "bot"
    IOS = "ios"
    WEB = "web"
    ANDROID = "android"
    CALL_CENTER = "call-center"
    CLICK_SUPERAPP = "click-superapp"
    UNKNOWN = "unknown"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
