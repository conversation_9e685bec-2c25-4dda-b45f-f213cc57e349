"""
the enumeration of delivery
"""
from enum import Enum


class UserRoleEnums(str, Enum):
    """
    The enumeration of telegram user state.
    """
    ADMIN = "admin"
    CLIENT = "client"
    OPERATOR = "operator"
    COURIER = "courier"
    DISPATCHER = "dispatcher"
    MANAGERS = "managers"
    MARKETOLOG = "marketolog"
    DIRECTOR = "director"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]

    @classmethod
    def allowed_values(cls):
        """
        Returns a list of allowed values.
        """
        return [member.value for member in cls]
