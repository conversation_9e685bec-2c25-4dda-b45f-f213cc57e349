"""
Module for handling delivery-related views and logic.
"""
from rest_framework import status, views
from rest_framework.response import Response

from apps.core.views import ServiceBaseView
from apps.order.service import OrderService
from apps.order.serializers.delivery import DeliveryPriceSerializer


class DeliveryPriceView(views.APIView, ServiceBaseView):
    """
    APIView for retrieving delivery prices.
    """
    service: OrderService = OrderService

    def get(self, request, *args, **kwargs):
        """
        Retrieve a list of delivery prices and names.
        """
        try:
            delivery_prices = self.service.get_all_active_prices()
            serializer = DeliveryPriceSerializer(delivery_prices, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"internal service error exc: {exc}",
                status_code=500
            ) from exc
