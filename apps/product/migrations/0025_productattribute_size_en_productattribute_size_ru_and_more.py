# Generated by Django 5.0.6 on 2025-02-04 11:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0024_remove_productattribute_size_en_and_more"),
    ]

    operations = [
        migrations.AddField(
            model_name="productattribute",
            name="size_en",
            field=models.CharField(
                default="Default", max_length=100, null=True, verbose_name="Size name"
            ),
        ),
        migrations.AddField(
            model_name="productattribute",
            name="size_ru",
            field=models.CharField(
                default="Default", max_length=100, null=True, verbose_name="Size name"
            ),
        ),
        migrations.AddField(
            model_name="productattribute",
            name="size_uz",
            field=models.CharField(
                default="Default", max_length=100, null=True, verbose_name="Size name"
            ),
        ),
    ]
