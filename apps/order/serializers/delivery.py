"""
Serializers for delivery-related models in the order application.
"""

from rest_framework import serializers
from apps.order.models.delivery import DeliveryPrice


class DeliveryPriceSerializer(serializers.ModelSerializer):
    """
    Serializer for the DeliveryPrice model.

    Handles conversion of DeliveryPrice instances to/from JSON.
    Includes fields for human-readable representations.

    Attributes:
        display_name (Char<PERSON>ield): Read-only field for delivery price display name.
    """
    display_name = serializers.CharField(source='get_display_name', read_only=True)

    class Meta:
        """
        Meta class for DeliveryPriceSerializer.

        Specifies the model to be serialized, the fields to include,
        and which fields should be read-only.

        Attributes:
            model (Model): The DeliveryPrice model to be serialized.
            fields (list): The fields to be included in the serialization.
            read_only_fields (list): Fields that should be treated as read-only.
        """
        model = DeliveryPrice
        fields = ['external_id', 'display_name', 'price', 'is_active']
        read_only_fields = ['display_name']

    def to_representation(self, instance):
        """
        Convert DeliveryPrice instance to JSON representation.

        Args:
            instance (DeliveryPrice): The instance to be serialized.

        Returns:
            dict: JSON-compatible representation of the instance.
        """
        representation = super().to_representation(instance)
        return representation
