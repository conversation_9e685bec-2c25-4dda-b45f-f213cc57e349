"""
the notification service initalizes
"""
from apps.notify.models import Notification, NotificationAccepters
from apps.notify.providers.firebase.firebase import FirebaseNotificationService, firebase_provider


class NotificationService:
    """
    The notification service class.
    """
    def __init__(self, provider: FirebaseNotificationService):
        self.provider = provider

    def send_notification(self, title: str, body: str, token: str) -> str:
        return self.provider.send_notification(title, body, token)

    def create_notification(self, provider: str, title: str, body: str):
        return Notification.create_notification(
            provider=provider,
            title=title,
            body=body
        )

    def create_notification_accepter(
        self, notification_id: str, user_id: int,
        user_type: int, status: str, notification_identity=None
    ):
        return NotificationAccepters.create_notification_accepter(
            notification_id=notification_id,
            user_id=user_id,
            user_type=user_type,
            status=status,
            notification_identity=notification_identity
        )

    def set_notification_identity(
        self, notification_id: str, user_id: int, notification_identity: str, status: str, error_message: str = None
    ):
        return NotificationAccepters.set_notification_identity(
            notification_id=notification_id,
            user_id=user_id,
            notification_identity=notification_identity,
            status=status,
            error_message=error_message
        )


notification_service = NotificationService(provider=firebase_provider)
