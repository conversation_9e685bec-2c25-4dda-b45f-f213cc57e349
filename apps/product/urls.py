"""
the url patterns for order applications
"""
from django.urls import path, include

from rest_framework.routers import DefaultRouter

from apps.product.views.stop_list import StopListAPIView
from apps.product.views.category import CategoryListView, CategoryBulkUpdateView
from apps.product.views.category import CategoryRetrieveView
from apps.product.views.banner import BannerAPIView
from apps.product.views.banner import BannerDetailAPIView
from apps.product.views.product import ProductViewSet, UpdateProductAPIView
from apps.product.views.modifiers import ModifierItemsModelViewSet


router = DefaultRouter()
router.register(r'', ProductViewSet)
router.register(r'modifiers', ModifierItemsModelViewSet)


urlpatterns = [
    path('stop_list/', StopListAPIView.as_view()),
    path('categories/', CategoryListView.as_view(), name='category-list'),
    path('categories/<int:pk>/', CategoryRetrieveView.as_view(), name='category-retrieve'),
    path('banners/', BannerAPIView.as_view(), name='banner-list'),
    path('banners/<int:pk>/', BannerDetailAPIView.as_view(), name='banner-detail'),
    path('update_product/', UpdateProductAPIView.as_view(), name='update-product'),
    path('categories/bulk-update/', CategoryBulkUpdateView.as_view(), name='category-bulk-update'),
]

urlpatterns.extend([
    path('', include(router.urls)),  # noqa
])
