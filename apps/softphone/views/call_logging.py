"""
Call logging functionality for Softphone.Pro integration
"""
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.utils.dateparse import parse_datetime

from apps.core.views import ServiceBaseView
from apps.user.models.user import Users
from apps.order.models.order import Order
from apps.softphone.models.call_log import CallLog
from apps.softphone.serializers.call_log import CallLogSerializer


class CallLoggingAPIView(APIView, ServiceBaseView):
    """
    API view for logging calls from Softphone.Pro
    This endpoint receives call data and stores it in the database
    """
    
    def post(self, request):
        """
        Log a call from Softphone.Pro
        Expected payload:
        {
            "caller_id": "+************",
            "called_number": "+************",
            "direction": "inbound|outbound",
            "status": "answered|missed|busy|failed|no_answer",
            "duration": 120,
            "start_time": "2024-01-01T10:00:00Z",
            "end_time": "2024-01-01T10:02:00Z",
            "external_id": "softphone_call_123",
            "recording_url": "http://recordings.example.com/call_123.wav",
            "did_number": "+************",
            "sip_account": "account1",
            "notes": "Customer inquiry about order"
        }
        """
        try:
            # Extract call data from request
            call_data = {
                'caller_id': request.data.get('caller_id', '').replace('+', ''),
                'called_number': request.data.get('called_number', '').replace('+', ''),
                'direction': request.data.get('direction', 'inbound'),
                'status': request.data.get('status', 'answered'),
                'duration': int(request.data.get('duration', 0)),
                'start_time': request.data.get('start_time'),
                'end_time': request.data.get('end_time'),
                'external_id': request.data.get('external_id'),
                'recording_url': request.data.get('recording_url'),
                'did_number': request.data.get('did_number', '').replace('+', ''),
                'sip_account': request.data.get('sip_account'),
                'notes': request.data.get('notes', '')
            }
            
            # Parse datetime strings
            if call_data['start_time']:
                call_data['start_time'] = parse_datetime(call_data['start_time'])
            
            if call_data['end_time']:
                call_data['end_time'] = parse_datetime(call_data['end_time'])
            
            # Try to find associated user
            user = None
            if call_data['caller_id']:
                try:
                    user = Users.objects.get(phone=call_data['caller_id'])
                    call_data['user'] = user
                except Users.DoesNotExist:
                    pass
            
            # Try to find associated operator (for outbound calls)
            operator = None
            if call_data['direction'] == 'outbound' and call_data['called_number']:
                try:
                    # Assuming the operator is the one making the outbound call
                    # You might need to adjust this logic based on your system
                    operator = Users.objects.filter(
                        role__in=['operator', 'manager', 'admin']
                    ).first()
                    call_data['operator'] = operator
                except Users.DoesNotExist:
                    pass
            
            # Create call log entry
            call_log = CallLog.objects.create(**call_data)
            
            # Try to associate with existing order if possible
            self._associate_with_order(call_log)
            
            # Serialize the created call log
            serializer = CallLogSerializer(call_log)
            
            return Response({
                "success": True,
                "call_log": serializer.data,
                "message": "Call logged successfully"
            }, status=status.HTTP_201_CREATED)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to log call: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
    
    def _associate_with_order(self, call_log):
        """
        Try to associate the call with an existing order
        """
        try:
            if call_log.user:
                # Find recent orders for this user
                recent_order = Order.objects.filter(
                    user=call_log.user,
                    status__in=['Created', 'Approved', 'InProgress', 'WaitCooking', 'CookingStarted']
                ).order_by('-created_at').first()
                
                if recent_order:
                    call_log.order = recent_order
                    call_log.save()
        except Exception:
            # Ignore errors in order association
            pass


class CallHistoryAPIView(APIView, ServiceBaseView):
    """
    API view for retrieving call history
    """
    
    def get(self, request):
        """
        Get call history with optional filters
        Expected parameters:
        - phone: Filter by phone number
        - direction: Filter by call direction (inbound/outbound)
        - status: Filter by call status
        - date_from: Filter calls from this date
        - date_to: Filter calls to this date
        - limit: Number of results (default: 50)
        - offset: Pagination offset (default: 0)
        """
        try:
            # Get filter parameters
            phone = request.query_params.get('phone')
            direction = request.query_params.get('direction')
            status_filter = request.query_params.get('status')
            date_from = request.query_params.get('date_from')
            date_to = request.query_params.get('date_to')
            limit = int(request.query_params.get('limit', 50))
            offset = int(request.query_params.get('offset', 0))
            
            # Build queryset
            queryset = CallLog.objects.all().select_related('user', 'operator', 'order')
            
            if phone:
                clean_phone = phone.replace('+', '')
                queryset = queryset.filter(
                    models.Q(caller_id=clean_phone) | models.Q(called_number=clean_phone)
                )
            
            if direction:
                queryset = queryset.filter(direction=direction)
            
            if status_filter:
                queryset = queryset.filter(status=status_filter)
            
            if date_from:
                queryset = queryset.filter(start_time__gte=parse_datetime(date_from))
            
            if date_to:
                queryset = queryset.filter(start_time__lte=parse_datetime(date_to))
            
            # Apply pagination
            total_count = queryset.count()
            calls = queryset.order_by('-start_time')[offset:offset + limit]
            
            # Serialize results
            serializer = CallLogSerializer(calls, many=True)
            
            return Response({
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "calls": serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to retrieve call history: {str(exc)}",
                status_code=500,
                notify_admin=True
            )


class CallStatsAPIView(APIView, ServiceBaseView):
    """
    API view for call statistics
    """
    
    def get(self, request):
        """
        Get call statistics
        Expected parameters:
        - date_from: Statistics from this date
        - date_to: Statistics to this date
        - operator_id: Filter by operator ID
        """
        try:
            from django.db.models import Count, Avg, Sum
            from django.utils import timezone
            from datetime import timedelta
            
            # Get filter parameters
            date_from = request.query_params.get('date_from')
            date_to = request.query_params.get('date_to')
            operator_id = request.query_params.get('operator_id')
            
            # Default to last 30 days if no dates provided
            if not date_from:
                date_from = timezone.now() - timedelta(days=30)
            else:
                date_from = parse_datetime(date_from)
            
            if not date_to:
                date_to = timezone.now()
            else:
                date_to = parse_datetime(date_to)
            
            # Build base queryset
            queryset = CallLog.objects.filter(
                start_time__gte=date_from,
                start_time__lte=date_to
            )
            
            if operator_id:
                queryset = queryset.filter(operator_id=operator_id)
            
            # Calculate statistics
            total_calls = queryset.count()
            
            # Calls by direction
            direction_stats = queryset.values('direction').annotate(
                count=Count('id')
            ).order_by('direction')
            
            # Calls by status
            status_stats = queryset.values('status').annotate(
                count=Count('id')
            ).order_by('status')
            
            # Average call duration
            avg_duration = queryset.aggregate(
                avg_duration=Avg('duration')
            )['avg_duration'] or 0
            
            # Total call time
            total_duration = queryset.aggregate(
                total_duration=Sum('duration')
            )['total_duration'] or 0
            
            # Answered calls percentage
            answered_calls = queryset.filter(status='answered').count()
            answer_rate = (answered_calls / total_calls * 100) if total_calls > 0 else 0
            
            return Response({
                "period": {
                    "from": date_from.isoformat(),
                    "to": date_to.isoformat()
                },
                "total_calls": total_calls,
                "answered_calls": answered_calls,
                "answer_rate": round(answer_rate, 2),
                "average_duration": round(avg_duration, 2),
                "total_duration": total_duration,
                "direction_breakdown": list(direction_stats),
                "status_breakdown": list(status_stats)
            }, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to calculate call statistics: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
