"""
Map API Keys Management Module

This module provides the `MapApiKey` model for managing API keys
associated with different map services like Yandex Map, Google Map, and Mapbox.
It includes methods for retrieving active API keys, and marking keys as expired
when usage limits are reached.
"""

from django.db import models
from apps.core.models.base import BaseModel


class MapApiKey(BaseModel):
    """
    Model for storing and managing API keys for various map services.

    This model keeps track of the API keys for different map services, such as Yandex Map,
    Google Map, and Mapbox. It includes functionality to:

    - Retrieve the active API key in use.
    - Mark an API key as expired when its usage limit is reached.
    - Associate API keys with specific map services through a choice field.

    Attributes:
        api_key (str): The unique API key for the map service.
        app_name (str): The name of the map service (Yandex Map, Google Map, Mapbox).
        in_use (bool): Indicates if the API key is currently in use.
        limit_expired (bool): Indicates if the API key's usage limit has been reached.
    """

    # Define the choices for app_name
    YANDEX_MAP = 'yandex_map'
    GOOGLE_MAP = 'google_map'
    MAPBOX = 'mapbox'

    APP_NAME_CHOICES = [
        (YANDEX_MAP, 'Yandex Map'),
        (GOOGLE_MAP, 'Google Map'),
        (MAPBOX, 'Mapbox'),
    ]

    YANDEX_GEOCODE = "geocode"
    YANDEX_GEOSUGGESTION = "geosuggestion"
    YANDEX_GEOCODER = "geocoder"

    class Meta:
        """
        Meta options for the MapApiKey model.

        Specifies the database table name and the plural and singular names
        used for the model in Django's admin interface.
        """
        db_table = 'map_api_keys'
        verbose_name_plural = 'Map API Keys'
        verbose_name = 'Map API Key'

    api_key = models.CharField(max_length=255, unique=True)
    app_name = models.CharField(
        max_length=255,
        choices=APP_NAME_CHOICES,
        default=YANDEX_MAP
    )
    in_use = models.BooleanField(default=False)
    limit_expired = models.BooleanField(default=False)
    usage_type = models.CharField(
        max_length=255,
        choices=[
            (YANDEX_GEOCODE, 'Yandex Geocode'),
            (YANDEX_GEOSUGGESTION, 'Yandex Geosuggestion'),
            (YANDEX_GEOCODER, 'Yandex Geocoder'),
        ],
        default=YANDEX_GEOCODE,
    )
    owner = models.CharField(max_length=255, blank=True, null=True, help_text="Optional Example: Muhammadalive")

    def __str__(self):
        """
        Returns a string representation of the MapApiKey instance.

        Returns:
            str: The API key as a string.
        """
        return str(self.api_key)

    @classmethod
    def get_active_api_key(cls, app_name=None, usage_type=None):
        """
        Retrieve the active API key that is currently in use.

        The method filters API keys to find the first key that is marked as in use
        and has not reached its usage limit.

        Args:
            app_name (str, optional): Filter by app name (e.g., 'yandex_map').
            usage_type (str, optional): Filter by usage type (e.g., 'geosuggestion').

        Returns:
            MapApiKey or None: The active MapApiKey instance or None if no key is active.
        """
        queryset = cls.objects.filter(limit_expired=False)

        if app_name:
            queryset = queryset.filter(app_name=app_name)

        if usage_type:
            queryset = queryset.filter(usage_type=usage_type)

        return queryset.first()

    @classmethod
    def mark_as_limit_expired(cls, api_key):
        """
        Mark a specific API key as expired and deactivate it.

        This method updates the status of the provided API key, marking it as
        expired (limit_expired=True) and setting it to not in use (in_use=False).
        It then attempts to retrieve the next available active API key.

        Args:
            api_key (str): The API key to be marked as expired.

        Returns:
            MapApiKey or None: The next active MapApiKey instance or None if no other key is active.
        """
        cls.objects.filter(api_key=api_key).update(in_use=False, limit_expired=True)
        return cls.get_active_api_key()

    @classmethod
    def get_yandex_geosuggestion_key(cls):
        """
        Get an active Yandex Geosuggestion API key with available daily limit.

        Returns:
            MapApiKey or None: Active Yandex Geosuggestion key or None if none available.
        """
        from apps.map.models.api_usage import ApiUsage

        # Get all active Yandex Geosuggestion keys
        keys = cls.objects.filter(
            app_name=cls.YANDEX_MAP,
            usage_type=cls.YANDEX_GEOSUGGESTION,
            limit_expired=False
        )

        # Find a key with available daily limit
        for key in keys:
            if ApiUsage.check_limit_available(key):
                return key

        return None

    @classmethod
    def use_yandex_geosuggestion_key(cls):
        """
        Get a Yandex Geosuggestion key and increment its usage count.
        Automatically switches to next available key if current one reaches daily limit.

        Returns:
            tuple: (MapApiKey instance, remaining_requests)

        Raises:
            ValueError: If daily limit is exceeded for all keys.
        """
        from apps.map.models.api_usage import ApiUsage

        # Get all non-expired Yandex Geosuggestion keys
        available_keys = cls.objects.filter(
            app_name=cls.YANDEX_MAP,
            usage_type=cls.YANDEX_GEOSUGGESTION,
            limit_expired=False
        ).order_by('id')

        if not available_keys.exists():
            raise ValueError("No available Yandex Geosuggestion API keys")

        # Try each key until we find one with remaining quota
        for api_key in available_keys:
            try:
                # Check if this key has remaining quota
                if ApiUsage.check_limit_available(api_key):
                    usage_record = ApiUsage.increment_usage(api_key)
                    remaining = usage_record.daily_limit - usage_record.usage_count
                    return api_key, remaining
                else:
                    # Mark this key as limit expired
                    cls.objects.filter(id=api_key.id).update(limit_expired=True)
                    continue
            except ValueError:
                # If increment fails (limit reached), mark key as expired and try next
                cls.objects.filter(id=api_key.id).update(limit_expired=True)
                continue

        # If we get here, all keys are exhausted
        raise ValueError("All Yandex Geosuggestion API keys have reached their daily limit")

    @classmethod
    def reset_daily_limits(cls):
        """
        Reset the limit_expired flag for all API keys.
        This should be called daily to reset the limits for a new day.

        Returns:
            int: Number of keys that were reset.
        """
        return cls.objects.filter(limit_expired=True).update(limit_expired=False)

    @classmethod
    def get_yandex_geocoder_key(cls):
        """
        Get an active Yandex Geocoder API key with available daily limit.

        Returns:
            MapApiKey or None: Active Yandex Geocoder key or None if none available.
        """
        from apps.map.models.api_usage import ApiUsage

        # Get all active Yandex Geocoder keys
        keys = cls.objects.filter(
            app_name=cls.YANDEX_MAP,
            usage_type=cls.YANDEX_GEOCODER,
            limit_expired=False
        )

        # Find a key with available daily limit
        for key in keys:
            if ApiUsage.check_limit_available(key):
                return key

        return None

    @classmethod
    def use_yandex_geocoder_key(cls):
        """
        Get a Yandex Geocoder key and increment its usage count.
        Automatically switches to next available key if current one reaches daily limit.

        Returns:
            tuple: (MapApiKey instance, remaining_requests)

        Raises:
            ValueError: If daily limit is exceeded for all keys.
        """
        from apps.map.models.api_usage import ApiUsage

        # Get all non-expired Yandex Geocoder keys
        available_keys = cls.objects.filter(
            app_name=cls.YANDEX_MAP,
            usage_type=cls.YANDEX_GEOCODER,
            limit_expired=False
        ).order_by('id')

        if not available_keys.exists():
            raise ValueError("No available Yandex Geocoder API keys")

        # Try each key until we find one with remaining quota
        for api_key in available_keys:
            try:
                # Check if this key has remaining quota
                if ApiUsage.check_limit_available(api_key):
                    usage_record = ApiUsage.increment_usage(api_key)
                    remaining = usage_record.daily_limit - usage_record.usage_count
                    return api_key, remaining
                else:
                    # Mark this key as limit expired
                    cls.objects.filter(id=api_key.id).update(limit_expired=True)
                    continue
            except ValueError:
                # If increment fails (limit reached), mark key as expired and try next
                cls.objects.filter(id=api_key.id).update(limit_expired=True)
                continue

        # If we get here, all keys are exhausted
        raise ValueError("All Yandex Geocoder API keys have reached their daily limit")
