"""
API Usage Admin Interface

This module provides Django admin interface for the ApiUsage model
to monitor and manage API usage statistics.
"""

from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Sum, Avg
from django.utils import timezone

from apps.core.admin.modeladmin import ModelAdmin
from apps.map.models.api_usage import ApiUsage


@admin.register(ApiUsage)
class ApiUsageAdmin(ModelAdmin):
    """
    Admin interface for API Usage tracking.
    
    Provides comprehensive monitoring and management of API usage statistics
    including daily usage, limits, and trends.
    """
    
    list_display = [
        'id',
        'api_key_info',
        'date',
        'usage_progress',
        'usage_count',
        'daily_limit',
        'remaining_requests',
        'usage_percentage',
        'created_at',
    ]
    
    list_filter = [
        'date',
        'api_key__app_name',
        'api_key__usage_type',
        'api_key__owner',
        'created_at',
    ]
    
    search_fields = [
        'api_key__api_key',
        'api_key__owner',
    ]
    
    readonly_fields = [
        'created_at',
        'updated_at',
        'usage_percentage',
        'remaining_requests',
    ]
    
    ordering = ['-date', '-usage_count']
    
    date_hierarchy = 'date'
    
    list_per_page = 50
    
    def api_key_info(self, obj):
        """
        Display API key information with app name and usage type.
        """
        return format_html(
            '<strong>{}</strong><br/><small>{} - {}</small>',
            obj.api_key.owner or 'No Owner',
            obj.api_key.get_app_name_display(),
            obj.api_key.get_usage_type_display()
        )
    api_key_info.short_description = 'API Key Info'
    api_key_info.admin_order_field = 'api_key__owner'
    
    def usage_progress(self, obj):
        """
        Display usage progress as a visual progress bar.
        """
        percentage = (obj.usage_count / obj.daily_limit) * 100
        
        # Determine color based on usage percentage
        if percentage >= 90:
            color = '#dc3545'  # Red
        elif percentage >= 70:
            color = '#ffc107'  # Yellow
        else:
            color = '#28a745'  # Green
        
        return format_html(
            '<div style="width: 100px; background-color: #e9ecef; border-radius: 3px;">'
            '<div style="width: {}%; height: 20px; background-color: {}; border-radius: 3px; '
            'text-align: center; line-height: 20px; color: white; font-size: 12px;">'
            '{:.1f}%</div></div>',
            min(percentage, 100),
            color,
            percentage
        )
    usage_progress.short_description = 'Usage Progress'
    usage_progress.admin_order_field = 'usage_count'
    
    def remaining_requests(self, obj):
        """
        Calculate and display remaining requests.
        """
        remaining = max(0, obj.daily_limit - obj.usage_count)
        return remaining
    remaining_requests.short_description = 'Remaining'
    
    def usage_percentage(self, obj):
        """
        Calculate usage percentage.
        """
        return f"{(obj.usage_count / obj.daily_limit) * 100:.1f}%"
    usage_percentage.short_description = 'Usage %'
    
    def get_queryset(self, request):
        """
        Optimize queryset with select_related for better performance.
        """
        return super().get_queryset(request).select_related('api_key')
    
    def changelist_view(self, request, extra_context=None):
        """
        Add summary statistics to the changelist view.
        """
        # Get today's statistics
        today = timezone.now().date()
        today_usage = self.get_queryset(request).filter(date=today)
        
        # Calculate summary statistics
        total_requests_today = today_usage.aggregate(Sum('usage_count'))['usage_count__sum'] or 0
        total_limit_today = today_usage.aggregate(Sum('daily_limit'))['daily_limit__sum'] or 0
        avg_usage_today = today_usage.aggregate(Avg('usage_count'))['usage_count__avg'] or 0
        
        # Keys approaching limit (>80%)
        keys_near_limit = today_usage.filter(
            usage_count__gt=models.F('daily_limit') * 0.8
        ).count()
        
        # Keys exhausted
        keys_exhausted = today_usage.filter(
            usage_count__gte=models.F('daily_limit')
        ).count()
        
        extra_context = extra_context or {}
        extra_context.update({
            'summary_stats': {
                'total_requests_today': total_requests_today,
                'total_limit_today': total_limit_today,
                'avg_usage_today': round(avg_usage_today, 1),
                'keys_near_limit': keys_near_limit,
                'keys_exhausted': keys_exhausted,
                'usage_percentage_today': round(
                    (total_requests_today / total_limit_today * 100) if total_limit_today > 0 else 0, 1
                ),
            }
        })
        
        return super().changelist_view(request, extra_context=extra_context)


# Import models to avoid circular imports
from django.db import models
