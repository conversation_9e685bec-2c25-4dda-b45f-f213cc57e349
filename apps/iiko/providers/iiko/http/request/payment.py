"""
Webhook request models for handling payment types and related operations.

This module defines models that validate and structure the request bodies for API
interactions involving payment types. It uses Pydantic's BaseModel for data validation
and includes utility methods for converting models into request body dictionaries.

Classes:
    PaymentTypesRequestBody: Represents the structure of a request body used for
    interacting with organization-related payment types.
"""

from pydantic import BaseModel


class PaymentTypesRequestBody(BaseModel):
    """
    Model for validating and structuring the request body for payment types.

    This model is used to represent the organization-specific information required
    for processing payment types in webhook requests.

    Attributes:
        organizationId (str): The unique identifier for the organization.

    Methods:
        as_request_body: Converts the model instance into a dictionary format suitable
                         for sending in API requests.
    """
    organizationId: str

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationIds": [self.organizationId]
        }
