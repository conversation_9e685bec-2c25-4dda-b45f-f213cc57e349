"""
This module provides an API view for updating or creating a courier's
geographical point.

The view allows authenticated users to update or create a courier point,
which includes latitude and longitude information.

Usage:
    POST /api/v1/courier/update-point/

Permissions:
    Requires authentication and the user must have the `COURIER` role.

Example Request:
    POST /api/v1/courier/update-point/
    {
        "latitude": 40.7128,
        "longitude": -74.0060
    }

Response:
    Status 200 OK or 201 Created, with a JSON response indicating whether
    a new point was created.
"""

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.user.enum.role import UserRoleEnums
from apps.map.serializer import PointSerializer
from apps.core.exceptions import ServiceAPIException
from apps.courier.permissions import IsCourierRole
from apps.courier.models.point import CourierPoint


class PointUpdateOrCreateAPIView(APIView):
    """
    API view for updating or creating a courier's geographical point.

    Permissions:
        Requires authentication and the user must have the `COURIER` role.

    Methods:
        post: Handles POST requests to update or create a courier point.
    """
    permission_classes = [IsCourierRole]

    def post(self, request):
        """
        Handles POST requests to update or create a courier point.

        Validates the input data, checks the user's role, and updates or
        creates a CourierPoint instance accordingly.

        Args:
            request (Request): The HTTP request object containing the data
                               to be processed.

        Returns:
            Response: A Response object with status code 200 OK if the point
                      was updated, or 201 Created if a new point was created.
                      Returns 400 Bad Request if the input data is invalid.
        """
        serializer = PointSerializer(data=request.data)

        if serializer.is_valid():
            courier = request.user
            if not courier.role == UserRoleEnums.COURIER:
                raise ServiceAPIException(
                    error_type='forbidden',
                    message='You are not allowed to perform this action',
                    status_code=status.HTTP_403_FORBIDDEN,
                )

            latitude = serializer.validated_data['latitude']
            longitude = serializer.validated_data['longitude']

            _, created = CourierPoint.update_or_create(
                courier_id=courier.id,
                latitude=latitude,
                longitude=longitude,
            )
            response_data = {
                'created': created
            }

            status_code = status.HTTP_200_OK
            if created:
                status_code = status.HTTP_201_CREATED

            return Response(response_data, status=status_code)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
