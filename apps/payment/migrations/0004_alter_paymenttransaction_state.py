# Generated by Django 5.0.6 on 2024-09-26 21:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payment", "0003_paymenttransaction"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="paymenttransaction",
            name="state",
            field=models.CharField(
                choices=[
                    ("Waiting", "Waiting"),
                    ("Processing", "Processing"),
                    ("Completed", "Completed"),
                    ("Refunded", "Refunded"),
                    ("Canceled", "Canceled"),
                    ("Expired", "Expired"),
                    ("Rejected", "Rejected"),
                    ("Pending", "Pending"),
                    ("Reversed", "Reversed"),
                    ("Reversed and Waiting", "Reversed and waiting"),
                ],
                default="Waiting",
            ),
        ),
    ]
