"""
This module provides various API views and service handling utilities, including health checks,
remote configuration retrieval, and error handling with optional admin notification support.

Key Components:
- `HealthCheckAPIView`: A health check endpoint to monitor the service status.
- `RemoteConfigAPIView`: A remote configuration endpoint that returns system parameters.
- `ServiceBaseView`: A base class offering logging, exception handling, and notification services for derived views.
"""

import logging
from django.utils import timezone

from rest_framework import views, response
from apps.core.enums.system import SystemParamsEnum
from apps.bot.tasks.message import send_message_task
from apps.core.exceptions.service import ServiceAPIException
from apps.core.models.system.parameters import SystemParameter
from apps.core.throttling import OneRequestPerFiveSecondsThrottle


class HealthCheckAPIView(views.APIView):
    """
    API view for performing health checks.

    This view is used to verify if the system is up and running. It does not require
    authentication or any parameters and can be extended to include more complex checks.
    """
    permission_classes = []
    throttle_classes = [OneRequestPerFiveSecondsThrottle]

    def get(self, request):
        """
        Handle GET requests for the health check.

        Returns:
            rest_framework.response.Response: A simple success response.
        """
        # Example logic can be added here for health checks
        return response.Response()


class RemoteConfigAPIView(views.APIView):
    """
    API view for retrieving remote configuration settings.

    This view returns essential system configuration parameters, such as order timeouts
    and courier update intervals. It pulls data from the system parameters stored in the database.
    """
    def get(self, request):
        """
        Handle GET requests to retrieve remote configuration.

        Returns:
            rest_framework.response.Response: A response with system configuration values.
        """
        return response.Response({
            "order_acception_timeout":  60,  # in seconds, could be updated from system parameters
            "courier_update_point_interval": 5,
            "is_bot_enabled": SystemParameter.get_sys_params(SystemParamsEnum.IS_MINI_APP_ENABLED.value).is_enabled,
        })


class ServiceBaseView:
    """
    Base class for service views, providing common functionality for error handling, logging, and notifications.

    This class can be extended by other views to incorporate consistent behavior for:
    - Exception handling (with the option to notify administrators).
    - Logging errors and warnings.
    - Sending notifications to system administrators when critical issues occur.
    """
    logger = logging.getLogger(__name__)

    def call_service_exception(self, error_type, message, status_code=400, notify_admin=False):
        """
        Handle exceptions during service execution, optionally sending a notification to the admin.

        This method logs the error, optionally sends a notification, and raises a `ServiceAPIException`.

        Args:
            error_type (str): The type of error encountered (e.g., "bad_request", "server_error").
            message (str): The error message to be logged and returned.
            status_code (int, optional): The HTTP status code to be returned with the error. Defaults to 400.
            notify_admin (bool, optional): Whether to send a notification to the administrator. Defaults to False.

        Raises:
            ServiceAPIException: The exception instance containing error details.
        """
        self.logger.error(f"{error_type}: {message}", exc_info=True)
        if notify_admin:
            self.send_notification(message)
        return self._create_service_exception(error_type, message, status_code)

    def bad_request_exception(self, message, notify_admin=False):
        """
        Raise a `ServiceAPIException` for bad requests, optionally sending a notification to the admin.

        This method is a shorthand for handling common client errors (status code 400).

        Args:
            message (str): The error message to be returned.
            notify_admin (bool, optional): Whether to send a notification to the administrator. Defaults to False.

        Returns:
            ServiceAPIException: The exception instance for the bad request.
        """
        return self.call_service_exception("bad_request", message, 400, notify_admin)

    def send_notification(self, message):
        """
        Send a notification to the admin via the configured notification provider (e.g., Telegram).

        This method is used to notify the administrator about critical issues that require immediate attention.

        Args:
            message (str): The message to send in the notification.

        Logs:
            Logs the notification event if successful, or logs an error if notification fails.
        """
        message = f"[{timezone.now()}] {message}"
        send_message_task.delay(message=message)

    def _create_service_exception(self, error_type, message, status_code):
        """
        Create and return a `ServiceAPIException` instance.

        This method standardizes how exceptions are raised throughout the service, ensuring consistency.

        Args:
            error_type (str): The type of error encountered.
            message (str): The error message to include in the exception.
            status_code (int): The HTTP status code to be returned with the error.

        Returns:
            ServiceAPIException: The exception instance to be raised.
        """
        return ServiceAPIException(
            error_type=error_type,
            message=message,
            status_code=status_code
        )
