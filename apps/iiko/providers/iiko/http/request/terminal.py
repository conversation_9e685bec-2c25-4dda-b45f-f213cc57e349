"""
the terminal group request
"""
from pydantic import BaseModel


class TerminalGroupRequestBody(BaseModel):
    """
    the organization ids request model
    """
    organizationId: str

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationIds": [self.organizationId]
        }


class IsAliveRequestBody(BaseModel):
    """
    the organization data model representing
    """
    organizationId: str
    terminalGroupId: str

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationId": self.organizationId,
            "terminalGroupIds": [self.terminalGroupId]
        }
