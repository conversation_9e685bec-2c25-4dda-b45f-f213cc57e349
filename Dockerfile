FROM python:3.12.4-slim

ENV TZ=Asia/Tashkent

WORKDIR /application

RUN apt-get update && apt-get install -y wget gnupg2 && \
    echo "deb http://apt.postgresql.org/pub/repos/apt bookworm-pgdg main" > /etc/apt/sources.list.d/pgdg.list && \
    wget -qO - https://www.postgresql.org/media/keys/ACCC4CF8.asc | apt-key add - && \
    apt-get update && apt-get install -y postgresql-client-16 && \
    rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --upgrade pip
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8005
EXPOSE 8008
EXPOSE 5555

RUN pg_dump --version
