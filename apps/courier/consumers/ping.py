"""
the ping consumer
"""
import json
import logging

from channels.generic.websocket import AsyncWebsocketConsumer

logger = logging.getLogger(__name__)


class PingConsumer(AsyncWebsocketConsumer):
    """
    The hello world consumer
    """
    async def connect(self):
        logger.info("WebSocket connection established.")
        await self.accept()

    async def disconnect(self, code):
        logger.info("WebSocket connection closed with code: %s", code)

    async def receive(self, text_data=None, bytes_data=None):
        logger.info("Received data: %s", text_data)

        await self.send(text_data=json.dumps({"message": "PONG"}))
