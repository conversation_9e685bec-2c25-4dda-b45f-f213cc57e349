"""
the product admin page
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.organization.models.terminal import TerminalGroup


class TerminalGroupUI(ModelAdmin):
    """
    the terminal group model admin page
    """
    list_display = [
        "id",
        "name",
        "in_use",
        "created_at",
        "updated_at"
    ]


admin.site.register(TerminalGroup, TerminalGroupUI)
