"""
This module defines the `CommandsRequestBody` class, which represents the structure of
the payload used to fetch command-related information in the iiko system.

It provides the attributes required to form a request body, such as the organization and
correlation identifiers, and a method to generate the corresponding dictionary for easy use in HTTP requests.
"""

from pydantic import BaseModel


class CommandsStatusRequestBody(BaseModel):
    """
    CommandsRequestBody represents the structure of the payload necessary to fetch
    command-related information from the iiko system.

    This class includes the following attributes:

    Attributes:
        organizationId (str): A string representing the unique identifier of the organization.
        correlationId (str): A string representing the unique correlation identifier,
            used for tracking and logging purposes.

    Methods:
        as_request_body() -> dict:
            Returns a dictionary representation of the request body, ready to be used in API requests.
    """

    organizationId: str
    correlationId: str

    def as_request_body(self):
        """
        Converts the CommandsRequestBody instance into a dictionary format, suitable for API requests.

        Returns:
            dict: A dictionary containing the 'organizationId' and 'correlationId' of the request.
        """
        return {
            "organizationId": self.organizationId,
            "correlationId": self.correlationId,
        }
