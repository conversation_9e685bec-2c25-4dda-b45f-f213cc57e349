"""
the enumeration of order types
"""
from enum import Enum


class OrderTypesEnum(str, Enum):
    """
    The enumeration of order types.
    """
    DELIVERY_BY_AGENT = "76067ea3-356f-eb93-9d14-1fa00d082c4e"
    DELIVERY_BY_PICKUP = "5b1508f9-fe5b-d6af-cb8d-043af587d5c2"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]


class OrderStatus(str, Enum):
    """
    The enumeration of order statuses.
    """
    IN_PROGRESS = "InProgress"
    UNCONFIRMED = "Unconfirmed"
    WAIT_COOKING = "WaitCooking"
    COOKING_STARTED = "CookingStarted"
    CANCELLED = "Cancelled"
    COOKING_COMPLETED = "CookingCompleted"
    CLOSED = "Closed"

    DELIVERED = "Delivered"
    DELIVERY_ORDER_ERROR = "DeliveryOrderError"

    WAITING = "Waiting"
    ON_WAY = "OnWay"
    TIMEOUT = "Timeout"
