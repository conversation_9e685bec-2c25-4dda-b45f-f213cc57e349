# Generated by Django 5.0.6 on 2024-08-20 11:29

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TelegramUser',
            fields=[
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('id', models.BigIntegerField(primary_key=True, serialize=False)),
                ('username', models.CharField(max_length=200, null=True)),
                ('first_name', models.CharField(max_length=200, null=True)),
                ('last_name', models.CharField(max_length=200, null=True)),
                ('is_active', models.BooleanField(default=False)),
                ('lang', models.Char<PERSON>ield(default='ru', max_length=20)),
                ('phone', models.Char<PERSON>ield(max_length=16, null=True)),
                ('latitude', models.<PERSON>loat<PERSON>ield(blank=True, null=True)),
                ('longitude', models.<PERSON>loat<PERSON>ield(blank=True, null=True)),
                ('is_state_notified', models.BooleanField(default=False)),
                ('state', models.Char<PERSON>ield(choices=[('on_start', 'On start'), ('not_started', 'Not started'), ('get_contact', 'Get contact'), ('get_location', 'Get location'), ('on_verification', 'On verification'), ('rejected', 'Rejected'), ('on_menu', 'On menu')], default='not_started', max_length=50)),
            ],
            options={
                'verbose_name': 'Telegram User',
                'verbose_name_plural': 'Telegram Users',
                'db_table': 'telegram_user',
                'ordering': ['-updated_at'],
            },
        ),
    ]
