"""
Service for handling log file operations.

This module provides functionality to access and retrieve log files from the system.
"""
import os
import logging
from pathlib import Path
from django.conf import settings


logger = logging.getLogger(__name__)


class LogService:
    """
    Service for handling log file operations.

    This service provides methods to access and retrieve log files from the system.
    """

    @staticmethod
    def get_log_file_path():
        """
        Get the path to the main log file.

        Returns:
            Path: The path to the log file.
        """
        log_file_path = settings.LOGGING.get('handlers', {}).get('file', {}).get('filename')

        if not log_file_path:
            logger.error("Log file path not found in settings")
            return None

        if not os.path.isabs(log_file_path):
            base_dir = Path(settings.BASE_DIR).parent
            log_file_path = os.path.join(base_dir, log_file_path)

        return log_file_path

    @classmethod
    def read_log_file(cls, max_lines=None):
        """
        Read the content of the log file.

        Args:
            max_lines (int, optional): Maximum number of lines to read from the end of the file.
                                      If None, reads the entire file.

        Returns:
            str: The content of the log file, or an error message if the file cannot be read.
        """
        log_file_path = cls.get_log_file_path()

        if not log_file_path:
            return "Log file path not configured"

        try:
            if not os.path.exists(log_file_path):
                logger.error(f"Log file not found at {log_file_path}")
                return f"Log file not found at {log_file_path}"

            if max_lines:
                return cls._read_last_n_lines(log_file_path, max_lines)
            else:
                with open(log_file_path, 'r', encoding='utf-8') as file:
                    return file.read()

        except Exception as e:
            error_message = f"Error reading log file: {str(e)}"
            logger.error(error_message)
            return error_message

    @staticmethod
    def _read_last_n_lines(file_path, n):
        """
        Read the last N lines from a file.

        Args:
            file_path (str): Path to the file.
            n (int): Number of lines to read from the end.

        Returns:
            str: The last N lines of the file.
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
                last_n_lines = lines[-n:] if n < len(lines) else lines
                return ''.join(last_n_lines)
        except Exception as e:
            return f"Error reading last {n} lines: {str(e)}"
