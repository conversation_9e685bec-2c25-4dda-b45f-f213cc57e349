"""
Softphone app configuration
"""
from django.apps import AppConfig


class SoftphoneConfig(AppConfig):
    """
    Configuration for the Softphone app
    """
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.softphone'
    verbose_name = 'Softphone Integration'
    
    def ready(self):
        """
        Import signals when the app is ready
        """
        try:
            import apps.softphone.signals  # noqa
        except ImportError:
            pass
