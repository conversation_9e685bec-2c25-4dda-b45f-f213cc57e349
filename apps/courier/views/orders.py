"""
getting orders of couriers
"""
from rest_framework import views
from rest_framework import response

from apps.order.models.order import Order
from apps.user.permissions import IsCourierRole
from apps.courier.models.courier import Courier
from apps.courier.models.orders import MissedOrder
from apps.order.enums.order import CourierFindingStatus
from apps.order.serializers.order import OrderSerializer
from apps.order.enums.order import OrderStatus as OrderStatusEnum


class ActiveOrdersAPIView(views.APIView):
    """
    the couriers active orders
    """
    permission_classes = [IsCourierRole]

    def get(self, request, *args, **kwargs):
        """
        the get method for getting active orders for a couriers
        """
        user = request.user

        # get active orders for the delivery agent
        active_orders = Order.objects.filter(
            delivery_agent__id=user.id,
            courier_finding_status=CourierFindingStatus.FOUND.value,
            status__in=[
                OrderStatusEnum.ON_WAY,
                OrderStatusEnum.WAITING,
                OrderStatusEnum.COOKING_COMPLETED,
                OrderStatusEnum.COOKING_STARTED,
                OrderStatusEnum.ARRIVED,
                OrderStatusEnum.DELIVERED
            ]
        )
        resp_data = []
        active_orders_data = OrderSerializer(active_orders, many=True).data

        resp_data.append({
            "active_orders": active_orders_data
        })

        courier = Courier.get_by_id(user.id)
        unmissed_orders = MissedOrder.get_unmissed_orders_by_courier(
            courier_id=courier.id
        )

        unmissed_orders_ids = []

        for order in unmissed_orders:
            unmissed_orders_ids.append(order.order_id)

        to_resp = OrderSerializer(Order.get_order_by_ids(unmissed_orders_ids), many=True)
        # combine active orders and unmissed orders
        resp_data.append({
            "new_order": to_resp.data
        })

        return response.Response(resp_data)
