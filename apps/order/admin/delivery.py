"""
This module defines the admin interface for managing delivery-related models in the order application.
It includes customized admin classes for the DeliveryPrice model, providing an intuitive
and efficient way to manage delivery pricing tiers in the Django admin panel.
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.order.models.delivery import DeliveryPrice


class DeliveryPriceUI(ModelAdmin):
    """
    Custom admin interface for the DeliveryPrice model.

    This class extends the ModelAdmin to provide a tailored admin experience
    for managing delivery price entries. It includes customizations for list display,
    filtering, search capabilities, and form layout to enhance usability and efficiency
    in managing delivery pricing data.

    Attributes:
        list_display (tuple): Fields to be displayed in the list view.
        list_filter (tuple): Fields available for filtering in the admin.
        search_fields (tuple): Fields that can be searched in the admin interface.
        readonly_fields (tuple): Fields that cannot be edited in the admin interface.
        fieldsets (tuple): Defines the layout of the edit form.

    Methods:
        get_queryset: Optimizes database queries for improved performance.
    """
    list_display = ('display_name', 'distance_type', 'external_id', 'price', 'is_active',)
    list_filter = ('distance_type', 'is_active')
    search_fields = ('external_id', 'distance_type')
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('external_id', 'display_name', 'distance_type', 'price', 'is_active')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """
        Override the default queryset to optimize database queries.

        This method adds select_related or prefetch_related calls as needed
        to reduce the number of database queries and improve admin performance.

        Args:
            request (HttpRequest): The current request.

        Returns:
            QuerySet: An optimized queryset for the admin interface.
        """
        return super().get_queryset(request).select_related()


admin.site.register(DeliveryPrice, DeliveryPriceUI)
