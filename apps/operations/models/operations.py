"""
init operations models
"""
from django.db import models, transaction
from django.contrib.auth.hashers import make_password, check_password
from django.contrib.auth.models import Group, AbstractBaseUser, PermissionsMixin

from apps.core.models.base import BaseModel
from apps.user.enum.role import UserRoleEnums
from apps.organization.models import Organization
from apps.operations.managers.operations import OperationsManager


class Operations(BaseModel, AbstractBaseUser, PermissionsMixin):
    """
    The operations model, merging Managers, Dispatchers, Operators, Marketologs, and Super Admins.
    """
    ROLE_CHOICES = (
        ('operator', 'Operator'),
        ('manager', 'Manager'),
        ('dispatcher', 'Dispatcher'),
        ('marketolog', 'Marketolog'),
        ('admin', 'Super Admin'),
        ('director', 'Director'),
    )

    phone = models.CharField(max_length=15, unique=True)
    name = models.CharField(max_length=255, verbose_name="Full Name")
    password = models.CharField(max_length=128, null=True, blank=True)
    passport = models.CharField(verbose_name="Passport", max_length=15, null=True, blank=True)
    external_id = models.CharField(max_length=255, null=True, db_index=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True)
    role = models.CharField(max_length=50, choices=ROLE_CHOICES)

    is_blocked = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)

    objects = OperationsManager()

    USERNAME_FIELD = 'phone'

    groups = models.ManyToManyField(
        'auth.Group',
        related_name='operations_user_set',
        blank=True,
        verbose_name='groups',
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='operations_user_permissions',
        blank=True,
        verbose_name='user permissions',
    )

    class Meta:
        """
        The operations meta class
        """
        db_table = 'operations'
        ordering = ['-created_at', '-updated_at']

    def __str__(self):
        return str(self.name)

    def set_password(self, raw_password):
        """
        Set the password for the operations.
        """
        self.password = make_password(raw_password)

    def check_password(self, raw_password):
        """
        Check the password for the operations.
        """
        return check_password(raw_password, self.password)

    @classmethod
    def get_by_phone(cls, phone) -> "Operations":
        """
        Get operations by phone
        """
        return cls.objects.get(phone=phone, is_deleted=False)

    @classmethod
    def get_by_id(cls, operations_id) -> "Operations":
        """
        Get operations by id
        """
        return cls.objects.get(id=operations_id, is_deleted=False)

    @classmethod
    def create(
        cls,
        name: str,
        phone: str,
        password: str,
        role: str,
        passport: str = None,
        external_id: str = None,
        organization_id: int = None,
    ) -> "Operations":
        """
        Create a new operations entry
        """
        if role not in dict(cls.ROLE_CHOICES):
            raise ValueError("Invalid role provided.")

        with transaction.atomic():
            role_group, _ = Group.objects.get_or_create(name=UserRoleEnums.get_role(role.upper()))

            organization = Organization.objects.get(
                external_id=organization_id
            ) if organization_id else None

            operations = Operations.objects.create(
                phone=phone,
                name=name,
                passport=passport,
                organization=organization,
                external_id=external_id,
                role=role,
                is_active=True,
                is_verified=False,
                is_staff=(role == 'super_admin'),
            )
            operations.set_password(password)
            operations.save()

            operations.groups.add(role_group)
            operations.save()

            return operations

    @classmethod
    def set_by_external_id(cls, external_id, status):
        """
        Set status by external id
        """
        operations = cls.objects.get(external_id=external_id)
        operations.status = status
        operations.save()
        return operations

    @classmethod
    def check_external_id_exists(cls, external_id) -> bool:
        """
        Check if an entry with the given external id exists
        """
        return cls.objects.filter(external_id=external_id, is_deleted=False).exists()
