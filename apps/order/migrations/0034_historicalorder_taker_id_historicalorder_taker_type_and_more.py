# Generated by Django 5.0.6 on 2025-02-10 10:41

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0033_monitoring"),
    ]

    operations = [
        migrations.AddField(
            model_name="historicalorder",
            name="taker_id",
            field=models.IntegerField(
                blank=True,
                help_text="Telegram user ID of the taker",
                null=True,
                verbose_name="Taker ID",
            ),
        ),
        migrations.AddField(
            model_name="historicalorder",
            name="taker_type",
            field=models.CharField(
                blank=True,
                choices=[("OPERATORS", "operators"), ("MANAGERS", "managers")],
                max_length=10,
                null=True,
                verbose_name="Taker Type",
            ),
        ),
        migrations.AddField(
            model_name="order",
            name="taker_id",
            field=models.IntegerField(
                blank=True,
                help_text="Telegram user ID of the taker",
                null=True,
                verbose_name="Taker ID",
            ),
        ),
        migrations.Add<PERSON>ield(
            model_name="order",
            name="taker_type",
            field=models.<PERSON><PERSON><PERSON><PERSON>(
                blank=True,
                choices=[("OPERATORS", "operators"), ("MANAGERS", "managers")],
                max_length=10,
                null=True,
                verbose_name="Taker Type",
            ),
        ),
    ]
