"""
the orders-create view
"""
import logging

from apps.loyalty.service import LoyaltyService
from apps.bot.models.telegram import TelegramUser
from apps.order.enums.delivery import DeliveryType
from apps.order.models.order_detail import init_order_detail
from apps.core.exceptions.service import ServiceAPIException
from apps.core.enums.initiator import Initiator as InitiatorEnum
from apps.order.typing.order import OrderCreateForTelegramSerializer
from apps.core.throttling import TelegramOrderCreateThrottle


# pylint: disable=E0611
from apps.order.views.order.create.base import BaseOrderCreateAPIView


logger = logging.getLogger(__name__)


class OrderCreateForTelegramAPIView(BaseOrderCreateAPIView):
    """
    The order view for Telegram users.

    This view handles order creation for Telegram bot users and includes
    throttling based on the chat_id from the URL path. Each Telegram user
    (identified by chat_id) is limited to one order creation request per 5 seconds.
    """
    permission_classes = []
    throttle_classes = [TelegramOrderCreateThrottle]

    def post(self, request, *args, **kwargs):
        """
        the post method for creating an order.
        """
        logger.info(f"order create via telegram with request body :{request.data}")

        chat_id = kwargs.get("chat_id")
        initiator = InitiatorEnum.BOT
        return super().post(request, initiator, chat_id=chat_id)

    def get_serializer(self, data):
        try:
            return OrderCreateForTelegramSerializer(**data)
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to create serializer\n\nError: {exc}",
                status_code=400,
                notify_admin=True
            )

    def get_order_detail(self, data, user, chat_id):
        if not TelegramUser.check_exsists(chat_id):
            raise ServiceAPIException(
                error_type="bad_request",
                message=f"Telegram user with id {chat_id} not found.",
                status_code=400
            )

        telegram_user = TelegramUser.get_by_chat_id(chat_id)
        delivery_type = DeliveryType.DELIVERY

        if data.order_detail.delivery_type == DeliveryType.SELF_CALL:
            delivery_type = DeliveryType.SELF_CALL

        has_promo = hasattr(data.order_detail, 'promo_code')
        promo_code = getattr(data.order_detail, 'promo_code', None) if has_promo else None

        if not promo_code and telegram_user.user:
            user_id = telegram_user.user.id
            cached_promo = LoyaltyService.get_cached_promo_code(user_id)
            if cached_promo:
                promo_code = cached_promo
                logger.info(f"Using cached promo code {promo_code} for user {user_id} in telegram order creation")

        return init_order_detail(
            name=telegram_user.first_name,
            phone=telegram_user.phone,
            latitude=data.order_detail.location.lat,
            longitude=data.order_detail.location.lng,
            comment=data.order_detail.comment,
            delivery_type=delivery_type,
            payment_method=data.order_detail.payment_method,
            delivery_address=data.order_detail.delivery_address,
            complete_before=data.order_detail.complete_before,
            subaddress=data.order_detail.subaddress,
            promo_code=promo_code,
        )
