"""
the products view
"""
from rest_framework.decorators import action
from rest_framework import filters, viewsets, response, views

from django.db import transaction
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator
from django_filters.rest_framework import DjangoFilterBackend

from apps.order.service import OrderService
from apps.product.filters import ProductFilter
from apps.product.models.product import Product
from apps.product.enums.type import ProductType
from apps.product.paginations import LimitAllPagination
from apps.product.paginations import Limit100Pagination
from apps.product.models.product import ProductAttribute
from apps.core.exceptions.service import ServiceAPIException
from apps.product.serializers import product as product_serializer
from apps.organization.models.organtzation import Organization
from apps.organization.exceptions import OrganizationsAreClosedExc
from apps.organization.helper.organization import organization_helper
from apps.iiko.tasks.polling import update_categories_and_products_task
from apps.product.models.modifier import ItemModifierGroups, ModifierItems
from apps.operations.permissions import IsAdminOr<PERSON>ana<PERSON>, IsAdminOrManagerOrMarketolog


class ProductViewSet(viewsets.ModelViewSet):
    """
    CRUD operations for Product model
    """
    filterset_class = ProductFilter
    queryset = Product.objects.filter(
        product_type=ProductType.DISH.value
    )
    http_method_names = ['get', 'put', 'delete']
    serializer_class = product_serializer.ProductSerializer
    search_fields = ['title', 'category__name']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]

    @transaction.atomic
    def update(self, request, *args, **kwargs):
        """
        Update a product.
        """
        try:
            return self.update_process(request, *args, **kwargs)
        except Exception as exc:
            raise ServiceAPIException(
                error_type="bad_request",
                message=f"Failed to update product error: {exc}",
                status_code=400,
            ) from exc

    def update_modifiers(self, attributes):
        """
        Update product attributes with proper handling of localization fields.
        """
        for attribute in attributes:
            modificator = attribute.pop("modificator", {})

            if modificator:
                item_modifier_groups = ItemModifierGroups.objects.filter(id=modificator.get('id'))
                if item_modifier_groups.exists():
                    item_modifier_group = item_modifier_groups.first()

                    # Update localized fields for ItemModifierGroups
                    item_modifier_group.name = modificator.get('name', item_modifier_group.name)
                    item_modifier_group.name_uz = modificator.get('name_uz', item_modifier_group.name_uz)
                    item_modifier_group.name_ru = modificator.get('name_ru', item_modifier_group.name_ru)
                    item_modifier_group.name_en = modificator.get('name_en', item_modifier_group.name_en)
                    item_modifier_group.description = modificator.get('description', item_modifier_group.description)
                    item_modifier_group.description_uz = modificator.get('description_uz', item_modifier_group.description_uz) # noqa
                    item_modifier_group.description_ru = modificator.get('description_ru', item_modifier_group.description_ru) # noqa
                    item_modifier_group.description_en = modificator.get('description_en', item_modifier_group.description_en) # noqa

                    item_modifier_group.save()

            if modificator and modificator.get("items"):
                for item in modificator.get("items"):
                    modifier_items = ModifierItems.objects.filter(id=item.get('id'))
                    if modifier_items.exists():
                        modifier_item = modifier_items.first()

                        # Update localized fields for ModifierItems
                        modifier_item.name_uz = item.get('name_uz', modifier_item.name_uz)
                        modifier_item.name_ru = item.get('name_ru', modifier_item.name_ru)
                        modifier_item.name_en = item.get('name_en', modifier_item.name_en)
                        modifier_item.description = item.get('description', modifier_item.description)
                        modifier_item.description_uz = item.get('description_uz', modifier_item.description_uz)
                        modifier_item.description_ru = item.get('description_ru', modifier_item.description_ru)
                        modifier_item.description_en = item.get('description_en', modifier_item.description_en)

                        modifier_item.save()

    def update_process(self, request, *args, **kwargs):
        """
        Update product attributes.
        """
        permission = [
            IsAdminOrManagerOrMarketolog
        ]
        self.permission_classes = permission
        self.check_permissions(request)

        attributes = request.data.pop('attributes', [])
        self.update_modifiers(attributes)
        product_attr_serializer = product_serializer.ProductAttributeSerializer(data=attributes, many=True)
        product_attr_serializer.is_valid(raise_exception=True)

        ProductAttribute.get_or_update_bulk(
            products=attributes
        )

        self.serializer_class = product_serializer.ProductUpdateSerializer

        partial = kwargs.pop('partial', True)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return response.Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='most-ordered')
    def most_ordered(self, request):
        """
        Get the most ordered products, filtered by organization_external_id if provided.
        """
        organization_external_id = request.query_params.get('organization_external_id', None)
        products = Product.objects.filter(is_top=True)

        serializer = self.get_serializer(products, many=True)
        result = serializer.data

        filtered_products = []
        for product in result:
            filtered_attributes = [
                attr for attr in product.get('attributes', [])
                if attr['organization_external_id'] == organization_external_id and attr['is_available']
            ]
            if filtered_attributes:
                product['attributes'] = filtered_attributes
                filtered_products.append(product)

        return response.Response(filtered_products)

    @action(detail=False, methods=['get'], url_path='product-sells-stat')
    def product_sells_stat(self, request):
        """
        Get the most ordered products.
        """
        return response.Response("Should be implemented with iiko")

    @action(detail=False, methods=['get'], url_path='product-for-dashboard')
    def product_for_dashboard(self, request):
        """
        Get products with pagination for the dashboard
        """
        organization_id = request.GET.get('organization_id')
        limit = request.query_params.get('limit', None)

        paginator = LimitAllPagination() if limit == 'all' else Limit100Pagination()

        products = Product.objects.all()
        if organization_id:
            products = products.filter(organization_id=organization_id)
        products = products.exclude(product_type="SERVICE")

        paginated_products = paginator.paginate_queryset(products, request, view=self)
        serializer = self.get_serializer(paginated_products, many=True)

        return paginator.get_paginated_response(serializer.data)

    @method_decorator(cache_page(60 * 60 * 24, key_prefix='products'))
    def list(self, request, *args, **kwargs):
        """
        List products with product count and pagination.
        """
        # Update organization status
        organization_helper.check_organization_is_alive()

        # Extract latitude and longitude
        lat = request.query_params.get('lat', None)
        long = request.query_params.get('long', None)

        # Delegate to location-based filtering if applicable
        try:
            return self._list_with_location_filter(request, lat=lat, long=long, *args, **kwargs)

        except OrganizationsAreClosedExc as exc:
            raise ServiceAPIException(
                error_type="closed_organizations",
                message=str(exc),
                status_code=400
            ) from exc

        except ServiceAPIException as exc:
            raise ServiceAPIException(
                error_type="temporal_error",
                message="temporal unavailable",
                status_code=400
            ) from exc

    def _list_with_location_filter(self, request, lat=None, long=None, *args, **kwargs):
        """
        List products with location-based filtering or by specific organization_id.
        """
        # Check if 'limit' parameter is set to 'all'
        organization = None
        delivery_price = None

        limit = request.query_params.get('limit', None)
        if limit == 'all':
            self.pagination_class = LimitAllPagination

        organization_id = request.query_params.get('organization_id', None)

        if organization_id:
            try:
                organization = Organization.get_by_id(organization_id)
            except Organization.DoesNotExist:
                return response.Response({'error': 'Invalid organization_id'}, status=400)

        if not organization:
            if not lat and not long:
                # Return default organization if no location is provided
                default_org = Organization.get_default()
                lat = default_org.latitude
                long = default_org.longitude

            try:
                latitude = float(lat)
                longitude = float(long)
            except ValueError:
                return response.Response({'error': 'Invalid latitude or longitude'}, status=400)

            organization, distance = Organization.find_nearest_organization(
                latitude=latitude,
                longitude=longitude,
                radius=20
            )

            delivery_price = OrderService.calculate_delivery_price(distance)

        if organization:
            organization_external_id = organization.external_id
            resp = super().list(request, *args, **kwargs)
            results = resp.data.get('results', [])

            # Filter attributes based on organization_external_id and is_available
            filtered_results = []
            for product in results:
                filtered_attributes = [
                    attr for attr in product.get('attributes', [])
                    if attr['organization_external_id'] == organization_external_id and attr['is_available']
                ]
                if filtered_attributes:
                    product["organization_name"] = organization.name
                    product["organization_id"] = organization.id
                    product['delivery'] = delivery_price.to_dict() if delivery_price else None
                    product['attributes'] = filtered_attributes
                    filtered_results.append(product)

                # fitering recommended products
                recommended_products = product.get("recommended_products")

                for recommended_product in recommended_products:
                    recommended_product_attributes = [
                        attr for attr in recommended_product.get('attributes', [])
                        if attr['organization_external_id'] == organization_external_id and attr['is_available']
                    ]
                    if recommended_product_attributes:
                        recommended_product['organization_name'] = organization.name
                        recommended_product['organization_id'] = organization.id
                        recommended_product['delivery'] = delivery_price.to_dict() if delivery_price else None
                        recommended_product['attributes'] = recommended_product_attributes
                        # results.append(recommended_product)

            resp.data['results'] = filtered_results
            return resp

        # Fallback if no organization is found
        return super().list(request, *args, **kwargs)

    @action(detail=False, methods=['get'], url_path='by-organization-id')
    def by_organization_id(self, request):
        """
        Get products by organization_external_id in their attributes.
        """
        organization_external_id = request.query_params.get('organization_external_id')

        if not organization_external_id:
            raise ServiceAPIException(
                error_type="bad_request",
                message="No organization_external_id provided",
                status_code=400,
            )

        # Filter attributes based on organization_external_id
        product_ids = ProductAttribute.by_organization_id(organization_external_id)

        products = Product.objects.filter(id__in=product_ids)
        results = self.get_serializer(products, many=True)

        # Filter attributes based on organization_external_id
        for product in results.data:
            filtered_attributes = [
                attr for attr in product.get('attributes', [])
                if attr['organization_external_id'] == organization_external_id
            ]
            product['attributes'] = filtered_attributes

        return response.Response(results.data)


class UpdateProductAPIView(views.APIView):
    """
    Update product attributes. for IsAdminOrManager
    """
    permission_classes = [IsAdminOrManager]

    def put(self, request, *args, **kwargs):
        """
        Update product attributes. for IsAdminOrManager
        """
        update_categories_and_products_task.delay(is_manual_update=True)
        return response.Response()
