"""
the courier token apiviews
"""
import random

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken

from django.core.cache import cache

from apps.sms.service import SMSService
from apps.courier.models import Courier
from apps.core.views import ServiceBaseView
from apps.core.exceptions import ServiceAPIException
from apps.courier.models.jwt import JWTCourierTokenList
from apps.courier.models.jwt import JWTCourierTokenBlockList


class CourierTokenObtainPairView(APIView, ServiceBaseView):
    """
    View to obtain JWT tokens for couriers.
    """
    def post(self, request, *args, **kwargs):
        """
        Handle POST request to obtain JWT tokens for the courier.
        """
        phone = request.data.get('phone')
        verification_code = request.data.get('code')

        if not verification_code:
            # If no verification code is provided, send a new one
            self.send_verification_code(phone)
            return Response({"message": "Verification code sent", "timeout": "120"}, status=200)

        try:
            courier = Courier.get_by_phone(phone)
        except Courier.DoesNotExist as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"An error occurred: {exc} request: {request.data}",
                status_code=404,
                notify_admin=True,
            )

        if not self.verify_code(phone, verification_code):
            raise self.call_service_exception(
                error_type="unauthorized",
                message="Invalid phone or verification code",
                status_code=401,
                notify_admin=True,
            )

        # Block the old token if it exists
        self.block_old_token(courier)

        # Create new tokens for the courier
        resp = self.create_token_for_courier(courier)

        return Response(resp)

    def send_verification_code(self, phone):
        """
        Send a verification code to the courier's phone using Django cache.
        """
        verification_code = random.randint(100000, 999999)
        SMSService.send_sms(
            phone=phone,
            message=f"master-kebab.uz saytida roʻyxatdan oʻtish uchun tasdiqlash kodi: {verification_code}" # noqa
        )
        cache.set(f"verification_code:{phone}", verification_code, timeout=120)

    def verify_code(self, phone, verification_code):
        """
        Verify the provided verification code for the courier using Django cache.
        """
        stored_code = cache.get(f"verification_code:{phone}")
        if stored_code and stored_code == verification_code:
            cache.delete(f"verification_code:{phone}")
            return True

        return False

    @staticmethod
    def create_token_for_courier(courier: Courier):
        """
        Create a JWT token for a Courier instance.
        """
        # Create a new refresh token
        refresh = RefreshToken()

        # Add custom claims to the refresh token
        refresh['name'] = courier.name
        refresh['phone'] = courier.phone
        refresh['id'] = courier.id

        # Create the access token associated with the refresh token
        access_token = refresh.access_token

        # Add custom claims to the access token
        access_token['name'] = courier.name
        access_token['phone'] = courier.phone
        access_token['role'] = courier.role

        # Save the new token
        JWTCourierTokenList.to_list(token=str(access_token), courier_id=courier.id)
        courier.mark_as_login()

        # Return tokens as strings in a dictionary
        return {
            'refresh': str(refresh),
            'access': str(access_token),
        }

    @staticmethod
    def block_old_token(courier):
        """
        Block the old token if it exists.
        """
        last_token = JWTCourierTokenList.get_last_token(courier.id)
        if last_token:
            JWTCourierTokenBlockList.to_block_list(last_token.token)


class CourierLogoutAPIView(APIView, ServiceBaseView):
    """
    Logout view for removing refresh token
    """
    def post(self, request, *args, **kwargs):
        """
        Handle POST request to remove refresh token.
        it should get from header
        """
        self.process(request)

        return Response({
            "status": "token has been revoked",
        })

    def process(self, request):
        try:
            token = request.headers.get('Authorization', '').split(' ')[1]
        except Exception as exc:
            raise ServiceAPIException(
                error_type="bad_request",
                message=f"Invalid token: {exc}"
            ) from exc

        try:
            JWTCourierTokenBlockList.to_block_list(token)

        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_server_error",
                message=f"An error occurred while block token: {exc}",
                status_code=500,
                notify_admin=True,
            )
        try:
            courier = Courier.get_by_id(request.user.id)
            courier.mark_as_logout()
        except Exception as exc:
            raise self.call_service_exception(
                error_type="unauthorized",
                message=f"Invalid token or courier not found exc: {exc}",
                status_code=401,
                notify_admin=True,
            )
