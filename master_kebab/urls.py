"""
master_kebab URL Configuration
"""
from urllib.parse import unquote

import requests

from django.contrib import admin
from django.conf import settings
from django.urls import path, include
from django.views.generic import RedirectView
from django.shortcuts import redirect
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt


from apps.payment.models.invoice import Invoice


urlpatterns = [
    path('admin/', admin.site.urls),
]

urlpatterns.extend([
    path('api/v1/', include('apps.core.urls')),
    path('api/v1/bot/', include('apps.bot.urls')),
    path('api/v1/map/', include('apps.map.urls')),
    path('api/v1/iiko/', include('apps.iiko.urls')),
    path('api/v1/users/', include('apps.user.urls')),
    path('api/v1/orders/', include('apps.order.urls')),
    path('api/v1/', include('apps.operations.urls')),
    path('api/v1/product/', include('apps.product.urls')),
    path('api/v1/payment/', include('apps.payment.urls')),
    path('api/v1/courier/', include('apps.courier.urls')),
    path('api/v1/organization/', include('apps.organization.urls')),
    path('api/v1/hr/', include('apps.hr.urls')),
    path('api/v1/notify/', include('apps.notify.urls')),
    path('api/v1/loyalty/', include('apps.loyalty.urls')),
    path('api/v1/softphone/', include('apps.softphone.urls')),
])

# Frontend URLs
urlpatterns.extend([
    path('softphone/', include('apps.softphone.urls')),
])


def payment_proxy_view(request, invoice):
    """
    Redirect the request to the target server.
    """
    try:
        paylink = Invoice.get_by_invoice(invoice)

    except Invoice.DoesNotExist:
        return redirect("https://master-kebab.uz/payment-not-found")

    return redirect(paylink)


@csrf_exempt
def proxy_qr_scanner(request):
    target_url = request.GET.get("url")

    if not target_url:
        return HttpResponse("No URL provided", status=400)

    try:
        decoded_url = unquote(target_url)
        response = requests.get(decoded_url, timeout=10, stream=True)

        django_response = HttpResponse(
            response.raw,
            content_type=response.headers.get('Content-Type', 'application/octet-stream'),
            status=response.status_code
        )

        return django_response

    except requests.RequestException as e:
        return HttpResponse(f"Proxy request failed: {str(e)}", status=502)


proxy = [
    path(
        route='',
        view=RedirectView.as_view(
            url=settings.TELEGRAM_REDIRECT_URL,
            permanent=True
        )
    ),
    path('checkout/<str:invoice>/', payment_proxy_view),
    path('proxy-qr-scanner/', proxy_qr_scanner),  # noqa
]

urlpatterns.extend(proxy)


if settings.IS_TEST_MODE:
    import debug_toolbar
    urlpatterns = [
        path('__debug__/', include(debug_toolbar.urls)),
    ] + urlpatterns
