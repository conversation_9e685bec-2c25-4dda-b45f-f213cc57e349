# Generated by Django 5.0.6 on 2025-02-12 13:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0034_historicalorder_taker_id_historicalorder_taker_type_and_more"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="historicalorder",
            name="taker_type",
            field=models.CharField(
                blank=True,
                choices=[("OPERATORS", "operator"), ("MANAGERS", "managers")],
                max_length=10,
                null=True,
                verbose_name="Taker Type",
            ),
        ),
        migrations.AlterField(
            model_name="order",
            name="taker_type",
            field=models.CharField(
                blank=True,
                choices=[("OPERATORS", "operator"), ("MANAGERS", "managers")],
                max_length=10,
                null=True,
                verbose_name="Taker Type",
            ),
        ),
    ]
