"""
Module for retrieving payment links.
"""
import logging

from rest_framework import views, response

from apps.core.views import ServiceBaseView
from apps.payment.service import PaymentService


logger = logging.getLogger(__name__)


class PaymentLinkAPIView(views.APIView, ServiceBaseView):
    """
    API view for retrieving payment links for given orders.
    """
    permission_classes = []
    service: PaymentService = PaymentService

    def get(self, request, *args, **kwargs):
        """
        Handle GET request to retrieve payment link for a given order.
        """
        order_id = request.GET.get('order_id')
        # Check if the initiator is Click SuperApp from the header
        initiator = request.headers.get('X-Initiator', 'web').lower()
        is_superapp = initiator == 'click-superapp'

        # Log the initiator for debugging
        logger.info(f"Request initiator: {initiator}")

        if not order_id:
            raise self.call_service_exception(
                error_type='bad_request',
                message='Missing order_id in query params for getting payment link',
                status_code=400,
                notify_admin=True,
            )

        try:
            # Check if the request is from Click SuperApp
            if is_superapp:
                logger.info(f"Creating SuperApp payment link for order {order_id} (initiator: {initiator})")
                payment_link = self.service.create_superapp_payment(order_id)
            else:
                logger.info(f"Creating regular payment link for order {order_id} (initiator: {initiator})")
                payment_link = self.service.create_payment(order_id)
        except Exception as exc:
            raise self.call_service_exception(
                error_type='internal_error',
                message=f'Unable to retrieve payment link: {exc}',
                status_code=500,
                notify_admin=True,
            )

        return response.Response(
            data={'payment_link': payment_link},
        )
