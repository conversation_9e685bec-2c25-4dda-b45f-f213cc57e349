"""
the sms provider interface
"""
import abc


class ISMSProvider(abc.ABC):
    """
    the sms provider interface
    """
    def send_sms(self, phone, message):
        """
        sending a sms message for the given phone
        """
        raise NotImplementedError("sms provider is not implemented")

    def check_balance(self):
        """
        checking the balance
        """
        raise NotImplementedError("check balance is not implemented")
