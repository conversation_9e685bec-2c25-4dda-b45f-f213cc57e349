# Generated by Django 5.0.6 on 2024-08-20 11:29

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Bookmark',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('address', models.CharField(max_length=255)),
                ('lat', models.FloatField(max_length=255)),
                ('long', models.FloatField(max_length=255)),
            ],
            options={
                'verbose_name': 'Bookmark',
                'verbose_name_plural': 'bookmarks',
                'db_table': 'bookmarks',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('status', models.CharField(choices=[('Created', 'Created'), ('Approved', 'Approved'), ('CreationError', 'Creationerror'), ('SearchingDeliveryAgent', 'Searchingdeliveryagent'), ('DeliveryAgentNotFound', 'Deliveryagentnotfound'), ('CourierFindingError', 'Courierfindingerror'), ('Arrived', 'Arrived'), ('WebhookError', 'Webhookerror'), ('Timeout', 'Timeout'), ('InProgress', 'Inprogress'), ('Unconfirmed', 'Unconfirmed'), ('WaitCooking', 'Waitcooking'), ('CookingStarted', 'Cookingstarted'), ('Cancelled', 'Cancelled'), ('CookingCompleted', 'Cookingcompleted'), ('Delivered', 'Delivered'), ('DeliveryOrderError', 'Deliveryordererror'), ('OnWay', 'Onway'), ('Waiting', 'Waiting'), ('Closed', 'Closed')], default='Created', max_length=50)),
                ('initiator', models.CharField(choices=[('bot', 'Bot'), ('ios', 'Ios'), ('web', 'Web'), ('admin', 'Admin'), ('android', 'Android'), ('operator', 'Operator'), ('unknown', 'Unknown')], default='operator', max_length=50)),
                ('external_id', models.CharField(blank=True, db_index=True, null=True)),
                ('is_courier_accepted', models.BooleanField(default=False)),
                ('courier_search_task_id', models.CharField(blank=True, max_length=255, null=True)),
                ('external_number', models.CharField(blank=True, max_length=255, null=True)),
                ('attempt_count', models.IntegerField(default=0)),
                ('is_paid', models.BooleanField(default=False)),
                ('log', models.TextField(blank=True, null=True)),
            ],
            options={
                'db_table': 'order',
            },
        ),
        migrations.CreateModel(
            name='OrderDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('delivery_type', models.CharField(choices=[('SelfCall', 'Selfcall'), ('Delivery', 'Delivery')], default='Delivery', max_length=50)),
                ('delivery_address', models.CharField(blank=True, max_length=400, null=True)),
                ('entrance', models.CharField(blank=True, max_length=300, null=True)),
                ('door_code', models.CharField(blank=True, max_length=255, null=True)),
                ('floor', models.CharField(blank=True, max_length=50, null=True)),
                ('comment', models.TextField()),
                ('promo_code', models.CharField(blank=True, max_length=50, null=True)),
                ('payment_method', models.CharField(choices=[('Click', 'Click'), ('Payme', 'Payme'), ('Terminal', 'Terminal'), ('Cash', 'Cash')], default='Cash', max_length=50)),
                ('total_cost', models.FloatField()),
                ('delivery_cost', models.FloatField(default=0)),
                ('distance', models.FloatField(default=0)),
                ('delivery_cost_id', models.CharField(blank=True, max_length=100, null=True)),
                ('latitude', models.FloatField(blank=True, null=True)),
                ('longitude', models.FloatField(blank=True, null=True)),
                ('delivery_duration', models.CharField(blank=True, null=True)),
                ('complete_before', models.CharField(blank=True, null=True)),
            ],
            options={
                'db_table': 'order_detail',
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('quantity', models.IntegerField()),
            ],
            options={
                'db_table': 'order_item',
            },
        ),
    ]
