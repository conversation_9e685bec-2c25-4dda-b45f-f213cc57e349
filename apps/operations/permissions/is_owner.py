"""
this permission helps to protect objects that means that only object owner can
edit or delete that object
"""
from rest_framework import permissions


class IsOwner(permissions.BasePermission):
    """
    Object-level permission to only allow owners of an object to edit it.
    """

    def has_object_permission(self, request, view, obj):
        # Check if the user making the request is the owner of the object
        return obj.owner == request.user


class IsAdminOrOperator(permissions.BasePermission):
    """
    Object-level permission to allow only admins or operators to edit objects.
    """
    def has_object_permission(self, request, view, obj):
        # Check if the user is authenticated and has either admin or operator role
        return (
            request.user.is_authenticated and
            request.user.role in ['admin', 'operator']
        )
