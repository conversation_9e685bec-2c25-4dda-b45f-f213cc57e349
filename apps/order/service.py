"""
This module provides services for order-related operations, including delivery price calculations.

It contains the OrderService class which encapsulates methods for various order processing tasks,
with a focus on determining delivery prices based on distance.
"""
import logging

from datetime import datetime

from django.db import transaction
from django.core.cache import cache
from django.db.models import Count, Sum
from django.db.models.functions import TruncDay

from apps.order.models.order import Order
from apps.iiko.service import IIKOService
from apps.order.enums.order import OrderStatus
from apps.loyalty.service import LoyaltyService
from apps.order.models.bookmarks import Bookmark
from apps.organization.models import Organization
from apps.order.models.order_item import OrderItem
from apps.order.enums.delivery import DeliveryType
from apps.order.models.delivery import DeliveryPrice
from apps.order.models.order_detail import OrderDetail
from apps.product.models.modifier import ModiferProductPrices
from apps.core.enums.initiator import Initiator as InitiatorEnum
from apps.product.models.orderd_product import OrderedProductModifier
from apps.order.helper.order_with_provider import order_with_provider_helper


logger = logging.getLogger(__name__)


ORDER_CORELATION_KEY = "order_corelation_id_{order_id}"


class OrderService:
    """
    Service class for handling order-related operations.

    This class provides static methods for various order processing tasks,
    including calculating delivery prices based on distance.
    """

    @staticmethod
    def calculate_delivery_price(distance: float) -> DeliveryPrice:
        """
        Calculate the delivery price based on the given distance.

        This method determines the appropriate delivery distance type based on the input distance,
        and then retrieves the corresponding price from the DeliveryPrice model.

        Args:
            distance (float): The delivery distance in kilometers.

        Returns:
            float: The calculated delivery price. Returns 0.0 if no price is found for the distance type.

        Raises:
            None, but logs an error if no delivery price is found for the calculated distance type.
        """
        distance_types = [
            (2, DeliveryPrice.DeliveryDistanceType.SLIGHTLY_NEAR),
            (4, DeliveryPrice.DeliveryDistanceType.NEARBY),
            (6, DeliveryPrice.DeliveryDistanceType.LOCAL_CLOSE),
            (8, DeliveryPrice.DeliveryDistanceType.LOCAL_MID),
            (10, DeliveryPrice.DeliveryDistanceType.LOCAL_FAR),
            (12, DeliveryPrice.DeliveryDistanceType.DISTANT_CLOSE),
            (15, DeliveryPrice.DeliveryDistanceType.DISTANT_MID),
            (18, DeliveryPrice.DeliveryDistanceType.DISTANT_FAR),
            (20, DeliveryPrice.DeliveryDistanceType.REMOTE),
        ]

        for max_distance, delivery_type in distance_types:
            if distance <= max_distance:
                break
        else:
            delivery_type = DeliveryPrice.DeliveryDistanceType.REMOTE

        delivery_price = DeliveryPrice.get_delivery_price(delivery_type)

        if delivery_price:
            return delivery_price
        else:
            logging.error("No delivery price found for distance type: %s", delivery_type)
            return None

    @staticmethod
    def get_all_active_prices():
        """
        Get a list of all active delivery prices.

        This method retrieves all active DeliveryPrice objects and returns them as a list.

        Returns:
            list: A list of all active DeliveryPrice objects.
        """
        return DeliveryPrice.get_all_active_prices()

    @staticmethod
    def add_bookmark(
        lat, long, address=None, street_id=None,
        subaddress=None, order=None, user=None, name=None, is_client_bookmarked=False
    ):
        """
        Add a new bookmark for a user.

        This method creates or updates a Bookmark instance for the provided user and address.
        It also calculates the order count for the bookmark.

        Args:
            user (User): The user for whom the bookmark is being added.
            lat (float): The latitude of the bookmark.
            long (float): The longitude of the bookmark.
            street_id (str): The street ID of the bookmark.
            subaddress (str): The subaddress of the bookmark.
            order (Order, optional): The order associated with the bookmark. Defaults to None.

        Returns:
            Bookmark: The created or updated Bookmark instance.
        """
        bookmark, created, distance = Bookmark.get_or_update(
            user=user,
            lat=lat,
            long=long,
            street_id=street_id,
            address=address,
            subaddress=subaddress,
            name=name,
            is_client_bookmarked=is_client_bookmarked
        )
        if order:
            bookmark.order_count += 1
            bookmark.save()

        return bookmark, created, distance

    @classmethod
    def create_order(
        cls,
        order_items: list[OrderItem],
        order_detail: OrderDetail,
        organization=None,
        user=None,
        initiator=InitiatorEnum.BOT
    ):
        """
        Create a new order with given items and details.

        Creates an Order instance, calculates delivery price if applicable,
        adds order items, and calculates total cost. Wrapped in a database
        transaction for data consistency.

        If a promo_code is provided in order_detail, it will be applied to the order.

        Args:
            order_items (list[OrderItem]): OrderItem instances to add.
            order_detail (OrderDetail): Order details.
            organization (Organization, optional): Associated organization.
            user (User, optional): Associated user.
            initiator (InitiatorEnum, optional): Order initiator.

        Returns:
            Order: The newly created Order instance.

        Raises:
            ValueError: If no delivery price is found for the provided location.
        """

        with transaction.atomic():
            order = Order.create(
                user=user,
                order_detail=order_detail,
                initiator=initiator,
                organization=organization
            )
            cls._calculate_and_set_delivery_cost(order_detail, initiator)
            total_cost = cls._add_order_items_and_calculate_total(order, order_items, order_detail.delivery_cost)
            order_detail.total_cost = total_cost

            # Apply promo code if provided in order_detail
            if user and order_detail.promo_code:
                try:
                    organization_id = organization.external_id if organization else None

                    # Calculate items cost (total cost - delivery cost)
                    # items_cost = total_cost - order_detail.delivery_cost

                    success, discount_amount, promo = LoyaltyService.apply_promo_code_during_order_creation(
                        user_id=user.id,
                        promo_code=order_detail.promo_code,
                        order_id=order.id,
                        order_amount=total_cost,
                        items_cost=total_cost,
                        organization_id=organization_id
                    )

                    if success:
                        # Update order_detail with promo and adjusted total cost
                        order_detail.promo = promo
                        order_detail.total_cost = total_cost - discount_amount
                        logger.info(
                            "Applied promo code %s to order %s with discount %s",
                            order_detail.promo_code, order.id, discount_amount
                        )
                    else:
                        logger.warning(
                            "Failed to apply promo code %s to order %s",
                            order_detail.promo_code, order.id
                        )
                except Exception as e:
                    logger.error("Error applying promo code during order creation: %s", e)

            order_detail.save()

        return order

    @classmethod
    def retry(cls, order_id):
        """
        Retry an order.

        Args:
            order_id (int): The ID of the order to be retried.
        """
        order = Order.get_by_id(order_id)

        if order.status not in [
            OrderStatus.IN_PROGRESS,
            OrderStatus.CONNECTION_ERROR,
            OrderStatus.APPROVED
        ] or order.external_id is not None:
            raise ValueError("Invalid order status to retry.")

        order_with_provider_helper.is_can_create_order(organization=order.organization)

        order.order_detail.complete_before = None
        order.order_detail.save()

        if order.is_created():
            order.mark_as_approved()

    @classmethod
    def bind_to_taker(cls, order_id, taker_id, taker_type):
        """
        Bind the order to a taker.

        Args:
            order_id (int): The ID of the order.
            taker_id (int): The ID of the taker.
            taker_type (str): The type of the taker (e.g., 'delivery_boy', 'courier').
        """
        Order.bind_to_taker(
            order_id=order_id,
            taker_id=taker_id,
            taker_type=taker_type
        )

    @classmethod
    def _calculate_and_set_delivery_cost(cls, order_detail: OrderDetail, initiator: InitiatorEnum):
        """Calculate and set the delivery cost for the order."""
        _, distance = Organization.find_nearest_organization(
            latitude=order_detail.latitude,
            longitude=order_detail.longitude,
            radius=50
        )
        order_detail.distance = distance

        if order_detail.is_self_call():
            order_detail.delivery_cost = 0.0
            order_detail.delivery_cost_id = None
            return

        if cls._can_auto_calculate_delivery_cost(order_detail, initiator):
            delivery_price = cls.calculate_delivery_price(distance)
        elif initiator in [
            InitiatorEnum.CALL_CENTER.value
        ]:
            delivery_price = DeliveryPrice.get_by_external_id(order_detail.delivery_cost_id)
        else:
            delivery_price = None

        if not delivery_price:
            raise ValueError("No delivery price found for the provided location.")

        order_detail.delivery_cost_id = delivery_price.external_id
        order_detail.delivery_cost = delivery_price.price

    @staticmethod
    def _can_auto_calculate_delivery_cost(order_detail: OrderDetail, initiator: InitiatorEnum) -> bool:
        """Check if delivery cost can be automatically calculated."""
        return all([
            order_detail.delivery_type == DeliveryType.DELIVERY.value,
            initiator not in [InitiatorEnum.CALL_CENTER.value]
        ])

    @staticmethod
    def _add_order_items_and_calculate_total(order: Order, order_items: list[OrderItem], delivery_cost: float) -> float:
        """
        Add order items to the order and calculate the total cost.

        """
        total_cost = delivery_cost

        for item in order_items:
            item.order = order
            item.save()

            ordered_modifers = OrderedProductModifier.get_by_ordered_product_id(
                item.product.id
            )
            item_total = item.product.attributes.price
            for ordered_modifer in ordered_modifers:
                modifier_price = ModiferProductPrices.get_ordered_modifer_item_price(
                    modifer_id=ordered_modifer.modifier_item.id,
                    organization_id=order.organization.id
                )
                item_total += modifier_price.price * (ordered_modifer.quantity - ordered_modifer.modifier_item.restrictions.free_quantity) # noqa

            total_cost += item_total * item.quantity

        return total_cost

    @staticmethod
    def check_creation_status(order_id):
        """
        Check if the order creation is allowed based on the corelation ID.
        """
        order = Order.get_by_id(order_id)
        corelation_id = OrderService.get_order_corelation_id(order_id)
        organization_id = order.organization.external_id

        if order.is_created():
            return order.status

        response = IIKOService.commands_status(
            organization_id=organization_id,
            correlation_id=corelation_id
        )
        if order.status != response.state:
            order.set_status(response.state)

            if response.exception:
                order.log = response.exception
                order.save()

        return order.status

    @staticmethod
    def get_order_corelation_id(order_id):
        """
        Get the corelation ID for the given order ID.
        """
        key = ORDER_CORELATION_KEY.format(order_id=order_id)
        return cache.get(key)

    @staticmethod
    def set_order_corelation_id(order_id, corelation_id):
        """
        Set the corelation ID for the given order ID.
        """
        timeout = 60 * 60 * 1
        key = ORDER_CORELATION_KEY.format(order_id=order_id)
        cache.set(key, corelation_id, timeout)

    @staticmethod
    def get_order_statistics(start_date: datetime, end_date: datetime):
        """
        Get order statistics for the specified period.
        """
        orders = Order.objects.select_related('order_detail').filter(
            created_at__range=(start_date, end_date)
        )

        total_orders = orders.count()
        total_revenue = orders.aggregate(
            total_revenue=Sum('order_detail__total_cost')
        )['total_revenue'] or 0
        average_cheque = total_revenue / total_orders if total_orders else 0

        # average_cooking_time = orders.aggregate(
        #     avg_cooking_time=Avg('order_detail__cooking_time')
        # )['avg_cooking_time'] or 0
        # average_delivery_time = orders.aggregate(
        #     avg_delivery_time=Avg('order_detail__delivery_time')
        # )['avg_delivery_time'] or 0

        # Payment type distribution
        payment_distribution = orders.values(
            'order_detail__payment_method'  # Changed from payment_type to payment_method
        ).annotate(count=Count('id')).order_by('-count')

        # Order type distribution
        order_type_distribution = orders.values(
            'order_detail__delivery_type'
        ).annotate(count=Count('id')).order_by('-count')

        # Orders and revenue by day
        orders_by_day = orders.annotate(
            day=TruncDay('created_at')
        ).values('day').annotate(count=Count('id')).order_by('day')

        revenue_by_day = orders.annotate(
            day=TruncDay('created_at')
        ).values('day').annotate(
            revenue=Sum('order_detail__total_cost')
        ).order_by('day')

        return {
            'total_orders': total_orders,
            'average_orders_per_day': total_orders / ((end_date - start_date).days or 1),
            'total_revenue': total_revenue,
            'average_cheque': average_cheque,
            # 'average_cooking_time': average_cooking_time,
            # 'average_delivery_time': average_delivery_time,
            'payment_distribution': list(payment_distribution),
            'order_type_distribution': list(order_type_distribution),
            'orders_by_day': list(orders_by_day),
            'revenue_by_day': list(revenue_by_day),
        }
