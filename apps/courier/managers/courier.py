"""
tha apps courier manager
"""
from django.contrib.auth.models import BaseUserManager
from django.utils.translation import gettext_lazy as _


class CourierManager(BaseUserManager):
    """
    the courier manager
    """
    def create_user(self, phone, password=None, **extra_fields):
        if not phone:
            raise ValueError(_('The Phone number must be set'))
        phone = self.normalize_email(phone)
        courier = self.model(phone=phone, **extra_fields)
        courier.set_password(password)
        courier.save(using=self._db)
        return courier
