"""
Core app views package.

This package provides various API views and service handling utilities, including health checks,
remote configuration retrieval, and error handling with optional admin notification support.

Key Components:
- `HealthCheckAPIView`: A health check endpoint to monitor the service status.
- `RemoteConfigAPIView`: A remote configuration endpoint that returns system parameters.
- `ServiceBaseView`: A base class offering logging, exception handling, and notification services for derived views.
"""

from apps.core.views.core import ServiceBaseView, HealthCheckAPIView, RemoteConfigAPIView # noqa
