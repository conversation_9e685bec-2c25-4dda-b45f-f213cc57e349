from datetime import datetime

from rest_framework.views import APIView
from rest_framework.response import Response

from apps.order.models.order import Order
from apps.order.service import OrderService
from apps.order.permissions import OrderStatisticsPermissionClass


class OrderStatisticsAPIView(APIView):
    """
    API view to get order statistics.
    """
    permission_classes = [OrderStatisticsPermissionClass]

    def get(self, request, *args, **kwargs):
        """
        Handle GET request to retrieve order statistics.
        If no dates provided, returns all-time statistics.
        """
        # Get query parameters
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        if not start_date and not end_date:
            # Get first order date for all-time statistics
            first_order = Order.objects.order_by('created_at').first()
            start_date = first_order.created_at if first_order else datetime.now()
            end_date = datetime.now()
        elif not start_date:
            # If only end_date is provided, use first order date as start
            first_order = Order.objects.order_by('created_at').first()
            start_date = first_order.created_at if first_order else end_date

        elif not end_date:
            # If only start_date is provided, use current date as end
            end_date = datetime.now()

        if start_date == end_date:
            end_date = datetime.now()

        statistics = OrderService.get_order_statistics(start_date, end_date)
        return Response(statistics)
