"""
This module defines the `OrderListView`, a Django Rest Framework view to handle
the retrieval and listing of `Order` objects. The view supports role-based
filtering, time-based filtering, and query parameter-based filtering.

Clients will only see their own orders, while other roles (such as admin, operator,
and manager) will have additional filtering based on query parameters and specific
time ranges. The `OrderHistoryPermissionClass` is used to restrict access based on
user permissions.

This module is designed to work with the following:
- Django Rest Framework for API view handling
- Django Filter for supporting query parameter-based filtering
- Custom user role and order status enumerations for role-based and status-based logic
"""
from datetime import datetime, timedelta

from django.db.models import Q

from rest_framework import filters
from rest_framework import generics
from django_filters.rest_framework import DjangoFilterBackend

from apps.order.models import Order
from apps.core.views import ServiceBaseView
from apps.user.enum.role import UserRoleEnums
from apps.order.enums.order import OrderStatus
from apps.order.filters.order import OrderFilter
from apps.payment.enums.method import PaymentMethodEnum
from apps.order.permissions import OrderHistoryPermissionClass
from apps.order.serializers.order import OrderSerializer, OrderListSerializer


class OrderListView(
    ServiceBaseView,
    generics.ListAPIView,
):
    """
    API view to list and retrieve `Order` objects based on the user's role,
    query parameters, and time-based conditions.

    This view supports the following functionality:
    - Clients can only see their own orders.
    - Non-client users (such as admin, operator, manager) can apply filters by
      status, order ID, phone number, and operator ID.
    - Time-based filtering ensures only orders created within the specified
      time range (from 07:00 of the current day to 07:00 of the next day) are shown
      to non-client users.
    """

    queryset = Order.objects.all()
    filterset_class = OrderFilter
    serializer_class = OrderSerializer
    permission_classes = [
        OrderHistoryPermissionClass
    ]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]

    def get_queryset(self):
        """
        Override the default `get_queryset` to apply role-based filtering,
        query parameter filtering, and time-based filtering if applicable.

        Also, exclude orders that are not paid with cash and have a status of 'created'.
        """
        queryset = super().get_queryset()
        user = self.request.user

        # Apply role-based filtering
        queryset = self.filter_by_role(queryset, user)

        # Apply query parameter filters
        queryset = self.filter_by_query_params(queryset)

        # Apply time-based filtering only if the user is not a client
        if not (user.is_authenticated and user.role == UserRoleEnums.CLIENT.value):
            queryset = self.filter_by_time_range(queryset)

        # Exclude orders where payment type is not cash and status is 'created'
        # queryset = self.exclude_non_cash_created_orders(queryset)

        return queryset

    def filter_by_role(self, queryset, user):
        """
        Filter the queryset based on the user's role.
        If the user is a client, only return the orders associated with the user.
        """
        if user.is_authenticated and user.role == UserRoleEnums.CLIENT.value:
            queryset = queryset.filter(user=user)  # Assuming `user` is the field for client
        return queryset

    def filter_by_query_params(self, queryset):
        """
        Apply filters to the queryset based on query parameters passed in the
        request URL. Supports filtering by order status, order ID, phone number,
        and operator ID.
        """
        status = self.request.GET.get('status', None)
        order_id = self.request.GET.get('id', None)
        phone_number = self.request.GET.get('phone_number', None)
        operator_id = self.request.GET.get('operator_id', None)

        if status:
            queryset = queryset.filter(status=status)
        elif order_id or phone_number or operator_id:
            queryset = queryset.filter(status=OrderStatus.CLOSED.value)
        else:
            queryset = queryset.exclude(status=OrderStatus.CLOSED.value)

        return queryset

    def filter_by_time_range(self, queryset):
        """
        Filter the queryset to only include orders created between 07:00 of the current day
        and 07:00 of the next day. This time-based filter is applied only for non-client users.
        """
        now = datetime.now()

        if now.hour >= 7 and now.hour < 24:
            start_time = now.replace(hour=7, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(days=1)
        else:
            start_time = now.replace(hour=7, minute=0, second=0, microsecond=0) - timedelta(days=1)
            end_time = start_time + timedelta(days=1)

        queryset = queryset.filter(created_at__range=(start_time, end_time))

        return queryset

    def exclude_non_cash_created_orders(self, queryset):
        """
        Exclude orders from the queryset where the payment method is not 'cash' and
        the order status is 'created'.
        """
        return queryset.exclude(
            ~Q(order_detail__payment_method=PaymentMethodEnum.CASH.value) &  # Exclude non-cash payment methods
            Q(status=OrderStatus.CREATED.value)
        )

    def list(self, request, *args, **kwargs):
        """
        Override the default `list` method to use the `OrderListSerializer` for
        serialization when returning multiple order objects.
        """
        self.serializer_class = OrderListSerializer
        return super().list(request, *args, **kwargs)
