"""
payme enumuations
"""
from enum import StrEnum
from enum import IntEnum


class Methods(StrEnum):
    """
    the enumeration of create transaction
    """
    CHECK_PERFORM_TRANSACTION = "CheckPerformTransaction"
    CREATE_TRANSACTION = "CreateTransaction"
    PERFORM_TRANSACTION = "PerformTransaction"
    CANCEL_TRANSACTION = "CancelTransaction"
    CHECK_TRANSACTION = "CheckTransaction"
    SET_FISCAL_DATA = "SetFiscalData"
    GET_STATEMENT = "GetStatement"

    def __str__(self):
        return str(self.value)


class PaymeTransactionStateEnum(IntEnum):
    """
    The enumeration of possible payment states during the transaction process.

    States:
    - 0: Check created. Awaiting payment confirmation.
    - 1: First stage of verifications. Creating a transaction in the provider's billing system.
    - 2: Deducting money from the card.
    - 3: Closing the transaction in the provider's billing system.
    - 4: Check paid.
    - 20: Check is paused for manual intervention.
    - 21: Check is queued for cancellation.
    - 30: Check is queued for closing the transaction in the provider's billing system.
    - 50: Check canceled.

    Docs: https://developer.help.paycom.uz/metody-merchant-api/tipy-dannykh/
    """

    # PENDING states
    CREATED = 0
    WITHDRAWAL_IN_PROGRESS_1 = 1
    WITHDRAWAL_IN_PROGRESS_2 = 2
    WITHDRAWAL_CLOSING = 3

    # SUCCESSFUL state
    CHECK_PAID = 4

    # States indicating the transaction will be cancelled or paused
    WAITING_TO_BE_CHECKED = 20
    WAITING_TO_BE_CANCELLED_1 = 21
    WAITING_TO_BE_CANCELLED_2 = 30

    # CANCELED state
    CANCELED = 50  # Check canceled.
    CANCELLED_WITHDRAWAL_IN_PROGRESS_2 = -2
    CANCELLED_AFTER_WITHDRAWAL_IN_PROGRESS_1 = -1

    def __str__(self):
        return f"Payment state {self.name} (Code: {self.value})"

    @classmethod
    def get_description(cls, state):
        """
        Returns a human-readable description for each transaction state.

        Args:
            state (int): The transaction state code.

        Returns:
            str: The description of the state.
        """
        descriptions = {
            cls.CREATED: "Check created. Awaiting payment confirmation.",
            cls.WITHDRAWAL_IN_PROGRESS_1: "First stage of verifications in the provider's billing system.",
            cls.WITHDRAWAL_IN_PROGRESS_2: "Deducting money from the card.",
            cls.WITHDRAWAL_CLOSING: "Closing the transaction in the provider's billing system.",
            cls.CHECK_PAID: "Check paid successfully.",
            cls.WAITING_TO_BE_CHECKED: "Check is paused for manual intervention.",
            cls.WAITING_TO_BE_CANCELLED_1: "Check is queued for cancellation.",
            cls.WAITING_TO_BE_CANCELLED_2: "Check is queued for closing in the provider's billing system.",
            cls.CANCELED: "Check canceled.",
            cls.CANCELLED_WITHDRAWAL_IN_PROGRESS_2: "The transaction is canceled after the second withdrawal stage.",
            cls.CANCELLED_AFTER_WITHDRAWAL_IN_PROGRESS_1: "The transaction is canceled after the first withdrawal stage.", # noqa
        }
        return descriptions.get(state, "Unknown transaction state")


class PaymeReasonCodesEnum(IntEnum):
    """
    Enum class for Payme error codes, representing different error states that can occur
    during a transaction.
    """
    RECIPIENT_NOT_FOUND = 1
    DEBIT_OPERATION_FAILED = 2
    TRANSACTION_FAILED = 3
    TRANSACTION_TIMEOUT = 4
    REFUND = 5
    UNKNOWN_ERROR = 10

    @classmethod
    def get_description(cls, code):
        """
        Returns a human-readable description for a given Payme error code.
        Args:
            code (int): The error code from the enum.

        Returns:
            str: The description of the error code.
        """
        descriptions = {
            cls.RECIPIENT_NOT_FOUND: "One or more recipients not found or inactive in Payme Business.",
            cls.DEBIT_OPERATION_FAILED: "Error during debit operation in the processing center.",
            cls.TRANSACTION_FAILED: "Error executing the transaction.",
            cls.TRANSACTION_TIMEOUT: "Transaction canceled due to timeout.",
            cls.REFUND: "Refund issued.",
            cls.UNKNOWN_ERROR: "Unknown error."
        }

        return descriptions.get(code, "Error code not found")
