"""
Yandex Geocoder API Serializers

This module provides serializers for the Yandex Geocoder API
endpoints, including request validation and response formatting.
"""

from rest_framework import serializers


class YandexGeocoderRequestSerializer(serializers.Serializer):
    """
    Serializer for Yandex Geocoder API request parameters.
    
    Validates the input parameters for geocoding requests.
    """
    
    name = serializers.CharField(
        max_length=255,
        required=True,
        help_text="Location name to geocode (e.g., 'Navoiy xalqaro aeroporti', 'Tashkent')"
    )
    
    language = serializers.CharField(
        max_length=10,
        default='en_US',
        required=False,
        help_text="Response language (e.g., 'en_US', 'ru_RU', 'uz_UZ')"
    )
    
    results = serializers.IntegerField(
        min_value=1,
        max_value=50,
        default=10,
        required=False,
        help_text="Number of results to return (1-50, default: 10)"
    )
    
    def validate_name(self, value):
        """
        Validate the name parameter.
        
        Args:
            value (str): The location name to geocode.
            
        Returns:
            str: Cleaned location name.
            
        Raises:
            serializers.ValidationError: If name is invalid.
        """
        if not value or not value.strip():
            raise serializers.ValidationError("Name cannot be empty")
        
        # Remove extra whitespace
        cleaned_value = value.strip()
        
        # Check minimum length
        if len(cleaned_value) < 2:
            raise serializers.ValidationError("Name must be at least 2 characters long")
        
        return cleaned_value
    
    def validate_language(self, value):
        """
        Validate the language parameter.
        
        Args:
            value (str): Language code.
            
        Returns:
            str: Validated language code.
            
        Raises:
            serializers.ValidationError: If language format is invalid.
        """
        if not value:
            return 'en_US'
        
        # Basic language code validation (xx_XX format)
        if len(value) == 5 and '_' in value:
            parts = value.split('_')
            if len(parts) == 2 and len(parts[0]) == 2 and len(parts[1]) == 2:
                return value
        
        # Allow simple language codes like 'en', 'ru', 'uz'
        if len(value) == 2 and value.isalpha():
            return f"{value}_US"  # Default to US variant
        
        raise serializers.ValidationError(
            "Language must be in format 'xx_XX' (e.g., 'en_US', 'ru_RU') or 'xx' (e.g., 'en', 'ru')"
        )


class AddressComponentSerializer(serializers.Serializer):
    """
    Serializer for address components.
    """
    kind = serializers.CharField()
    name = serializers.CharField()


class BoundsSerializer(serializers.Serializer):
    """
    Serializer for location bounds.
    """
    lower_corner = serializers.CharField()
    upper_corner = serializers.CharField()


class LocationSerializer(serializers.Serializer):
    """
    Serializer for individual location data.
    """
    name = serializers.CharField()
    description = serializers.CharField(allow_blank=True)
    formatted_address = serializers.CharField(allow_blank=True)
    uri = serializers.CharField(allow_blank=True)
    coordinates = serializers.DictField(required=False, allow_null=True)
    precision = serializers.CharField(allow_blank=True)
    kind = serializers.CharField(allow_blank=True)
    country_code = serializers.CharField(allow_blank=True)
    components = AddressComponentSerializer(many=True, default=list)
    bounds = BoundsSerializer(required=False, allow_null=True)


class YandexGeocoderResponseSerializer(serializers.Serializer):
    """
    Serializer for Yandex Geocoder API response.
    
    Formats the response data in a consistent structure for frontend consumption.
    """
    locations = LocationSerializer(many=True)
    total_results = serializers.IntegerField()
    found = serializers.IntegerField()
    request = serializers.CharField()
    remaining_requests = serializers.IntegerField()
    status = serializers.CharField()


class GeocoderApiAvailabilitySerializer(serializers.Serializer):
    """
    Serializer for Geocoder API availability status.
    """
    available = serializers.BooleanField()
    message = serializers.CharField()
    remaining_requests = serializers.IntegerField(required=False)


class SimpleLocationSerializer(serializers.Serializer):
    """
    Simplified serializer for basic location data (for quick responses).
    """
    name = serializers.CharField()
    formatted_address = serializers.CharField()
    uri = serializers.CharField()
    coordinates = serializers.DictField()
    
    
class YandexGeocoderSimpleResponseSerializer(serializers.Serializer):
    """
    Simplified response serializer for basic geocoding needs.
    """
    locations = SimpleLocationSerializer(many=True)
    total_results = serializers.IntegerField()
    remaining_requests = serializers.IntegerField()
    status = serializers.CharField()
