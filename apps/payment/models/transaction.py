"""
This module contains models and functionality for tracking changes in payment transactions.
It logs any significant modifications to payment transactions such as amount, state, or payment method,
allowing for a detailed historical record of each transaction's state over time.
"""
from django.db import models
from django.utils import timezone

from apps.order.models.order import Order
from apps.core.models.base import BaseModel
from apps.payment.models.method import PaymentMethod
from apps.payment.enums.state import TransactionStateEnum
from apps.payment.enums.state import TransactionCancelReasonEnum


class PaymentTransaction(BaseModel):
    """
    the payment transaction model
    """
    transaction_id = models.CharField(max_length=50, unique=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    state = models.CharField(
        choices=TransactionStateEnum.choices(),
        default=TransactionStateEnum.WAITING.value,
    )
    payment_method = models.ForeignKey(
        PaymentMethod,
        on_delete=models.CASCADE,
        related_name='payment_transactions',
    )
    order = models.ForeignKey(
        Order,
        on_delete=models.CASCADE,
        related_name='payment_transactions',
    )
    cancel_reason = models.CharField(
        choices=TransactionCancelReasonEnum.choices(),
        null=True, blank=True
    )
    performed_at = models.DateTimeField(
        null=True, blank=True, db_index=True
    )
    cancelled_at = models.DateTimeField(
        null=True, blank=True, db_index=True
    )

    def __str__(self) -> str:
        """
        returns a string representation of the payment transaction
        """
        return f"{self.transaction_id} - {self.amount} - {self.state} - {self.payment_method} - {self.order}"

    class Meta:
        """
        the meta fields
        """
        db_table = 'payment_transactions'
        ordering = ['-created_at']
        unique_together = ['transaction_id', 'payment_method', 'order']
        verbose_name = "Payment Transaction"
        verbose_name_plural = "Payment Transactions"

    @classmethod
    def update_or_create(
        cls, transaction_id,
        amount, state, payment_method, order
    ) -> tuple["PaymentTransaction", bool]:
        """
        Class method to get or create a PaymentTransaction instance.

        :param transaction_id: The unique ID of the transaction.
        :param amount: The amount for the transaction.
        :param state: The current state of the transaction (e.g., pending, completed).
        :param payment_method: The payment method used for the transaction.
        :param order: The related order instance for the transaction.
        :return: (instance, created) tuple where 'instance' is the PaymentTransaction instance and
                'created' is a boolean indicating if it was created or not.
        """
        transaction, updated = cls.objects.update_or_create(
            order=order,
            defaults={
                'amount': amount,
                'state': state,
                'payment_method': payment_method,
                'transaction_id': transaction_id,  # Pass the Order instance here, not order_id
            }
        )
        return transaction, updated

    def mark_as_completed(self):
        """
        Mark the transaction as paid.
        """
        self.state = TransactionStateEnum.COMPLETED.value
        self.performed_at = timezone.now()
        self.save()
        return self

    def mark_as_cancelled(self, cancel_reason=TransactionCancelReasonEnum.REFUND):
        """
        Mark the transaction as cancelled.
        """
        self.state = TransactionStateEnum.CANCELLED.value
        self.cancel_reason = cancel_reason
        self.cancelled_at = timezone.now()
        self.save()
        return self

    @classmethod
    def get_transaction_history(cls, created_at=None, updated_at=None):
        """
        Class method to get all PaymentTransaction instances related to a specific order.

        :param created_at: Limit results to those created after this date (if provided).
        :param updated_at: Limit results to those updated after this date (if provided).
        :return: A QuerySet of PaymentTransaction instances related to the order.
        """
        queryset = cls.objects.filter(order=cls.order)
        if created_at:
            queryset = queryset.filter(created_at__gte=created_at)
        if updated_at:
            queryset = queryset.filter(updated_at__gte=updated_at)
        return queryset

    @classmethod
    def get_by_transaction_id(cls, transaction_id):
        """
        Class method to get a PaymentTransaction instance by its transaction ID.

        :param transaction_id: The unique ID of the transaction.
        :return: The PaymentTransaction instance or None if not found.
        """
        return cls.objects.get(transaction_id=transaction_id)

    @classmethod
    def is_transaction_exists(cls, transaction_id):
        """
        Class method to check if a PaymentTransaction instance exists by its transaction ID.

        :param transaction_id: The unique ID of the transaction.
        :return: True if the transaction exists, False otherwise.
        """
        return cls.objects.filter(transaction_id=transaction_id).exists()

    def is_completed(self):
        """
        Returns True if the transaction is completed, False otherwise.
        """
        return self.state == TransactionStateEnum.COMPLETED.value

    def is_created(self):
        """
        Returns True if the transaction is created, False otherwise.
        """
        return self.state == TransactionStateEnum.CREATED.value

    def is_cancelled(self):
        """
        Returns True if the transaction is cancelled, False otherwise.
        """
        return self.state == TransactionStateEnum.CANCELLED.value
