"""
init settings
"""
from master_kebab.settings.default import * # noqa

from master_kebab.settings.internal.bot import * # noqa
from master_kebab.settings.internal.auth import * # noqa
from master_kebab.settings.internal.courier import * # noqa
from master_kebab.settings.internal.storage import * # noqa
from master_kebab.settings.internal.user import * # noqa
from master_kebab.settings.internal.order import * # noqa
from master_kebab.settings.internal.sms import * # noqa
from master_kebab.settings.internal.payment import * # noqa
from master_kebab.settings.internal.map import * # noqa
from master_kebab.settings.internal.click_up import * # noqa
from master_kebab.settings.internal.iiko import * # noqa
from master_kebab.settings.internal.core import * # noqa
from master_kebab.settings.internal.organization import * # noqa
from master_kebab.settings.internal.product import * # noqa
from master_kebab.settings.internal.notify import * # noqa
from master_kebab.settings.internal.operations import * # noqa
from master_kebab.settings.internal.hr import * # noqa
from master_kebab.settings.internal.loyalty import * # noqa
from master_kebab.settings.internal.softphone import * # noqa
from master_kebab.settings.external.drf import * # noqa
from master_kebab.settings.external.unfold import * # noqa
from master_kebab.settings.external.telebot import * # noqa
from master_kebab.settings.external.faststream import * # noqa
from master_kebab.settings.external.eskiz import * # noqa
from master_kebab.settings.external.channels import * # noqa
from master_kebab.settings.external.mini_app import * # noqa
from master_kebab.settings.external.storages import * # noqa
from master_kebab.settings.external.debug import * # noqa
from master_kebab.settings.external.django_redis import * # noqa
from master_kebab.settings.external.simple_history import * # noqa
