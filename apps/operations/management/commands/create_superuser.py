"""
create_superuser.py

Custom management command for creating a superuser with a specific phone number and role.
"""

from django.core.management.base import BaseCommand

from apps.user.enum.role import UserRoleEnums
from apps.operations.models.operations import Operations


class Command(BaseCommand):
    """
    Create a superuser with a specific phone number and role.

    python manage.py create_superuser --phone <phone_number> --password <password>
    """
    help = 'Create a superuser with a specific phone number and role'

    def add_arguments(self, parser):
        parser.add_argument('--phone', type=str, required=True, help='Phone number for the superuser')
        parser.add_argument('--password', type=str, required=True, help='Password for the superuser')

    def handle(self, *args, **kwargs):
        phone = kwargs['phone']
        password = kwargs['password']

        # Check if the superuser already exists
        if Operations.objects.filter(phone=phone).exists():
            self.stdout.write(self.style.ERROR('Superuser with this phone number already exists.'))
            return

        try:
            # Create superuser
            Operations.objects.create_superuser(phone=phone, password=password, role=UserRoleEnums.ADMIN.value)
            self.stdout.write(self.style.SUCCESS(f'Superuser created successfully with phone: {phone}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating superuser: {e}'))
