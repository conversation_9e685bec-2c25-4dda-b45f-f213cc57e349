"""
This module provides API views for handling address-related operations in the IIKO system.
"""

from rest_framework import views
from rest_framework import response
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page

from apps.iiko.service import IIKOService
from apps.core.views import ServiceBaseView


class ByCityAPIView(views.APIView, ServiceBaseView):
    """
    API view for retrieving cities from the IIKO system.

    This view handles GET requests to fetch cities based on the provided organization ID.
    It inherits from both APIView and ServiceBaseView to leverage REST framework features
    and custom service-related functionalities.
    """

    @method_decorator(cache_page(60 * 15))  # Cache for 15 minutes
    def get(self, request):
        """
        Handle GET requests to retrieve cities.

        Args:
            request (Request): The incoming request object.

        Returns:
            Response: A response containing the list of cities.

        Raises:
            ServiceAPIException: If no organization_id is provided in the query parameters.
        """
        try:
            cities = IIKOService.get_streets()

        except Exception as exc:
            raise self.call_service_exception(
                error_type='internal_server_error',
                message=f'An error occurred while retrieving cities: {exc}',
                status_code=500,
            ) from exc

        return response.Response(cities.model_dump())
