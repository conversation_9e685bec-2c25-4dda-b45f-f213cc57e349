"""
init iiko nomenclature class
"""
from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import WebHookSettingRequestBody
from apps.iiko.providers.iiko.http.response import WebHookSettingResponseBody


class IIKOWebHookAPI:
    """
    IIKO getting nomenclature API client
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def settings(self, organization_id: str, headers: dict) -> WebHookSettingResponseBody:
        """
        Based on the Organization ID you received above, request a menu.
        """
        endpoint = "/api/1/webhooks/settings"
        request_body = WebHookSettingRequestBody(
            organizationId=organization_id
        ).as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        response_body = WebHookSettingResponseBody(**response_body)
        return response_body


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
webhook = IIKOWebHookAPI(http=http_base_client)
