"""
the banner model
"""
from django.db import models
from django.core.validators import FileExtensionValidator

from apps.core.models.base import BaseModel

from master_kebab import settings


if settings.DEBUG:
    UPLOAD_TO = "test/banners"

else:
    UPLOAD_TO = "banners"


class Banner(BaseModel):
    """
    Banner model
    """
    class Meta:
        """
        Meta fields.
        """
        ordering = ['id']
        db_table = 'banner'

    name = models.CharField(max_length=100, null=True, blank=True)
    image_url = models.ImageField(
        validators=[FileExtensionValidator(
            allowed_extensions=["jpeg", "jpg", "png", "webp",]
        )], upload_to=UPLOAD_TO, null=True
    )
    ad_link = models.URLField(null=True, blank=True, default=None)
    position = models.IntegerField(unique=True)

    def __str__(self) -> str:
        return str(self.name)

    @classmethod
    def get_by_external_id(cls, external_id):
        """
        get banner by external id
        """
        return cls.objects.get(external_id=external_id)
