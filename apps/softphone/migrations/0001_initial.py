# Generated by Django 5.0.6 on 2025-05-29 12:49

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('order', '0037_alter_order_initiator'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SoftphoneConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True, db_index=True)),
                ('name', models.CharField(help_text='Configuration name', max_length=100, unique=True)),
                ('screen_popup_url', models.URLField(default='http://localhost:8000/api/v1/softphone/popup/', help_text='URL for screen popup on incoming calls')),
                ('customer_data_url', models.URLField(default='http://localhost:8000/api/v1/softphone/customer-data/', help_text='URL for retrieving customer data')),
                ('call_log_url', models.URLField(default='http://localhost:8000/api/v1/softphone/log-call/', help_text='URL for logging calls')),
                ('recording_upload_url', models.URLField(default='http://localhost:8000/api/v1/softphone/upload-recording/', help_text='URL for uploading call recordings')),
                ('default_sip_account', models.CharField(blank=True, help_text='Default SIP account to use', max_length=100, null=True)),
                ('enable_screen_popup', models.BooleanField(default=True, help_text='Enable automatic screen popup on incoming calls')),
                ('enable_call_logging', models.BooleanField(default=True, help_text='Enable automatic call logging')),
                ('enable_recording_upload', models.BooleanField(default=True, help_text='Enable automatic recording upload')),
                ('click_to_call_enabled', models.BooleanField(default=True, help_text='Enable click-to-call functionality')),
                ('caller_id_regex', models.TextField(blank=True, help_text='Regular expression for caller ID modification', null=True)),
                ('is_active', models.BooleanField(default=True)),
            ],
            options={
                'verbose_name': 'Softphone Configuration',
                'verbose_name_plural': 'Softphone Configurations',
                'db_table': 'softphone_config',
            },
        ),
        migrations.CreateModel(
            name='CallLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True, db_index=True)),
                ('caller_id', models.CharField(help_text='Phone number of caller', max_length=20)),
                ('called_number', models.CharField(help_text='Phone number that was called', max_length=20)),
                ('direction', models.CharField(choices=[('inbound', 'Inbound'), ('outbound', 'Outbound')], max_length=10)),
                ('status', models.CharField(choices=[('answered', 'Answered'), ('missed', 'Missed'), ('busy', 'Busy'), ('failed', 'Failed'), ('no_answer', 'No Answer')], max_length=15)),
                ('duration', models.IntegerField(default=0, help_text='Call duration in seconds')),
                ('start_time', models.DateTimeField(help_text='Call start time')),
                ('end_time', models.DateTimeField(blank=True, help_text='Call end time', null=True)),
                ('recording_url', models.URLField(blank=True, help_text='URL to call recording', null=True)),
                ('recording_file', models.FileField(blank=True, null=True, upload_to='call_recordings/')),
                ('external_id', models.CharField(blank=True, help_text='External ID from Softphone.Pro', max_length=255, null=True)),
                ('notes', models.TextField(blank=True, help_text='Call notes', null=True)),
                ('did_number', models.CharField(blank=True, help_text='DID number used for the call', max_length=20, null=True)),
                ('sip_account', models.CharField(blank=True, help_text='SIP account used', max_length=100, null=True)),
                ('operator', models.ForeignKey(blank=True, help_text='Operator who handled the call', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='operator_calls', to=settings.AUTH_USER_MODEL)),
                ('order', models.ForeignKey(blank=True, help_text='Order associated with this call', null=True, on_delete=django.db.models.deletion.SET_NULL, to='order.order')),
                ('user', models.ForeignKey(blank=True, help_text='User associated with this call', null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Call Log',
                'verbose_name_plural': 'Call Logs',
                'db_table': 'softphone_call_logs',
                'ordering': ['-start_time'],
                'indexes': [models.Index(fields=['caller_id'], name='softphone_c_caller__552e08_idx'), models.Index(fields=['called_number'], name='softphone_c_called__404f86_idx'), models.Index(fields=['start_time'], name='softphone_c_start_t_68639f_idx'), models.Index(fields=['external_id'], name='softphone_c_externa_a17cb8_idx')],
            },
        ),
    ]
