"""
the running faststream command
"""
import subprocess

from django.core.management.base import BaseCommand


COMMAND = [
    'uvicorn',
    'master_kebab.asgi:application',
    '--host', '0.0.0.0', '--port', '8001'
]


class Command(BaseCommand):
    """
    Running command for subscriber.
    """
    def handle(self, *args, **options):
        """
        Main entry point for executing the command.
        """
        process = subprocess.<PERSON><PERSON>(COMMAND)

        try:
            process.communicate()
        except KeyboardInterrupt:
            process.terminate()
            process.wait()
