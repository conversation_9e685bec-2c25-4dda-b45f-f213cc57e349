"""
the payment transaction admin
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin
from apps.payment.models.transaction import PaymentTransaction


class PaymentTransactionUI(ModelAdmin):
    """
    The payment transaction admin page
    """
    list_display = [
        'id',
        'transaction_id',
        'payment_method',
        'state',
        'cancel_reason',
        'created_at',
        'updated_at'
    ]
    list_filter = ['payment_method', 'state']
    search_fields = ['transaction_id']
    list_display_links = list_display

    date_hierarchy = 'created_at'
    readonly_fields = (
        'created_at', 'updated_at', 'performed_at', 'cancel_reason',
        'cancelled_at', 'transaction_id', 'payment_method', 'state', 'order'
    )
    fieldsets = (
        (None, {
            'fields': (
                'transaction_id',
                'payment_method',
                'state',
                'amount',
                'order',
                'cancel_reason'
            ),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at', 'performed_at', 'cancelled_at'),
            'classes': ('collapse',)
        }),
    )


admin.site.register(PaymentTransaction, PaymentTransactionUI)
