"""
the product type enomuration
"""
from enum import Enum


class ProductType(str, Enum):
    """
    The enumeration of product types.
    """
    DISH = "DISH"
    MODIFIER = "Modifier"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
