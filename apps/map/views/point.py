"""
point serializers
"""
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from apps.user.enum.role import UserRoleEnums
from apps.map.serializer import PointSerializer
from apps.core.exceptions import ServiceAPIException
from apps.map.models.point import update_or_create_point


class PointUpdateOrCreateAPIView(APIView):
    """
    the user register serialization view
    """
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """
        register allows only POST requests
        """
        serializer = PointSerializer(data=request.data)

        if serializer.is_valid():
            user = request.user

            if not user.role == UserRoleEnums.COURIER:
                raise ServiceAPIException(
                    error_type='forbidden',
                    message='You are not allowed to perform this action',
                    status_code=status.HTTP_403_FORBIDDEN,
                )

            latitude = serializer.validated_data['latitude']
            longitude = serializer.validated_data['longitude']

            point, created = update_or_create_point(
                user=user,
                latitude=latitude,
                longitude=longitude
            )
            response_data = {
                'point': PointSerializer(point).data,
                'created': created
            }

            status_code = status.HTTP_200_OK
            if created:
                status_code = status.HTTP_201_CREATED

            return Response(response_data, status_code)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
