# Generated by Django 5.0.6 on 2024-12-21 15:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0031_alter_deliveryprice_distance_type"),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name="deliveryprice",
            name="distance_type",
            field=models.CharField(
                choices=[
                    ("SN", "Slightly Near (0-2km)"),
                    ("NB", "Nearby (2-4 km)"),
                    ("LC", "Local Close (4-6 km)"),
                    ("LM", "Local Mid (6-8 km)"),
                    ("LF", "Local Far (8-10 km)"),
                    ("DC", "Distant Close (10-12 km)"),
                    ("DM", "Distant Mid (12-15 km)"),
                    ("DF", "Distant Far (15-18 km)"),
                    ("RM", "Remote (18-20 km)"),
                ],
                default="NB",
                help_text="Type of delivery based on distance",
                max_length=8,
                unique=True,
            ),
        ),
    ]
