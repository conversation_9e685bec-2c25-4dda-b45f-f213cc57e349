"""
the base model module.
"""
import logging

from django.db import models


logger = logging.getLogger(__name__)


class BaseModel(models.Model):
    """
    The time-stamped model class with auto-managed created_at and updated_at fields.
    """
    created_at = models.DateTimeField(auto_now_add=True, db_index=True)
    updated_at = models.DateTimeField(auto_now=True, db_index=True)

    class Meta:
        """
        The time-stamped model meta class fields.
        """
        abstract = True
