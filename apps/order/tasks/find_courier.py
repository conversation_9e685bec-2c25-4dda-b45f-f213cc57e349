"""
the celery taks
"""
from celery import Task

from django.db import transaction

from master_kebab import celery_app

from apps.order.models.order import Order
from apps.courier.models.orders import MissedOrder


@celery_app.task(bind=True)
def reassign_order_if_no_response_task(self: Task, order_id):
    # pylint: disable=C0415
    try:
        order = Order.get_by_id(order_id)
        reassign_order_if_no_response(order)
    except Exception as exc:
        order.mark_as_courier_finding_error((str(exc)))


@transaction.atomic
def reassign_order_if_no_response(order: Order):
    """
    reassign order if no response after 60 seconds
    """
    # pylint: disable=C0415
    from apps.order.utility.find_courier import find_courier_util

    if order.is_courier_accepted():
        print(f"courier already accepted order_id: {order.id} courier_id: {order.delivery_agent.id}")
        return

    if order.attempt_count < 10:
        order.attempt_count += 1

        # mark as missed for last courier if courier binded
        if order.delivery_agent:
            MissedOrder.mark_as_missed_by_courier(
                courier_id=order.delivery_agent.id,
                order_id=order.id,
            )
            # unbinding courier if courier was not accepted
            order.delivery_agent = None
        order.save()

        find_courier_util.assign_to_next_courier(order)
