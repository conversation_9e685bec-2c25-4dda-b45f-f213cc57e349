"""
unfold ui settings.
"""
from master_kebab.settings import default
from master_kebab.settings.external.unfold import site
from master_kebab.settings.external.unfold import sidebar
from master_kebab.settings.external.unfold import extantion

IS_UNFOLD_UI_ENABLED = default.env.bool("IS_UNFOLD_UI_ENABLED", True)

if IS_UNFOLD_UI_ENABLED:
    for index, app in enumerate([
        'unfold',
        'unfold.contrib.forms',
        'unfold.contrib.filters',
        'unfold.contrib.import_export',
        'unfold.contrib.guardian',
        'unfold.contrib.simple_history',
    ]):
        default.INSTALLED_APPS.insert(
            index, app,
        )

    UNFOLD = {}

    UNFOLD.update(
        site.SITE,
    )
    UNFOLD.update(
        sidebar.SIDEBAR
    )
    UNFOLD.update(
        extantion.EXTENSIONS
    )
