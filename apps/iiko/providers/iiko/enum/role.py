"""
the enumeration of roles
"""
from enum import Enum


class RoleEnum(str, Enum):
    """
    The enumeration of role.
    """
    DELIVERY_AGENT = "Курьер"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
