"""
the order item model
"""
from django.db import models
from django.db import transaction
from django.db.models import Count, Max

from apps.core.models.base import BaseModel
from apps.product.models.product import ProductAttribute
from apps.product.models.orderd_product import init_ordered_product
from apps.product.models.orderd_product import OrderedProduct
from apps.product.models.product import get_product_by_id


class OrderItem(BaseModel):
    """
    the order item model
    """
    class Meta:
        """
        the meta fields
        """
        db_table = 'order_item'

    order = models.ForeignKey(
        'Order',  # use string reference to avoid circular import issues
        on_delete=models.CASCADE,
        null=True,
        related_name='order_items'
    )
    product = models.ForeignKey(
        OrderedProduct,
        on_delete=models.CASCADE,
        verbose_name="Ordered Product"
    )
    quantity = models.IntegerField()

    def __str__(self):
        return f"Product: {self.product.title} Quantity: {self.quantity}" # noqa

    @staticmethod
    def init_order_item(
        product_id,
        quantity,
        attribute: ProductAttribute,
        comment: str = None,
        modifiers: list = None
    ):
        """
        init the order item model
        """
        with transaction.atomic():
            product_obj = get_product_by_id(product_id)
            attribute_obj = ProductAttribute.get_by_id(attribute.id)

            # attribute comment that means that ordered product comment
            ordered_product = init_ordered_product(product_obj, attribute_obj, comment, modifiers)
            return OrderItem.objects.create(
                product_id=ordered_product.id,
                quantity=quantity
            )

    @staticmethod
    def get_most_ordered_products(limit=20):
        """
        Get unique product external IDs ordered by the most ordered.
        """
        most_ordered = (
            OrderItem.objects
            .values('product__external_id')  # Group by external_id
            .annotate(
                title=Max('product__title'),  # Get the max title for each external_id
                order_count=Count('product__external_id')  # Count the occurrences
            )
            .order_by('-order_count')[:limit]  # Order by the most ordered and limit the result
        )
        product_external_ids = [item['product__external_id'] for item in most_ordered]

        return product_external_ids
