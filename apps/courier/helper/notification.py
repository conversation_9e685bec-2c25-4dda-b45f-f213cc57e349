"""
sending notification helper for deliver agents
"""
from apps.courier.models.fcm import FCMDevice
from apps.notify.service import notification_service


class DeliveryNotificationHelper:
    """
    The delivery notification helper
    """
    @classmethod
    def send_notification(cls, courier_id, message):
        """
        sending notification helper for deliver agents
        """
        token = FCMDevice.get_by_courier_id(courier_id).token
        notification_service.send_notification(title="Yangi buyurtma", body=message, token=token)  # TODO: title should be multi-language # noqa


delivery_notification_helper = DeliveryNotificationHelper()
