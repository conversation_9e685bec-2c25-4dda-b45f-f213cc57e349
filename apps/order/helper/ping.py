"""
the ping utility
"""
import requests


from apps.order.exception.create import WebHookListenerError


def ping(host) -> bool:
    """
    Send a ping request to the specified host and return True if the host is reachable, False otherwise.

    raises: WebHookListenerError if the host is not reachable
    """
    try:
        response = requests.get(f"{host}", timeout=10)

        if response.status_code == 200:
            return True
        raise WebHookListenerError(f"webhook is not available exc: {response}")

    except requests.exceptions.RequestException as exc:
        raise WebHookListenerError(f"Failed to ping {host} exc: {exc}") from exc
