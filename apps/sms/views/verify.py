"""
send verification
"""
from datetime import timedelta
from django.utils import timezone

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken

from apps.core import core
from apps.core import exceptions
from apps.user.models import Users
from apps.sms.serializers.otp import OTPSerializer
from apps.sms.models.verification import get_verification_code


class VerifyOTPView(APIView):
    """
    verify otp view for authentication
    """
    def post(self, request):
        """
        verify otp allows only POST requests
        """
        serializer = OTPSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data

        phone = data.get('phone')
        code = data.get('code')

        try:
            user = Users.get_by_phone(phone)
            otp = get_verification_code(user=user, code=code)

        except Users.DoesNotExist as exc:
            msg = f"user does not exist exc: {exc}"
            core.log(level='error', message=msg)

            raise exceptions.ServiceAPIException(
                error_type="user_not_found",
                message=f"user does not exist phone: {phone}",
                status_code=404
            )

        if otp and otp.created_at > timezone.now() - timedelta(minutes=2):
            otp.is_used = True
            user.is_verified = True
            otp.save()

            refresh = RefreshToken.for_user(user)

            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
            }, status=status.HTTP_200_OK)

        return Response({'detail': 'Invalid OTP'}, status=status.HTTP_400_BAD_REQUEST)
