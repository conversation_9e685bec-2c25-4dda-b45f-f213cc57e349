# Generated by Django 5.0.6 on 2024-09-27 12:00

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payment", "0004_alter_paymenttransaction_state"),
    ]

    operations = [
        migrations.AddField(
            model_name="paymenttransaction",
            name="cancel_reason",
            field=models.CharField(
                blank=True,
                choices=[
                    ("RecipientNotFound", "Recipientnotfound"),
                    ("DebitOperationFailed", "Debitoperationfailed"),
                    ("TransactionFailed", "Transactionfailed"),
                    ("TransactionTimeout", "Transactiontimeout"),
                    ("Refund", "Refund"),
                    ("UnknownError", "Unknownerror"),
                ],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name="paymenttransaction",
            name="state",
            field=models.CharField(
                choices=[
                    ("Created", "Created"),
                    ("Waiting", "Waiting"),
                    ("Processing", "Processing"),
                    ("Completed", "Completed"),
                    ("Refunded", "Refunded"),
                    ("Canceled", "Canceled"),
                    ("Expired", "Expired"),
                    ("Rejected", "Rejected"),
                    ("Pending", "Pending"),
                    ("Reversed", "Reversed"),
                    ("Reversed and Waiting", "Reversed and waiting"),
                ],
                default="Waiting",
            ),
        ),
    ]
