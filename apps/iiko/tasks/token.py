"""
implementation of state tasks
"""
from celery import Task

from master_kebab import celery_app

from apps.core import core
from apps.iiko.providers.iiko.http.client import client


@celery_app.task(bind=True, acks_late=False, task_acks_on_failure_or_timeout=False)
def update_token_task(self: Task):
    """
    print message
    """
    core.log("info", "updating iiko access token")
    client.variables.update_token()
