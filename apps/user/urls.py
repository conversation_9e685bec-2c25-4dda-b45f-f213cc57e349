"""
the users-app url patterns
"""
from django.urls import path, include

from rest_framework.routers import DefaultRouter

from apps.user.views.user import UserViewSet
from apps.user.views.token import LogoutView
from apps.user.views.token import GetTokenView
from apps.sms.views.send import RequestOTPView
from apps.sms.views.verify import VerifyOTPView
from apps.user.views.register import RegisterView
from apps.user.views.user import UserStatusAPIView, IsTelegramUserExists
from apps.user.views import orders as order_view
from apps.user.views import clients as clients_view
from apps.user.views.token import DeleteAccountView
from apps.user.views.stats import ClientStatisticsAPIView


router = DefaultRouter()
router.register(r'', UserViewSet)

urlpatterns = [
    path('logout/', LogoutView.as_view()),
    path('get/token/', GetTokenView.as_view()),
    path('register/', RegisterView.as_view()),
    path('verify-otp/', VerifyOTPView.as_view()),
    path("request-otp/", RequestOTPView.as_view()),
    path('status/', UserStatusAPIView.as_view()),
    path('check-user/', IsTelegramUserExists.as_view()),
    path('delete/', DeleteAccountView.as_view()),
    path('statistics/', ClientStatisticsAPIView.as_view(), name='client-statistics'),
]


urlpatterns.extend([
    path("active-orders/", order_view.ActiveOrdersAPIView.as_view()),
    path("order-history/", order_view.OrderHistoryListView.as_view()),
    path("order-history/<int:pk>", order_view.OrderHistoryDetailView.as_view()),
])


urlpatterns.extend([
    path('detail/', clients_view.ClientDetailView.as_view(), name='customer-update'),
])


urlpatterns.extend([
    path('', include(router.urls)),
])
