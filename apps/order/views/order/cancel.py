"""
init order cancel view for delivery agents
"""
import logging

from celery import current_app

from rest_framework import views
from rest_framework import response

from apps.order.models.order import Order
from apps.courier.models.courier import Courier
from apps.order.helper import order_status_helper
from apps.courier.models.orders import Missed<PERSON>rder
from apps.core.exceptions import ServiceAPIException
from apps.core.throttling import OneRequestPerFiveSecondsThrottle
from apps.order.tasks.find_courier import reassign_order_if_no_response_task


logger = logging.getLogger(__name__)


class OrderCancelAPIView(views.APIView):
    """
    the order accept api view for delivery agents
    """
    throttle_classes = [OneRequestPerFiveSecondsThrottle]

    def post(self, request, *args, **kwargs):
        """
        the post method for accepting an order.
        """
        order_id = kwargs.get("order_id")
        courier_id = request.user.id

        if not order_id or not courier_id:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Missing required parameters: order_id, courier_user",
                status_code=400
            )

        try:
            self.cancel_order_without_provider(order_id, courier_id)

        except Exception as exc:
            msg = f"cancel order failed exc: {exc}"
            raise ServiceAPIException(
                error_type="internal_server_error",
                message=msg,
                status_code=500
            ) from exc

        return response.Response(status=200)

    def cancel_order_without_provider(self, order_id, courier_id):
        courier = Courier.get_by_id(courier_id)
        MissedOrder.mark_as_missed_by_courier(
            courier_id=courier.id,
            order_id=order_id
        )
        order = Order.get_by_id(order_id=order_id)
        existing_task = order.courier_search_task_id

        if existing_task:
            # Revoke the existing task if it exists
            current_app.control.revoke(existing_task, terminate=True)

            logger.debug(f"Order cancalled by courier_id: {courier.id}")

            logger.debug(f"Started new courier finding order_id: {order_id}")
            reassign_order_if_no_response_task.delay(order.id)

    def cancel_order_with_provider(self, order_id):
        order = order_status_helper.get_order_by_id(order_id)

        order_status_helper.cancel_order_with_provider(
            order_ext_id=order.external_id
        )
