from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin
from apps.loyalty.models import LoyaltyProgram, Promo, PromoCodeUsage, LoyaltyProgramInitiator


class LoyaltyProgramInitiatorInline(admin.TabularInline):
    """Inline admin for LoyaltyProgramInitiator model"""
    model = LoyaltyProgramInitiator
    extra = 1


@admin.register(LoyaltyProgram)
class LoyaltyProgramAdmin(ModelAdmin):
    """Admin interface for LoyaltyProgram model"""
    list_display = ('name', 'service_from', 'service_to', 'is_active',
                    'program_type', 'refill_type', 'get_initiators', 'external_id')
    list_filter = ('is_active', 'program_type', 'refill_type')
    search_fields = ('name', 'external_id')
    inlines = [LoyaltyProgramInitiatorInline]

    def get_initiators(self, obj):
        """Get comma-separated list of initiators"""
        initiators = list(obj.initiators.values_list('initiator', flat=True))
        if initiators:
            return ", ".join(initiators)
        if obj.initiator:
            return f"{obj.initiator} (legacy)"
        return "All"

    get_initiators.short_description = "Initiators"


@admin.register(Promo)
class PromoAdmin(ModelAdmin):
    """Admin interface for Promo model"""
    list_display = ('name', 'program', 'promocode_type', 'promo_amount',
                    'min_order_amount', 'referal_id', 'created_at')
    list_filter = ('program', 'promocode_type')
    search_fields = ('name', 'referal_id')
    list_editable = ('promo_amount', 'min_order_amount')


@admin.register(PromoCodeUsage)
class PromoCodeUsageAdmin(ModelAdmin):
    """Admin interface for PromoCodeUsage model"""
    list_display = ('user', 'promo', 'program', 'is_used', 'order_id',
                    'discount_amount', 'order_total', 'created_at')
    list_filter = ('promo', 'program', 'is_used')
    search_fields = ('user__phone', 'promo__name', 'order_id')
