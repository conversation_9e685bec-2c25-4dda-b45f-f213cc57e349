"""
the enumeration of order status
"""
from enum import Enum


class OrderStatus(str, Enum):
    """
    The enumeration of order status.
    """
    CREATED = "Created"
    APPROVED = "Approved"
    CREATION_ERROR = "CreationError"
    ARRIVED = "Arrived"
    WEBHOOK_ERROR = "WebhookError"

    TIMEOUT = "Timeout"
    IN_PROGRESS = "InProgress"
    UNCONFIRMED = "Unconfirmed"
    WAIT_COOKING = "WaitCooking"
    COOKING_STARTED = "CookingStarted"
    CANCELLED = "Cancelled"
    COOKING_COMPLETED = "CookingCompleted"
    CONNECTION_ERROR = "ConnectionError"

    DELIVERED = "Delivered"
    DELIVERY_ORDER_ERROR = "DeliveryOrderError"
    ON_WAY = "OnWay"
    WAITING = "Waiting"
    CLOSED = "Closed"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]

    @classmethod
    def get_by_display_name(cls, display_name):
        """
        Returns the enum value for the given display name.
        """
        for member in cls:
            if member.value.replace('_', ' ').capitalize() == display_name:
                return member
        return None

    @classmethod
    def get_display_name(cls, enum_value):
        """
        Returns the display name for a given enum value.
        """
        display_names = {
            cls.CREATED: "Yangi",
            cls.APPROVED: "Tasdiqlandi",
            cls.CREATION_ERROR: "Xatolik",
            cls.ARRIVED: "Kuryer keldi",
            cls.WEBHOOK_ERROR: "Webhook xatolik",
            cls.TIMEOUT: "Muddati o'tdi",
            cls.IN_PROGRESS: "Jarayonda",
            cls.UNCONFIRMED: "Tasdiqlanmagan",
            cls.WAIT_COOKING: "Navbatda",
            cls.COOKING_STARTED: "Tayyorlanmoqda",
            cls.CANCELLED: "Bekor qilindi",
            cls.COOKING_COMPLETED: "Tayyor",
            cls.DELIVERED: "Yetkazildi",
            cls.DELIVERY_ORDER_ERROR: "Yetkazish xatolik",
            cls.ON_WAY: "Yo'lda",
            cls.WAITING: "Kutilmoqda",
            cls.CLOSED: "Bajarildi",
            cls.CONNECTION_ERROR: "Aloqada nosozlik"
        }
        return display_names.get(enum_value, "Unknown Status")


class CourierFindingStatus(str, Enum):
    """
    The enumeration of courier finding status.
    """
    NOT_STARTED = "NotStarted"
    IN_SEARCHING = "InSearching"
    SEARCHING_ERROR = "SearchingError"
    FOUND = "Found"
    NOT_FOUND = "NotFound"

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]

    def __str__(self):
        return str(self.value)
