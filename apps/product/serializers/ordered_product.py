"""
the ordered product serialization
"""
from rest_framework import serializers

from apps.product.models.orderd_product import OrderedProduct
from apps.product.models.orderd_product import OrderedProductAttribute
from apps.product.models.orderd_product import OrderedProductModifier
from apps.product.models.modifier import ModifierItems, ModiferProductPrices, Restrictions


class OrderedModifierRestrictionsSerializer(serializers.ModelSerializer):
    """
    Serializer for Restrictions model.
    """
    class Meta:
        """
        Meta class for RestrictionsSerializer.
        """
        model = Restrictions
        fields = [
            'free_quantity'
        ]


class OrderedModiferItemsSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderedModiferItems
    """
    prices = serializers.SerializerMethodField()
    restrictions = OrderedModifierRestrictionsSerializer(read_only=True)

    def get_prices(self, obj):
        """
        Custom method to serialize prices.
        """
        prices = ModiferProductPrices.objects.filter(modifer_items=obj).values_list('price', flat=True)
        return list(prices)

    class Meta:
        """
        Meta class for OrderedModiferItemsSerializer.
        """
        model = ModifierItems
        fields = [
            "name",
            "restrictions",
            "prices",
        ]


class OrderedProductAttributeSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderedProductAttribute
    """

    class Meta:
        """
        the meta fields
        """
        model = OrderedProductAttribute
        fields = [
            'id',
            'description',
            'price',
            'size',
            'external_id',
        ]


class OrderedProductModifierSerializer(serializers.ModelSerializer):
    """
    Serializer for OrderedProductModifier
    """
    modifier_item = OrderedModiferItemsSerializer(read_only=True)

    class Meta:
        """
        the meta fields
        """
        model = OrderedProductModifier
        fields = [
            'id',
            'modifier_item',
            'quantity',
        ]
        read_only_fields = ['modifier_item']


class OrderdProductSerializer(serializers.ModelSerializer):
    """
    product serializer for Product
    """
    attributes = serializers.SerializerMethodField()
    modifiers = serializers.SerializerMethodField()

    class Meta:
        """
        the meta fields
        """
        model = OrderedProduct
        fields = "__all__"

    def get_attributes(self, obj):
        """
        Return the list of ProductAttribute instances for the given Product.
        """
        attributes = OrderedProductAttribute.objects.filter(product=obj)
        serializer = OrderedProductAttributeSerializer(attributes, many=True)
        return serializer.data

    def get_modifiers(self, obj):
        """
        Return the list of OrderedProductModifier instances for the given OrderedProduct.
        """
        modifiers = OrderedProductModifier.objects.filter(ordered_product=obj)
        serializer = OrderedProductModifierSerializer(modifiers, many=True)
        return serializer.data
