"""
the organization serialization
"""
from rest_framework import serializers

from apps.organization.models import Organization


class OrganizationSerializer(serializers.ModelSerializer):
    """
    The organization serialization.
    """
    class Meta:
        """
        The meta fields.
        """
        model = Organization
        fields = "__all__"
        read_only_fields = [
            "external_id",
            "code",
            "delivery_duration",
            "self_service_duration",
            "default_min_sum",
        ]

    def update(self, instance, validated_data):
        """
        Update and return an existing `Organization` instance, given the validated data.
        """
        for field, value in validated_data.items():
            setattr(instance, field, value)
        instance.save()
        return instance
