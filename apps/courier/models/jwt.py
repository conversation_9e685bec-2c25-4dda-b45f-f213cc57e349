"""
the jwt models
"""
from django.db import models

from apps.core.models.base import BaseModel
from apps.courier.models.courier import Courier


class JWTCourierTokenList(BaseModel):
    """
    The JWT courier token list model class.
    """
    class Meta:
        """
        the meta fields
        """
        verbose_name = "JWT Courier Token List"
        verbose_name_plural = "JWT Courier Token Lists"
        db_table = "jwt_courier_token_list"
        ordering = ["-created_at"]

    token = models.CharField(max_length=500, verbose_name="Token")
    courier = models.OneToOneField(Courier, on_delete=models.CASCADE)

    @classmethod
    def to_list(cls, token, courier_id):
        """
        Convert token to list format.
        """
        return JWTCourierTokenList.objects.update_or_create(
            courier_id=courier_id,
            defaults={
                'token': token,
            }
        )

    @classmethod
    def get_last_token(cls, courier_id):
        """
        Get last token by courier id.
        """
        return cls.objects.filter(courier_id=courier_id).last()


class JWTCourierTokenBlockList(BaseModel):
    """
    The JWT courier token block list model class.
    """
    class Meta:
        """
        the meta fields
        """
        verbose_name = "JWT Courier Token Block List"
        verbose_name_plural = "JWT Courier Token Block Lists"
        db_table = "jwt_courier_token_blacklist"
        ordering = ["-created_at"]
        unique_together = ["token"]

    token = models.CharField(max_length=500, verbose_name="Token")

    @classmethod
    def to_block_list(cls, token):
        """
        Convert token to block list format.
        """
        return JWTCourierTokenBlockList.objects.get_or_create(
            token=token,
        )

    @classmethod
    def is_blocked_token(cls, token):
        """
        Check if token is blocked.
        """
        return cls.objects.filter(token=token).exists()
