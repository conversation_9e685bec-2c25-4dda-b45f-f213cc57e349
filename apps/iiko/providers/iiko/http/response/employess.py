"""
the employees response
"""
from uuid import UUID
from typing import List, Optional

from pydantic import BaseModel

from apps.iiko.providers.iiko.http import exception as iiko_exception


class EmployeeItem(BaseModel):
    """
    the employee item model
    """
    id: UUID
    code: str
    isDeleted: bool
    displayName: str
    firstName: Optional[str] = None
    middleName: Optional[str] = None
    lastName: Optional[str] = None


class Employee(BaseModel):
    """
    the employee model
    """
    organizationId: UUID
    items: List[EmployeeItem]


class EmployeeResponseBody(BaseModel):
    """
    the employees response model
    """
    correlationId: UUID
    employees: List[Employee]


class EmployeeInfo(BaseModel):
    """
    employees info model
    """
    id: str
    firstName: Optional[str] = None
    middleName: Optional[str] = None
    lastName: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    cellPhone: Optional[str] = None


class EmployeeInfoResponseBody(BaseModel):
    """
    employees info response model
    """
    correlationId: str
    employeeInfo: EmployeeInfo


class CheckRoleResult(BaseModel):
    """
    the check role result model
    """
    checkedRoleCode: str
    employeeHasRole: bool


class EmployeeByRoleItem(BaseModel):
    """
    the employee item model
    """
    id: str
    firstName: Optional[str]
    middleName: Optional[str]
    lastName: Optional[str]
    displayName: str
    code: str
    isDeleted: bool
    checkRolesResult: List[CheckRoleResult]


class EmployeesWithCheckRolesResponse(BaseModel):
    """
    the employees with check roles response model
    """
    correlationId: str
    employeesWithCheckRoles: List[dict]

    class Config:
        """
        settings for model
        """
        populate_by_name = True


class EmployeesShiftIsOpenResponseBody(BaseModel):
    """
    the employees shift is open response model
    """
    correlationId: str
    isSessionOpened: bool
    error: str

    def handle_error_response(self):
        if self.errorDescription or (self.error and self.error != "NO_ERROR"):
            raise iiko_exception.ShiftClockInErrorResponse(self)


class EmployeesShiftClockOutResponseBody(BaseModel):
    """
    the employees shift clock out response model
    """
    correlationId: str
    error: str

    def handle_error_response(self):
        if (self.error and self.error != "NO_ERROR"):
            raise iiko_exception.ShiftClockInErrorResponse(self)


class IikoErrorResponse(BaseModel):
    """
    IIKO emplooyess ERROR response
    """
    correlationId: str
    errorDescription: str

    def is_deleted(self) -> bool:
        """
        checking if error is deleted
        """
        return self.errorDescription == "Exception of type 'iikoTransport.UocService.Contracts.Transport.Exceptions.UserNotFoundException' was thrown." # noqa
