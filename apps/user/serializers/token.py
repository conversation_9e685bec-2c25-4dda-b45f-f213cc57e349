"""
customizing token claims
"""
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer


# pylint: disable=W0223
class GetTokenSerializer(serializers.Serializer):
    """
    the get token serializer
    """
    phone = serializers.CharField()
    password = serializers.CharField()


# pylint: disable=W0223
class ObtainPairSerializer(TokenObtainPairSerializer):
    """
    the custom optain serializer.
    """
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token['phone'] = user.phone

        return token
