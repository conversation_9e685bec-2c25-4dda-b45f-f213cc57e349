"""
Yandex Geosuggestion Service Module

This module provides the YandexGeosuggestionService class for making
requests to the Yandex Geosuggestion API. It handles API key management,
usage tracking, and response formatting.
"""

import logging
import requests
from typing import Dict, List, Optional, Tuple
from django.conf import settings

from apps.map.models.api_keys import MapApiKey


logger = logging.getLogger(__name__)


class YandexGeosuggestionService:
    """
    Service class for interacting with Yandex Geosuggestion API.
    
    This service handles:
    - API key management and rotation
    - Usage tracking and daily limits
    - HTTP requests to Yandex Geosuggestion API
    - Response parsing and formatting
    """
    
    BASE_URL = "https://suggest-maps.yandex.ru/v1/suggest"
    DEFAULT_RESULTS = 5
    DEFAULT_COORDINATES = "65.38055,40.09075"  # Navoi coordinates as default
    
    def __init__(self):
        """Initialize the Yandex Geosuggestion service."""
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'MasterKebab/1.0',
            'Accept': 'application/json',
        })
    
    def get_suggestions(self, text: str, coordinates: Optional[str] = None, 
                       results: int = DEFAULT_RESULTS) -> Dict:
        """
        Get location suggestions from Yandex Geosuggestion API.
        
        Args:
            text (str): Search text for location suggestions.
            coordinates (str, optional): Coordinates in format "longitude,latitude".
            results (int): Number of results to return (default: 5).
            
        Returns:
            Dict: Formatted response with suggestions and metadata.
            
        Raises:
            ValueError: If no API key is available or daily limit exceeded.
            requests.RequestException: If API request fails.
        """
        # Get API key and track usage
        try:
            api_key, remaining_requests = MapApiKey.use_yandex_geosuggestion_key()
            logger.info(f"Using Yandex Geosuggestion API key (Owner: {api_key.owner or 'Unknown'}, "
                       f"Remaining: {remaining_requests})")
        except ValueError as e:
            logger.error(f"Failed to get Yandex Geosuggestion API key: {e}")
            raise
        
        # Prepare request parameters
        params = {
            'text': text,
            'apikey': api_key.api_key,
            'results': min(results, 10),  # Limit to max 10 results
        }
        
        # Add coordinates if provided, otherwise use default
        if coordinates:
            params['ll'] = coordinates
        else:
            params['ll'] = self.DEFAULT_COORDINATES
        
        try:
            logger.info(f"Making Yandex Geosuggestion request for text: {text}")
            response = self._make_request(params)
            
            # Format and return response
            formatted_response = self._format_response(response, remaining_requests)
            
            logger.info(f"Successfully retrieved {len(formatted_response.get('suggestions', []))} suggestions")
            return formatted_response
            
        except requests.RequestException as e:
            logger.error(f"Yandex Geosuggestion API request failed: {e}")
            raise
    
    def _make_request(self, params: Dict) -> Dict:
        """
        Make HTTP request to Yandex Geosuggestion API.
        
        Args:
            params (Dict): Request parameters.
            
        Returns:
            Dict: Raw API response.
            
        Raises:
            requests.RequestException: If request fails.
        """
        response = self.session.get(self.BASE_URL, params=params, timeout=10)
        response.raise_for_status()
        return response.json()
    
    def _format_response(self, raw_response: Dict, remaining_requests: int) -> Dict:
        """
        Format the raw Yandex API response into a standardized format.
        
        Args:
            raw_response (Dict): Raw response from Yandex API.
            remaining_requests (int): Number of remaining API requests for today.
            
        Returns:
            Dict: Formatted response.
        """
        suggestions = []
        
        # Extract suggestions from response
        results = raw_response.get('results', [])
        for result in results:
            suggestion = {
                'title': result.get('title', {}).get('text', ''),
                'subtitle': result.get('subtitle', {}).get('text', ''),
                'uri': result.get('uri', ''),
                'distance': result.get('distance', {}).get('text', ''),
                'tags': result.get('tags', []),
            }
            
            # Extract coordinates if available
            if 'pos' in result:
                coords = result['pos'].split(' ')
                if len(coords) == 2:
                    suggestion['coordinates'] = {
                        'longitude': float(coords[0]),
                        'latitude': float(coords[1])
                    }
            
            suggestions.append(suggestion)
        
        return {
            'suggestions': suggestions,
            'total_results': len(suggestions),
            'remaining_requests': remaining_requests,
            'status': 'success'
        }
    
    @classmethod
    def check_api_availability(cls) -> Tuple[bool, str]:
        """
        Check if Yandex Geosuggestion API is available.
        
        Returns:
            Tuple[bool, str]: (availability status, message)
        """
        try:
            api_key = MapApiKey.get_yandex_geosuggestion_key()
            if not api_key:
                return False, "No available Yandex Geosuggestion API keys"
            
            from apps.map.models.api_usage import ApiUsage
            remaining = ApiUsage.get_remaining_requests(api_key)
            
            if remaining <= 0:
                return False, "Daily limit exceeded for all API keys"
            
            return True, f"API available with {remaining} remaining requests"
            
        except Exception as e:
            logger.error(f"Error checking API availability: {e}")
            return False, f"Error checking API availability: {str(e)}"
