from rest_framework.views import APIView
from rest_framework.response import Response

from django.db.models import Count, Q

from apps.courier.models.shift import CourierShifts


class CourierHeatmapView(APIView):
    """
    API view to provide data for the courier heatmap.
    """

    def get(self, request, *args, **kwargs):
        organization_id = request.query_params.get('organization_id')

        # Filter shifts by active status and optionally by organization
        shifts_query = CourierShifts.objects.filter(is_active=True)
        if organization_id:
            shifts_query = shifts_query.filter(organization_id=organization_id)

        couriers_data = shifts_query.select_related(
            'courier',
            'courier__courierpoint',
            'organization'
        ).annotate(
            completed_orders=Count('orders', filter=Q(orders__status='COMPLETED')),
            active_orders=Count('orders', filter=Q(orders__status='IN_PROGRESS')),
        ).values(
            'courier__id',
            'courier__name',
            'courier__phone',
            'courier__passport',
            'courier__address',
            'courier__status',
            'courier__external_id',
            'courier__courierpoint__latitude',
            'courier__courierpoint__longitude',
            'organization__name',
            'start_time',
            'completed_orders',
            'active_orders',
            'is_active'
        )

        response_data = []
        for courier in couriers_data:
            response_data.append({
                'id': courier['courier__id'],
                'name': courier['courier__name'],
                'phone': courier['courier__phone'],
                'passport': courier['courier__passport'],
                'address': courier['courier__address'],
                'status': courier['courier__status'],
                'external_id': courier['courier__external_id'],
                'location': {
                    'latitude': courier['courier__courierpoint__latitude'],
                    'longitude': courier['courier__courierpoint__longitude']
                },
                'organization': courier['organization__name'],
                'shift_start_time': courier['start_time'],
                'completed_orders': courier['completed_orders'],
                'active_orders': courier['active_orders'],
                'courier_status': 'Active' if courier['is_active'] else 'Inactive'
            })

        return Response(response_data)
