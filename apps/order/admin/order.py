"""
the product admin page
"""
from django.contrib import admin

from apps.order.models.order import Order
from apps.order.models.order_item import OrderItem
from apps.order.models.order_detail import OrderDetail
from apps.core.admin.modeladmin import ModelAdmin, StackedInline, TabularInline


class OrderDetailInline(StackedInline):
    """
    The admin page of order detail Inline
    """
    model = OrderDetail
    extra = 1

    fieldsets = (
        (None, {
            'fields': (
                'name', 'phone', 'delivery_type', 'delivery_address',
                'subaddress', 'entrance', 'door_code', 'floor', 'comment', 'promo_code',
                'payment_method', 'total_cost', 'delivery_cost', 'distance', 'delivery_cost_id',
                'latitude', 'longitude', 'delivery_duration', 'house', 'complete_before', 'street_id',
                'payment_phone'
            )
        }),
    )

    readonly_fields = ('total_cost', 'delivery_cost')


class SentCouriersInline(TabularInline):
    """
    sent to couriers information
    """
    model = Order.sent_to_couriers.through
    extra = 0

    readonly_fields = ('courier',)

    def courier(self, obj):
        return obj.courier.name

    verbose_name = 'Sent Courier'
    verbose_name_plural = 'Sent Couriers'


class OrderUI(ModelAdmin):
    """
    Admin page focused on managing and viewing sent couriers in the Order model.
    """
    inlines = [OrderDetailInline]

    list_display = (
        'id',
        'user',
        'status',
        'initiator',
        'operator',
        'delivery_agent',
        'organization',
        'created_at',
        'updated_at'
    )

    list_editable = ('status',)
    readonly_fields = (
        'log',
        'sent_to_couriers',
        'created_at',
        'updated_at',
    )

    list_per_page = 10
    search_fields = ('id', 'user__id', 'status')
    list_display_links = ('id',)
    list_filter = ('status', 'initiator', 'created_at')

    fieldsets = (
        (None, {
            'fields': (
                'user',
                'status',
                'operator',
                'delivery_agent',
                'organization',
                'initiator',
                'is_paid',
                'log'
            )
        }),
        ('Sent Couriers', {
            'fields': ('sent_to_couriers',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at')
        }),
    )

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.select_related('user')
        queryset = queryset.prefetch_related('sent_to_couriers')
        return queryset

    def clear_sent_couriers(self, request, queryset):
        """
        Custom action to clear sent couriers for selected orders.
        """
        queryset.update(sent_to_couriers=None)
    clear_sent_couriers.short_description = "Clear sent couriers"

    actions = [clear_sent_couriers]


class OrderItemUI(ModelAdmin):
    """
    the order item admin page
    """


class OrderOrderDetailUI(ModelAdmin):
    """
    the order item admin page
    """


admin.site.register(Order, OrderUI)
admin.site.register(OrderItem, OrderItemUI)
admin.site.register(OrderDetail, OrderOrderDetailUI)
