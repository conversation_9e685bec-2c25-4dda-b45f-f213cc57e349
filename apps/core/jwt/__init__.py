"""
jwt customization
"""
from django.contrib.auth import get_user_model

from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication

from rest_framework.exceptions import AuthenticationFailed

from apps.user.enum.role import UserRoleEnums
from apps.courier.models.courier import Courier
from apps.operations.models.operations import Operations
from apps.courier.models.jwt import JWTCourierTokenBlockList

from master_kebab.settings import IIKO_WEBHOOK_AUTH_TOKEN


User = get_user_model()


class CustomJWTAuthentication(JWTAuthentication):
    """
    Custom JWT authentication class
    """
    def get_raw_token(self, request):  # pylint: disable=W0237
        """
        xtract the token from the Authorization header, if present
        """
        auth = request.headers.get('Authorization', '')

        if not auth.startswith('Bearer '):
            return None
        return auth.split(' ')[1]

    def authenticate(self, request):
        """
        Get the token from the request
        """
        raw_token = self.get_raw_token(request)

        if raw_token is None:
            return None

        elif raw_token == IIKO_WEBHOOK_AUTH_TOKEN:
            return None

        try:
            # Decode the token
            validated_token = self.get_validated_token(raw_token)

            user = self.get_user(validated_token)
        except AuthenticationFailed as exc:
            raise AuthenticationFailed(f'Token contained no recognizable user identification exc: {exc}') from exc

        return (user, validated_token)

    def get_user(self, validated_token):
        """
        get user model
        """
        # Extract user ID or phone number from the token
        phone = validated_token.get('phone')
        role = validated_token.get('role')

        # Try to fetch the user from both models
        if role == UserRoleEnums.COURIER.value:
            is_blocked_token = JWTCourierTokenBlockList.is_blocked_token(
                token=validated_token
            )
            if is_blocked_token:
                raise AuthenticationFailed('Token is blocked')

            try:
                user = Courier.get_by_phone(phone=phone)

            except Courier.DoesNotExist as exc:
                raise AuthenticationFailed('Courier not found') from exc

        elif role in [
            UserRoleEnums.ADMIN.value,
            UserRoleEnums.OPERATOR.value,
            UserRoleEnums.DISPATCHER.value,
            UserRoleEnums.MANAGERS.value,
            UserRoleEnums.MARKETOLOG.value,
        ]:
            try:
                user = Operations.get_by_phone(phone)
            except Operations.DoesNotExist as exc:
                raise AuthenticationFailed('User not found') from exc

        else:
            try:
                user = User.objects.get(id=validated_token.get("user_id"))
            except User.DoesNotExist as exc:
                raise AuthenticationFailed('Courier not found') from exc

        return user
