"""
The user serialization
"""
from rest_framework import serializers
from apps.user.models import Users


class UserSerializer(serializers.ModelSerializer):
    """
    The user serialization
    """

    class Meta:
        """
        The meta fields
        """
        model = Users
        fields = [
            'id',
            'name',
            'email',
            'phone',
            'gender',
            'date_of_birth',
            'role',
            'lang',
            'is_active',
            'is_verified',
            'is_staff'
        ]
        extra_kwargs = {
            'role': {'required': False},
            'phone': {'required': False},
            'is_active': {'read_only': True},
            'is_verified': {'read_only': True},
            'is_staff': {'read_only': True},
            'name': {'required': False}
        }

    def validate_name(self, name=None):
        """
        Validate the name.
        - It should not exceed 50 characters in length.
        """
        if not name:
            name = "Master Kebab Client"

        if len(name) > 50:
            raise serializers.ValidationError("Name must not exceed 50 characters in length.")

        return name

    def validate_phone(self, value):
        """
        Validate the phone number.
        - It should not start with a `+`.
        - It should not exceed 16 characters in length.
        - The first three digits should be `998`.
        """
        if value.startswith('+'):
            raise serializers.ValidationError("Phone number should not start with '+'.")

        if len(value) > 16:
            raise serializers.ValidationError("Phone number must not exceed 16 characters in length.")

        if not value.startswith('998'):
            raise serializers.ValidationError("Phone number must start with '998'.")

        return value

    def create(self, validated_data):
        """
        Create and return a new `Users` instance, given the validated data.
        """
        return Users.objects.create(**validated_data)

    def update(self, instance, validated_data):
        """
        Update and return an existing `Users` instance, given the validated data.
        """
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class UserSerializerForOrderHistoryList(UserSerializer):
    """
    The user serialization for order history list
    """
    class Meta(UserSerializer.Meta):
        """
        The meta fields for order history list
        """
        fields = ('phone', )
