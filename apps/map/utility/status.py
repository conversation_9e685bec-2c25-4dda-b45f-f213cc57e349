import math


LAT_NAVOI = 40.1104
LON_NAVOI = 65.355
LAT_BUKHARA = 39.7748
LON_BUKHARA = 64.4286


def is_within_circle(lat_point, lon_point):
    """
    Check if a given point (lat_point, lon_point) is within 35 km of Navoi or Bukhara.
    Uses static variables for the coordinates of Navoi and Bukhara.

    :param lat_point: Latitude of the point to check.
    :param lon_point: Longitude of the point to check.
    :return: True if the point is within 35 km of either Navoi or Bukhara, False otherwise.
    """
    def haversine(lat1, lon1, lat2, lon2):
        """
        Helper function to calculate the great-circle distance between two points
        on the Earth's surface using the Haversine formula.
        """
        # Convert degrees to radians
        lat1_rad = math.radians(lat1)
        lon1_rad = math.radians(lon1)
        lat2_rad = math.radians(lat2)
        lon2_rad = math.radians(lon2)

        # Haversine formula
        dlon = lon2_rad - lon1_rad
        dlat = lat2_rad - lat1_rad
        a = math.sin(dlat / 2)**2 + math.cos(lat1_rad) * math.cos(lat2_rad) * math.sin(dlon / 2)**2
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a))
        return 6371 * c

    within_navoi = haversine(lat_point, lon_point, LAT_NAVOI, LON_NAVOI) <= 35

    within_bukhara = haversine(lat_point, lon_point, LAT_BUKHARA, LON_BUKHARA) <= 35

    return within_navoi or within_bukhara
