"""
URL patterns for core app endpoints.

This module defines URL patterns for core functionality such as:
- Health check endpoint
- Remote configuration endpoint
- Log file access endpoint
"""
from django.urls import path

from apps.core.views.log import LogFileAPIView
from apps.core import views

urlpatterns = [
    path("ping/", views.HealthCheckAPIView.as_view()),
    path("remote-config/", views.RemoteConfigAPIView.as_view()),
    path("logs/", LogFileAPIView.as_view(), name="log-file"),
]
