# Generated by Django 5.0.6 on 2024-09-01 18:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('map', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='MapApiKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('api_key', models.CharField(max_length=255, unique=True)),
                ('app_name', models.CharField(default='yandex_map', max_length=255)),
                ('in_use', models.BooleanField(default=False)),
                ('limit_expired', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Map API Key',
                'verbose_name_plural': 'map_api_keys',
                'db_table': 'map_api_keys',
            },
        ),
    ]
