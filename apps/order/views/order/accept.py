"""
init order accept view for delivery agents
"""
from django.db import transaction

from rest_framework import views
from rest_framework import response

from apps.core.views import ServiceBaseView
from apps.courier.models.courier import Courier
from apps.order.helper import order_status_helper
from apps.core.exceptions import ServiceAPIException
from apps.courier.models.orders import Missed<PERSON>rder
from apps.courier.models.shift import CourierShifts
from apps.core.throttling import OneRequestPerFiveSecondsThrottle


class OrderAcceptAPIView(views.APIView, ServiceBaseView):
    """
    the order accept api view for delivery agents
    """
    throttle_classes = [OneRequestPerFiveSecondsThrottle]

    def post(self, request, *args, **kwargs):
        """
        the post method for accepting an order.
        """
        order_id = kwargs.get("order_id")
        courier_id = request.user.id

        if not order_id or not courier_id:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Missing required parameters: order_id, courier_id",
                status_code=400,
                notify_admin=True,
            )

        try:
            self.process(order_id, courier_id)
        except Exception as exc:
            msg = f"accept order_id: {order_id} courier_id: {courier_id} failed exc: {exc}"
            raise self.call_service_exception(
                error_type="internal_server_error",
                message=msg,
                status_code=500,
                notify_admin=True,
            )

        return response.Response()

    def process(self, order_id, courier_id):
        courier_shift = CourierShifts.get_active_shift(courier_id)
        courier = Courier.get_by_id(courier_id)

        if not courier_shift:
            courier.mark_as_not_is_shift_active()
            raise ServiceAPIException(
                error_type="bad_request",
                message="The courier is not in a shift",
                status_code=400
            )

        order = order_status_helper.get_order_by_id(order_id)

        if MissedOrder.has_courier_missed_order(
            courier_id=courier.id,
            order_id=order_id
        ):
            raise ServiceAPIException(
                error_type="bad_request",
                message="The courier is already marked as missed this order",
                status_code=400
            )

        with transaction.atomic():
            response = order_status_helper.bind_delivery_agent_with_provider(
                order_ext_id=order.external_id,
                delivery_agent_ext_id=courier.external_id
            )
            courier.mark_as_in_order()
            message = f"Request was send to binding courier to iiko and response: {response}"
            self.logger.info(message)
