"""
Initialize MinIO client
"""
import os
import uuid

from minio import <PERSON>o
from apps.core import core
from master_kebab import settings


minio_client = Minio(
    endpoint="s3.storage.servisoft.uz",
    access_key="root",
    secret_key="oqzLPsk1bITzhVFeE1EbEA8",
    secure=True
)

# TODO: Change to settings.MINIO_ENDPOINT, settings.MINIO_ACCESS_KEY, settings.MINIO_SECRET_KEY
# endpoint=settings.MINIO_ENDPOINT.replace("https://", ""),
# access_key=settings.MINIO_ACCESS_KEY,
# secret_key=settings.MINIO_SECRET_KEY,
# secure=True

MINIO_BUCKET_NAME = "backupdatabase"
FOLDER = "master_kebab/production"

if settings.DEBUG:
    FOLDER = "master_kebab/test"


def upload_file_to_minio(file, pathname=FOLDER, filename=None):
    """
    Upload file to MinIO

    Args:
        file: File object to be uploaded
        pathname: Path in the bucket where the file will be stored
        filename: Optional; Name of the file in the bucket. If not provided, a UUID will be used.

    Returns:
        str: The object name of the uploaded file, or None if the upload failed.

    Example usage:
        with open('path/to/local/file.txt', 'rb') as file:
            upload_file_to_minio(file, 'path/in/bucket', 'file.txt')
    """
    filename = filename or str(uuid.uuid4())
    content_type = file.name.split(".")[-1] if '.' in file.name else 'application/octet-stream'
    object_name = f"{pathname}/{filename}"

    try:
        file.seek(0, os.SEEK_END)
        file_size = file.tell()
        file.seek(0)

        minio_client.put_object(
            bucket_name=MINIO_BUCKET_NAME,
            object_name=object_name,
            data=file,
            length=file_size,
            content_type=content_type,
        )
        return object_name
    except Exception as exc:
        error_message = f"MinIO upload error: {exc}"
        core.log("error", error_message)
        return exc
