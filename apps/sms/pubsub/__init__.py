"""
init pubsub module
"""
from faststream import FastStream
from faststream.redis import RedisBroker

from apps.sms import tasks
from apps.sms.pubsub import model
from master_kebab.settings import PUB_SUB_BROKER_URL as BROKER_URL

broker = RedisBroker(BROKER_URL)
app = FastStream(broker=broker)


@broker.subscriber("sms.send")
async def send_sms(data: model.SendSMSEventModel):
    """
    handler for send sms
    """
    print(f"sms service received: {data.message}")
    tasks.send_sms_message_task.delay(
        phone=data.phone_number, message=data.message
    )
