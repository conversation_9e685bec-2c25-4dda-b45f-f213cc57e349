"""
Softphone configuration serializers
"""
from rest_framework import serializers
from apps.softphone.models.softphone_config import SoftphoneConfig


class SoftphoneConfigSerializer(serializers.ModelSerializer):
    """
    Serializer for SoftphoneConfig model
    """
    
    class Meta:
        model = SoftphoneConfig
        fields = [
            'id',
            'name',
            'screen_popup_url',
            'customer_data_url',
            'call_log_url',
            'recording_upload_url',
            'default_sip_account',
            'enable_screen_popup',
            'enable_call_logging',
            'enable_recording_upload',
            'click_to_call_enabled',
            'caller_id_regex',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class SoftphoneConfigCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating SoftphoneConfig entries
    """
    
    class Meta:
        model = SoftphoneConfig
        fields = [
            'name',
            'screen_popup_url',
            'customer_data_url',
            'call_log_url',
            'recording_upload_url',
            'default_sip_account',
            'enable_screen_popup',
            'enable_call_logging',
            'enable_recording_upload',
            'click_to_call_enabled',
            'caller_id_regex',
            'is_active'
        ]
    
    def validate_name(self, value):
        """Ensure unique configuration name"""
        if SoftphoneConfig.objects.filter(name=value).exists():
            raise serializers.ValidationError("Configuration with this name already exists")
        return value
