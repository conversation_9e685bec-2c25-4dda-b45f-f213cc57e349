"""
the enumeration of event types
"""
from enum import Enum


class EventTypesEnum(str, Enum):
    """
    The enumeration of order types.
    """
    PERSONAL_SHIFT = "PersonalShift"
    DELIVERY_ORDER_ERROR = "DeliveryOrderError"
    DELIVERY_ORDER_UPDATE = "DeliveryOrderUpdate"

    KITCHEN_ORDER_UPDATE = "KitchenOrderUpdate"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]

    @classmethod
    def allowed_update_events(cls):
        """
        Returns a list of allowed update events.
        """
        return [
            cls.PERSONAL_SHIFT,
            cls.KITCHEN_ORDER_UPDATE,
            cls.DELIVERY_ORDER_ERROR.value,
            cls.DELIVERY_ORDER_UPDATE.value,
        ]
