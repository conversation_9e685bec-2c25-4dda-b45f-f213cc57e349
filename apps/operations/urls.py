"""
urls.py

This module registers API viewsets with the URL router to provide CRUD functionality for `Manager`, `Operator`, `Dispatcher`, and `SuperAdmin` roles. # noqa
"""
from django.urls import path, include

from rest_framework.routers import DefaultRouter
from apps.operations.views import login as login_view
from apps.operations.views import operations as operations_view


router = DefaultRouter()
router.register(r'managers', operations_view.ManagersViewSet, basename='manager')
router.register(r'operators', operations_view.OperatorsViewSet, basename='operator')
router.register(r'dispatchers', operations_view.DispatchersViewSet, basename='dispatcher')
router.register(r'marketologs', operations_view.MarketologViewSet, basename='marketologs')


urlpatterns = [
    path('dashboard/login/', login_view.TokenObtainPairView.as_view())
]

# Include router URLs
urlpatterns.extend([
    path('', include(router.urls)),  # noqa
])
