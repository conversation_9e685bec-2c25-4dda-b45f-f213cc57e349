"""
the order category
"""
from datetime import timedelta

import pytz

from django.db import models
from django.utils.timezone import now

from apps.loyalty.models import Promo
from apps.order.models.order import Order
from apps.core.models.base import BaseModel
from apps.order.enums.delivery import DeliveryType
from apps.order.enums.delivery import StreetEnum
from apps.payment.enums.method import PaymentMethodEnum


class OrderDetail(BaseModel):
    """
    The order detail model.
    """
    order = models.OneToOneField(Order, related_name='order_detail', on_delete=models.CASCADE, null=True)
    name = models.CharField(max_length=200, null=True, blank=True)
    phone = models.CharField(max_length=20, null=True, blank=True)
    delivery_type = models.CharField(
        max_length=50,
        choices=DeliveryType.choices(),
        default=DeliveryType.DELIVERY.value,
    )
    delivery_address = models.CharField(max_length=400,  null=True, blank=True)
    subaddress = models.CharField(max_length=255, null=True, blank=True)
    entrance = models.CharField(max_length=300, null=True, blank=True)
    door_code = models.CharField(max_length=255,  null=True, blank=True)
    floor = models.CharField(max_length=50, null=True, blank=True)
    comment = models.TextField()
    promo_code = models.CharField(max_length=50, null=True, blank=True)
    payment_method = models.CharField(
        max_length=50,
        choices=PaymentMethodEnum.choices(),
        default=PaymentMethodEnum.CASH.value,
    )
    promo = models.ForeignKey(
        Promo, on_delete=models.SET_NULL,
        null=True, blank=True, related_name='order_details'
    )
    total_cost = models.FloatField()
    delivery_cost = models.FloatField(default=0)
    distance = models.FloatField(default=0, null=True, blank=True)
    street_id = models.CharField(max_length=255, null=True, blank=True)
    delivery_cost_id = models.CharField(max_length=100, null=True, blank=True)
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    delivery_duration = models.CharField(null=True, blank=True)
    house = models.CharField(max_length=10, default="0", null=True, blank=True)
    payment_phone = models.CharField(null=True, blank=True)
    complete_before = models.CharField(null=True, blank=True)

    class Meta:
        """
        The meta fields.
        """
        db_table = 'order_detail'

    def __str__(self):
        # Retrieve the related Order instance
        order = getattr(self, 'order', None)
        order_id = order.id if order else 'No Order'
        return f"OrderDetail for Order ID: {order_id}, Name: {self.name}, Phone: {self.phone}"

    def set_complete_before(self, delivery_duration):
        """
        Set the complete_before field based on the delivery duration.
        """
        delivery_duration = delivery_duration + 10
        if delivery_duration:
            current_time_utc = now()
            # Define the Asia/Tashkent timezone
            tashkent_tz = pytz.timezone('Asia/Tashkent')
            future_time_utc = current_time_utc + timedelta(minutes=delivery_duration)
            future_time_tashkent = future_time_utc.astimezone(tashkent_tz)
            dt = future_time_tashkent.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
            self.complete_before = dt
            self.save()

    def is_self_call(self):
        """
        Check if the delivery type is self-call.
        """
        return self.delivery_type == DeliveryType.SELF_CALL


def init_order_detail(
    name=None,
    phone=None,
    delivery_type=DeliveryType.DELIVERY,
    delivery_address=None,
    entrance=None,
    door_code=None,
    floor=None,
    comment=None,
    promo_code=None,
    payment_method=PaymentMethodEnum.CASH,
    latitude=None,
    longitude=None,
    total_cost=0,
    delivery_duration=None,
    complete_before=None,
    subaddress=None,
    delivery_cost_id=None,
    street_id=StreetEnum.DEFAULT_STREET_ID.value,
    payment_phone=None,
    house=None,
    promo=None,
): # noqa
    """
    Initialize the order detail model.
    """
    order_detail = OrderDetail.objects.create(
        name=name,
        phone=phone,
        delivery_type=delivery_type,
        delivery_address=delivery_address,
        entrance=entrance,
        door_code=door_code,
        floor=floor,
        comment=comment,
        promo_code=promo_code,
        payment_method=payment_method,
        total_cost=total_cost,
        latitude=latitude,
        longitude=longitude,
        delivery_duration=delivery_duration,
        complete_before=complete_before,
        subaddress=subaddress,
        delivery_cost_id=delivery_cost_id,
        street_id=street_id,
        house=house,
        payment_phone=payment_phone,
        promo=promo,
    )
    return order_detail
