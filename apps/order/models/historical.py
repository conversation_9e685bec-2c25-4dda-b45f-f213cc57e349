"""
This module defines the HistoricalOrderModel, which represents a historical record
of an Order in the system. It captures the state of an Order at specific points in time,
allowing for tracking changes and historical analysis.

It inherits from the BaseModel, which may contain common fields and functionality
shared across various models in the application.

Usage:
    To query historical orders, use the class method `get_by_id` with the desired
    order ID as an argument.
"""

from django.db import models
from apps.core.models.base import BaseModel


class HistoricalOrderModel(BaseModel):
    """
    Historical representation of an Order.

    This model stores the historical status of an Order, providing insight into
    the changes that have occurred over time. The `managed` attribute is set to
    False to prevent Djan<PERSON> from attempting to create or modify the underlying
    database table, as it is expected to be managed externally or used in a
    read-only manner.

    Attributes:
        status (str): The status of the order at the time of this historical record.

    Meta:
        managed (bool): Indicates whether Django should manage this model's table.
        db_table (str): The name of the database table used for this model.
        ordering (list): Default ordering of records by `created_at` in descending order.

    Methods:
        get_by_id(order_id): Class method to retrieve historical orders by their ID.
    """
    status = models.CharField(max_length=50)

    class Meta:
        """
        Historical metadata for the HistoricalOrderModel.

        Attributes:
            managed (bool): If False, Django does not create, modify, or delete
            the database table for this model.
            db_table (str): The name of the database table for the historical orders.
            ordering (list): The default ordering for querysets of this model.
        """
        managed = False
        db_table = 'order_historicalorder'
        ordering = ['-created_at']

    def __str__(self):
        return f'Historical Order {self.id} - Status: {self.status}'

    @classmethod
    def get_by_id(cls, order_id):
        """
        Get historical orders by order ID.

        Args:
            order_id (int): The unique identifier for the order whose historical
            records are to be retrieved.

        Returns:
            QuerySet: A queryset containing historical orders associated with the
            specified order ID. If no records are found, the queryset will be empty.
        """
        return cls.objects.filter(id=order_id)
