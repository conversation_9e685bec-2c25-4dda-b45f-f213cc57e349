# Generated by Django 5.0.6 on 2025-05-13 23:53

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('loyalty', '0009_promo_is_organization_specific_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='loyaltyprogram',
            name='initiator',
            field=models.CharField(blank=True, choices=[('bot', 'Bot'), ('ios', 'iOS'), ('web', 'Web'), ('android', 'Android'), ('call-center', 'Call Center'), ('unknown', 'Unknown')], help_text='Legacy field - use LoyaltyProgramInitiator instead', max_length=20, null=True),
        ),
        migrations.CreateModel(
            name='LoyaltyProgramInitiator',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('initiator', models.Char<PERSON>ield(choices=[('bot', 'Bot'), ('ios', 'iOS'), ('web', 'Web'), ('android', 'Android'), ('call-center', 'Call Center'), ('unknown', 'Unknown')], help_text='Initiator allowed to use this program', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='initiators', to='loyalty.loyaltyprogram')),
            ],
            options={
                'verbose_name': 'Loyalty Program Initiator',
                'verbose_name_plural': 'Loyalty Program Initiators',
                'unique_together': {('program', 'initiator')},
            },
        ),
    ]
