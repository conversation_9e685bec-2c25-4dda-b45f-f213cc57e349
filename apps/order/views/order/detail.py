"""
the orders detail view
"""
from rest_framework import generics

from apps.order.models import Order
from apps.order.serializers.order import OrderSerializer
from apps.order.permissions import OrderHistoryPermissionClass


class OrderDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    The order detail, update, and delete view
    """
    queryset = Order.objects.all()
    serializer_class = OrderSerializer
    permission_classes = [OrderHistoryPermissionClass]
