"""
init category managers
"""
from django.db import models


class Category<PERSON>anager(models.Manager):
    """
    the category manager
    """
    def build_category(self, title, external_id):
        """
        Build category
        """
        return self.model(title=title, external_id=external_id)

    def get_all_categories(self):
        """
        Get all categories except those with the title 'delivery'
        """
        return self.exclude(title="delivery")

    def update_categories(self, categories):
        """
        Update categories
        """
        updated_categories = []

        if not categories:
            return updated_categories

        for category in categories:
            updated_category, _ = self.update_or_create(
                external_id=category.external_id,
                defaults={
                    'title': category.title,
                    'image_url': category.image_url,
                }
            )
            updated_categories.append(updated_category)

        return updated_categories
