# Generated by Django 5.0.6 on 2024-09-25 07:13

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("map", "0005_alter_mapapikey_owner"),
    ]

    operations = [
        migrations.AlterField(
            model_name="mapapikey",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="mapapikey",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="point",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name="point",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_index=True),
        ),
    ]
