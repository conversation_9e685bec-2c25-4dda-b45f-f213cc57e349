version: '3.11'

services:
  celery_beat:
    container_name: celery_beat_container
    network_mode: host

    build:
      context: .
    command: celery -A master_kebab beat -l INFO
    deploy:
      replicas: 1

  celery_worker:
    container_name: celery_worker_container
    network_mode: host

    build:
      context: .
    command: celery -A master_kebab worker -Q bot,sms,iiko,iiko_updates,order,user,delivery --loglevel=info --concurrency=10
    deploy:
      replicas: 1

  sms_event_listener:
    network_mode: host
    container_name: sms_event_listener

    build:
      context: .
    command: python3 manage.py run_sms_service
    deploy:
      replicas: 1

  web_socket:
    container_name: web_socket_container

    network_mode: host

    build:
      context: .

    command: daphne -p 8008 -b 0.0.0.0 master_kebab.asgi:application

    ports:
      - "8008:8008"

  master_kebab:
    container_name: master_kebab_container
  
    env_file:
      - .env

    network_mode: host

    build:
      context: .

    command: >
      sh -c "python3 manage.py migrate &&
             python3 manage.py createadmin &&
             gunicorn master_kebab.wsgi:application --bind 0.0.0.0:8005"
    volumes:
      - .:/app
    ports:
      - "8005:8005"
