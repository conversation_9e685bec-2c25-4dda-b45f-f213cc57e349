"""
Celery tasks for map application.

This module contains Celery tasks for map-related operations,
including daily API limit resets and usage monitoring.
"""

import logging
from celery import shared_task
from django.utils import timezone
from apps.map.models.api_keys import MapApiKey
from apps.map.models.api_usage import ApiUsage


logger = logging.getLogger(__name__)


@shared_task(bind=True, name='map.reset_daily_api_limits')
def reset_daily_api_limits(self):
    """
    Celery task to reset daily API limits for all map API keys and update usage records.

    This task should be scheduled to run daily (24.5 hours cycle) to:
    1. Reset the limit_expired flag for all API keys
    2. Update or create new daily usage records for today
    3. Clean up old usage records (older than 30 days)

    Returns:
        dict: Task execution results with statistics
    """
    try:
        current_time = timezone.now()
        today = current_time.date()
        logger.info(f"Starting daily API limit reset task at {current_time}")

        # Get statistics before reset
        total_keys = MapApiKey.objects.count()
        expired_keys_count = MapApiKey.objects.filter(limit_expired=True).count()

        # Reset the API key limits
        reset_count = MapApiKey.reset_daily_limits()

        # Update/create today's usage records for all API keys
        usage_records_created = 0
        usage_records_updated = 0

        for api_key in MapApiKey.objects.all():
            usage_record, created = ApiUsage.objects.get_or_create(
                api_key=api_key,
                date=today,
                defaults={
                    'usage_count': 0,
                    'daily_limit': 1000
                }
            )

            if created:
                usage_records_created += 1
            else:
                # Reset usage count for existing record (in case task runs multiple times)
                if usage_record.usage_count > 0:
                    usage_record.usage_count = 0
                    usage_record.save(update_fields=['usage_count'])
                    usage_records_updated += 1

        # Clean up old usage records (older than 30 days)
        cutoff_date = today - timezone.timedelta(days=30)
        old_records_deleted, _ = ApiUsage.objects.filter(date__lt=cutoff_date).delete()

        # Log the results
        logger.info(f"Daily API limit reset completed:")
        logger.info(f"  - Total API keys: {total_keys}")
        logger.info(f"  - Keys that were expired: {expired_keys_count}")
        logger.info(f"  - Keys reset: {reset_count}")
        logger.info(f"  - Usage records created: {usage_records_created}")
        logger.info(f"  - Usage records updated: {usage_records_updated}")
        logger.info(f"  - Old records deleted: {old_records_deleted}")

        # Get Yandex Geosuggestion specific statistics
        yandex_geo_keys = MapApiKey.objects.filter(
            app_name=MapApiKey.YANDEX_MAP,
            usage_type=MapApiKey.YANDEX_GEOSUGGESTION
        )
        yandex_geo_total = yandex_geo_keys.count()
        yandex_geo_active = yandex_geo_keys.filter(limit_expired=False).count()

        logger.info(f"  - Yandex Geosuggestion keys: {yandex_geo_active}/{yandex_geo_total} active")

        return {
            'status': 'success',
            'timestamp': current_time.isoformat(),
            'total_keys': total_keys,
            'expired_keys': expired_keys_count,
            'reset_count': reset_count,
            'usage_records_created': usage_records_created,
            'usage_records_updated': usage_records_updated,
            'old_records_deleted': old_records_deleted,
            'yandex_geosuggestion': {
                'total': yandex_geo_total,
                'active': yandex_geo_active
            }
        }

    except Exception as e:
        logger.error(f"Error in daily API limit reset task: {str(e)}")
        # Re-raise the exception so Celery can handle it properly
        raise self.retry(exc=e, countdown=300, max_retries=3)
