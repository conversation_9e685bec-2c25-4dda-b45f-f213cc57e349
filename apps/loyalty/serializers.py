from rest_framework import serializers
from apps.loyalty.models import PromoCodeUsage, Promo


class ApplyPromoSerializer(serializers.Serializer):
    """
    Serializer for applying promo code
    """
    promo_code = serializers.CharField(max_length=255)
    organization_id = serializers.CharField(max_length=255)
    order_amount = serializers.FloatField(required=False, default=0)
    items_cost = serializers.FloatField(required=False)


class TelegramApplyPromoSerializer(serializers.Serializer):
    """
    Serializer for applying promo code via Telegram
    """
    chat_id = serializers.IntegerField(required=True)
    promo_code = serializers.CharField(max_length=255)
    organization_id = serializers.CharField(max_length=255)
    order_amount = serializers.FloatField(required=False, default=0)
    items_cost = serializers.FloatField(required=False)


class PromoCodeUsageSerializer(serializers.ModelSerializer):
    """
    Serializer for PromoCodeUsage model
    """
    promo_name = serializers.SerializerMethodField()
    program_name = serializers.SerializerMethodField()

    class Meta:
        model = PromoCodeUsage
        fields = ('id', 'promo', 'program', 'is_used', 'order_id',
                  'discount_amount', 'created_at', 'promo_name', 'program_name')

    def get_promo_name(self, obj):
        """Get the name of the promo code"""
        return obj.promo.name if obj.promo else None

    def get_program_name(self, obj):
        """Get the name of the loyalty program"""
        return obj.program.name if obj.program else None


class PromoSerializer(serializers.ModelSerializer):
    """
    Serializer for Promo model
    """
    program_name = serializers.SerializerMethodField()

    class Meta:
        """Meta options for PromoSerializer"""
        model = Promo
        fields = ('id', 'name', 'program', 'program_name', 'promo_amount',
                  'promocode_type', 'created_at')

    def get_program_name(self, obj):
        """Get the name of the loyalty program"""
        return obj.program.name if obj.program else None
