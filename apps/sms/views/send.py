"""
send verification
"""
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.user.models import Users
from apps.core.views import ServiceBaseView
from apps.core.enums.initiator import Initiator
from apps.sms.serializers.otp import OTPSerializer
from apps.sms.tasks.send import send_sms_message_task
from apps.sms.models.verification import VerificationCodes
from apps.core.throttling import OTPRequestThrottle


class RequestOTPView(APIView, ServiceBaseView):
    """
    request otp view for authentication
    """

    throttle_classes = [OTPRequestThrottle]

    def get(self, request):
        """
        getting status of sms
        """
        response_data = {
            "message": "No query parameter provided"
        }

        # Accessing query parameters
        phone = request.query_params.get('phone', None)

        if phone:
            response_data = {
                "status": "success",
            }

        return Response(response_data)

    def post(self, request):
        """
        Handles OTP requests
        """
        serializer = OTPSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        phone = serializer.validated_data.get('phone', None)
        user = Users.get_or_create_by_phone(phone)

        # Retrieve user's preferred language, defaulting to 'uz' if none is provided
        user_lang = request.headers.get('Accept-Language', 'uz').split(',')[0]

        # Retrieve the X-Initiator header to identify the source
        initiator = request.headers.get('X-Initiator', None)
        initiator = initiator.lower() if initiator else None

        # Validate the initiator value
        if initiator not in [Initiator.ANDROID, Initiator.IOS, Initiator.WEB]:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Invalid X-Initiator for sending OTP code",
                status_code=400,
                notify_admin=True
            )

        # Generate an OTP for the user
        otp = VerificationCodes.objects.create(user=user)

        # Define messages for each initiator type and language
        messages = {
            Initiator.ANDROID: {
                "uz": "Masterkebab mobil ilovasiga kirish uchun tasdiqlash kodi: {otp_code}",
                "ru": "Код верификации для входа к мобильному приложению Masterkebab: {otp_code}"
            },
            Initiator.IOS: {
                "uz": "Masterkebab mobil ilovasiga kirish uchun tasdiqlash kodi: {otp_code}",
                "ru": "Код верификации для входа к мобильному приложению Masterkebab: {otp_code}"
            },
            Initiator.WEB: {
                "uz": "master-kebab.uz saytida roʻyxatdan oʻtish uchun tasdiqlash kodi: {otp_code}",
                "ru": "Код подтверждения для регистрации на сайте master-kebab.uz: {otp_code}",
            }
        }

        message_template = messages[initiator].get(user_lang, messages[initiator]['uz'])

        message = message_template.format(otp_code=otp.code)

        send_sms_message_task.delay(phone, message)

        return Response({'detail': 'OTP sent'}, status=status.HTTP_200_OK)
