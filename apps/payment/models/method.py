"""
the payment methods
"""
from django.db import models

from apps.core.models.base import BaseModel
from apps.core.decorators import cached_object
from apps.organization.models import Organization


class PaymentMethod(BaseModel):
    """
    the payment method model
    """
    class Meta:
        """
        the meta fields
        """
        db_table = 'payment_method'
        unique_together = (
            ('organization', 'name'),
        )

    name = models.CharField(max_length=50)
    display_name = models.CharField(max_length=50, null=True)
    is_enabled = models.BooleanField(default=False)
    external_id = models.CharField(max_length=255, null=True)
    payment_type_kind = models.Char<PERSON>ield(max_length=10, null=True)
    is_processed_externally = models.BooleanField(default=False)
    code = models.Char<PERSON>ield(max_length=10, null=True)
    is_enabled_for_dashboard = models.BooleanField(default=False)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True)

    def __str__(self) -> str:
        """
        returns a string representation of the payment method
        """
        return str(self.name)

    @classmethod
    def update_or_create(
        cls,
        organization_id,
        name,
        external_id,
        payment_type_kind,
        code,
    ):
        """
        Class method to get or create a PaymentMethod instance. If the instance already exists,
        it updates the defaults (is_enabled, payment_type_kind, external_id, code) with new values.

        :param organization_id: The ID of the organization associated with the payment method.
        :type organization_id: int
        :param name: The name of the payment method.
        :type name: str
        :param external_id: An external identifier for the payment method.
        :type external_id: str
        :param payment_type_kind: The type of payment method (e.g., 'credit card', 'bank transfer').
        :type payment_type_kind: str
        :param code: The code representing the payment method.
        :type code: str
        :param is_enabled: A boolean flag indicating whether the payment method is enabled.
        :return: A tuple containing the PaymentMethod instance and a boolean indicating if it was created.
        :rtype: tuple (PaymentMethod, bool)
        """
        defaults = {
            "payment_type_kind": payment_type_kind,
            "code": code,
            "external_id": external_id
        }

        payment_method, created = cls.objects.update_or_create(
            organization_id=organization_id,
            name=name,
            defaults=defaults
        )

        return payment_method, created

    @classmethod
    def get_by_name(cls, name):
        """
        Class method to get a PaymentMethod instance by its name
        :param name: The name of the payment method
        :return: The PaymentMethod instance or None if not found
        """
        return cls.objects.filter(name=name).first()

    @classmethod
    # @cached_object(
    #     cache_key_prefix="payment-method-get-by-code",
    #     timeout=60
    # ) TODO FIX
    def get_by_code(cls, code, organization_id=None):
        """
        Class method to get a PaymentMethod instance by its code
        :param code: The code of the payment method
        :return: The PaymentMethod instance or None if not found
        """
        return cls.objects.get(code=code, organization_id=organization_id)

    @classmethod
    def delete_by_code(cls, organization_id, code):
        """
        Delete a payment method by organization_id and code
        """
        return cls.objects.filter(organization_id=organization_id, code=code).delete()

    @classmethod
    # @cached_object(
    #     cache_key_prefix="payment-method-get-by-organization-id",
    #     timeout=60
    # ) TODO FIXME
    def get_by_organization_id(cls, organization_id):
        """
        Get payment methods by organization id
        """
        return cls.objects.filter(organization_id=organization_id)
