# Generated by Django 5.0.6 on 2024-10-14 17:19

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0010_alter_productattribute_is_available"),
    ]

    operations = [
        migrations.AddField(
            model_name="itemmodifiergroups",
            name="size_id",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="product",
            name="image_url",
            field=models.ImageField(
                null=True,
                upload_to="products",
                validators=[
                    django.core.validators.FileExtensionValidator(
                        allowed_extensions=["jpeg", "jpg", "png", "webp"]
                    )
                ],
            ),
        ),
    ]
