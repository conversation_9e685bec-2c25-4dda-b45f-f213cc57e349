import os
import datetime
import subprocess

from celery import shared_task

from master_kebab import settings

from apps.bot.services.telegram_service import TelegramService
from apps.core.external.minio.client import upload_file_to_minio


DB_HOST = settings.DATABASES['default']['HOST']
DB_PORT = settings.DATABASES['default']['PORT']
DB_USER = settings.DATABASES['default']['USER']
DB_NAME = settings.DATABASES['default']['NAME']
DB_PASSWORD = settings.DATABASES['default']['PASSWORD']

BACKUP_DIR = os.path.join(settings.BASE_DIR, 'backups')


def get_psql_dump_command(timestamp):
    """
    Get the psql dump command.
    """
    dumper = f'PGPASSWORD={DB_PASSWORD} pg_dump -h {DB_HOST} -w -p {DB_PORT} -U {DB_USER} {DB_NAME}'
    target_path = f'{BACKUP_DIR}/backup_{timestamp}.sql'
    psql_dump_command = f'{dumper} > {target_path}'

    return psql_dump_command


def create_database_dump():
    """
    Create a database dump and save it to a backup file.
    """
    os.makedirs(BACKUP_DIR, exist_ok=True)
    timestamp = datetime.datetime.now().strftime('%Y%m%d%H%M%S')

    command = get_psql_dump_command(timestamp)

    result = subprocess.run(command, shell=True, text=True, capture_output=True)

    if result.returncode != 0:
        raise Exception(f'Failed to create database dump: {result.stderr}')

    file_path = f'{BACKUP_DIR}/backup_{timestamp}.sql'
    with open(file_path, 'rb') as file:
        upload_file_to_minio(
            file=file,
            filename=f'backup_{timestamp}.sql'
        )

    os.remove(file_path)


@shared_task
def create_database_dump_task():
    """
    Task to create a database dump and save it to a backup file.
    """
    try:
        create_database_dump()
        TelegramService.send_text_message(
            chat_id=settings.ORDERS_CHANNEL,
            title='��� Database dump is being created...',
            message='✅ Database dump created successfully.'
        )

    except Exception as exc:
        TelegramService.send_text_message(
            chat_id=settings.ORDERS_CHANNEL,
            title='��� Database dump creation failed...',
            message=f'�� Error creating database dump: {str(exc)}'
        )
