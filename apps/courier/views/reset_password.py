"""
the reset password apiview
"""
import random

from django.core.cache import cache

from rest_framework import views, status
from rest_framework.response import Response

from apps.user.models.user import Users
from apps.core.views import ServiceBaseView
from apps.bot.tasks import send_message_task
from apps.sms.text import get_credentials_text
from apps.sms.tasks import send_sms_message_task
from apps.courier.models.courier import Courier
from apps.core.exceptions.service import ServiceAPIException

from master_kebab.settings import ORDERS_CHANNEL


class ResetPasswordAPIView(views.APIView, ServiceBaseView):
    """
    API view for resetting password.
    """
    permission_classes = []

    def post(self, request, *args, **kwargs):
        """
        the reset password apiview post method
        """
        phone = request.data.get("phone")
        otp = request.data.get("otp")

        if not phone:
            raise ServiceAPIException(
                error_type="bad_request", message="Phone number is required"
            )

        if not otp:
            self.send_otp(phone)  # TODO: will be send with OTP
            return Response({"message": "OTP sent successfully"}, status=status.HTTP_200_OK)

        # Verify the OTP
        if not self.verify_otp(phone, otp):
            raise self.call_service_exception(
                error_type="bad_request",
                message=f"Invalid or expired OTP request data: {request.data}",
                status_code=400,
                notify_admin=True,
            )

        try:
            courier = Courier.get_by_phone(phone)
        except Courier.DoesNotExist as exc:
            raise self.call_service_exception(
                error_type="not_found",
                message=f"Courier not found: {phone}: request: {request.data}",
                status_code=404,
                notify_admin=True,
            ) from exc

        # Generate a new password
        new_password = Users.generate_random_password()
        courier.set_password(new_password)
        courier.save()

        message = get_credentials_text(phone=phone, password=new_password, lang="ru")
        send_sms_message_task.delay(phone=phone, message=message)
        send_message_task.delay(chat_id=ORDERS_CHANNEL, message=message)

        return Response({"success": True})

    def send_otp(self, phone):
        otp = random.randint(100000, 999999)
        cache.set(f"otp:{phone}", otp, timeout=300)  # OTP valid for 5 minutes

        message = f"Ваш OTP-код: {otp}"
        send_message_task.delay(chat_id=ORDERS_CHANNEL, message=message)

    def verify_otp(self, phone, otp):
        cached_otp = cache.get(f"otp:{phone}")
        if cached_otp and str(cached_otp) == str(otp):
            cache.delete(f"otp:{phone}")
            return True
        return False
