"""
user's admin interface
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.bot.models.telegram import TelegramUser


class TelegramUserUI(ModelAdmin):
    """
    the user admin page
    """
    list_display = (
        "id",
        "first_name",
        "username",
        "state",
        "is_active"
    )
    search_fields = (
        "id",
        "username",
    )


admin.site.register(TelegramUser, TelegramUserUI)
