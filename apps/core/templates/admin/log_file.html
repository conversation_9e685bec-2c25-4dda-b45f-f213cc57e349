{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrastyle %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .log-file-container {
        margin: 20px 0;
        padding: 30px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
    }
    .log-file-actions {
        margin-top: 30px;
    }
    .log-file-actions .button {
        margin-right: 10px;
        background-color: #79aec8;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
        display: inline-flex;
        align-items: center;
        font-weight: 500;
    }
    .log-file-actions .button:hover {
        background-color: #417690;
    }
    .log-file-actions .button i {
        margin-right: 8px;
    }
    .log-file-info {
        margin-bottom: 25px;
        color: #666;
        background-color: #f8f8f8;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #79aec8;
    }
    .log-file-error {
        color: #c00;
        font-weight: bold;
        background-color: #fff0f0;
        padding: 15px;
        border-radius: 4px;
        border-left: 4px solid #c00;
    }
    .log-file-section {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    .log-file-section h2 {
        color: #417690;
        margin-bottom: 15px;
        font-size: 1.4em;
    }
    .form-row {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    .form-row label {
        margin-right: 10px;
        min-width: 150px;
        font-weight: 500;
    }
    .form-row input[type="number"] {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        width: 100px;
    }
    .form-actions {
        margin-top: 15px;
    }
    .card {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }
    .card-header {
        background-color: #417690;
        color: white;
        padding: 15px 20px;
        font-weight: 500;
        font-size: 1.1em;
    }
    .card-body {
        padding: 20px;
    }
    .help-text {
        color: #888;
        font-size: 0.9em;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; {% trans 'Log File Management' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <h1><i class="fas fa-file-alt"></i> {% trans 'Log File Management' %}</h1>

    <div class="log-file-container">
        {% if log_file_exists %}
            <div class="log-file-section">
                <h2><i class="fas fa-download"></i> {% trans 'Download Options' %}</h2>

                <div class="card" style="margin-top: 20px;">
                    <div class="card-header">
                        {% trans 'Partial Log File' %}
                    </div>
                    <div class="card-body">
                        <p>{% trans 'Download only the most recent log entries to focus on recent activity.' %}</p>
                        <form action="{% url 'admin:core_log_file_download' %}" method="post">
                            {% csrf_token %}
                            <input type="hidden" name="download_type" value="partial">
                            <div class="form-row">
                                <label for="max_lines">{% trans 'Number of lines:' %}</label>
                                <input type="number" name="max_lines" id="max_lines" min="1" value="100">
                            </div>
                            <div class="help-text">
                                {% trans 'Specify how many of the most recent log lines you want to download.' %}
                            </div>
                            <div class="form-actions">
                                <button type="submit" class="button">
                                    <i class="fas fa-file-download"></i> {% trans 'Download Selected Lines' %}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="log-file-section">
                <h2><i class="fas fa-search"></i> {% trans 'Log Preview' %}</h2>
                <div class="card">
                    <div class="card-header">
                        {% trans 'Last 1000 Log Entries' %}
                    </div>
                    <div class="card-body">
                        {% if log_preview %}
                            <pre style="background-color: #f5f5f5; padding: 15px; border-radius: 4px; overflow: auto; max-height: 300px;">{{ log_preview }}</pre>
                        {% else %}
                            <p>{% trans 'No log entries available for preview.' %}</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% else %}
            <div class="card">
                <div class="card-header" style="background-color: #c00;">
                    <i class="fas fa-exclamation-triangle"></i> {% trans 'Error' %}
                </div>
                <div class="card-body">
                    <div class="log-file-error">
                        <p>{% trans 'Log file not found at:' %} <code>{{ log_file_path }}</code></p>
                        <p>{% trans 'Please check your logging configuration.' %}</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
    // Add CSRF token to AJAX requests
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Set up CSRF token for all AJAX requests
    const csrftoken = getCookie('csrftoken');

    // Add event listeners to forms to handle submission with CSRF token
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                // The form already has the CSRF token from the template tag
                // This is just for any additional JavaScript-based submissions
                console.log('Form submitted with CSRF protection');
            });
        });
    });
</script>
{% endblock %}
