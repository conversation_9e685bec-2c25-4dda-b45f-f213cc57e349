"""
Middleware for logging detailed request and response information in a Django application.
This middleware captures essential details such as HTTP method, path, headers, request body,
client IP, response body, and the time taken for the request-response cycle. It supports logging
the information and optionally saving it to a database for further analysis or auditing.

Classes:
    RequestResponseLoggingMiddleware(MiddlewareMixin):
        Captures request and response details, logs them, and optionally stores the logs in a database.

Functions:
    process_request(self, request):
        Extracts and stores details of the incoming request before it's passed to the view.

    process_response(self, request, response):
        Logs request and response details and calculates the duration of the request-response cycle.

    get_client_ip(self, request):
        Retrieves the client IP address from the request metadata.

    save_log_to_db(self, log_data):
        Placeholder function to optionally save the log data to a database (needs to be implemented).

Usage:
    - Add this middleware to the `MIDDLEWARE` list in the Django settings.
    - Ensure logging is appropriately configured in the Django project settings for file or console logging.
    - Extend `save_log_to_db` if logs need to be stored in a database.
"""

import json
import logging
from datetime import datetime
from django.utils.deprecation import MiddlewareMixin
from apps.payment.models import RequestResponseLog

logger = logging.getLogger(__name__)


class RequestResponseLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log request and response details for monitoring and auditing purposes.

    Attributes:
        logger (Logger): Logger instance to capture and output log information.

    Methods:
        process_request(request): Captures request details like method, path, headers, body, and client IP.
        process_response(request, response): Logs both request and response details, calculates duration.
        save_log_to_db(log_data): Saves log details to a database.
        get_client_ip(request): Retrieves the client's IP address from the request headers.
    """

    def process_request(self, request):
        """
        Extracts and stores details of the incoming request before passing it to the view.
        Only logs requests matching the path '/api/v1/payment/updates/' with the query parameter 'provider_type=payme'.

        Args:
            request (HttpRequest): The incoming HTTP request.

        Stores:
            request.start_time (datetime): The time the request started, used to calculate request duration.
            request.full_info (dict): A dictionary containing HTTP method, path, headers, body, and client IP.
        """
        # Check if the request path and query match the condition
        if request.path == '/api/v1/payment/updates/' and request.GET.get('provider_type') == 'payme':
            request_body = ""
            if request.body:
                try:
                    request_body = json.loads(request.body.decode('utf-8'))
                except json.JSONDecodeError:
                    request_body = request.body.decode('utf-8')

            request.start_time = datetime.now()
            request.full_info = {
                "method": request.method,
                "path": request.path,
                "headers": dict(request.headers),
                "body": request_body,
                "client_ip": self.get_client_ip(request),
            }

    def process_response(self, request, response):
        """
        Logs the request and response details and calculates the duration of the request-response cycle.
        Only logs responses for requests that match '/api/v1/payment/updates/' with 'provider_type=payme'.

        Args:
            request (HttpRequest): The original HTTP request.
            response (HttpResponse): The HTTP response generated by the view.

        Returns:
            HttpResponse: The same response object passed to this method.

        Logs:
            Logs request and response details such as method, path, headers, status code, and duration.
            Optionally, logs can be saved to a database via `save_log_to_db`.
        """
        # Check if `full_info` was set in `process_request`
        if hasattr(request, 'full_info'):
            duration = (datetime.now() - request.start_time).total_seconds() if hasattr(request, 'start_time') else None

            response_body = ""
            if hasattr(response, 'content'):
                try:
                    response_body = json.loads(response.content.decode('utf-8'))
                except json.JSONDecodeError:
                    response_body = response.content.decode('utf-8')

            log_data = {
                "method": request.full_info.get("method"),
                "path": request.full_info.get("path"),
                "headers": request.full_info.get("headers"),
                "body": request.full_info.get("body"),
                "client_ip": request.full_info.get("client_ip"),
                "response_status": response.status_code,
                "response_body": response_body,
                "duration": duration,
            }

            logger.info(f"Request-Response Log: {log_data}")

            self.save_log_to_db(log_data)

        return response

    def save_log_to_db(self, log_data):
        """
        Saves log data to the database.

        Args:
            log_data (dict): The dictionary containing request and response details to be stored.
        """
        try:
            RequestResponseLog.save_log_to_db(log_data)
        except Exception as exc:
            logger.error(f"Failed to save log to database: {exc}")

    def get_client_ip(self, request):
        """
        Retrieves the client's IP address from the request headers or metadata.

        Args:
            request (HttpRequest): The incoming HTTP request.

        Returns:
            str: The client's IP address.
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
