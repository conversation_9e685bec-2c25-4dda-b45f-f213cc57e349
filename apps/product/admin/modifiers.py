"""
Admin module for managing product modifiers in the admin interface.
"""

from django.contrib import admin
from modeltranslation.admin import TabbedTranslationAdmin

from apps.core.admin.modeladmin import ModelAdmin
from apps.product.models.modifier import Restrictions, ModiferProductPrices, ItemModifierGroups, ModifierItems


@admin.register(Restrictions)
class RestrictionsAdmin(ModelAdmin):
    """
    Admin interface for managing Restrictions.
    """
    list_display = ('min_quantity', 'max_quantity', 'free_quantity', 'by_default', 'hide_if_default_quantity')
    search_fields = ('min_quantity', 'max_quantity', 'free_quantity', 'by_default', 'hide_if_default_quantity')


@admin.register(ModiferProductPrices)
class ModiferProductPricesAdmin(ModelAdmin):
    """
    Admin interface for managing Modifier Product Prices.
    """
    list_display = ('organization', 'price', 'modifer_items')
    search_fields = ('organization__name', 'modifer_items__name')


@admin.register(ItemModifierGroups)
class ItemModifierGroupsAdmin(TabbedTranslationAdmin, ModelAdmin):
    """
    Admin interface for managing Item Modifier Groups.
    """
    list_display = (
        'product', 'name', 'description', 'can_be_divided', 'item_group_id',
        'is_hidden', 'child_modifier_have_min_max_restrictions', 'sku'
    )
    search_fields = ('product__title', 'name', 'description', 'item_group_id', 'sku')


@admin.register(ModifierItems)
class ModifierItemsAdmin(TabbedTranslationAdmin, ModelAdmin):
    """
    Admin interface for managing Modifier Items.
    """
    list_display = (
        'sku', 'name', 'description', 'portion_weight_grams', 'is_hidden',
        'item_id', 'position', 'independent_quantity', 'measure_unit_type', 'button_image_url'
    )
    search_fields = ('sku', 'name', 'description', 'item_id')
