"""
clients view
"""
from rest_framework.permissions import IsAuthenticated
from rest_framework import generics

from apps.user.models.user import Users
from apps.user.serializers.client import ClientSerializer


class ClientDetailView(generics.RetrieveUpdateDestroyAPIView):
    """
    The customer detail, update, and delete view
    """
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user

    queryset = Users.objects.values('gender', 'name', 'email', 'date_of_birth')
    serializer_class = ClientSerializer
