"""
the language enumuration
"""
from enum import Enum


class LanguageEnum(str, Enum):
    """
    the enumeration of initiator
    """
    UZBEK = "uz"
    RUSSIAN = "ru"
    ENGLISH = "en"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
