"""
Call log model for storing call history and recordings
"""
from django.db import models
from apps.core.models.base import BaseModel
from apps.user.models.user import Users
from apps.order.models.order import Order


class CallLog(BaseModel):
    """
    Model to store call logs and recordings from Softphone.Pro
    """
    
    CALL_DIRECTION_CHOICES = [
        ('inbound', 'Inbound'),
        ('outbound', 'Outbound'),
    ]
    
    CALL_STATUS_CHOICES = [
        ('answered', 'Answered'),
        ('missed', 'Missed'),
        ('busy', 'Busy'),
        ('failed', 'Failed'),
        ('no_answer', 'No Answer'),
    ]
    
    caller_id = models.Char<PERSON><PERSON>(max_length=20, help_text="Phone number of caller")
    called_number = models.CharField(max_length=20, help_text="Phone number that was called")
    direction = models.CharField(max_length=10, choices=CALL_DIRECTION_CHOICES)
    status = models.CharField(max_length=15, choices=CALL_STATUS_CHOICES)
    duration = models.IntegerField(default=0, help_text="Call duration in seconds")
    start_time = models.DateTimeField(help_text="Call start time")
    end_time = models.DateTimeField(null=True, blank=True, help_text="Call end time")
    
    # Relations
    user = models.ForeignKey(Users, on_delete=models.SET_NULL, null=True, blank=True, 
                           help_text="User associated with this call")
    order = models.ForeignKey(Order, on_delete=models.SET_NULL, null=True, blank=True,
                            help_text="Order associated with this call")
    operator = models.ForeignKey(Users, on_delete=models.SET_NULL, null=True, blank=True,
                               related_name='operator_calls', help_text="Operator who handled the call")
    
    # Recording and additional data
    recording_url = models.URLField(null=True, blank=True, help_text="URL to call recording")
    recording_file = models.FileField(upload_to='call_recordings/', null=True, blank=True)
    external_id = models.CharField(max_length=255, null=True, blank=True, 
                                 help_text="External ID from Softphone.Pro")
    notes = models.TextField(null=True, blank=True, help_text="Call notes")
    
    # Softphone.Pro specific fields
    did_number = models.CharField(max_length=20, null=True, blank=True, 
                                help_text="DID number used for the call")
    sip_account = models.CharField(max_length=100, null=True, blank=True,
                                 help_text="SIP account used")
    
    class Meta:
        db_table = 'softphone_call_logs'
        verbose_name = 'Call Log'
        verbose_name_plural = 'Call Logs'
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['caller_id']),
            models.Index(fields=['called_number']),
            models.Index(fields=['start_time']),
            models.Index(fields=['external_id']),
        ]
    
    def __str__(self):
        return f"Call {self.direction} - {self.caller_id} -> {self.called_number} ({self.status})"
    
    @property
    def formatted_duration(self):
        """Return formatted duration as MM:SS"""
        if self.duration:
            minutes = self.duration // 60
            seconds = self.duration % 60
            return f"{minutes:02d}:{seconds:02d}"
        return "00:00"
    
    @classmethod
    def create_from_softphone(cls, call_data):
        """
        Create call log from Softphone.Pro webhook data
        """
        # Try to find associated user by phone number
        user = None
        if call_data.get('caller_id'):
            try:
                user = Users.objects.get(phone=call_data['caller_id'])
            except Users.DoesNotExist:
                pass
        
        return cls.objects.create(
            caller_id=call_data.get('caller_id', ''),
            called_number=call_data.get('called_number', ''),
            direction=call_data.get('direction', 'inbound'),
            status=call_data.get('status', 'answered'),
            duration=call_data.get('duration', 0),
            start_time=call_data.get('start_time'),
            end_time=call_data.get('end_time'),
            user=user,
            external_id=call_data.get('external_id'),
            recording_url=call_data.get('recording_url'),
            did_number=call_data.get('did_number'),
            sip_account=call_data.get('sip_account'),
            notes=call_data.get('notes', '')
        )
