"""
This module provides custom form widgets for the Unfold application.
"""

from typing import Any, Dict, Optional
from django.forms import TextInput  # should be change to PasswordInput
from unfold.widgets import INPUT_CLASSES


class UnfoldPasswordField(TextInput):
    """
    A custom password input field for the Unfold application.

    This class extends the default Django PasswordInput widget to include
    additional CSS classes defined in the Unfold application.
    """
    def __init__(self, attrs: Optional[Dict[str, Any]] = None) -> None:
        """
        Initialize the UnfoldPasswordField with custom attributes.

        Args:
            attrs (Optional[Dict[str, Any]]): Optional dictionary of HTML attributes.
        """
        super().__init__(
            attrs={"class": " ".join(INPUT_CLASSES), **(attrs or {})}
        )
