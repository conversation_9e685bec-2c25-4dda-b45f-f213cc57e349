"""
implementation of category views
"""
from django.db.models import Count
from django.views.decorators.cache import cache_page
from django.utils.decorators import method_decorator

from rest_framework import status
from rest_framework import generics
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.product.models.category import Category
from apps.operations.permissions import IsAdminOrManagerOrMarketolog
from apps.product.serializers.category import CategorySerializer, CategoryBulkUpdateInputSerializer  # pylint: disable=E0611 # noqa


class CategoryListView(generics.ListAPIView):
    """
    the category list view
    """
    queryset = Category.objects.annotate(product_count=Count('product')).filter(product_count__gt=0)
    serializer_class = CategorySerializer

    def get_queryset(self):
        queryset = Category.objects.annotate(product_count=Count('product')).filter(product_count__gt=0)
        queryset = queryset.exclude(title__in=["Доставка", "delivery"])
        limit = self.request.query_params.get('limit', None)
        if limit == 'all':
            self.pagination_class = None
        return queryset

    @method_decorator(cache_page(15))
    def dispatch(self, *args, **kwargs):
        return super().dispatch(*args, **kwargs)


class CategoryRetrieveView(generics.RetrieveAPIView):
    """
    The category retrieve view
    """
    queryset = Category.objects.all()
    serializer_class = CategorySerializer


class CategoryBulkUpdateView(APIView):
    """
    View for bulk updating categories' localization names and positions
    """
    permission_classes = [
        IsAdminOrManagerOrMarketolog,
    ]

    def put(self, request, *args, **kwargs):
        serializer = CategoryBulkUpdateInputSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        categories_data = serializer.validated_data['categories']
        category_dict = {cat.id: cat for cat in Category.objects.filter(
            id__in=[item['id'] for item in categories_data]
        )}

        updateable_fields = []

        for item in categories_data:
            category = category_dict.get(item['id'])
            if category:
                for field in ['title_uz', 'title_ru', 'title_en', 'position_id']:
                    if field in item:
                        setattr(category, field, item[field])
                        if field not in updateable_fields:
                            updateable_fields.append(field)

        if updateable_fields:
            Category.objects.bulk_update(
                category_dict.values(),
                updateable_fields
            )

        return Response(
            CategorySerializer(category_dict.values(), many=True).data,
            status=status.HTTP_200_OK
        )
