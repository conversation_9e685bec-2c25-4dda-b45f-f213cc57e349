"""
Tests for Yandex Geosuggestion API functionality.

This module contains unit tests for the Yandex Geosuggestion service,
models, and API views to ensure proper functionality and error handling.
"""

from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status

from apps.map.models.api_keys import MapApiKey
from apps.map.models.api_usage import ApiUsage
from apps.map.services.yandex_geosuggestion import YandexGeosuggestionService
from apps.map.tasks import reset_daily_api_limits, monitor_api_usage


User = get_user_model()


class YandexGeosuggestionModelTests(TestCase):
    """Test cases for Yandex Geosuggestion related models."""
    
    def setUp(self):
        """Set up test data."""
        self.api_key = MapApiKey.objects.create(
            api_key="test-api-key-123",
            app_name=MapApiKey.YANDEX_MAP,
            usage_type=MapApiKey.YANDEX_GEOSUGGESTION,
            owner="Test Owner"
        )
    
    def test_api_usage_creation(self):
        """Test ApiUsage model creation and methods."""
        usage_record, created = ApiUsage.get_or_create_today_usage(self.api_key)
        
        self.assertTrue(created)
        self.assertEqual(usage_record.api_key, self.api_key)
        self.assertEqual(usage_record.usage_count, 0)
        self.assertEqual(usage_record.daily_limit, 1000)
    
    def test_usage_increment(self):
        """Test usage count increment functionality."""
        # First increment
        usage_record = ApiUsage.increment_usage(self.api_key)
        self.assertEqual(usage_record.usage_count, 1)
        
        # Second increment
        usage_record = ApiUsage.increment_usage(self.api_key)
        self.assertEqual(usage_record.usage_count, 2)
    
    def test_usage_limit_check(self):
        """Test daily limit checking."""
        # Should have available requests initially
        self.assertTrue(ApiUsage.check_limit_available(self.api_key))
        
        # Set usage to limit
        usage_record, _ = ApiUsage.get_or_create_today_usage(self.api_key)
        usage_record.usage_count = 1000
        usage_record.save()
        
        # Should not have available requests
        self.assertFalse(ApiUsage.check_limit_available(self.api_key))
    
    def test_remaining_requests(self):
        """Test remaining requests calculation."""
        remaining = ApiUsage.get_remaining_requests(self.api_key)
        self.assertEqual(remaining, 1000)

        # Use some requests
        ApiUsage.increment_usage(self.api_key)
        ApiUsage.increment_usage(self.api_key)

        remaining = ApiUsage.get_remaining_requests(self.api_key)
        self.assertEqual(remaining, 998)

    def test_api_key_switching(self):
        """Test automatic switching to next available API key when limit is reached."""
        # Create a second API key
        api_key2 = MapApiKey.objects.create(
            api_key="test-api-key-456",
            app_name=MapApiKey.YANDEX_MAP,
            usage_type=MapApiKey.YANDEX_GEOSUGGESTION,
            owner="Test Owner 2"
        )

        # Exhaust the first key
        usage_record, _ = ApiUsage.get_or_create_today_usage(self.api_key)
        usage_record.usage_count = 1000
        usage_record.save()

        # Use the API key method - should automatically switch to second key
        api_key, remaining = MapApiKey.use_yandex_geosuggestion_key()

        # Should return the second key
        self.assertEqual(api_key.id, api_key2.id)
        self.assertEqual(remaining, 999)  # Should have used 1 request

        # First key should be marked as limit expired
        self.api_key.refresh_from_db()
        self.assertTrue(self.api_key.limit_expired)

    def test_all_keys_exhausted(self):
        """Test behavior when all API keys are exhausted."""
        # Exhaust the only key
        usage_record, _ = ApiUsage.get_or_create_today_usage(self.api_key)
        usage_record.usage_count = 1000
        usage_record.save()

        # Should raise ValueError when no keys available
        with self.assertRaises(ValueError) as context:
            MapApiKey.use_yandex_geosuggestion_key()

        self.assertIn("daily limit", str(context.exception))

    def test_daily_limit_reset(self):
        """Test resetting daily limits."""
        # Mark key as limit expired
        self.api_key.limit_expired = True
        self.api_key.save()

        # Reset limits
        reset_count = MapApiKey.reset_daily_limits()

        self.assertEqual(reset_count, 1)

        # Key should no longer be expired
        self.api_key.refresh_from_db()
        self.assertFalse(self.api_key.limit_expired)


class YandexGeosuggestionServiceTests(TestCase):
    """Test cases for YandexGeosuggestionService."""
    
    def setUp(self):
        """Set up test data."""
        self.api_key = MapApiKey.objects.create(
            api_key="test-api-key-123",
            app_name=MapApiKey.YANDEX_MAP,
            usage_type=MapApiKey.YANDEX_GEOSUGGESTION,
            owner="Test Owner"
        )
        self.service = YandexGeosuggestionService()
    
    @patch('apps.map.services.yandex_geosuggestion.requests.Session.get')
    @patch('apps.map.models.api_keys.MapApiKey.use_yandex_geosuggestion_key')
    def test_get_suggestions_success(self, mock_use_key, mock_get):
        """Test successful API call to get suggestions."""
        # Mock API key usage
        mock_use_key.return_value = (self.api_key, 999)
        
        # Mock API response
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            'results': [
                {
                    'title': {'text': 'Test Location'},
                    'subtitle': {'text': 'Test Address'},
                    'uri': 'ymapsbm1://geo?ll=65.38055%2C40.09075',
                    'distance': {'text': '100 m'},
                    'tags': ['restaurant'],
                    'pos': '65.38055 40.09075'
                }
            ]
        }
        mock_get.return_value = mock_response
        
        # Test the service
        result = self.service.get_suggestions('test location')
        
        # Verify results
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['total_results'], 1)
        self.assertEqual(result['remaining_requests'], 999)
        self.assertEqual(len(result['suggestions']), 1)
        
        suggestion = result['suggestions'][0]
        self.assertEqual(suggestion['title'], 'Test Location')
        self.assertEqual(suggestion['subtitle'], 'Test Address')
        self.assertIn('coordinates', suggestion)
        self.assertEqual(suggestion['coordinates']['longitude'], 65.38055)
        self.assertEqual(suggestion['coordinates']['latitude'], 40.09075)
    
    def test_check_api_availability_no_key(self):
        """Test API availability check when no key is available."""
        # Delete the API key
        self.api_key.delete()
        
        available, message = YandexGeosuggestionService.check_api_availability()
        
        self.assertFalse(available)
        self.assertIn("No available", message)


class YandexGeosuggestionAPITests(TestCase):
    """Test cases for Yandex Geosuggestion API views."""
    
    def setUp(self):
        """Set up test data."""
        self.user = User.objects.create_user(
            phone='+998901234567',
            name='Test User'
        )
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        self.api_key = MapApiKey.objects.create(
            api_key="test-api-key-123",
            app_name=MapApiKey.YANDEX_MAP,
            usage_type=MapApiKey.YANDEX_GEOSUGGESTION,
            owner="Test Owner"
        )
        
        self.url = reverse('yandex-geosuggestion')
        self.status_url = reverse('yandex-geosuggestion-status')
    
    def test_geosuggestion_missing_name_parameter(self):
        """Test API call without required name parameter."""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_geosuggestion_invalid_coordinates(self):
        """Test API call with invalid coordinates."""
        response = self.client.get(self.url, {
            'name': 'test location',
            'coordinates': 'invalid-coordinates'
        })
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_status_endpoint(self):
        """Test the status endpoint."""
        response = self.client.get(self.status_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('available', response.data)
        self.assertIn('message', response.data)
    
    def test_authentication_required(self):
        """Test that authentication is required."""
        client = APIClient()  # Unauthenticated client
        
        response = client.get(self.url, {'name': 'test'})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        response = client.get(self.status_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)


class CeleryTaskTests(TestCase):
    """Test cases for Celery tasks."""

    def setUp(self):
        """Set up test data."""
        self.api_key = MapApiKey.objects.create(
            api_key="test-api-key-123",
            app_name=MapApiKey.YANDEX_MAP,
            usage_type=MapApiKey.YANDEX_GEOSUGGESTION,
            owner="Test Owner",
            limit_expired=True  # Start with expired key
        )

    def test_reset_daily_api_limits_task(self):
        """Test the daily API limits reset Celery task."""
        # Ensure key is marked as expired
        self.assertTrue(self.api_key.limit_expired)

        # Run the task
        result = reset_daily_api_limits()

        # Check task result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['reset_count'], 1)
        self.assertEqual(result['expired_keys'], 1)

        # Check that key is no longer expired
        self.api_key.refresh_from_db()
        self.assertFalse(self.api_key.limit_expired)

    def test_monitor_api_usage_task(self):
        """Test the API usage monitoring Celery task."""
        # Create some usage data
        ApiUsage.objects.create(
            api_key=self.api_key,
            usage_count=800,  # 80% of 1000 limit
            daily_limit=1000
        )

        # Run the monitoring task
        result = monitor_api_usage()

        # Check task result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['keys_near_limit_count'], 1)
        self.assertEqual(result['keys_exhausted_count'], 0)

        # Check that the key info is included
        self.assertEqual(len(result['keys_near_limit']), 1)
        key_info = result['keys_near_limit'][0]
        self.assertEqual(key_info['usage_percentage'], 80.0)
