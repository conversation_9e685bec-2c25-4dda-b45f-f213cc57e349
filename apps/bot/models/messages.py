"""
the telegram messages for order
"""
import logging

from django.db import models

from apps.core.models import base

logger = logging.getLogger(__name__)


class Messages(base.BaseModel):
    """
    the telegram messages for order model
    """
    class Meta:
        """
        the meta fields
        """
        db_table = 'messages'
        verbose_name = "Messages"
        verbose_name_plural = "Messages"
        ordering = ["-created_at"]
        unique_together = ["order", "message_id"]

    order = models.ForeignKey("order.Order", on_delete=models.CASCADE, null=True)
    chat_id = models.BigIntegerField(null=True)
    message_id = models.BigIntegerField()
    message = models.TextField(null=True)
    state = models.CharField(max_length=255, null=True)

    @classmethod
    def create(cls, chat_id, order_id, message_id, message, state):
        """
        create new telegram messages for order
        """
        return cls.objects.create(
            order_id=order_id,
            message_id=message_id,
            message=message,
            state=state,
            chat_id=chat_id
        )

    @classmethod
    def create_for_broadcast(cls, chat_id, message, message_id):
        """
        create new telegram messages for broadcast
        """
        try:
            return cls.objects.create(
                chat_id=chat_id,
                message=message,
                message_id=message_id,
                state="delivered",
            )
        except Exception as e:
            logger.error(f"Error creating message for broadcast: {e}")
            return None

    @classmethod
    def get_by_order_id(cls, order_id: str):
        """
        Retrieves a Telegram message based on the provided order ID

        Args:
            order_id (str): The ID of the order associated with the message.

        Returns:
            TelegramMessage: The Telegram message object that matches the given criteria.

        Raises:
            ObjectDoesNotExist: If no message is found with the given order ID
        """
        return cls.objects.get(order_id=order_id)

    @classmethod
    def get_old_messages(cls, threshold_time):
        """
        Retrieves messages that were created before the threshold time.

        Args:
            threshold_time: The datetime threshold. Messages created before this time will be returned.

        Returns:
            QuerySet: A queryset of Messages that were created before the threshold time.
        """
        return cls.objects.filter(created_at__lte=threshold_time)
