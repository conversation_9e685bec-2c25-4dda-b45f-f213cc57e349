# Generated by Django 5.0.6 on 2025-02-06 12:06

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("hr", "0012_workinghours_remove_jobposition_working_hours_en_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="jobposition",
            name="working_hours",
        ),
        migrations.CreateModel(
            name="JobWorkingHour",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("working_hours", models.CharField(max_length=100)),
                (
                    "working_hours_uz",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "working_hours_ru",
                    models.Char<PERSON>ield(blank=True, max_length=100, null=True),
                ),
                (
                    "working_hours_en",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                (
                    "job_position",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="working_hours",
                        to="hr.jobposition",
                    ),
                ),
            ],
            options={
                "db_table": "hr_job_working_hour",
            },
        ),
        migrations.DeleteModel(
            name="WorkingHours",
        ),
    ]
