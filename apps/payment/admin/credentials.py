"""
the credentials admin site
"""

from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin
from apps.payment.forms import ProviderCredentialsForm
from apps.payment.models.credentials import ProviderCredentials


class ProviderCredentialsUI(ModelAdmin):
    """
    The provider credentials admin page
    """
    form = ProviderCredentialsForm
    list_display = [
        'id',
        'title',
        'payment_method',
        'organization',
        'created_at',
        'updated_at',
    ]
    list_filter = ['payment_method', 'organization']
    readonly_fields = [
        'created_at', 'updated_at',
    ]
    search_fields = ['organization']
    list_display_links = list_display
    fieldsets = (
        (None, {
            'fields': (
                'title',
                'payment_method',
                'organization',
                'identification',
                'service_id',
                'key',
                'url',
                'minimal_amount',
                'return_url',
                'transaction_key'),
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


admin.site.register(ProviderCredentials, ProviderCredentialsUI)
