"""
The delivery update event helpers
"""
from master_kebab.settings import ORDERS_CHANNEL

from apps.bot.tasks import send_message_task
from apps.iiko.helpers.common import CommonEventHelper
from apps.iiko.serializers import DeliveryOrderErrorEvent
from apps.order.models.order import get_order_by_external_id
from apps.order.enums.order import OrderStatus as OrderStatusInternal


class DeliveryOrderErrorEventHelper(CommonEventHelper):
    """
    The delivery update event helper
    """
    @classmethod
    def process_update_event(cls, update_event: DeliveryOrderErrorEvent):
        """
        Process delivery update event
        """
        for event in update_event.event_body:
            external_id = event.eventInfo.id

            status = OrderStatusInternal.DELIVERY_ORDER_ERROR

            if event.is_timeout_error():
                status = OrderStatusInternal.TIMEOUT

            cache_key = f"delivery_order_error_event:{external_id}:{status}"

            if cls.has_been_processed(cache_key):
                return

            cls.mark_as_processed(cache_key)

            # Prepare and send the message
            message = f"✅ Order has been updated\n\nStatus: {status}\nExternal OrderID: {external_id}"
            send_message_task.delay(chat_id=ORDERS_CHANNEL, message=message)

            order = get_order_by_external_id(external_id)
            order.status = status
            order.save()

            print(update_event)
