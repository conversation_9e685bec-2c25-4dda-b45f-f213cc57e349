"""
monitoring model
"""
from django.db import models

from apps.order.models.order import Order
from apps.core.models.base import BaseModel


class Monitoring(BaseModel):
    """
    the monitoring model
    """
    class Meta:
        """
        the meta fields
        """
        db_table = 'monitoring'

    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name="monitoring")
    user_id = models.IntegerField()
    user_role = models.CharField()
    action = models.CharField()

    @classmethod
    def processed_by(cls, order_id, user_id, user_type, action):
        """
        process order
        """
        monitoring = cls(
            order_id=order_id, user_id=user_id,
            user_type=user_type, action=action
        )
        monitoring.save()
