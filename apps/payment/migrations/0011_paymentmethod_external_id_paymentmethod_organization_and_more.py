# Generated by Django 5.0.6 on 2024-10-04 15:34

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organization", "0004_alter_organization_created_at_and_more"),
        ("payment", "0010_providercredentials_return_url"),
    ]

    operations = [
        migrations.AddField(
            model_name="paymentmethod",
            name="external_id",
            field=models.CharField(max_length=255, null=True),
        ),
        migrations.AddField(
            model_name="paymentmethod",
            name="organization",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="organization.organization",
            ),
        ),
        migrations.AlterField(
            model_name="providercredentials",
            name="identification",
            field=models.CharField(
                blank=True, max_length=255, verbose_name="Provider ID"
            ),
        ),
        migrations.AlterField(
            model_name="providercredentials",
            name="key",
            field=models.Char<PERSON>ield(
                blank=True, max_length=255, verbose_name="Provider Key"
            ),
        ),
    ]
