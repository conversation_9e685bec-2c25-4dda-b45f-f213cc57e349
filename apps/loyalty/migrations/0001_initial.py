# Generated by Django 5.0.6 on 2025-04-26 19:14

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LoyaltyProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON><PERSON>(max_length=100)),
                ('service_from', models.DateField()),
                ('service_to', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('program_type', models.IntegerField()),
                ('refill_type', models.IntegerField()),
                ('external_id', models.Char<PERSON>ield(max_length=100, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
