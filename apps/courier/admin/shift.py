"""
user's admin interface
"""
from django.contrib import admin

from apps.core.admin.modeladmin import ModelAdmin

from apps.courier.models.shift import CourierShifts


class CourierShiftsUI(ModelAdmin):
    """
    the user admin page
    """
    list_display = [
        "id",
        "courier",
        "organization",
        "start_time",
        "end_time",
        "is_active",
        "created_at",
        "updated_at",
    ]


admin.site.register(CourierShifts, CourierShiftsUI)
