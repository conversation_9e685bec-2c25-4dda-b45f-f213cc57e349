"""
the order create helper
"""
from django.core.cache import cache

from apps.iiko.providers.iiko.http.request import Order
from apps.iiko.providers.iiko.http.client import client, IIKOClientAPI
from apps.iiko.providers.iiko.http.response import WebHookSettingResponseBody

from apps.order.exception import TerminalDisabledError
from apps.organization.models.terminal import TerminalGroup
from apps.organization.models.organtzation import Organization


from apps.order.helper.ping import ping


class OrderWithProviderHelper:
    """
    The order create helper class
    """
    def __init__(self, provider: IIKOClientAPI):
        self.client = provider

    def create_delivery(self, organization_id, terminal_group_id, order: Order):
        """
        create order
        """
        return self.client.delivery.order_create_without_ghost(
            organization_id=organization_id,
            terminal_group_id=terminal_group_id,
            order=order,
            headers=client.variables.get_headers()
        )

    def update_deliver_order_status(self, organization_id: str, order_id: str, delivery_status: str):
        """
        update order status
        """
        return self.client.delivery.update_order_delivery_status(
            organization_id=organization_id,
            order_id=order_id,
            delivery_status=delivery_status,
            headers=client.variables.get_headers()
        )

    def is_can_create_order(self, organization: Organization):
        """
        Check if user can create an order, with caching.
        """
        cache_key = f"is_can_create_order_{organization.id}"
        cache_timeout = 20

        cached_result = cache.get(cache_key)
        if cached_result is not None:
            return cached_result

        is_can_create: bool = all([
            # self.check_webhook_alive(organization),
            self.check_is_terminal_alive(organization),
        ])

        cache.set(cache_key, is_can_create, cache_timeout)

        return is_can_create

    def check_webhook_alive(self, organization: Organization):
        """
        check if webhook is alive
        """
        result: WebHookSettingResponseBody = client.webhook.settings(
            organization_id=organization.external_id,
            headers=client.variables.get_headers()
        )
        host = result.webHooksUri
        return ping(host=host)

    def check_is_terminal_alive(self, organization: Organization):
        terminal_group = TerminalGroup.get_active_terminal_group(organization.id)
        if not terminal_group:
            raise TerminalDisabledError(f"Can't create order for organization {organization}, terminal group not found")

        results = self.client.terminal_group.is_alive(
            organization_id=str(organization.external_id),
            terminal_group_id=str(terminal_group.external_id),
            headers=client.variables.get_headers()
        )

        for result in results.isAliveStatus:
            if result.organizationId == organization.external_id and result.terminalGroupId == terminal_group.external_id: # noqa
                if result.isAlive:
                    return True

        raise TerminalDisabledError(f"Can't create order for organization {organization}, terminal disabled")


order_with_provider_helper = OrderWithProviderHelper(provider=client)
