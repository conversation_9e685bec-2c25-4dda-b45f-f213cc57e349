"""
Bot message sending views
"""
import logging
from typing import List

from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, MultiPartParser

from apps.bot.models.telegram import TelegramUser
from apps.operations.permissions import IsMarketolog
from apps.bot.serializers.message import MessageSerializer
from apps.bot.services.storage_service import StorageService
from apps.bot.tasks.send_bot_message import send_bot_message_task


logger = logging.getLogger(__name__)


class SendBotMessageView(APIView):
    """
    API View for sending messages to all active Telegram users.
    Only marketologs are allowed to send messages.
    """

    parser_classes = (<PERSON>PartParser, FormParser)
    permission_classes = [IsAuthenticated, IsMarketolog]
    serializer_class = MessageSerializer

    def post(self, request, *args, **kwargs) -> Response:
        """
        Handle POST request to send message to all active users.
        Only accessible by marketologs.

        Returns:
            Response: API response with status and recipient count
        """
        serializer = self.serializer_class(data=request.data)
        if not serializer.is_valid():
            return Response(
                serializer.errors,
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            self.process(
                title=serializer.validated_data.get('title', ''),
                media_file=serializer.validated_data.get('media_file'),
                message=serializer.validated_data.get('message', '')
            )

            return Response()

        except Exception as e:
            logger.error(
                f"Failed to process message sending by {request.user.name}: {str(e)}"
            )
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @staticmethod
    def process(title, media_file=None, message=None, notification_id=None):
        """
        Process message sending
        """
        active_users = SendBotMessageView._get_active_users()
        if not active_users:
            return SendBotMessageView._no_users_response()

        media_path = None
        if media_file:
            media_path = StorageService.save_file(
                media_file.read(),
                media_file.name
            )

        try:
            tasks = SendBotMessageView._queue_tasks(
                users=active_users,
                media_path=media_path,
                title=title,
                message=message,
                notification_id=notification_id
            )

            return {
                'total_recipients': len(tasks)
            }

        except Exception as e:
            if media_path:
                StorageService.cleanup_file(media_path, "view error")
            raise e

    @staticmethod
    def _get_active_users() -> List[TelegramUser]:
        """Get list of active Telegram users"""
        return list(TelegramUser.objects.filter(is_active=True))

    @staticmethod
    def _no_users_response() -> Response:
        """Generate response for no active users case"""
        return Response(
            {'error': 'No active telegram users found'},
            status=status.HTTP_404_NOT_FOUND
        )

    @staticmethod
    def _queue_tasks(
        users: List[TelegramUser], media_path: str,
        title: str, message: str, notification_id: str
    ) -> List:
        """
        Queue tasks for sending messages

        Args:
            users: List of users to send to
            media_path: Path to media file in storage
            message: Optional message text

        Returns:
            List: List of queued tasks
        """
        tasks = []
        total_users = len(users)

        for i, user in enumerate(users, 1):
            is_last = (i == total_users)
            task = send_bot_message_task.delay(
                chat_id=str(user.id),
                message=message,
                media_path=media_path,
                title=title,
                is_last=is_last,
                notification_id=notification_id
            )
            tasks.append(task)

        return tasks
