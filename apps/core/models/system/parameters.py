"""
Defines the SystemParameter model for storing system-wide configuration parameters.
"""

from django.db import models
from apps.core import managers
from apps.core.enums import SystemParamsEnum
from apps.iiko.typing import system as system_typing


class SystemParameter(models.Model):
    """
    Model representing a system parameter.

    Attributes:
        name (str): The unique name of the parameter.
        value (str): The value of the parameter.
        is_enabled (bool): Flag indicating if the parameter is enabled.
        description (str): Optional description of the parameter.
    """

    class Meta:
        """
        Metadata options for the SystemParameter model.

        Attributes:
            ordering (list): Default ordering of the model's records.
            db_table (str): The database table name for the model.
        """
        ordering = ["id"]
        db_table = 'sys_params'

    name = models.CharField(
        max_length=50,
        unique=True,
        choices=SystemParamsEnum.choices(),
        help_text="Unique name for the system parameter"
    )
    value = models.Char<PERSON>ield(
        max_length=255,
        help_text="Value of the system parameter"
    )
    is_enabled = models.BooleanField(
        default=False,
        help_text="Flag to indicate if the parameter is enabled"
    )
    description = models.TextField(
        blank=True,
        help_text="Optional description for the parameter"
    )

    def __str__(self):
        """
        String representation of the SystemParameter instance.

        Returns:
            str: The name of the system parameter.
        """
        return str(self.name)

    objects = managers.SystemParameterManager()

    @classmethod
    def get_sys_params(cls, key) -> system_typing.SystemParameter:
        """
        Retrieve the system parameter value from cache or database.

        Args:
            key (str): The unique name of the system parameter to retrieve.

        Returns:
            system_typing.SystemParameter: The system parameter object.
        """
        return cls.objects.get_sys_params(key)
