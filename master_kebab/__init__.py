"""
initialize project.
"""
import os

from datetime import <PERSON><PERSON><PERSON>

from celery import Celery
from celery.schedules import crontab

from master_kebab.settings import default


os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'master_kebab.settings')

celery_app = Celery('master_kebab')

celery_app.config_from_object('django.conf:settings', namespace='CELERY')

celery_app.autodiscover_tasks()

celery_app.conf.task_default_queue = 'default'
celery_app.conf.broker_url = default.env('CELERY_BROKER_URL')
celery_app.conf.result_backend = default.env('CELERY_RESULT_BACKEND')
celery_app.conf.broker_connection_retry_on_startup = True
celery_app.conf.accept_content = ['pickle', 'json']
celery_app.conf.ignore_results = True


celery_app.conf.task_routes = {
    "apps.bot.tasks.send_bot_message.send_bot_message_task": {"queue": "bot"},
    "apps.bot.tasks.message.send_message_task": {"queue": "bot"},
    "apps.bot.tasks.clean_up.cleanup_old_messages": {"queue": "bot"},
    "apps.bot.tasks.delete.delete_message_task": {"queue": "bot"},
    "apps.sms.tasks.send.send_sms_message_task": {"queue": "sms"},
    "apps.iiko.tasks.token.update_token_task": {"queue": "iiko"},
    "apps.iiko.tasks.delivery.confirm.confirm_task": {"queue": "iiko"},
    "apps.order.tasks.timeout.mark_order_as_timeout_task": {"queue": "order"},
    "apps.iiko.tasks.polling.update_categories_and_products_task": {"queue": "iiko"},
    "apps.iiko.tasks.polling.create_or_update_group_task": {"queue": "iiko"},
    "apps.user.tasks.state.send_notification_for_state_task": {"queue": "user"},
    "apps.order.tasks.find_courier.reassign_order_if_no_response_task": {"queue": "delivery"},
    "apps.organization.tasks.sync.organization.update_organization": {"queue": "iiko"},
    "apps.organization.tasks.sync.terminal.update_terminal_group": {"queue": "iiko"},
    "apps.courier.tasks.notify.send_notification_task": {"queue": "delivery"},
    "apps.iiko.tasks.update.process_update_event_task": {"queue": "iiko_updates"},
    "apps.payment.tasks.update_payment_methods": {"queue": "iiko_updates"},
    "apps.payment.tasks.send_invoice": {"queue": "delivery"},
    "apps.core.tasks.create_database_dump_task": {"queue": "core"},
    "apps.user.tasks.create.create_telegram_user_task": {"queue": "user"},
    "map.reset_daily_api_limits": {"queue": "map"},
}

celery_app.conf.beat_schedule = {
    # 'state_notification': {
    #     'task': "apps.user.tasks.state.send_notification_for_state_task",
    #     'schedule': 600,  # sec
    # },
    "update-iiko-token": {
        'task': "apps.iiko.tasks.token.update_token_task",
        'schedule': timedelta(seconds=60),
    },
    "mark-as-timeout-orders": {
        'task': "apps.order.tasks.timeout.mark_order_as_timeout_task",
        'schedule': timedelta(minutes=10),
    },
    "updating-payment-methods": {
        'task': "apps.payment.tasks.update_payment_methods",
        'schedule': timedelta(minutes=10),
    },
    'cleanup-old-messages-every-5-minutes': {
        'task': 'apps.bot.tasks.clean_up.cleanup_old_messages',
        'schedule': timedelta(minutes=5)
    },
    "update-organization": {
        'task': "apps.organization.tasks.sync.organization.update_organization",
        'schedule': timedelta(minutes=10),
    },
    "update-terminal-group": {
        'task': "apps.organization.tasks.sync.terminal.update_terminal_group",
        'schedule': timedelta(minutes=10),
    },
    "database-dump": {
        'task': "apps.core.tasks.create_database_dump_task",
        'schedule': crontab(hour=6, minute=0),  # every day at 6:00 AM
    },
    "reset-api-limits": {
        'task': "map.reset_daily_api_limits",
        'schedule': crontab(hour=0, minute=30),  # every day at 12:30 AM (24.5 hours cycle)
    },
    "monitor-api-usage": {
        'task': "map.monitor_api_usage",
        'schedule': crontab(minute='*/30'),  # every 30 minutes
    },
}
