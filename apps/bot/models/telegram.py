"""
customer model
"""
from datetime import timedelta

from django.db import models
from django.utils import timezone

from apps.user.models.user import Users
from apps.core.models.base import BaseModel
from apps.user.enum.state import StateEnums


class TelegramUser(BaseModel):
    """
    the customer model class.
    """
    id = models.BigIntegerField(primary_key=True)
    user = models.OneToOneField(Users, on_delete=models.CASCADE, null=True, blank=True)
    username = models.Char<PERSON>ield(max_length=200,  null=True)
    first_name = models.Char<PERSON>ield(max_length=200,  null=True)
    last_name = models.CharField(max_length=200, null=True, blank=True)
    is_active = models.BooleanField(default=False)
    lang = models.Char<PERSON>ield(max_length=20, default="ru")
    phone = models.CharField(max_length=16, null=True, blank=True)
    latitude = models.FloatField(null=True, blank=True)
    longitude = models.FloatField(null=True, blank=True)
    is_state_notified = models.<PERSON><PERSON>anField(default=False)
    state = models.CharField(
        max_length=50,
        choices=StateEnums.choices(),
        default=StateEnums.NOT_STARTED.value,
    )

    class Meta:
        """
        the meta fields
        """
        db_table = 'telegram_user'
        verbose_name = 'Telegram User'
        verbose_name_plural = 'Telegram Users'
        ordering = ["-updated_at"]

    def __str__(self):
        return str(self.username)

    @classmethod
    def get_by_chat_id(cls, chat_id):
        """
        get telegram user by chat id
        """
        return cls.objects.get(id=chat_id, is_active=True)

    @classmethod
    def get_by_user_id(cls, user_id):
        """
        get chat id by user id
        """
        return cls.objects.get(user_id=user_id)

    @classmethod
    def check_exsists(cls, chat_id):
        """
        check if the telegram user exists
        """
        return cls.objects.filter(id=chat_id, is_active=True).exists()

    @classmethod
    def check_user_exists(cls, chat_id) -> bool:
        """
        check if the user exists
        """
        try:
            telegram_user = cls.objects.get(id=chat_id, is_active=True)

        except cls.DoesNotExist:
            return False

        return telegram_user.user_id is not None

    @classmethod
    def delete_by_chat_id(cls, chat_id):
        """
        delete telegram user by chat id
        """
        cls.objects.filter(id=chat_id).delete()

    @classmethod
    def get_pending_states(cls):
        """
        get pending states that were last updated more than x minutes ago
        """
        interval = timezone.now() - timedelta(minutes=10)
        return cls.objects.filter(
            state__in=[
                StateEnums.GET_CONTACT.value,
                StateEnums.GET_LOCATION.value,
                StateEnums.ON_START.value
            ],
            is_active=True,
            is_state_notified=False,
            updated_at__lt=interval
        )

    @classmethod
    def create(
        cls,
        id,
        username,
        first_name,
        last_name,
        is_active=True,
        state="on_start",
        is_state_notified=False
    ):
        """
        create new telegram user
        """
        cls.objects.get_or_create(
            id=id,
            defaults={
                'username': username,
                'first_name': first_name,
                'last_name': last_name,
                'is_active': is_active,
                'state': state,
                'is_state_notified': is_state_notified
            }
        )

    @classmethod
    def get_user_by_chat_id(cls, chat_id):
        """
        get user object with chat_id
        """
        return cls.objects.get(id=chat_id, is_active=True)
