"""
This module defines the data structures for handling HTTP responses related to address
information in the iiko system. It uses Pydantic for data validation and serialization.
"""

from typing import List, Optional

from pydantic import BaseModel


class Street(BaseModel):
    """
    Represents a street in the iiko system.

    Attributes:
        id (UUID4): Unique identifier for the street.
        name (str): Name of the street.
        externalRevision (int): External revision number for the street data.
        classifierId (Optional[str]): Optional classifier ID for the street.
        isDeleted (bool): Indicates whether the street has been marked as deleted.
    """
    id: str
    name: str
    externalRevision: int
    classifierId: Optional[str] = None
    isDeleted: bool


class ByCityResponseBody(BaseModel):
    """
    Represents the structure of the response received from the iiko system
    when querying for street information.

    This model encapsulates the correlation ID for request tracking and
    a list of Street objects containing detailed information about each street.

    Attributes:
        correlationId (UUID4): A unique identifier for correlating the response with the request.
        streets (List[Street]): A list of Street objects containing detailed information about each street.
    """
    correlationId: str
    streets: Optional[List[Street]] = None
