"""
init couriers helper class
"""
from django.db.models import Q
from apps.courier.models.courier import Courier
from apps.iiko.providers.iiko.enum import RoleEnum
from apps.iiko.providers.iiko.http.client import client, IIKOClientAPI


class CouriersHelper:
    """
    The couriers helper class
    """
    def __init__(self, provider: IIKOClientAPI):
        self.client = provider

    def _get_existing_external_ids(self):
        """
        Get a set of existing external IDs from the database
        """
        return set(
            Courier.objects.filter(
                Q(external_id__isnull=False) & Q(is_deleted=False)
            ).values_list('external_id', flat=True)
        )

    def _filter_couriers(self, iiko_couriers_response, existing_external_ids):
        """
        Filter out employees with isDeleted=True or existing external_id
        """
        filtered_couriers = [
            {
                'organizationId': courier['organizationId'],
                'items': [
                    item for item in courier['items']
                    if not item['isDeleted'] and item['id'] not in existing_external_ids
                ]
            }
            for courier in iiko_couriers_response.employeesWithCheckRoles
        ]

        # Remove any organizations that have no new couriers
        return [courier for courier in filtered_couriers if courier['items']]

    def get_new_couriers(self, organization_ids):
        """
        Get new couriers from iiko that are not in the database
        """
        iiko_couriers_response = self.client.employees.by_role(
            role=RoleEnum.DELIVERY_AGENT.value,
            organization_ids=organization_ids,
            headers=client.variables.get_headers()
        )

        existing_external_ids = self._get_existing_external_ids()
        filtered_couriers = self._filter_couriers(iiko_couriers_response, existing_external_ids)

        # Construct the response with filtered couriers
        response_data = {
            'correlationId': iiko_couriers_response.correlationId,
            'employeesWithCheckRoles': filtered_couriers
        }
        return response_data


# Instantiate the couriers helper
couriers_helper = CouriersHelper(provider=client)
