# Generated by Django 5.0.6 on 2024-09-06 02:31

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('order', '0009_deliveryprice_price'),
    ]

    operations = [
        migrations.AlterField(
            model_name='deliveryprice',
            name='distance_type',
            field=models.CharField(choices=[('NB', 'Nearby (0-3 km)'), ('LC', 'Local (5-10 km)'), ('DS', 'Distant (10-20 km)'), ('RM', 'Remote (20+ km)'), ('RM_PLUS', 'Remote plus (40+ km)')], default='NB', help_text='Type of delivery based on distance', max_length=8, unique=True),
        ),
    ]
