"""
the core app configuration
"""
import hashlib

from master_kebab.settings import env, SECRET_KEY
from master_kebab.settings.default import INSTALLED_APPS

INSTALLED_APPS.append("apps.iiko")

IIKO_NETWORK = env.str("IIKO_NETWORK", "https://api-ru.iiko.services")
IIKO_API_LOGIN = env.str("IIKO_API_LOGIN", "df75ce2f-5776-454b-97e4-06f2c9384404")

DELIVERY_DURATION_TIME = env.int("DELIVERY_DURATION_TIME", 40)
MENU_ID = env.int("MENU_ID", 29334)

CITY_ID = env.str("CITY_ID", "2189f314-64b4-458b-a04e-3328bf297d4c")


def generate_sha256_hash(key1: str, key2: str) -> str:
    """
    Generate a SHA-256 hash of the concatenation of two keys.

    :param key1: The first key
    :param key2: The second key
    :return: The hexadecimal representation of the SHA-256 hash of the merged keys
    :rtype: str
    """
    merged_key = key1 + key2

    hash_object = hashlib.sha256(merged_key.encode())

    merged_hash_hex = hash_object.hexdigest()

    return merged_hash_hex


IIKO_WEBHOOK_AUTH_TOKEN = generate_sha256_hash(SECRET_KEY, IIKO_API_LOGIN)
