"""
the storing fcm device
"""
from django.db import models

from apps.core.models.base import BaseModel

from apps.courier.models.courier import Courier


class FCMDevice(BaseModel):
    """
    The FCM device model.
    """
    courier = models.ForeignKey(Courier, on_delete=models.CASCADE)
    token = models.CharField(max_length=255, unique=True)

    class Meta:
        """
        The meta fields.
        """
        db_table = 'fcm_device'
        verbose_name = 'FCM device'
        verbose_name_plural = 'FCM devices'
        ordering = ['-created_at']

    @classmethod
    def create_or_update(cls, courier_id, token):
        """
        Create or update a FCM device for the given courier.

        Args:
            courier_id (int): The id of the courier.
            token (str): The token of the FCM device.
        """
        fcm_device, _ = cls.objects.get_or_create(courier_id=courier_id, defaults={"token": token})
        fcm_device.token = token
        fcm_device.save()

    @classmethod
    def get_by_courier_id(cls, courier_id):
        """
        Get a FCM device by the given courier id.

        Args:
            courier_id (int): The id of the courier.

        Returns:
            FCMDevice: The FCM device if found, otherwise None.
        """
        return cls.objects.get(courier_id=courier_id)
