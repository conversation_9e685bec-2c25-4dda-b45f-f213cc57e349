"""
the common helpers for subhelper classes
"""
from django.core.cache import cache


class CommonEventHelper:
    """
    The delivery update event helper
    """
    CACHE_TIMEOUT = 60 * 60 * 24  # 24 hours

    @classmethod
    def has_been_processed(cls, cache_key) -> bool:
        """
        Check if the event with the given external_id and status has been processed.
        """
        cache_key = f"processed_event_{cache_key}"
        return cache.get(cache_key) is not None

    @classmethod
    def mark_as_processed(cls, cache_key):
        """
        Mark the event with the given external_id and status as processed.
        """
        cache_key = f"processed_event_{cache_key}"
        cache.set(cache_key, True, cls.CACHE_TIMEOUT)
