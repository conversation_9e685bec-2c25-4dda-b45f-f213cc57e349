# Generated by Django 5.0.6 on 2024-08-20 11:29

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='PaymentMethod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('name', models.CharField(choices=[('Click', 'Click'), ('Payme', 'Payme'), ('Terminal', 'Terminal'), ('Cash', 'Cash')], default='Cash', max_length=50, unique=True)),
                ('is_enabled', models.BooleanField(default=False)),
            ],
            options={
                'db_table': 'payment_method',
            },
        ),
    ]
