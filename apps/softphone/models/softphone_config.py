"""
Softphone configuration model
"""
from django.db import models
from apps.core.models.base import BaseModel


class SoftphoneConfig(BaseModel):
    """
    Configuration settings for Softphone.Pro integration
    """
    
    name = models.CharField(max_length=100, unique=True, help_text="Configuration name")
    
    # Screen popup settings
    screen_popup_url = models.URLField(
        help_text="URL for screen popup on incoming calls",
        default="http://localhost:8000/api/v1/softphone/popup/"
    )
    
    # Customer data retrieval settings
    customer_data_url = models.URLField(
        help_text="URL for retrieving customer data",
        default="http://localhost:8000/api/v1/softphone/customer-data/"
    )

    # Call logging settings
    call_log_url = models.URLField(
        help_text="URL for logging calls",
        default="http://localhost:8000/api/v1/softphone/log-call/"
    )
    
    # Recording upload settings
    recording_upload_url = models.URLField(
        help_text="URL for uploading call recordings",
        default="http://localhost:8000/api/v1/softphone/upload-recording/"
    )
    
    # SIP settings
    default_sip_account = models.CharField(
        max_length=100, 
        null=True, 
        blank=True,
        help_text="Default SIP account to use"
    )
    
    # Auto popup settings
    enable_screen_popup = models.BooleanField(
        default=True,
        help_text="Enable automatic screen popup on incoming calls"
    )
    
    enable_call_logging = models.BooleanField(
        default=True,
        help_text="Enable automatic call logging"
    )
    
    enable_recording_upload = models.BooleanField(
        default=True,
        help_text="Enable automatic recording upload"
    )
    
    # Click-to-call settings
    click_to_call_enabled = models.BooleanField(
        default=True,
        help_text="Enable click-to-call functionality"
    )
    
    # Caller ID modification regex
    caller_id_regex = models.TextField(
        null=True,
        blank=True,
        help_text="Regular expression for caller ID modification"
    )
    
    # Additional settings
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'softphone_config'
        verbose_name = 'Softphone Configuration'
        verbose_name_plural = 'Softphone Configurations'
    
    def __str__(self):
        return f"Softphone Config: {self.name}"
    
    @classmethod
    def get_active_config(cls):
        """Get the active softphone configuration"""
        return cls.objects.filter(is_active=True).first()
    
    def get_popup_url_with_params(self, caller_id=None, did=None, direction=None):
        """Generate popup URL with parameters"""
        url = self.screen_popup_url
        params = []
        
        if caller_id:
            params.append(f"callerid={caller_id}")
        if did:
            params.append(f"did={did}")
        if direction:
            params.append(f"direction={direction}")
        
        if params:
            url += "?" + "&".join(params)
        
        return url
