"""
Navigation settings.
Icons are: https://fonts.google.com/icons
"""
from django.urls import reverse_lazy
from django.utils.translation import gettext_lazy as _


PROFILES = {
    "title": _("Profiles"),
    "separator": True,
    "items": [
        {
            "title": _("Clients"),
            "icon": "settings_accessibility",
            "link": reverse_lazy(
                "admin:user_users_changelist"
            ),
        },
        {
            "title": _("Telegram"),
            "icon": "3p",
            "link": reverse_lazy(
                "admin:bot_telegramuser_changelist"
            ),
        },
        {
            "title": _("Courier"),
            "icon": "local_shipping",
            "link": reverse_lazy(
                "admin:courier_courier_changelist"
            ),
        },
    ],
}

PAYMENTS = {
    "title": _("Payments"),
    "separator": True,
    "items": [
        {
            "title": _("Methods"),
            "icon": "account_balance_wallet",
            "link": reverse_lazy(
                "admin:payment_paymentmethod_changelist"
            ),
        },
        {
            "title": _("Transactions"),
            "icon": "paid",
            "link": reverse_lazy(
                "admin:payment_paymenttransaction_changelist"
            ),
        },
        {
            "title": _("Credentials"),
            "icon": "credit_card",
            "link": reverse_lazy(
                "admin:payment_providercredentials_changelist"
            ),
        },
    ],
}

POINTS = {
    "title": _("Points"),
    "separator": True,
    "items": [
        {
            "title": _("Location"),
            "icon": "account_balance_wallet",
            "link": reverse_lazy(
                "admin:map_point_changelist"
            ),
        },
    ],
}


SYS_PARAMS = {
    "title": _("Settings"),
    "separator": True,
    "items": [
        {
            "title": _("Params"),
            "icon": "settings",
            "link": reverse_lazy(
                "admin:core_systemparameter_changelist"
            ),
        },
    ],
}


PRODUCTS = {
    "title": _("Products"),
    "separator": True,
    "items": [
        {
            "title": _("Category"),
            "icon": "category",
            "link": reverse_lazy(
                "admin:product_category_changelist"
            ),
        },
        {
            "title": _("Products"),
            "icon": "lunch_dining",
            "link": reverse_lazy(
                "admin:product_product_changelist"
            ),
        },
        {
            "title": _("Order"),
            "icon": "orders",
            "link": reverse_lazy(
                "admin:order_order_changelist"
            ),
        },
    ],
}

ORGANIZATION = {
    "title": _("Organizations"),
    "separator": True,
    "items": [
        {
            "title": _("Organizations"),
            "icon": "business",
            "link": reverse_lazy(
                "admin:organization_organization_changelist"
            ),
        },
        {
            "title": _("Terminal Groups"),
            "icon": "device_hub",
            "link": reverse_lazy(
                "admin:organization_terminalgroup_changelist"
            ),
        },
    ],
}


NAVIGATION = [
    PROFILES,
    PAYMENTS,
    PRODUCTS,
    POINTS,
    ORGANIZATION,
    SYS_PARAMS,
]
