# Generated by Django 5.0.6 on 2024-08-20 11:29

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=150)),
                ('title_uz', models.CharField(max_length=150, null=True)),
                ('title_ru', models.CharField(max_length=150, null=True)),
                ('title_en', models.CharField(max_length=150, null=True)),
                ('image_url', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('external_id', models.Char<PERSON>ield(db_index=True, editable=False, unique=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'db_table': 'category',
            },
        ),
        migrations.CreateModel(
            name='OrderedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=150)),
                ('title_uz', models.CharField(max_length=150, null=True)),
                ('title_ru', models.CharField(max_length=150, null=True)),
                ('title_en', models.CharField(max_length=150, null=True)),
                ('image_url', models.CharField(max_length=255, null=True)),
                ('product_type', models.CharField(choices=[('DISH', 'Dish'), ('Modifier', 'Modifier')], default='DISH', max_length=50)),
                ('external_id', models.CharField(blank=True, null=True)),
            ],
            options={
                'db_table': 'ordered_products',
            },
        ),
        migrations.CreateModel(
            name='OrderedProductAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('price', models.FloatField()),
                ('size', models.CharField()),
                ('external_id', models.CharField(blank=True, null=True)),
                ('size_id', models.CharField(blank=True, null=True)),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='product.orderedproduct')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('title', models.CharField(max_length=150)),
                ('title_uz', models.CharField(max_length=150, null=True)),
                ('title_ru', models.CharField(max_length=150, null=True)),
                ('title_en', models.CharField(max_length=150, null=True)),
                ('image_url', models.ImageField(null=True, upload_to='products', validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpeg', 'jpg', 'png'])])),
                ('description', models.CharField(max_length=500, null=True)),
                ('description_uz', models.CharField(max_length=500, null=True)),
                ('description_ru', models.CharField(max_length=500, null=True)),
                ('description_en', models.CharField(max_length=500, null=True)),
                ('external_id', models.CharField(blank=True, null=True)),
                ('product_type', models.CharField(choices=[('DISH', 'Dish'), ('Modifier', 'Modifier')], default='DISH', max_length=50)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='product.category')),
            ],
            options={
                'db_table': 'product',
            },
        ),
        migrations.CreateModel(
            name='ProductAttribute',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('description_uz', models.TextField(blank=True, null=True)),
                ('description_ru', models.TextField(blank=True, null=True)),
                ('description_en', models.TextField(blank=True, null=True)),
                ('price', models.FloatField()),
                ('size', models.CharField(default='Default', max_length=100, verbose_name='Size name')),
                ('external_id', models.CharField(blank=True, null=True, verbose_name='Item ID')),
                ('organization_external_id', models.CharField(blank=True, null=True)),
                ('sku', models.CharField(blank=True, max_length=30, null=True)),
                ('is_default', models.BooleanField(default=False)),
                ('size_id', models.CharField(blank=True, max_length=255, null=True)),
                ('measure_unit_type', models.CharField(blank=True, max_length=10, null=True)),
                ('size_code', models.CharField(blank=True, max_length=15, null=True)),
                ('product', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='product.product')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
