"""
the orders-create view
"""
import logging

from apps.loyalty.service import LoyaltyService
from apps.user.models.user import detect_initiator
from apps.order.models.order_detail import init_order_detail
from apps.order.typing.order import OrderCreateSerializer

# pylint: disable=E0611

from apps.order.views.order.create.base import BaseOrderCreateAPIView


logger = logging.getLogger(__name__)


class OrderCreateAPIView(BaseOrderCreateAPIView):
    """
    The order view for authenticated users
    """
    def post(self, request, *args, **kwargs):
        """
        the post method for creating an order.
        """
        logger.info(f"order create client with request body: {request.data}")

        try:
            initiator = detect_initiator(request)
        except Exception as exc:
            self.logger.error(f"Error during detecting initiator: {exc}")
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"missed or invalid X-Initiator was sent: {request.user}",
                notify_admin=True,
            )

        return super().post(request, initiator, user=request.user)

    def get_serializer(self, data):
        return OrderCreateSerializer(**data)

    def get_order_detail(self, data, user, chat_id):
        # Get promo_code from data or from cache if not provided
        promo_code = getattr(data.order_detail, 'promo_code', None)

        # If promo_code is not provided and user is authenticated, try to get it from cache
        if not promo_code and user:
            cached_promo = LoyaltyService.get_cached_promo_code(user.id)
            if cached_promo:
                promo_code = cached_promo
                logger.info(f"Using cached promo code {promo_code} for user {user.id}")

        return init_order_detail(
            name=data.order_detail.name,
            phone=data.order_detail.phone,
            delivery_type=data.order_detail.delivery_type,
            delivery_address=data.order_detail.delivery_address,
            entrance=data.order_detail.entrance,
            door_code=data.order_detail.door_code,
            floor=data.order_detail.floor,
            comment=data.order_detail.comment,
            promo_code=promo_code,
            payment_method=data.order_detail.payment_method,
            latitude=data.order_detail.location.lat,
            longitude=data.order_detail.location.lng,
            complete_before=data.order_detail.complete_before,
            subaddress=data.order_detail.subaddress,
            street_id=data.order_detail.street_id,
            delivery_cost_id=data.order_detail.delivery_cost_id,
            payment_phone=data.order_detail.payment_phone,
            house=data.order_detail.house,
        )
