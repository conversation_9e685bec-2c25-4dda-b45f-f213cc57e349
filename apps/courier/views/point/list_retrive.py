"""
This module defines the viewset for the CourierPoint model, providing
CRUD operations via a RESTful API.

The viewset supports:
- `list`: Retrieve a list of all CourierPoint instances.
- `retrieve`: Retrieve a specific CourierPoint instance by its ID.

It is registered with a router to expose these endpoints.
"""

from rest_framework import viewsets
from rest_framework.exceptions import PermissionDenied

from apps.courier.models import CourierPoint
from apps.courier.serializers import CourierPointSerializer
from apps.operations.permissions.dispatcher import IsAdminOrDispatcherOrManager


class CourierPointViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for managing CourierPoint instances with read-only access.

    Actions:
        list: Retrieve a list of all CourierPoint instances.
        retrieve: Retrieve a specific CourierPoint instance by its ID.

    Attributes:
        queryset (QuerySet): The queryset of CourierPoint instances.
        serializer_class (Type[Serializer]):
            The serializer class used to
            convert CourierPoint data to
            and from JSON.
    """

    queryset = CourierPoint.objects.all()
    serializer_class = CourierPointSerializer
    permission_classes = [IsAdminOrDispatcherOrManager]

    def get_queryset(self):
        """
        Restrict queryset to ensure that only 'list' and 'retrieve' actions
        are available and filter by the user's organization if necessary.
        """
        user = self.request.user

        # Check if the user is a dispatcher or manager
        if user.role in ['dispatcher', 'manager']:
            # Filter points by the user's organization
            return CourierPoint.objects.filter(courier__organization=user.organization)
        else:
            # For other roles, return all CourierPoints
            return super().get_queryset()

    def retrieve(self, request, *args, **kwargs):
        """
        Retrieve a specific CourierPoint instance by its ID.
        """
        # Get the instance
        instance = self.get_object()
        # Check if the instance belongs to the user's organization
        if request.user.role in ['dispatcher', 'manager'] and instance.courier.organization != request.user.organization: # noqa
            raise PermissionDenied("You do not have permission to access this resource.")
        return super().retrieve(request, *args, **kwargs)
