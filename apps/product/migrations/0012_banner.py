# Generated by Django 5.0.6 on 2024-11-15 20:53

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0011_itemmodifiergroups_size_id_alter_product_image_url"),
    ]

    operations = [
        migrations.CreateModel(
            name="Banner",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("name", models.CharField(max_length=100)),
                (
                    "image_url",
                    models.ImageField(
                        null=True,
                        upload_to="banners",
                        validators=[
                            django.core.validators.FileExtensionValidator(
                                allowed_extensions=["jpeg", "jpg", "png", "webp"]
                            )
                        ],
                    ),
                ),
                ("ad_link", models.URLField()),
                ("position", models.IntegerField()),
            ],
            options={
                "db_table": "banner",
            },
        ),
    ]
