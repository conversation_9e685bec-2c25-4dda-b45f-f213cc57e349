"""
This module provides services related to payment processing.
"""
import uuid
import logging

import requests

from django.conf import settings
from django.utils import timezone

from rest_framework.exceptions import NotFound, ValidationError
from rest_framework_simplejwt.tokens import RefreshToken


from apps.user.models import Users
from apps.order.models import Order
from apps.core.views import ServiceBaseView
from apps.payment.models.method import PaymentMethod
from apps.iiko.providers.iiko.http.client import client
from apps.payment.enums.method import PaymentMethodEnum
from apps.payment.providers.client import ProviderFactory
from apps.payment.models.superapp import ClickSuperAppUser
from apps.organization.models.organtzation import Organization
from apps.payment.models.credentials import ProviderCredentials


logger = logging.getLogger(__name__)


class PaymentService:
    """
    The PaymentService class provides methods to handle payment-related operations.
    """
    @staticmethod
    def create_payment(order_id: int) -> str:
        """
        Creates a payment link for the given order ID.

        Args:
            order_id (int): The ID of the order for which to create a payment link.

        Returns:
            str: A string representing the generated payment link.
        """
        order = Order.get_by_id(order_id)

        if order.order_detail.payment_method not in [
            PaymentMethodEnum.PAYME,
            PaymentMethodEnum.CLICK,
        ]:
            error_message = f"Payment method '{order.order_detail.payment_method}' is not supported."
            raise NotImplementedError(error_message)

        provider = ProviderFactory.create_provider(
            provider_type=order.order_detail.payment_method,
            organization_id=order.organization_id
        )

        paylink = provider.generate_pay_link(
            order_id=order.id,
            amount=int(order.total_cost),
            return_url="https://master-kebab.uz"
        )
        # Invoice.create(order_id=order_id, paylink=paylink) disable for future updates
        return paylink

    @staticmethod
    def create_superapp_payment(order_id: int) -> str:
        """
        Creates a payment link for Click SuperApp for the given order ID.

        Args:
            order_id (int): The ID of the order for which to create a payment link.

        Returns:
            str: A string representing the generated payment link for Click SuperApp.
        """
        order = Order.get_by_id(order_id)

        if order.order_detail.payment_method != PaymentMethodEnum.CLICK:
            error_message = f"Payment method '{order.order_detail.payment_method}' is not supported for SuperApp."
            raise NotImplementedError(error_message)

        provider = ProviderFactory.create_provider(
            provider_type=order.order_detail.payment_method,
            organization_id=order.organization_id
        )

        # Check if the provider has the generate_superapp_pay_link method
        if not hasattr(provider, 'generate_superapp_pay_link'):
            error_message = "Provider does not support SuperApp payments."
            raise NotImplementedError(error_message)

        paylink = provider.generate_superapp_pay_link(
            order_id=order.id,
            amount=int(order.total_cost),
            return_url="https://master-kebab.uz",
            auth=True,
            hide_cross=True
        )

        return paylink

    @staticmethod
    def update_payment_methods():
        for org in Organization.get_active_organizations():
            resp = client.payment.get_payment_types(
                organization_id=org.external_id,
                headers=client.variables.get_headers()
            )

            for payment_method in resp.paymentTypes:
                if payment_method.isDeleted:
                    PaymentMethod.delete_by_code(
                        organization_id=org.external_id,
                        code=payment_method.code
                    )
                    logger.info(f"Deleted payment method {payment_method.name}")
                    continue

                payment_method_obj, created = PaymentMethod.update_or_create(
                    organization_id=org.id,
                    name=payment_method.name,
                    payment_type_kind=payment_method.paymentTypeKind,
                    code=payment_method.code,
                    external_id=payment_method.id
                )
                if created:
                    logger.info(f"Created payment method {payment_method_obj}")
                else:
                    logger.info(f"Payment method {payment_method_obj} updated")

    @staticmethod
    def has_key(key) -> bool:
        """
        Checks if a specific key exists in the payment system.

        Args:
            key (str): The key to check.

        Returns:
            bool: True if the key exists, False otherwise.
        """
        return ProviderCredentials.has_key(key)

    @staticmethod
    def mark_as_approved(order: Order):
        """
        Marks the given order as approved.
        """
        if not order.is_created():
            logger.error("Order is not in a state to be approved.")
            return

        order.mark_as_approved(is_operator=False)
        message = f"✅ Order has been paid successfully order_id: {order.id}"

        service_base_view = ServiceBaseView()
        service_base_view.send_notification(message)

    @staticmethod
    def update_payment_method_status(payment_method_id, is_enabled):
        """
        Update the status of a payment method to enable or disable it.

        Args:
            payment_method_id (int): The ID of the payment method to update.
            is_enabled (bool): The new status to set for the payment method.

        Returns:
            PaymentMethod: The updated payment method instance.

        Raises:
            NotFound: If the payment method does not exist.
            ValidationError: If the input data is invalid.
        """
        try:
            payment_method = PaymentMethod.objects.get(id=payment_method_id)
        except PaymentMethod.DoesNotExist as exc:
            raise NotFound('Payment method not found.') from exc

        if not isinstance(is_enabled, bool):
            raise ValidationError('Invalid value for is_enabled. Must be a boolean.')

        payment_method.is_enabled = is_enabled
        payment_method.save()
        return payment_method

    @staticmethod
    def get_user_info_from_click(web_session):
        """
        Get user information from Click API using web_session.

        Args:
            web_session (str): Click web session token

        Returns:
            dict: User information or None if failed
        """
        try:
            # API endpoint
            url = "https://api.click.uz/integration"

            # Получаем ключ из настроек
            auth_token = settings.CLICK_SUPERAPP_KEY
            headers = {
                "Content-Type": "application/json",
                "web_session": web_session,
                "Authorization": f"Bearer {auth_token}"
            }

            # Request body
            payload = {
                "jsonrpc": "2.0",
                "method": "user.profile",
                "id": 12345
            }

            # Make the request with timeout to prevent hanging
            response = requests.post(url, headers=headers, json=payload, timeout=10)

            # Log the response for debugging
            logger.info(f"Click API response status: {response.status_code}")
            logger.info(f"Click API response: {response.text}")

            # Check if the request was successful
            if response.status_code == 200:
                data = response.json()

                # Check if the response contains user information
                if "result" in data:
                    return {
                        "client_id": data["result"].get("client_id"),
                        "name": data["result"].get("name", ""),
                        "surname": data["result"].get("surname", ""),
                        "patronym": data["result"].get("patronym", ""),
                        "gender": data["result"].get("gender", ""),
                        "phone_number": data["result"].get("phone_number", "")
                    }

                if "error" in data:
                    logger.error(f"Click API returned an error: {data['error']}")

                return None

            logger.error(f"Failed to get user info from Click API: {response.text}")
            return None

        except Exception as e:
            logger.error(f"Error getting user info from Click API: {e}")
            return None

    @staticmethod
    def _create_or_update_user(phone_number, full_name, user_info):
        """
        Create a new user or update an existing one based on phone number.

        Args:
            phone_number (str): User's phone number
            full_name (str): User's full name
            user_info (dict): Additional user information

        Returns:
            Users: Created or updated user object or None if failed
        """
        try:
            if not phone_number:
                logger.error("Phone number is required to create a user")
                return None

            try:
                # Try to get existing user by phone
                user = Users.get_by_phone(phone_number)

                # Update name if provided
                if full_name and user.name != full_name:
                    user.name = full_name
                    user.save()

                # Update gender if provided
                if user_info.get("gender") and hasattr(user, "gender"):
                    user.gender = user_info.get("gender")
                    user.save()

            except Users.DoesNotExist:
                # Create a new user
                user = Users.objects.create(
                    phone=phone_number,
                    name=full_name,
                    role='client',
                    is_verified=True
                )

                # Set gender if provided and the model has this field
                if user_info.get("gender") and hasattr(user, "gender"):
                    user.gender = user_info.get("gender")
                    user.save()

            return user

        except Exception as e:
            logger.error(f"Error creating or updating user: {e}")
            return None

    @staticmethod
    def create_or_get_user_from_click(user_info):
        """
        Create or get a user from Click SuperApp.

        Args:
            user_info (dict): User information from Click API

        Returns:
            tuple: (ClickSuperAppUser, bool) - user and created flag
        """
        if not user_info:
            logger.error("No user information provided")
            return None, False

        client_id = str(user_info.get("client_id"))
        phone_number = user_info.get("phone_number", "")

        # Format the phone number if needed
        if phone_number and not phone_number.startswith("+"):
            phone_number = f"+{phone_number}"

        # Create full name from parts
        name_parts = []
        if user_info.get("name"):
            name_parts.append(user_info.get("name"))
        if user_info.get("surname"):
            name_parts.append(user_info.get("surname"))

        full_name = " ".join(name_parts)

        try:
            # Try to get existing Click SuperApp user
            click_user = ClickSuperAppUser.get_by_click_user_id(client_id)
            created = False

            # Update phone if provided and has changed
            if phone_number and click_user.phone != phone_number:
                click_user.phone = phone_number
                click_user.save()

                # Also update the associated user's phone
                click_user.user.phone = phone_number
                click_user.user.save()

            # Update name if provided and has changed
            if full_name and click_user.user.name != full_name:
                click_user.user.name = full_name
                click_user.user.save()

            # Update gender if provided
            if user_info.get("gender") and hasattr(click_user.user, "gender"):
                click_user.user.gender = user_info.get("gender")
                click_user.user.save()

            return click_user, created

        except ClickSuperAppUser.DoesNotExist:
            # Create a new user if one doesn't exist
            try:
                # Create or update user
                user = PaymentService._create_or_update_user(phone_number, full_name, user_info)

                if not user:
                    return None, False

                # Create Click SuperApp user
                click_user = ClickSuperAppUser.objects.create(
                    user=user,
                    click_user_id=client_id,
                    merchant_user_id=PaymentService.generate_merchant_user_id(),
                    phone=phone_number
                )

                return click_user, True

            except Exception as e:
                logger.error(f"Error creating user from Click data: {e}")
                return None, False

    @staticmethod
    def generate_merchant_user_id():
        """
        Generate a unique merchant user ID.
        """
        return str(uuid.uuid4())

    @staticmethod
    def verify_click_signature(*_):
        """
        Verify the signature from Click SuperApp.

        Returns:
            bool: Always returns True for now
        """
        # Всегда возвращаем True, так как логика проверки подписи удалена
        return True

    @staticmethod
    def create_session_for_click_user(click_user):
        """
        Create a session for a Click SuperApp user.

        Args:
            click_user (ClickSuperAppUser): Click SuperApp user

        Returns:
            dict: token dictionary
        """
        # Generate JWT token
        refresh = RefreshToken.for_user(click_user.user)

        # Add custom claims
        refresh['click_user_id'] = click_user.click_user_id
        refresh['merchant_user_id'] = click_user.merchant_user_id

        # Create token dictionary
        token_dict = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }

        # Update last login
        click_user.last_login = timezone.now()
        click_user.save()

        return token_dict

    @staticmethod
    def authenticate_click_user(web_session):
        """
        Authenticate a user with Click SuperApp.

        Args:
            web_session (str): Click web session token

        Returns:
            dict: Authentication response with tokens and user info, or None if failed
        """
        try:
            # Get user information from Click API
            user_info = PaymentService.get_user_info_from_click(web_session)

            if not user_info:
                logger.error("Failed to get user information from Click API")
                return None

            # Create or get user
            click_user, _ = PaymentService.create_or_get_user_from_click(user_info)

            if not click_user:
                logger.error("Failed to create or get user")
                return None

            # Create session and generate tokens
            token_dict = PaymentService.create_session_for_click_user(click_user)

            # Prepare response
            response_data = {
                "token": token_dict["access"],
                "refresh_token": token_dict["refresh"],
                "user": {
                    "id": click_user.user.id,
                    "phone": click_user.phone,
                    "name": click_user.user.name or "",
                    "click_user_id": click_user.click_user_id,
                    "merchant_user_id": click_user.merchant_user_id
                }
            }

            return response_data

        except Exception as e:
            logger.error(f"Error in Click SuperApp authentication: {e}")
            return None
