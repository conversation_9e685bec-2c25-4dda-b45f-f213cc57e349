"""
the initiator enumuration
"""
from enum import Enum


class SystemParamsEnum(str, Enum):
    """
    the enumeration of initiator
    """
    IIKO = "iiko"
    SEARCH_COURIER_SCHEDULER = "search_courier_scheduler"
    MAX_ORDERS_PER_DAY = "max_orders_per_day"
    IS_MINI_APP_ENABLED = "is_mini_app_enabled"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
