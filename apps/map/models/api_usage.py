"""
API Usage Tracking Module

This module provides the `ApiUsage` model for tracking daily API usage
for different map services and usage types. It enables monitoring and
enforcing daily limits for API keys.
"""

from django.db import models
from django.utils import timezone
from apps.core.models.base import BaseModel
from apps.map.models.api_keys import MapApiKey


class ApiUsage(BaseModel):
    """
    Model for tracking daily API usage for map services.
    
    This model tracks the number of API requests made per day for each
    API key and usage type combination. It helps enforce daily limits
    and provides usage statistics.
    
    Attributes:
        api_key (ForeignKey): Reference to the MapApiKey being tracked.
        date (DateField): The date for which usage is being tracked.
        usage_count (PositiveIntegerField): Number of requests made on this date.
        daily_limit (PositiveIntegerField): Maximum allowed requests per day.
    """
    
    class Meta:
        """
        Meta options for the ApiUsage model.
        """
        db_table = 'map_api_usage'
        verbose_name_plural = 'API Usage Records'
        verbose_name = 'API Usage Record'
        unique_together = ['api_key', 'date']
        indexes = [
            models.Index(fields=['api_key', 'date']),
            models.Index(fields=['date']),
        ]
    
    api_key = models.ForeignKey(
        MapApiKey,
        on_delete=models.CASCADE,
        related_name='usage_records'
    )
    date = models.DateField(default=timezone.now)
    usage_count = models.PositiveIntegerField(default=0)
    daily_limit = models.PositiveIntegerField(default=1000)
    
    def __str__(self):
        """
        Returns a string representation of the ApiUsage instance.
        
        Returns:
            str: Usage information as a string.
        """
        return f"{self.api_key.app_name} - {self.api_key.usage_type} - {self.date}: {self.usage_count}/{self.daily_limit}"
    
    @classmethod
    def get_or_create_today_usage(cls, api_key_instance):
        """
        Get or create today's usage record for the given API key.
        If it's a new day, reset the API key's limit_expired flag.

        Args:
            api_key_instance (MapApiKey): The API key instance.

        Returns:
            tuple: (ApiUsage instance, created boolean)
        """
        today = timezone.now().date()
        usage_record, created = cls.objects.get_or_create(
            api_key=api_key_instance,
            date=today,
            defaults={'daily_limit': 1000}
        )

        # If this is a new day's record and the API key was marked as limit expired,
        # reset the limit_expired flag since it's a new day
        if created and api_key_instance.limit_expired:
            api_key_instance.limit_expired = False
            api_key_instance.save(update_fields=['limit_expired'])

        return usage_record, created
    
    @classmethod
    def increment_usage(cls, api_key_instance):
        """
        Increment the usage count for today's record.
        
        Args:
            api_key_instance (MapApiKey): The API key instance.
            
        Returns:
            ApiUsage: Updated usage record.
            
        Raises:
            ValueError: If daily limit is exceeded.
        """
        usage_record, created = cls.get_or_create_today_usage(api_key_instance)
        
        if usage_record.usage_count >= usage_record.daily_limit:
            raise ValueError(f"Daily limit of {usage_record.daily_limit} requests exceeded")
        
        usage_record.usage_count += 1
        usage_record.save()
        
        return usage_record
    
    @classmethod
    def check_limit_available(cls, api_key_instance):
        """
        Check if the API key has available requests for today.
        
        Args:
            api_key_instance (MapApiKey): The API key instance.
            
        Returns:
            bool: True if requests are available, False otherwise.
        """
        usage_record, created = cls.get_or_create_today_usage(api_key_instance)
        return usage_record.usage_count < usage_record.daily_limit
    
    @classmethod
    def get_remaining_requests(cls, api_key_instance):
        """
        Get the number of remaining requests for today.
        
        Args:
            api_key_instance (MapApiKey): The API key instance.
            
        Returns:
            int: Number of remaining requests.
        """
        usage_record, _ = cls.get_or_create_today_usage(api_key_instance)
        return max(0, usage_record.daily_limit - usage_record.usage_count)
