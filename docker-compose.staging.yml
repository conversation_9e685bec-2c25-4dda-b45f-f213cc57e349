version: "3"
services:
  master-kebab-backend-staging:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-backend-staging
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - python3 manage.py migrate &&  python3 manage.py init_data && python3 manage.py createadmin && gunicorn master_kebab.wsgi:application --bind 127.0.0.1:11003
    network_mode: host
    volumes:
      - /var/www/master_kebab/static:/application/static
    env_file:
      - .env

  master-kebab-celery-beat-staging:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-celery-beat-staging
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - celery -A master_kebab beat -l INFO
    network_mode: host
    env_file:
      - .env

  master-kebab-celery-worker-staging:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-celery-worker-staging
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - celery -A master_kebab worker -Q bot,sms,iiko,iiko_updates,order,user,delivery,core --loglevel=info --concurrency=10
    network_mode: host
    env_file:
      - .env

  master-kebab-sms-service-listener-staging:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-sms-listener-staging
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - python3 manage.py run_sms_service
    network_mode: host
    env_file:
      - .env

  master-kebab-user-service-listener-staging:
    image: registry.gitlab.com/servisoft.uz/master_kebab_backend:latest
    container_name: master-kebab-user-service-listener-staging
    restart: on-failure
    command:
      - /bin/sh
      - -c
      - python3 manage.py run_user_service
    network_mode: host
    env_file:
      - .env
  