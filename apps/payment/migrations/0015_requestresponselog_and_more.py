# Generated by Django 5.0.6 on 2024-10-10 15:23

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("payment", "0014_paymentmethod_display_name_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="RequestResponseLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "method",
                    models.CharField(
                        help_text="HTTP method used (e.g., GET, POST).", max_length=10
                    ),
                ),
                (
                    "path",
                    models.Char<PERSON>ield(
                        help_text="The URL path of the request.", max_length=255
                    ),
                ),
                (
                    "headers",
                    models.TextField(
                        help_text="JSON-encoded string of the request headers."
                    ),
                ),
                (
                    "body",
                    models.TextField(
                        blank=True, help_text="The request body (optional).", null=True
                    ),
                ),
                (
                    "client_ip",
                    models.GenericIPAddressField(
                        help_text="IP address of the client making the request."
                    ),
                ),
                (
                    "response_status",
                    models.Integer<PERSON>ield(
                        help_text="HTTP status code returned in the response."
                    ),
                ),
                (
                    "response_body",
                    models.TextField(
                        blank=True,
                        help_text="The body of the response (optional).",
                        null=True,
                    ),
                ),
                (
                    "duration",
                    models.FloatField(
                        blank=True,
                        help_text="Time in seconds taken to process the request.",
                        null=True,
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        auto_now_add=True,
                        help_text="The time when the request was processed.",
                    ),
                ),
            ],
        ),
        migrations.AlterField(
            model_name="providercredentials",
            name="payment_method",
            field=models.CharField(
                choices=[("CLICK", "Click"), ("PAYME", "Payme"), ("CASH", "Cash")],
                db_index=True,
                max_length=20,
                verbose_name="Payment Method",
            ),
        ),
    ]
