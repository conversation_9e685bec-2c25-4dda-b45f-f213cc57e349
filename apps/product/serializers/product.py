"""
The product serialization module.
"""
from rest_framework import serializers

from apps.product.models.modifier import (
    ItemModifierGroups,
    ModiferProductPrices,
    ModifierItems,
    Restrictions,
)
from apps.product.models.product import Label, Product, ProductAttribute
from apps.product.serializers.label import LabelSerializer


class RestrictionsSerializer(serializers.ModelSerializer):
    """
    Serializer for Restrictions model.
    """
    class Meta:
        """
        Meta class for RestrictionsSerializer.
        """
        model = Restrictions
        fields = '__all__'


class ModifierItemsSerializer(serializers.ModelSerializer):
    """
    Serializer for ModifierItems model.
    """
    prices = serializers.SerializerMethodField()
    restrictions = RestrictionsSerializer(read_only=True)

    class Meta:
        """
        Meta class for ModifierItemsSerializer.
        """
        model = ModifierItems
        fields = (
            'id',
            'name',
            'name_uz',
            'name_ru',
            'name_en',
            'description',
            'description_uz',
            'description_ru',
            'description_en',
            'prices',
            'item_id',
            'position',
            'restrictions'
        )

    def get_prices(self, obj):
        """
        Get the prices for the given ModifierItems instance.

        Args:
            obj (ModifierItems): The ModifierItems instance.

        Returns:
            list: A list of serialized ModiferProductPrices instances.
        """
        prices = ModiferProductPrices.objects.filter(modifer_items=obj)
        return ModiferProductPricesSerializer(prices, many=True).data


class ModiferProductPricesSerializer(serializers.ModelSerializer):
    """
    Serializer for ModiferProductPrices model.
    """
    organization_name = serializers.SerializerMethodField()

    class Meta:
        """
        Meta class for ModiferProductPricesSerializer.
        """
        model = ModiferProductPrices
        fields = (
            'price',
            'organization_name'
        )

    def get_organization_name(self, obj):
        """
        Get the organization name for the given ModiferProductPrices instance.

        Args:
            obj (ModiferProductPrices): The ModiferProductPrices instance.

        Returns:
            str: The name of the organization.
        """
        return obj.organization.name


class ItemModifierGroupsSerializer(serializers.ModelSerializer):
    """
    Serializer for ItemModifierGroups model.
    """
    items = ModifierItemsSerializer(many=True, read_only=True)
    restrictions = RestrictionsSerializer(read_only=True)

    class Meta:
        """
        Meta class for ItemModifierGroupsSerializer.
        """
        model = ItemModifierGroups
        fields = (
            'id',
            'item_group_id',
            'name',
            'name_uz',
            'name_ru',
            'name_en',
            'description',
            'description_uz',
            'description_ru',
            'description_en',
            'size_id',
            'restrictions',
            'items',
            'child_modifier_have_min_max_restrictions'
        )


class ProductAttributeSerializer(serializers.ModelSerializer):
    """
    Serializer for ProductAttribute model.
    """
    class Meta:
        """
        Meta class for ProductAttributeSerializer.
        """
        model = ProductAttribute
        fields = [
            'id',
            'price',
            'size',
            'size_ru',
            'size_uz',
            'size_en',
            'description',
            'size_id',
            'is_available',
            'is_default',
            'organization_external_id'
        ]


class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for Product model.
    """
    attributes = serializers.SerializerMethodField()
    labels = serializers.SerializerMethodField()
    recommended_products = serializers.SerializerMethodField()

    class Meta:
        """
        Meta class for ProductSerializer.
        """
        model = Product
        fields = "__all__"  # Include both attributes and labels

    def get_labels(self, obj):
        """
        Get labels associated with the product.
        """
        labels = Label.objects.filter(product=obj)
        return LabelSerializer(labels, many=True).data

    def get_recommended_products(self, obj):
        """
        Get recommended products for the given product.
        """
        recommended_products = obj.recommended_products.all()
        return ProductSerializer(recommended_products, many=True).data

    def get_attributes(self, obj):
        """
        Return the list of ProductAttribute instances with associated modificator
        for the given Product.

        Args:
            obj (Product): The Product instance.

        Returns:
            list: A list of serialized ProductAttribute instances,
                  each with its associated modificator.
        """
        # Get the attributes for the product
        attributes = ProductAttribute.objects.filter(product=obj)
        # Get all modificator attributes related to the product
        modificator_attributes = ItemModifierGroups.objects.filter(product=obj)

        # Serialize both
        attribute_serializer = ProductAttributeSerializer(attributes, many=True)
        modificator_serializer = ItemModifierGroupsSerializer(modificator_attributes, many=True)

        # Convert them into dictionaries for easy access
        attribute_data = attribute_serializer.data
        modificator_data = modificator_serializer.data

        # Create a map of modificators by size_id
        modificator_map = {}
        # A list to collect modificators that have no size_id
        empty_size_modificators = []

        for mod in modificator_data:
            size_id = mod.get("size_id")
            if size_id:
                modificator_map[size_id] = mod
            else:
                empty_size_modificators.append(mod)  # Collect modificators without size_id

        # Attach the corresponding modificator to each attribute based on size_id
        for attribute in attribute_data:
            size_id = attribute.get("size_id")

            if size_id:
                # Bind the modificator if size_id matches
                attribute["modificator"] = modificator_map.get(size_id)
            else:
                # If no size_id, bind the first empty-size modificator (or none if empty)
                attribute["modificator"] = empty_size_modificators[0] if empty_size_modificators else None

        return attribute_data


class ProductUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating Product model.
    """
    attributes = serializers.SerializerMethodField()
    labels = serializers.SerializerMethodField()

    def get_labels(self, obj):
        """
        Get labels associated with the product.
        """
        labels = Label.objects.filter(product=obj)
        return LabelSerializer(labels, many=True).data

    class Meta:
        """
        Meta class for ProductUpdateSerializer.
        """
        model = Product
        fields = "__all__"

    def get_attributes(self, obj):
        """
        Get attributes associated with the product.
        """
        attributes = ProductAttribute.objects.filter(product=obj)
        return ProductAttributeSerializer(attributes, many=True).data


class ProductStatisticsSerializer(serializers.Serializer):
    """
    Serializer for Product statistics.
    """
    product_title = serializers.CharField()
    times_sold = serializers.IntegerField()
    total_quantity = serializers.IntegerField()
    total_revenue = serializers.DecimalField(max_digits=10, decimal_places=2)
