"""
the terminal group response model
"""
from typing import List, Optional
from pydantic import BaseModel, Field


class TerminalGroupItem(BaseModel):
    """
    the terminal group item model
    """
    id: str
    organizationId: str
    name: str
    address: Optional[str] = ""
    timeZone: str


class TerminalGroup(BaseModel):
    """
    the terminal group model
    """
    organizationId: str
    items: List[TerminalGroupItem]


class TerminalGroupResponseBody(BaseModel):
    """
    the terminal group response model
    """
    correlationId: str
    terminalGroups: List[TerminalGroup]
    terminalGroupsInSleep: List[TerminalGroup] = Field(default_factory=list)


class IsAliveStatus(BaseModel):
    """
    the is alive status model
    """
    isAlive: bool
    terminalGroupId: str
    organizationId: str


class IsAliveResponseBody(BaseModel):
    """
    is alive status response model
    """
    correlationId: str
    isAliveStatus: List[IsAliveStatus]
