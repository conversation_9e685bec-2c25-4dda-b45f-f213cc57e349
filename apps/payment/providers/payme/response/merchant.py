"""
the merchant webhook responses
"""
from typing import Literal, Optional

from pydantic import BaseModel, Field

from apps.payment.providers.payme.enums import PaymeTransactionStateEnum
from apps.payment.providers.payme.utils import current_timestamp_millis


class BaseResponse(BaseModel):
    """
    Base response class with a common as_resp method
    """
    def as_resp(self):
        """
        Returning as response
        """
        return {
            "result": self.model_dump()
        }


class CheckPerformTransactionsResponse(BaseResponse):
    """
    the check perform transactions response
    """
    allow: bool

    def as_resp(self):
        """
        returning as response
        """
        return {
            "result": self.model_dump()
        }


class CreateTransactionResponse(BaseResponse):
    """
    the check perform transactions response
    """
    transaction: str
    state: Literal[
        PaymeTransactionStateEnum.CREATED,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_1,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_2
    ]
    create_time: int = Field(default_factory=current_timestamp_millis)

    def as_resp(self):
        """
        returning as response
        """
        return {
            "result": self.model_dump()
        }


class PerformTransactionResponse(BaseResponse):
    """
    the check perform transactions response
    """
    transaction: str
    state: Literal[
        PaymeTransactionStateEnum.CREATED,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_1,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_2,
    ]
    perform_time: int = Field(default_factory=current_timestamp_millis)

    def as_resp(self):
        """
        returning as response
        """
        return {
            "result": self.model_dump()
        }


class CancelTransactionResponse(BaseResponse):
    """
    the check perform transactions response
    """
    transaction: str
    state: Literal[
        PaymeTransactionStateEnum.CANCELED,
        PaymeTransactionStateEnum.CANCELLED_AFTER_WITHDRAWAL_IN_PROGRESS_1,
        PaymeTransactionStateEnum.CANCELLED_WITHDRAWAL_IN_PROGRESS_2,
        PaymeTransactionStateEnum.CREATED,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_1,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_2,
    ]
    cancel_time: int = Field(default_factory=current_timestamp_millis)

    def as_resp(self):
        """
        returning as response
        """
        return {
            "result": self.model_dump()
        }


class CheckTransactionResponse(BaseResponse):
    """
    the check perform transactions response
    """
    transaction: str
    state: Literal[
        PaymeTransactionStateEnum.CANCELED,
        PaymeTransactionStateEnum.CANCELLED_AFTER_WITHDRAWAL_IN_PROGRESS_1,
        PaymeTransactionStateEnum.CANCELLED_WITHDRAWAL_IN_PROGRESS_2,
        PaymeTransactionStateEnum.CREATED,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_1,
        PaymeTransactionStateEnum.WITHDRAWAL_IN_PROGRESS_2,
    ]
    reason: Optional[int]
    create_time: Optional[int] = Field(default_factory=current_timestamp_millis)
    perform_time: Optional[int] = Field(default_factory=current_timestamp_millis)
    cancel_time: Optional[int] = Field(default_factory=current_timestamp_millis)

    def as_resp(self):
        """
        returning as response
        """
        return {
            "result": self.model_dump()
        }


class GetStatementResponse(BaseResponse):
    """
    the check perform transactions response
    """
    transactions: list[str]

    def as_resp(self):
        """
        returning as response
        """
        return {
            "result": self.model_dump()
        }
