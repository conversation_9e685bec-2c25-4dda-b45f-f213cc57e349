"""
terminal group model
"""
from django.db import models

from apps.core.models.base import BaseModel
from apps.organization.models.organtzation import Organization


class TerminalGroup(BaseModel):
    """
    The terminal group model
    """
    name = models.CharField(max_length=255)
    external_id = models.CharField(max_length=255, unique=True, db_index=True)
    address = models.CharField(max_length=255, null=True, blank=True)
    organization = models.ForeignKey(Organization, related_name='terminal_groups', on_delete=models.CASCADE)
    in_use = models.BooleanField(default=False)

    class Meta:
        """
        The meta fields
        """
        db_table = 'terminal_group'

    def __str__(self):
        return str(self.name)

    @classmethod
    def create_terminal_group(
        cls, name, external_id, organization, address=None
    ) -> "TerminalGroup":
        """
        The class method to create a terminal group
        """
        terminal_group = cls(name=name, external_id=external_id, organization=organization, address=address)
        terminal_group.save()
        return terminal_group

    @classmethod
    def update_or_create_terminal_group(
        cls, external_id, organization_external_id, name=None, address=None
    ) -> "TerminalGroup": # noqa
        """
        The class method to update or create a terminal group
        """
        defaults = {'name': name, 'address': address}
        organization = Organization.get_by_external_id(organization_external_id)

        terminal_group, _ = cls.objects.update_or_create(
            external_id=external_id,
            organization=organization,
            defaults=defaults
        )
        return terminal_group

    @classmethod
    def get_active_terminal_group(cls, organization_id) -> "TerminalGroup":
        """
        Get active terminal groups
        """
        return cls.objects.filter(
            organization_id=organization_id,
            in_use=True
        ).first()
