"""
Bot app permissions
"""
from rest_framework.permissions import BasePermission


class IsMarketolog(BasePermission):
    """
    Permission class to check if the user is a marketolog.
    Only marketologs can send messages in the bot
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        return (
            request.user.role == 'marketolog' and
            request.user.is_active
        )
