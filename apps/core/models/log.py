"""
Log file proxy model for admin integration.

This module defines a proxy model for log files to enable integration with the Django admin interface.
"""
from apps.core.models.system.parameters import SystemParameter


class LogFile(SystemParameter):
    """
    Proxy model for log files.

    This model doesn't create a new database table but uses the SystemParameter model
    as a base for admin integration.
    """

    class Meta:
        """
        Metadata for the LogFile model.
        """
        proxy = True
        verbose_name = "Log File"
        verbose_name_plural = "Log Files"
