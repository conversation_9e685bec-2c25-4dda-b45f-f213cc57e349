# Generated by Django 5.0.6 on 2024-09-13 09:09

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0005_alter_itemmodifiergroups_restrictions_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="OrderedProductModifier",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("quantity", models.IntegerField(default=1)),
                (
                    "modifier_item",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="product.modifieritems",
                    ),
                ),
                (
                    "ordered_product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="product.orderedproduct",
                    ),
                ),
            ],
            options={
                "db_table": "ordered_product_modifiers",
            },
        ),
    ]
