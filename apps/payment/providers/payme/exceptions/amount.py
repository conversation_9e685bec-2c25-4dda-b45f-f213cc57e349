"""
the incorrect amount of payme
"""
from apps.payment.providers.payme.exceptions.base import BasePaymeException # noqa


class IncorrectAmount(BasePaymeException):
    """
    IncorrectAmount APIException
    That is raised when the amount is not incorrect.
    """
    status_code = 200
    error_code = -31001
    message = {
        'ru': 'Неверная сумма',
        'uz': "Noto'g'ri qiymat",
        'en': 'Incorrect amount',
    }
