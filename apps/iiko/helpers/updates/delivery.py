"""
The delivery update event helpers
"""
import logging
import traceback
from django.db import transaction
from django.core.cache import cache

from celery import current_app

from master_kebab.settings import ORDERS_CHANNEL

from apps.iiko.tasks import confirm_task
from apps.core.models import SystemParameter
from apps.core.enums import SystemParamsEnum
from apps.courier.models.courier import Courier
from apps.bot.tasks import send_message_task
from apps.user.enum import agent as agent_enum
from apps.user.enum.agent import AgentStatusEnums
from apps.courier.models.orders import MissedOrder
from apps.courier.models.shift import CourierShifts
from apps.iiko.providers.iiko.enum import OrderStatus
from apps.iiko.helpers.common import CommonEventHelper
from apps.order.enums.order import CourierFindingStatus
from apps.order.typing import cooking_complete as cooking_complete_typing
from apps.courier.tasks.notify import send_notification_task
from apps.order.typing import cancelled as cancelled_typing
from apps.order.models.order import get_order_by_external_id, Order
from apps.order.enums.order import OrderStatus as OrderStatusInternal
from apps.iiko.serializers import DeliveryOrderUpdate<PERSON>vent, CourierInfo
from apps.order.tasks.find_courier import reassign_order_if_no_response_task


logger = logging.getLogger(__name__)


class DeliveryUpdateEventHelper(CommonEventHelper):
    """
    The delivery update event helper
    """
    STATUS_MAP = {
        OrderStatus.UNCONFIRMED: OrderStatusInternal.UNCONFIRMED,
        OrderStatus.WAIT_COOKING: OrderStatusInternal.WAIT_COOKING,
        OrderStatus.CANCELLED: OrderStatusInternal.CANCELLED,
        OrderStatus.WAITING: OrderStatusInternal.WAITING,
        OrderStatus.ON_WAY: OrderStatusInternal.ON_WAY,
        OrderStatus.DELIVERED: OrderStatusInternal.DELIVERED,
        OrderStatus.CLOSED: OrderStatusInternal.CLOSED,
        OrderStatus.TIMEOUT:  OrderStatusInternal.TIMEOUT
    }

    @classmethod
    def process_update_event(cls, update_event: DeliveryOrderUpdateEvent):
        """
        Process delivery update event
        """
        try:
            for event in update_event.event_body:
                external_id = event.eventInfo.id
                status = event.eventInfo.order.status

                courier_info = event.eventInfo.order.courierInfo
                number = event.eventInfo.order.number
                complete_before = event.eventInfo.order.completeBefore

                cls.update_order_status(
                    external_id=external_id,
                    status=status,
                    courier_info=courier_info,
                    number=number,
                    complete_before=complete_before
                )
                cls.handle_courier_info(
                    external_id=external_id,
                    status=status,
                    courier_info=courier_info
                )

                cls.notify_status_update(external_id, status)

        except Courier.DoesNotExist:
            logger.error(f"Courier not found for external ID: {external_id}")

        except Exception as exc:
            full_trace = traceback.format_exc()

            Order.mark_as_webook_error(
                external_id=external_id,
                log=f"Error: {str(exc)}\n\nTraceback:\n{full_trace}"
            )
            raise exc

    @classmethod
    def handle_courier_info(
        cls,
        external_id, status, courier_info
    ):
        """
        Handle the courier info if available and send appropriate messages
        """
        if courier_info and courier_info.isCourierSelectedManually:
            courier_id = courier_info.courier.id
            message = (
                f"✅ Manual courier selection detected\n\n"
                f"Status: {status}\nExternal Order ID: {external_id}\n"
                f"External Courier ID: {courier_id}\nCourier Name: {courier_info.courier.name}"
            )
            send_message_task.delay(chat_id=ORDERS_CHANNEL, message=message)

    @classmethod
    def search_courier(cls, order: Order, is_auto_search=False):
        """
        Search for a new delivery agent if needed.

        Args:
            order (Order): The order object for which a courier needs to be found.
            is_auto_search (bool): Flag to indicate if the search is automatic.
        """
        key = SystemParamsEnum.SEARCH_COURIER_SCHEDULER
        sys_param = SystemParameter.get_sys_params(key)

        # Check if the courier search scheduler is enabled
        if not sys_param.is_enabled:
            logger.debug(f"Skipping search for new courier - Scheduler is disabled for order ID: {order.id}")
            return

        # Check if a courier has already been accepted for the order
        if order.is_courier_accepted():
            logger.debug(f"Skipping search for new courier - Courier already accepted for order ID: {order.id}")
            return

        if is_auto_search:
            countdown = int(sys_param.value)

            if order.is_status_in_cooking_started():  # TODO: check as property
                countdown = 1

            # Schedule a task to run in minutes using apply_async
            task = reassign_order_if_no_response_task.apply_async(
                args=[order.id],
                countdown=countdown,
            )
            order.courier_search_task_id = task.id
            order.save()
            logger.debug(f"Scheduled courier search task to run in {countdown} minutes with task ID: {task.id} for order ID: {order.id}") # noqa
            return

        # Revoke existing task if it exists
        existing_task = order.courier_search_task_id
        if existing_task:
            current_app.control.revoke(existing_task, terminate=True)
            logger.debug(f"Revoked existing courier search task ID: {existing_task} for order ID: {order.id}")

        # Start a new task immediately
        task = reassign_order_if_no_response_task.apply_async(args=[order.id])
        order.courier_search_task_id = task.id
        order.save()
        logger.debug(f"Started new courier search task with task ID: {task.id} for order ID: {order.id}")

    @classmethod
    def update_order_status(
        cls,
        external_id,
        status,
        courier_info,
        number,
        complete_before
    ):
        """
        Update the order status and manage delivery agent status if applicable
        """
        order = get_order_by_external_id(external_id)

        if not order.external_number:
            order.external_number = number

        if not order.order_detail.complete_before:
            order.order_detail.complete_before = complete_before
            order.order_detail.save()

        if status == OrderStatusInternal.UNCONFIRMED or status == OrderStatusInternal.WAIT_COOKING:
            cls.handle_status_update(order, status, external_id, courier_info)

        elif status == OrderStatusInternal.COOKING_COMPLETED:
            cls.handle_cooking_completed(order, courier_info)
        elif status == OrderStatusInternal.COOKING_STARTED:
            cls.handle_cooking_started(order, courier_info)
        elif status in cls.STATUS_MAP:
            cls.handle_status_update(order, status, external_id, courier_info)

    @classmethod
    def handle_cooking_completed(cls, order: Order, courier_info):
        """
        Handle the COOKING_COMPLETED status
        """
        order.mark_as_cooking_complete()
        if order.is_order_type_delivery():
            if courier_info is None and order.attempt_count == 0:
                with transaction.atomic():
                    cls.search_courier(order=order, is_auto_search=False)
            else:
                cls.update_agent_status(
                    courier_info,
                    agent_enum.AgentStatusEnums.WAITING, order
                )
                if order.delivery_agent:
                    message = cooking_complete_typing.OrderCookingComplete.make_message(
                        order_id=order.id
                    )
                    # Sending notification for the courier
                    send_notification_task.delay(
                        courier_id=order.delivery_agent.id,
                        message=message
                    )
                    return

    @classmethod
    @transaction.atomic
    def handle_cooking_started(cls, order: Order, courier_info):
        """
        Handle the COOKING_STARTED status with deduplication logic
        """
        deduplication_key = f"{order.id}-{courier_info}-{OrderStatusInternal.COOKING_STARTED}"
        if not cls.deduplicate(deduplication_key):
            return

        with transaction.atomic():
            order.mark_as_cooking_started()
            if order.is_order_type_delivery():
                if not courier_info and order.attempt_count == 0:
                    cls.search_courier(order=order, is_auto_search=True)

        if courier_info and courier_info.isCourierSelectedManually:
            # binding courier to order with using webhook events
            courer_external_id = courier_info.courier.id
            courier = Courier.get_by_external_id(courer_external_id)
            courier_shift = CourierShifts.get_active_shift(courier.id)

            # change delivery status to waiting
            order.mark_as_waiting(
                courier_shift=courier_shift,
                is_courier_accepted=CourierFindingStatus.FOUND.value,
            )
            # removing from courier missed orders
            MissedOrder.remove_order(
                courier_id=courier.id,
                order_id=order.id
            )
            cls.update_agent_status(courier_info, agent_enum.AgentStatusEnums.WAITING, order)

    @classmethod
    def deduplicate(cls, deduplication_key):
        """
        Deduplicate the status update based on the given key
        """
        # Assuming there's a cache system in place to store deduplication keys
        cache_key = f"deduplication:{deduplication_key}"
        if cache.get(cache_key):
            return False  # Key already exists, deduplicate
        else:
            cache.set(cache_key, True, timeout=180)  # Set key to expire in 3 minutes
            return True  # Key does not exist, allow update

    @classmethod
    def handle_status_update(cls, order: Order, status, external_id, courier_info):
        """
        Handle status updates based on the given status.
        """
        mapped_status = cls.STATUS_MAP.get(status)

        if mapped_status == OrderStatusInternal.UNCONFIRMED:
            order.mark_as_unconfirmed()
            confirm_task.delay(external_id=external_id)

        elif mapped_status == OrderStatusInternal.WAIT_COOKING:
            order.mark_as_wait_cooking()

        elif mapped_status == OrderStatusInternal.WAITING:
            cls.update_agent_status(courier_info, agent_enum.AgentStatusEnums.WAITING, order)
            order.mark_as_waiting()

        elif mapped_status == OrderStatusInternal.ON_WAY:
            cls.update_agent_status(courier_info, agent_enum.AgentStatusEnums.ON_WAY, order)
            order.mark_as_on_way()

        elif mapped_status == OrderStatusInternal.CANCELLED:
            cls.update_agent_status(courier_info, agent_enum.AgentStatusEnums.AVAILABLE, order)
            cls.handle_order_cancelled(order)
            order.mark_as_cancelled()

        elif mapped_status == OrderStatusInternal.DELIVERED:
            cls.update_agent_status(courier_info, agent_enum.AgentStatusEnums.DELIVERED, order)
            order.mark_as_delivered()

        elif mapped_status == OrderStatusInternal.CLOSED:
            cls.update_agent_status(courier_info, agent_enum.AgentStatusEnums.AVAILABLE, order)
            order.mark_as_closed()

        elif mapped_status == OrderStatusInternal.TIMEOUT:
            order.mark_as_connection_error()

    @classmethod
    def handle_order_cancelled(cls, order):
        """
        Handle the CANCELLED status
        """
        if order.delivery_agent:
            message = cancelled_typing.OrderCancelled.make_message(order_id=order.id)
            send_notification_task.delay(courier_id=order.delivery_agent.id, message=message)

    @classmethod
    def update_agent_status(cls, courier_info: CourierInfo, status, order: Order = None):
        """
        Update the status of the delivery agent
        """
        if order.delivery_agent:
            if status == OrderStatus.CLOSED:
                order.delivery_agent.status = AgentStatusEnums.AVAILABLE.value
            else:
                order.delivery_agent.status = status

            order.delivery_agent.save()

        elif order.is_order_type_delivery() and courier_info and courier_info.isCourierSelectedManually:
            courier = Courier.set_by_external_id(
                external_id=courier_info.courier.id, status=status
            )
            order.delivery_agent = courier
            order.courier_finding_status = CourierFindingStatus.FOUND
            order.save()

    @classmethod
    def notify_status_update(cls, external_id, status):
        """
        Notify about the status update via chat message
        """
        message = f"✅ Order has been updated\n\nStatus: {status}\nExternal Order ID: {external_id}"
        send_message_task.delay(chat_id=ORDERS_CHANNEL, message=message)
