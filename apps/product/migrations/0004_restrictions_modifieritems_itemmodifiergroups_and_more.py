# Generated by Django 5.0.6 on 2024-09-12 14:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organization", "0002_rename_is_closed_organization_is_alive"),
        ("product", "0003_alter_category_options_category_position_id"),
    ]

    operations = [
        migrations.CreateModel(
            name="Restrictions",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("min_quantity", models.IntegerField(default=0)),
                ("max_quantity", models.IntegerField(default=0)),
                ("free_quantity", models.IntegerField(default=0)),
                ("by_default", models.IntegerField(default=0)),
                ("hide_if_default_quantity", models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name="ModifierItems",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sku", models.CharField(max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True, null=True)),
                ("portion_weight_grams", models.IntegerField(default=0)),
                ("is_hidden", models.BooleanField(default=False)),
                ("item_id", models.CharField(max_length=255)),
                ("position", models.IntegerField(default=0)),
                ("independent_quantity", models.BooleanField(default=False)),
                ("measure_unit_type", models.CharField(default="GRAM", max_length=50)),
                ("button_image_url", models.URLField(blank=True, null=True)),
                (
                    "restrictions",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="product.restrictions",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ItemModifierGroups",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                ("can_be_divided", models.BooleanField(default=False)),
                (
                    "item_group_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
                ("is_hidden", models.BooleanField(default=False)),
                (
                    "child_modifier_have_min_max_restrictions",
                    models.BooleanField(default=False),
                ),
                ("sku", models.CharField(blank=True, max_length=255, null=True)),
                (
                    "product",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="product.product",
                    ),
                ),
                ("items", models.ManyToManyField(to="product.modifieritems")),
                (
                    "restrictions",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="product.restrictions",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ModiferProductPrices",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("price", models.FloatField(default=0)),
                (
                    "organization",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organization.organization",
                    ),
                ),
                (
                    "modifer_items",
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="product.modifieritems",
                    ),
                ),
            ],
            options={
                "verbose_name": "Modifier Product Price",
                "verbose_name_plural": "Modifier Product Prices",
                "ordering": ["organization", "modifer_items"],
                "unique_together": {("organization", "modifer_items")},
            },
        ),
    ]
