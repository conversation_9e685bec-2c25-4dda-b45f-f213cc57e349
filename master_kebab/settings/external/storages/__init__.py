"""
init minio client configuration
"""
from master_kebab.settings import env

MINIO_ENDPOINT = env.str("MINIO_ENDPOINT")
MINIO_ACCESS_KEY = env.str("MINIO_ACCESS_KEY")
MINIO_SECRET_KEY = env.str("MINIO_SECRET_KEY")
MINIO_BUCKET_NAME = env.str("MINIO_BUCKET_NAME")

DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"

AWS_ACCESS_KEY_ID = MINIO_ACCESS_KEY
AWS_SECRET_ACCESS_KEY = MINIO_SECRET_KEY
AWS_STORAGE_BUCKET_NAME = MINIO_BUCKET_NAME
AWS_S3_ENDPOINT_URL = MINIO_ENDPOINT
AWS_QUERYSTRING_AUTH = env.bool("MINIO_QUERYSTRING_AUTH", False)
