"""
the bookmarks model
"""
from typing import Tuple, Optional

from django.db import models
from django.db.models import Count

from apps.user.models.user import Users
from apps.order.models.order import Order
from apps.core.models.base import BaseModel


class Bookmark(BaseModel):
    """
    The bookmarks model class.
    """
    class Meta:
        """
        the meta fields
        """
        db_table = 'bookmarks'
        verbose_name_plural = 'bookmarks'
        verbose_name = 'Bookmark'
        ordering = ['-created_at']

    user = models.ForeignKey(Users, on_delete=models.CASCADE)
    address = models.CharField(max_length=255, null=True, blank=True)
    subaddress = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=255, null=True, blank=True)
    lat = models.FloatField(default=0)
    long = models.FloatField(default=0)
    street_id = models.Char<PERSON>ield(max_length=255, blank=True, null=True)
    orders = models.ManyToManyField(Order, related_name='bookmarked_orders', blank=True)
    is_client_bookmarked = models.Bo<PERSON>anField(default=False)

    def __str__(self):
        return f'{self.user} - {self.address}'

    @classmethod
    def get_or_update(
        cls, user, address, lat, long,
        street_id=None, subaddress=None, order=None, name=None, is_client_bookmarked=False
    ) -> Tuple["Bookmark", bool, Optional[float]]:
        """
        Attempts to find an existing bookmark within a certain distance of the given coordinates.
        If found, updates the bookmark with the provided information. If not found, creates a new bookmark.
        Returns a tuple containing the bookmark, a boolean indicating if a new bookmark was created,
        and the distance to the nearest existing bookmark (if found).
        """
        # pylint: disable=C0415
        from apps.order.utility.find_courier import haversine

        # Find existing bookmarks within 15 meters of the new point
        nearby_bookmarks = cls.objects.filter(user=user)

        for bookmark in nearby_bookmarks:
            distance = haversine(lat, long, bookmark.lat, bookmark.long)
            if distance <= 0.05:  # 50 meters in kilometers
                bookmark.address = address  # Update address if needed
                bookmark.save()
                if order:
                    bookmark.orders.add(order)
                return bookmark, False, distance

        # Create new bookmark if no nearby bookmarks found
        bookmark = cls.objects.create(
            user=user,
            address=address,
            subaddress=subaddress,
            lat=lat,
            street_id=street_id,
            name=name,
            long=long,
            is_client_bookmarked=is_client_bookmarked
        )

        if order:
            bookmark.orders.add(order)

        return bookmark, True, None

    @classmethod
    def get_most_ordered_bookmarks(cls, user):
        """
        Get the most ordered bookmarks for a user.
        """
        query = cls.objects.filter(user=user)\
            .annotate(order_count=Count('orders'))\
            .order_by('-order_count')

        return query

    @classmethod
    def update_point(cls, order_id, latitude, longitude):
        """
        Update the latitude and longitude of bookmarks associated with the given order.
        """
        order = Order.get_by_id(order_id)
        cls.objects.filter(orders=order).update(
            lat=latitude,
            long=longitude,
        )
