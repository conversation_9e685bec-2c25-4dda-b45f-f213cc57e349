"""
Module for registering translation modifiers for the ItemModifierGroups model.

This module uses the modeltranslation library to enable translation support for
the ItemModifierGroups model's name and description fields.

Classes:
    ItemModifierGroupsTranslationOptions: Translation options for the ItemModifierGroups model.
"""

from modeltranslation.translator import register, TranslationOptions
from apps.product.models.modifier import ItemModifierGroups, ModifierItems


@register(ItemModifierGroups)
class ItemModifierGroupsTranslationOptions(TranslationOptions):
    """
    Translation options for the ItemModifierGroups model.

    This class defines the fields that should be translated for the ItemModifierGroups model.
    In this case, the name and description fields are registered for translation.

    Attributes:
        fields (tuple): A tuple of field names that should be translated.
    """
    fields = ('name', 'description')


@register(ModifierItems)
class ModifierItemsTranslationOptions(TranslationOptions):
    """
    Translation options for the ModifierItems model.

    This class defines the fields that should be translated for the ModifierItems model.
    In this case, the name, description, and 'for' fields are registered for translation.

    Attributes:
        fields (tuple): A tuple of field names that should be translated.
    """
    fields = ('name', 'description')
