"""
the delivery order error event model
"""
from typing import Optional
from pydantic import BaseModel


class TerminalGroup(BaseModel):
    """
    terminal group id representing model
    """
    id: str
    name: str


class ErrorInfo(BaseModel):
    """
    error info representing model
    """
    code: str
    message: str
    description: str
    additionalData: Optional[str] = None


class EventInfo(BaseModel):
    """
    event info representing model
    """
    id: str
    posId: str
    externalNumber: Optional[str] = None
    organizationId: str
    timestamp: int
    creationStatus: str
    errorInfo: ErrorInfo
    order: Optional[str] = None


class EventBody(BaseModel):
    """
    the evet body
    """
    eventType: str
    eventTime: str
    organizationId: str
    correlationId: str
    eventInfo: EventInfo

    def is_timeout_error(self) -> bool:
        """
        Check if the order is marked as timeout
        """
        has_timeout = False

        if self.eventInfo.errorInfo.message == "Creation timeout expired, order automatically transited to error creation status": # noqa
            has_timeout = True

        return has_timeout


class DeliveryOrderErrorEvent(BaseModel):
    """
    delivery order error event model
    """
    event_body: list[EventBody]
