"""
the job application serializer
"""
from rest_framework import serializers
from apps.hr.models.job_application import JobApplication
from apps.hr.enums.job_application import StatusEnum


class JobApplicationSerializer(serializers.ModelSerializer):
    """
    the job application serializer
    """
    class Meta:
        """
        the meta fields
        """
        model = JobApplication
        fields = '__all__'
        extra_kwargs = {
            'status': {
                'required': False,
                'choices': [status.value for status in StatusEnum]
            }
        }

    def validate_status(self, value):
        """
        Validate that the status is a valid StatusEnum value
        """
        valid_statuses = [status.value for status in StatusEnum]
        if value and value not in valid_statuses:
            raise serializers.ValidationError(f"Status must be one of: {', '.join(valid_statuses)}")
        return value

    def create(self, validated_data):
        """
        create method to set default status if not provided
        """
        if 'status' not in validated_data:
            validated_data['status'] = StatusEnum.PENDING.value

        return super().create(validated_data)
