"""
the product model
"""
from django.core.validators import FileExtensionValidator
from django.db import models, transaction
from django.shortcuts import get_object_or_404

from apps.core.models.base import BaseModel
from apps.organization.models import Organization
from apps.product.enums.type import ProductType as ProductTypeEnum
from apps.product.models.category import Category
from master_kebab import settings

if settings.DEBUG:
    UPLOAD_TO = "test/products"

else:
    UPLOAD_TO = "products"


class Product(BaseModel):
    """
    Product model.
    """
    class Meta:
        """
        Meta fields.
        """
        db_table = 'product'

    title = models.CharField(max_length=150, null=True, blank=True)
    image_url = models.ImageField(
        validators=[FileExtensionValidator(
            allowed_extensions=["jpeg", "jpg", "png", "webp",]
        )],
        null=True, upload_to=UPLOAD_TO
    )
    description = models.CharField(max_length=500, null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    external_id = models.CharField(null=True, blank=True)
    product_type = models.CharField(
        max_length=50,
        choices=ProductTypeEnum.choices(),
        default=ProductTypeEnum.DISH.value,
    )
    is_top = models.BooleanField(default=False)
    recommended_products = models.ManyToManyField(
        'self',
        symmetrical=False,
        related_name='recommended_by',
        blank=True,
        verbose_name='Recommended Products'
    )

    def __str__(self) -> str:
        return str(self.title)

    @classmethod
    def get_by_external_id(cls, external_id):
        """
        get product by external id
        """
        return cls.objects.get(external_id=external_id)


class Label(BaseModel):
    """
    Label model that can be attached to product attributes.
    """
    product = models.ForeignKey(Product, related_name="products", on_delete=models.CASCADE, null=True)
    code = models.CharField(max_length=50, null=True, blank=True)
    name = models.CharField(max_length=100)


class ProductAttribute(BaseModel):
    """
    Product attributes model.
    """
    description = models.TextField(null=True, blank=True)
    price = models.FloatField()
    size = models.CharField(max_length=100, verbose_name="Size name", default="Default")  # Added max_length
    external_id = models.CharField(null=True, blank=True, verbose_name="Item ID")
    organization_external_id = models.CharField(null=True, blank=True)  # organization_id
    product = models.ForeignKey(Product, on_delete=models.CASCADE, null=True)
    sku = models.CharField(max_length=30, null=True, blank=True)
    is_default = models.BooleanField(default=False)
    size_id = models.CharField(max_length=255, null=True, blank=True)
    measure_unit_type = models.CharField(max_length=10, null=True, blank=True)
    size_code = models.CharField(max_length=15, null=True, blank=True)
    is_available = models.BooleanField(default=False)

    def __str__(self) -> str:
        return f"{self.product.title} {self.size} {self.measure_unit_type} {self.price} - UZS"

    @classmethod
    def get_organization(cls, id):
        """
        Get product attribute by external id.
        """
        product_attribute = cls.objects.get(id=id)
        return Organization.get_by_external_id(product_attribute.organization_external_id)

    @classmethod
    def by_organization_id(cls, organization_external_id):
        """
        Get product attribute by organization_external_id.
        """
        return cls.objects.filter(
            organization_external_id=organization_external_id,
            is_available=True
        ).values_list('product_id', flat=True)

    @classmethod
    def get_by_id(cls, id) -> "ProductAttribute":
        """
        getting product attribute by id
        """
        return cls.objects.get(id=id)

    @classmethod
    def get_or_update_bulk(cls, products: list[dict]):
        """
        get or update bulk products
        """
        for product in products:
            cls.get_or_update_product(product['id'], product)
        return products

    @classmethod
    def get_or_update_product(cls, id: int, defaults: dict = None):
        """
        get or update product
        """
        result = cls.objects.update_or_create(id=id, defaults=defaults)
        return result


def build_attribute(
    size: str,
    price: float,
    description: str = None,
    external_id: str = None,
    organization_external_id: str = None,
    product: Product = None,
    sku: str = None,
    is_default: bool = False,
    size_id: str = None,
    measure_unit_type: str = None,
    size_code: str = None
) -> ProductAttribute:
    """
    Build attribute.
    """
    product_attribute = ProductAttribute(
        size=size,
        price=price,
        description=description,
        external_id=external_id,
        organization_external_id=organization_external_id,
        product=product,
        sku=sku,
        is_default=is_default,
        size_id=size_id,
        measure_unit_type=measure_unit_type,
        size_code=size_code
    )
    return product_attribute


def update_products(
    external_id: str, defaults: dict = None
) -> Product:
    """
    Update or create products based on the provided data.

    Parameters:
    - external_id: UUID of the product to update or create.
    - defaults: Dictionary of default values for the product fields.

    Returns:
    - Product instance
    """
    # Update or create the product
    product, _ = Product.objects.update_or_create(
        external_id=external_id,
        defaults=defaults
    )

    return product


@transaction.atomic
def update_or_create_labels(
    product: Product,  # Product instance
    labels_data: list  # List of dictionaries, each containing 'code' and 'name' of the label
) -> None:
    """
    Update or create labels for a product.

    Parameters:
    - product: The Product instance to which the labels belong.
    - labels_data: A list of dictionaries, each containing 'code' and 'name' of a label.
    """
    for label_item in labels_data:
        _, _ = Label.objects.update_or_create(
            code=label_item['code'],
            product=product,
            defaults={'name': label_item['name']}
        )
        if label_item.get("name") == "top":
            product.is_top = True
            product.save()


def update_or_create_attributes(
    product: Product,
    attributes: list[ProductAttribute]
) -> None:
    """
    Update or create a list of product attributes.

    Parameters:
    - product: The product instance to which the attributes belong.
    - attributes: A list of dictionaries, each containing the size, price, description, external_id, and other fields of an attribute. # noqa
    """
    for attr_data in attributes:
        _, _ = ProductAttribute.objects.update_or_create(
            product=product,
            external_id=attr_data.external_id,
            organization_external_id=attr_data.organization_external_id,
            size_id=attr_data.size_id,
            defaults={
                'size': attr_data.size,
                'price': attr_data.price,
                'description': attr_data.description,
                'sku': attr_data.sku,
                'is_default': attr_data.is_default,
                'measure_unit_type': attr_data.measure_unit_type,
                'size_code': attr_data.size_code
            }
        )


def get_product_by_id(product_id: int) -> Product:
    """
    Get product by ID.
    """
    return get_object_or_404(Product, id=product_id)


def get_product_by_external_id(external_id: str) -> Product:
    """
    Get product by external ID.
    """
    return get_object_or_404(Product, external_id=external_id)
