"""
implementation of state tasks
"""
from celery import Task

from master_kebab import celery_app

from apps.core import core
from apps.organization.helper.organization import organization_helper


@celery_app.task(bind=True, acks_late=False, task_acks_on_failure_or_timeout=False)
def update_organization(self: Task):
    """
    updating organization
    """
    core.log("info", "updating organization")
    try:
        organization_helper.update_organization()

    except Exception as e:
        core.log("error", f"Error updating organization: {e}")
