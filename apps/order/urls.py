"""
the url patterns for order applications.
"""
from django.urls import path, include

from rest_framework.routers import DefaultRouter

from apps.order.views import order as order_view
from apps.order.views import bookmarks as bookmark_view
from apps.order.views import delivery as delivery_view
from apps.order.views.order.stats import OrderStatisticsAPIView
from apps.order.views.order.user_orders import (
    UserActiveOrdersAPIView, UserOrderHistoryAPIView, UserOrderDetailAPIView
)


router = DefaultRouter()


urlpatterns = [
    path('', include(router.urls)),
    path('history/', order_view.OrderListView.as_view(), name='order-list'),
    path('statistics/', OrderStatisticsAPIView.as_view(), name='order-statistics'),
    path('delivery-prices/', delivery_view.DeliveryPriceView.as_view(),
         name='order-delivery-price'),
    path('history/<int:pk>/', order_view.OrderDetailView.as_view(), name='order-detail'),
    path('create/', order_view.OrderCreateAPIView.as_view(), name='order-create'),
    path('create/<int:chat_id>/telegram/',
         order_view.OrderCreateForTelegramAPIView.as_view()),  # noqa
    path('user/active/', UserActiveOrdersAPIView.as_view(), name='user-active-orders'),
    path('user/history/', UserOrderHistoryAPIView.as_view(), name='user-order-history'),
    path('user/<int:order_id>/', UserOrderDetailAPIView.as_view(), name='user-order-detail'),  # noqa
]

urlpatterns.extend([
    path('order-take/<int:order_id>/', order_view.TakeOrderAPIView.as_view(), name='order-take'),
    path('order-status/<int:order_id>/', order_view.OrderStatusAPIView.as_view(), name='order-status'),
    path('active-order/<int:pk>/<str:user_type>/', order_view.ActiveOrderAPIView.as_view(), name='active-order'),
    path('<int:order_id>/creation-status/', order_view.OrderCreationStatusAPIView.as_view(), name='order-creation-status') # noqa
])


urlpatterns.extend([
    path('<int:order_id>/cancel/', order_view.OrderCancelAPIView.as_view(), name='order-cancel'),  # noqa
    path('<int:order_id>/accept/', order_view.OrderAcceptAPIView.as_view(), name='order-accept'), # noqa
    path('<int:order_id>/finish/', order_view.OrderFinishAPIView.as_view(), name='order-finish'),  # noqa
    path('<int:order_id>/arrived/', order_view.OrderArrivedAPIView.as_view(), name='order-arrived'),  # noqa
    path('<int:order_id>/retry/', order_view.OrderRetryAPIView.as_view(), name='order-retry'),  # noqa
    path('check-in/', order_view.OrderCheckAPIView.as_view(), name='order-check'),
    path('most-ordered-bookmarks/', bookmark_view.MostOrderedBookmarksView.as_view({'get': 'list', 'post': 'create'}), name='most-ordered-bookmarks'),  # noqa
    path('most-ordered-bookmarks/<int:pk>/', bookmark_view.MostOrderedBookmarksView.as_view({'delete': 'destroy'}), name='most-ordered-bookmarks-detail'), # noqa
])
