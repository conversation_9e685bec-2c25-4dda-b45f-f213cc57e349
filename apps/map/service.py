"""
The map service is responsible for map interface communication
"""
from apps.map.models import MapApiKey
from apps.map.services.yandex_geosuggestion import YandexGeosuggestionService


class MapService:
    """
    The map service class
    """
    @staticmethod
    def get_active_api_key() -> MapApiKey:
        """
        Retrieve the active API key for the map service
        """
        return MapApiKey.get_active_api_key()

    @staticmethod
    def mark_as_expired_api_key(api_key) -> MapApiKey:
        """
        Mark the given API key as expired
        """
        return MapApiKey.mark_as_limit_expired(api_key)

    @staticmethod
    def get_yandex_geosuggestion(text: str, coordinates: str = None, results: int = 5) -> dict:
        """
        Get location suggestions from Yandex Geosuggestion API

        Args:
            text (str): Search text for location suggestions
            coordinates (str, optional): Coordinates in format "longitude,latitude"
            results (int): Number of results to return (default: 5)

        Returns:
            dict: Formatted response with suggestions and metadata
        """
        service = YandexGeosuggestionService()
        return service.get_suggestions(text=text, coordinates=coordinates, results=results)

    @staticmethod
    def check_yandex_geosuggestion_availability() -> tuple:
        """
        Check if Yandex Geosuggestion API is available

        Returns:
            tuple: (availability status, message)
        """
        return YandexGeosuggestionService.check_api_availability()
