"""
the iiko updates webhook
"""
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.core.views import ServiceBaseView
from apps.iiko.tasks.update import process_update_event_task
from apps.iiko.providers.iiko.enum.event import EventTypesEnum


class IIKOUpdatesAPIView(APIView, ServiceBaseView):
    """
    The iiko updates webhook view
    """
    permission_classes = []

    def get(self, request, *args, **kwargs):
        """
        The GET method for handling iiko updates webhook
        """
        return Response(status=200)

    def post(self, request, *args, **kwargs):
        """
        The post method for handling iiko updates webhook
        """
        event_body = request.data
        # print(event_body)
        if not isinstance(event_body, list) or not event_body:
            raise self.call_service_exception(
                error_type="bad_request",
                message=f"Invalid event body format:\n\n {event_body}",
                status_code=400,
                notify_admin=True,
            )

        for event in event_body:
            event_type = event.get("eventType")
            event_info = event.get("eventInfo")
            if event_type not in EventTypesEnum.allowed_update_events():
                raise self.call_service_exception(
                    error_type="bad_request",
                    message=f"Invalid event type: {event_type}",
                    status_code=400,
                    notify_admin=True,
                )

            if event_type == EventTypesEnum.DELIVERY_ORDER_ERROR:
                message = f"Delivery Order Error: {event_info.get("errorInfo").get("description")}"
                self.send_notification(message)

            # Use Celery to process the update event, specifying the queue
            process_update_event_task.apply_async(
                args=[event_type, event],
                queue='iiko_updates',
                countdown=1  # one second scheduling
            )

        return Response(status=200)
