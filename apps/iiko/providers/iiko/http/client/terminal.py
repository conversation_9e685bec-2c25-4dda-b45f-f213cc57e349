"""
init iiko nomenclature class
"""
from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import IsAliveRequestBody
from apps.iiko.providers.iiko.http.response import IsAliveResponseBody


class IIKOTerminalGroupAPI:
    """
    IIKO getting nomenclature API client
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def is_alive(self, organization_id: str, terminal_group_id: str, headers: dict) -> IsAliveResponseBody:
        """
        Based on the Organization ID you received above, request a menu.
        """
        endpoint = "/api/1/terminal_groups/is_alive"
        request_body = IsAliveRequestBody(
            organizationId=organization_id,
            terminalGroupId=terminal_group_id
        ).as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        response_body = IsAliveResponseBody(**response_body)
        return response_body


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
terminal_group = IIKOTerminalGroupAPI(http=http_base_client)
