"""
Softphone app URL configuration
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter

from apps.softphone.views.click_to_call import ClickToCallAPIView, BulkClickToCallAPIView
from apps.softphone.views.screen_popup import <PERSON>PopupAPIView, CallControlAPIView
from apps.softphone.views.customer_data import CustomerDataAPIView, CustomerSearchAPIView
from apps.softphone.views.call_logging import CallLoggingAPIView, CallHistoryAPIView, CallStatsAPIView
from apps.softphone.views.recording_upload import (
    RecordingUploadAPIView,
    RecordingDownloadAPIView,
    RecordingListAPIView
)
from apps.softphone.views.frontend import CallPopupView, SoftphoneConfigView

# Create router for viewsets if needed
router = DefaultRouter()

urlpatterns = [
    # Click-to-call endpoints
    path('click-to-call/', ClickToCallAPIView.as_view(), name='click-to-call'),
    path('bulk-click-to-call/', BulkClickToCallAPIView.as_view(), name='bulk-click-to-call'),

    # Screen popup endpoints
    path('popup/', ScreenPopupAPIView.as_view(), name='screen-popup'),
    path('call-control/', CallControlAPIView.as_view(), name='call-control'),

    # Customer data endpoints
    path('customer-data/', CustomerDataAPIView.as_view(), name='customer-data'),
    path('customer-search/', CustomerSearchAPIView.as_view(), name='customer-search'),

    # Call logging endpoints
    path('log-call/', CallLoggingAPIView.as_view(), name='log-call'),
    path('call-history/', CallHistoryAPIView.as_view(), name='call-history'),
    path('call-stats/', CallStatsAPIView.as_view(), name='call-stats'),

    # Recording endpoints
    path('upload-recording/', RecordingUploadAPIView.as_view(), name='upload-recording'),
    path('recordings/', RecordingListAPIView.as_view(), name='recording-list'),
    path('recordings/<int:call_log_id>/download/', RecordingDownloadAPIView.as_view(), name='recording-download'),

    # Frontend views
    path('popup/', CallPopupView.as_view(), name='call-popup'),
    path('config/', SoftphoneConfigView.as_view(), name='softphone-config'),

    # Include router URLs
    path('', include(router.urls)),
]
