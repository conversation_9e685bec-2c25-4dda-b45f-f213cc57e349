"""
the courier shift apiview
"""
from rest_framework import views, response
from rest_framework.permissions import IsAuthenticated

from django.utils.dateparse import parse_datetime

from apps.order.models.order import Order
from apps.courier.models import CourierShifts
from apps.organization.models.terminal import TerminalGroup
from apps.courier.permissions.is_courier import IsCourierRole
from apps.core.exceptions.service import ServiceAPIException
from apps.iiko.providers.iiko.http.client import client
from apps.operations.permissions.dispatcher import IsAdminOrDispatcherOrManager


class CourierIsShiftAPIView(views.APIView):
    """
    The courier shift viewset
    """
    permission_classes = (IsAuthenticated, IsCourierRole)

    def get(self, request, *args, **kwargs):
        """
        The get method for getting the list of courier shifts.
        """
        courier = request.user
        shifts = CourierShifts.get_last_shift(courier.id)

        if not shifts:
            return response.Response({
                "is_open": False
            })

        return response.Response({
            "is_open": shifts.is_active,
            "open_time": shifts.start_time,
            "organization_id": shifts.organization.id,
            "organization_name": shifts.organization.name
        })


class CourierShiftCloseAPIView(views.APIView):
    """
    The courier shift close viewset
    """
    permission_classes = (IsAuthenticated, IsCourierRole)

    def post(self, request, *args, **kwargs):
        """
        The post method for closing the current shift.
        """
        courier = request.user

        last_shift = CourierShifts.get_last_shift(courier.id)

        if not last_shift:
            raise ServiceAPIException(
                error_type="bad_request",
                message="No active shift found for the courier",
                status_code=400
            )

        try:
            headers = client.variables.get_headers()
            employee_id = last_shift.courier.external_id
            organization_id = last_shift.organization.external_id
            terminal_group_id = TerminalGroup.get_active_terminal_group(last_shift.organization.id).external_id

            client.employees.shift_clockout(
                organization_id=organization_id,
                terminal_group_id=terminal_group_id,
                employee_id=employee_id,
                headers=headers
            )

        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal error",
                message=str(exc),
                status_code=500
            ) from exc

        return response.Response({
            "status": "Request was sent successfully"
        })


class CourierShiftsView(views.APIView):
    """
    API view to get the shifts of a specific courier with optional date filtering.
    """
    permission_classes = (IsAdminOrDispatcherOrManager, )

    def get(self, request, courier_id, *args, **kwargs):
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        if start_date and end_date:
            start_date = parse_datetime(start_date)
            end_date = parse_datetime(end_date)
            shifts_query = CourierShifts.objects.filter(
                courier_id=courier_id,
                start_time__gte=start_date,
                end_time__lte=end_date
            )
        else:
            shifts_query = CourierShifts.objects.filter(courier_id=courier_id)

        shifts = shifts_query.values('id', 'start_time', 'end_time', 'is_active')

        # Prepare the response data
        response_data = [
            {
                'shift_id': shift['id'],
                'start_time': shift['start_time'],
                'end_time': shift['end_time'],
                'is_active': shift['is_active']
            }
            for shift in shifts
        ]

        return response.Response(response_data)


class ShiftOrdersView(views.APIView):
    """
    API view to get orders completed during a specific shift.
    """
    permission_classes = (IsAdminOrDispatcherOrManager, )

    def get(self, request, shift_id, *args, **kwargs):
        orders = Order.objects.filter(courier_shift_id=shift_id).values(
            'id', 'external_number', 'status', 'created_at', 'updated_at'
        )

        response_data = []
        for order in orders:
            response_data.append({
                'order_id': order['id'],
                'external_number': order['external_number'],
                'status': order['status'],
                'created_at': order['created_at'],
                'updated_at': order['updated_at']
            })

        return response.Response(response_data)
