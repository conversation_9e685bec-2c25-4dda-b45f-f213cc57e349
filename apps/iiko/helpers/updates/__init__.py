"""
the updates helper module
"""
from apps.iiko import serializers
from apps.iiko.helpers.updates import delivery, kitchen, error, shift


class UpdateEventHelper:
    """
    the updates helper class
    """
    def __init__(
        self,
        kitchen_update: kitchen.KitchenUpdateEventHelper,
        personal_shift: shift.PersonalShiftUpdateEventHelper,
        delivery_update: delivery.DeliveryUpdateEventHelper,
        delivery_update_error: error.DeliveryOrderErrorEventHelper,
    ):
        self.__delivery_update = delivery_update
        self.__kitchen_update = kitchen_update
        self.__personal_shift = personal_shift
        self.__delivery_update_error = delivery_update_error

    def process_update_event(self, update_event):
        """
        process update event
        """
        if isinstance(update_event, serializers.DeliveryOrderUpdateEvent):
            self.__delivery_update.process_update_event(update_event)

        elif isinstance(update_event, serializers.KitchenOrderUpdateEvent):
            self.__kitchen_update.process_update_event(update_event)

        elif isinstance(update_event, serializers.DeliveryOrderErrorEvent):
            self.__delivery_update_error.process_update_event(update_event)

        elif isinstance(update_event, serializers.PersonalShiftUpdateEvent):
            self.__personal_shift.process_update_event(update_event)


update_event_helper = UpdateEventHelper(
    delivery_update=delivery.DeliveryUpdateEventHelper(),
    kitchen_update=kitchen.KitchenUpdateEventHelper(),
    delivery_update_error=error.DeliveryOrderErrorEventHelper(),
    personal_shift=shift.PersonalShiftUpdateEventHelper(),  # Shift update events are not supported yet.
)
