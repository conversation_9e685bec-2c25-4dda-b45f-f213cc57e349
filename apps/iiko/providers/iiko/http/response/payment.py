"""
Module: payment_types_response_models

This module defines Pydantic models to handle the response structures for payment types
in the system. It contains three main classes: `TerminalGroup`, `PaymentType`, and
`PaymentTypesResponseBody`.

Each class represents specific aspects of the payment system response:

1. **TerminalGroup**: Represents terminal group data, such as the terminal's organization ID,
   name, address, and time zone. It is used within the `PaymentType` model to define the
   various terminal groups associated with a payment type.

2. **PaymentType**: Defines the details of a payment type, including its ID, code (e.g., CASH, CLK),
   name, combinability, and processing methods. Each payment type has associated terminal groups.

3. **PaymentTypesResponseBody**: This is the main response model, containing a `correlationId` for
   identifying the response and a list of payment types, each represented by the `PaymentType` model.

These models enforce the structure of data passed in responses and ensure type safety, making it
easier to work with the payment types API responses.

Example usage:
    - Parse and validate API responses using `PaymentTypesResponseBody`.
    - Ensure the structure of terminal groups and payment types is consistent and correct.
"""
from typing import List, Optional

from pydantic import BaseModel


class TerminalGroup(BaseModel):
    """
    Represents a terminal group associated with a payment type.

    Attributes:
        id (str): The unique identifier of the terminal group.
        organizationId (str): The organization to which the terminal group belongs.
        name (str): The name of the terminal group.
        address (Optional[str]): The address of the terminal group (can be empty).
        timeZone (str): The time zone in which the terminal group operates.
    """
    id: str
    organizationId: str
    name: str
    address: Optional[str]
    timeZone: str


class PaymentType(BaseModel):
    """
    Represents a payment type within the system.

    Attributes:
        id (str): The unique identifier of the payment type.
        code (str): The payment type code (e.g., CASH, CLK).
        name (str): The display name of the payment type.
        comment (Optional[str]): An optional comment about the payment type.
        combinable (bool): Indicates whether the payment type is combinable with others.
        externalRevision (int): External revision number of the payment type.
        applicableMarketingCampaigns (List): A list of applicable marketing campaigns (empty here).
        isDeleted (bool): Whether the payment type is deleted.
        printCheque (bool): Whether to print a cheque for this payment type.
        paymentProcessingType (str): The type of payment processing (e.g., Both, Internal).
        paymentTypeKind (str): The kind of payment (e.g., Cash, Card).
        terminalGroups: List[TerminalGroup]
    """
    id: str
    code: str
    name: str
    comment: Optional[str]
    combinable: bool
    externalRevision: int
    applicableMarketingCampaigns: List
    isDeleted: bool
    printCheque: bool
    paymentProcessingType: str
    paymentTypeKind: str
    terminalGroups: List[TerminalGroup]


class PaymentTypesResponseBody(BaseModel):
    """
    The response body for payment types, including correlation ID and a list of payment types.

    Attributes:
        correlationId (str): The unique identifier for correlating the response.
        paymentTypes (List[PaymentType]): A list of payment types associated with the response.
    """
    correlationId: str
    paymentTypes: List[PaymentType]
