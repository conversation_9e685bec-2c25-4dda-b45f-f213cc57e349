"""
the transaction states
"""
from enum import StrEnum


class TransactionStateEnum(StrEnum):
    """
    The enumeration of transaction states.
    """
    CREATED = "Created"
    WAITING = "Waiting"
    PROCESSING = "Processing"
    COMPLETED = "Completed"
    REFUNDED = "Refunded"
    CANCELLED = "Cancelled"
    EXPIRED = "Expired"
    REJECTED = "Rejected"
    PENDING = "Pending"
    REVERSED = "Reversed"
    REVERSED_AND_WAITING = "Reversed and Waiting"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]


class TransactionCancelReasonEnum(StrEnum):
    """
    Enumeration of transaction cancellation reasons and their corresponding descriptions.

    Cancellation Reasons:
    - RECIPIENT_NOT_FOUND: One or more recipients not found or inactive in Payme Business.
    - DEBIT_OPERATION_FAILED: Error during debit operation in the processing center.
    - TRANSACTION_FAILED: Transaction execution error.
    - TRANSACTION_TIMEOUT: Transaction cancelled due to timeout.
    - REFUND: Refund issued.
    - CREATED_AND_REFUNDED: Transaction created and a refund was issued.
    - COMPLETED_AND_REFUNDED: Transaction completed and a refund was issued.
    - UNKNOWN_ERROR: An unknown error occurred during the transaction.
    """
    REFUND = "Refund"
    RECIPIENT_NOT_FOUND = "RecipientNotFound"
    DEBIT_OPERATION_FAILED = "DebitOperationFailed"
    TRANSACTION_FAILED = "TransactionFailed"
    TRANSACTION_TIMEOUT = "TransactionTimeout"
    CREATED_AND_REFUNDED = "CreatedAndRefund"
    COMPLETED_AND_REFUNDED = "CompletedAndRefund"
    UNKNOWN_ERROR = "UnknownError"

    @classmethod
    def get_description(cls, code):
        """Get the description for a given cancellation reason code."""
        descriptions = {
            cls.RECIPIENT_NOT_FOUND: "One or more recipients not found or inactive in Provider Business Side.",
            cls.DEBIT_OPERATION_FAILED: "Error during debit operation in the processing center.",
            cls.TRANSACTION_FAILED: "Transaction execution error.",
            cls.TRANSACTION_TIMEOUT: "Transaction cancelled due to timeout.",
            cls.REFUND: "Refund issued.",
            cls.UNKNOWN_ERROR: "An unknown error occurred during the transaction."
        }
        return descriptions.get(code, "Cancellation reason not found.")

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
