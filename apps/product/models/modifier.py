"""
Modifier models for product items.
"""
from django.db import models

from apps.organization.models import Organization
from apps.product.models.product import Product


class ModifierItems(models.Model):
    """
    Represents a modifier item.
    """
    sku = models.CharField(max_length=255)
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    restrictions = models.ForeignKey("Restrictions", on_delete=models.CASCADE, null=True)
    portion_weight_grams = models.IntegerField(default=0)
    is_hidden = models.BooleanField(default=False)
    item_id = models.CharField(max_length=255)
    position = models.IntegerField(default=0)
    independent_quantity = models.BooleanField(default=False)
    measure_unit_type = models.CharField(max_length=50, default="GRAM")
    button_image_url = models.URLField(null=True, blank=True)

    def __str__(self) -> str:
        return str(self.name)

    @classmethod
    def update_or_create(cls, defaults=None, **kwargs):
        if defaults is None:
            defaults = {}
        restrictions_data = defaults.pop('restrictions', None)
        obj, created = cls.objects.update_or_create(defaults=defaults, **kwargs)
        if restrictions_data:
            Restrictions.update_or_create(modifier_item=obj, defaults=restrictions_data)
        return obj, created

    @classmethod
    def create(
        cls, sku, name, description, portion_weight_grams,
        is_hidden, item_id, position, independent_quantity, measure_unit_type, button_image_url
    ):
        """
        create a modifier item
        """
        return cls.objects.create(
            sku=sku,
            name=name,
            description=description,
            portion_weight_grams=portion_weight_grams,
            is_hidden=is_hidden,
            item_id=item_id,
            position=position,
            independent_quantity=independent_quantity,
            measure_unit_type=measure_unit_type,
            button_image_url=button_image_url
        )

    @classmethod
    def get_item_group_and_item_id(cls, modifier_id):
        """
        Get the item group ID and item ID for the given modifier ID.

        Args:
            modifier_id (int): The ID of the modifier item.

        Returns:
            tuple: A tuple containing the item group ID and item ID.
        """
        try:
            modifier_item = cls.objects.get(id=modifier_id)
            item_group_id = modifier_item.itemmodifiergroups_set.first().item_group_id
            item_id = modifier_item.item_id
            return item_group_id, item_id
        except cls.DoesNotExist:
            return None, None


class Restrictions(models.Model):
    """
    Represents restrictions for a modifier item.
    """
    min_quantity = models.IntegerField(default=0)
    max_quantity = models.IntegerField(default=0)
    free_quantity = models.IntegerField(default=0)
    by_default = models.IntegerField(default=0)
    hide_if_default_quantity = models.BooleanField(default=False)

    @classmethod
    def update_or_create(cls, **kwargs):
        obj, created = cls.objects.update_or_create(**kwargs)
        return obj, created

    @classmethod
    def create(
        cls,
        min_quantity,
        max_quantity,
        free_quantity,
        by_default,
        hide_if_default_quantity
    ):
        return cls.objects.create(
            min_quantity=min_quantity,
            max_quantity=max_quantity,
            free_quantity=free_quantity,
            by_default=by_default,
            hide_if_default_quantity=hide_if_default_quantity
        )


class ModiferProductPrices(models.Model):
    """
    Represents prices for modifier items by organization.
    """
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True)
    price = models.FloatField(default=0)
    modifer_items = models.ForeignKey(ModifierItems, on_delete=models.CASCADE, null=True)

    class Meta:
        """
        the meta class for modifer product prices
        """
        verbose_name = "Modifier Product Price"
        verbose_name_plural = "Modifier Product Prices"
        ordering = ['organization', 'modifer_items']
        unique_together = ('organization', 'modifer_items')

    def __str__(self) -> str:
        return f"{self.modifer_items} price branch: {self.organization.name}"

    @classmethod
    def update_or_create(cls, organization_external_id, modifer_item, price):
        organization = Organization.get_by_external_id(organization_external_id)

        if not price:
            price = 0

        obj, created = cls.objects.update_or_create(
            organization=organization,
            modifer_items=modifer_item,
            defaults={'price': price}
        )
        return obj, created

    @classmethod
    def get_by_id(cls, id) -> "ModiferProductPrices":
        """
        Get modifier product price by ID.
        """
        return cls.objects.get(id=id)

    @classmethod
    def get_by_modifer_item_id(cls, modifer_item_id) -> "ModiferProductPrices":
        """
        Get modifier product prices by modifier item ID.
        """
        return cls.objects.get(modifer_items_id=modifer_item_id)

    @classmethod
    def get_ordered_modifer_item_price(cls, organization_id, modifer_id):
        """
        getting ordered modifier product price
        """
        return cls.objects.get(
            organization_id=organization_id,
            modifer_items_id=modifer_id
        )


class ItemModifierGroups(models.Model):
    """
    Represents groups of modifier items.
    """
    product = models.ForeignKey(Product, on_delete=models.CASCADE)
    size_id = models.CharField(max_length=255, blank=True, null=True)
    name = models.CharField(max_length=100)
    description = models.TextField(null=True, blank=True)
    restrictions = models.ForeignKey(Restrictions, on_delete=models.CASCADE, null=True)
    can_be_divided = models.BooleanField(default=False)
    item_group_id = models.CharField(max_length=255, null=True, blank=True)
    is_hidden = models.BooleanField(default=False)
    child_modifier_have_min_max_restrictions = models.BooleanField(default=False)
    sku = models.CharField(max_length=255, null=True, blank=True)
    items = models.ManyToManyField(ModifierItems)

    @classmethod
    def update_or_create(
        cls,
        product,
        name,
        description=None,
        can_be_divided=False,
        item_group_id=None,
        is_hidden=False,
        child_modifier_have_min_max_restrictions=False,
        sku=None,
        size_id=None
    ):
        obj, created = cls.objects.update_or_create(
            product=product,
            size_id=size_id,
            name=name,
            defaults={
                'description': description,
                'can_be_divided': can_be_divided,
                'item_group_id': item_group_id,
                'is_hidden': is_hidden,
                'child_modifier_have_min_max_restrictions': child_modifier_have_min_max_restrictions,
                'sku': sku,
            }
        )
        return obj, created
