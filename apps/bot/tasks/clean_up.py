"""
Task module for managing Telegram messages.

This module contains Celery tasks related to handling Telegram messages,
including cleaning up old messages that have not been modified in a day.
"""
import logging

from datetime import timedelta
from django.utils import timezone

from celery import shared_task

from apps.bot.client import bot
from apps.bot.models import Messages


logger = logging.getLogger(__name__)


@shared_task
def cleanup_old_messages():
    """
    Cleans up Telegram messages that have not been modified in the last day.

    This task retrieves all messages from the `Messages` model that have
    not been modified within the last 24 hours. For each old message:

    1. Attempts to delete the message from Telegram using the Telegram API.
    2. Deletes the message from the database.

    Logs any errors encountered during the process, both for message deletion
    from Telegram and for database operations.
    """
    try:
        # Define the time threshold for messages to be considered old (12 hours)
        threshold_time = timezone.now() - timedelta(hours=12)

        # Log the start of the cleanup process
        logger.info(f"Starting cleanup of messages older than {threshold_time}")

        # Retrieve messages older than the threshold time
        old_messages = Messages.get_old_messages(threshold_time)

        # Log the number of messages to be cleaned up
        logger.info(f"Found {old_messages.count()} messages to clean up")

        deleted_count = 0
        error_count = 0

        for message in old_messages:
            try:
                # Delete the message from Telegram
                bot.delete_message(
                    chat_id=message.chat_id,
                    message_id=message.message_id
                )

                # Delete the message from the database
                message.delete()

                deleted_count += 1

                # Log every 10 messages to avoid excessive logging
                if deleted_count % 10 == 0:
                    logger.info(f"Deleted {deleted_count} messages so far")

            except Exception as e:
                error_count += 1
                logger.error(f"Failed to delete message {message.message_id} from chat {message.chat_id}: {e}")

                # Still delete from database even if Telegram API deletion fails
                try:
                    message.delete()
                except Exception as db_error:
                    logger.error(f"Failed to delete message {message.message_id} from database: {db_error}")

        # Log the completion of the cleanup process
        logger.info(f"Cleanup completed. Successfully deleted {deleted_count} messages. Errors: {error_count}")

    except Exception as exc:
        logger.error(f"cleanup_old_messages exception: {exc}")
