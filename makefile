stop:
	sudo lsof -t -i tcp:8000 | xargs kill -9

setup:
	docker-compose down
	docker compose up --build

run.bash:
	docker run -it master_kebab-master_kebab /bin/bash

run.local:
	docker compose -f docker-compose.local.yml down
	docker compose -f docker-compose.local.yml up --build

run.celery:
	celery -A master_kebab worker -Q bot,sms,iiko,iiko_updates,order,user,delivery,core --loglevel=info --concurrency=10 --pool threads

run.celery.beat:
	celery -A master_kebab beat --loglevel=info

run.celery.all:
	celery -A master_kebab worker -Q bot,sms,iiko,iiko_updates,order,user,delivery,core --loglevel=info --concurrency=10 --pool threads & celery -A master_kebab beat --loglevel=info

makemigrate:
	docker compose exec master_kebab python manage.py makemigrations

migrate:
	docker compose exec master_kebab python manage.py migrate

dumpdata:
	python3 manage.py dumpdata --exclude auth.permission --exclude contenttypes --indent 2 > backup.json

loaddata:
	python3 manage.py loaddata backup.json

cleanup.messages:
	python3 manage.py cleanup_old_messages
