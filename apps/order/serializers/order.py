"""
the order serialization
"""
from rest_framework import serializers

from apps.order.models import Order
from apps.operations.models.operations import Operations
from apps.order.models.historical import HistoricalOrderModel
from apps.order.enums.order import OrderStatus as OrderStatusEnum
from apps.courier.serializers.courier import CourierModelSerializer
from apps.order.serializers.order_item import OrderItemSerializer # noqa
from apps.order.serializers.historical import HistoricalOrderSerializer
from apps.organization.serializers.organization import OrganizationSerializer
from apps.user.serializers.user import UserSerializer, UserSerializerForOrderHistoryList
from apps.order.serializers.order_detail import OrderDetailSerializer, OrderDetailForListSerializer
from apps.operations.serializers import OperationsSerializer, OperatorForOrderListSerializer


class OrganizationSerializerUPT(OrganizationSerializer):
    """
    The organization serialization for update
    """
    class Meta(OrganizationSerializer.Meta):
        """
        The meta fields for update
        """
        fields = (
            'name',
        )


class OrderSerializer(serializers.ModelSerializer):
    """
    The order serialization
    """
    user = UserSerializer()
    order_detail = OrderDetailSerializer()
    order_items = OrderItemSerializer(many=True)
    delivery_agent = CourierModelSerializer(required=False)
    operator = OperationsSerializer()
    organization = OrganizationSerializerUPT()

    class Meta:
        """
        The meta fields
        """
        model = Order
        fields = "__all__"

    def to_representation(self, instance):
        """
        Convert the instance to its display representation, including the status display name.
        """
        # Call the base implementation to get the initial representation
        representation = super().to_representation(instance)

        # Fetch the display name for the status and add it to the representation
        status_display = OrderStatusEnum.get_display_name(OrderStatusEnum(instance.status)) if instance.status else None
        representation['status_display_name'] = status_display

        return representation


class OrderListSerializer(OrderSerializer):
    """
    The order list serialization
    """
    user = UserSerializerForOrderHistoryList()
    order_detail = OrderDetailForListSerializer()
    operator = OperatorForOrderListSerializer()
    historical = serializers.SerializerMethodField()
    taker = serializers.SerializerMethodField()

    class Meta(OrderSerializer.Meta):
        """
        The meta fields for list
        """
        fields = (
            'id',
            'user',
            'external_number',
            'status',
            'order_detail',
            'initiator',
            'operator',
            'taker',
            'is_paid',
            'created_at',
            'updated_at',
            'historical',
        )

    def get_taker(self, obj):
        """
        Get the taker for the given order instance.
        """
        if obj.taker_id:
            return OperatorForOrderListSerializer(Operations.get_by_id(
                operations_id=obj.taker_id,
            )).data
        else:
            return None

    def get_historical(self, obj):
        """
        Get the historical orders for the given order instance.
        """
        historical_orders = HistoricalOrderModel.get_by_id(obj.id)
        return HistoricalOrderSerializer(historical_orders, many=True).data


class OrderUpdateSerializer(serializers.Serializer):
    """
    the order update serialization
    """
    status = serializers.ChoiceField(choices=OrderStatusEnum.choices())
