from django.db import models

from apps.core.models.base import BaseModel
from apps.payment.utils import generate_invoice


class Invoice(BaseModel):
    """
    Invoice model.
    """
    class Meta:
        """
        The meta fields for the Invoice model.
        """
        db_table = 'invoices'
        verbose_name_plural = 'invoices'
        ordering = ['-created_at']
        verbose_name = 'Invoice'

    order = models.ForeignKey(
        "order.Order",
        on_delete=models.CASCADE,
        null=True
    )
    invoice = models.TextField(default=generate_invoice)
    paylink = models.TextField()

    @classmethod
    def get_by_invoice(cls, invoice):
        """
        Get invoice by invoice number.
        """
        obj = cls.objects.get(invoice=invoice)
        return obj.paylink

    @classmethod
    def create(cls, order_id, paylink):
        """
        Create invoice and pay_link.
        """
        invoice = cls(order_id=order_id, paylink=paylink)
        invoice.save()
