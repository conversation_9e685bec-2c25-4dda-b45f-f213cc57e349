"""
init menu request models
"""
from typing import List

from pydantic import BaseModel, Field


class MenuByIdRequestBody(BaseModel):
    """
    the menu by id request body model
    """
    externalMenuId: int
    organizationIds: List[str]

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "externalMenuId": self.externalMenuId,
            "organizationIds": self.organizationIds,
        }


class StopListsRequestBody(BaseModel):
    """
    the stop lists request body model
    """
    organizationIds: List[str]
    returnSize: bool = Field(default=False)
    terminalGroupsIds: List[str]

    def as_request_body(self):
        """
        the as request body method
        """
        return {
            "organizationIds": self.organizationIds,
            "returnSize": self.returnSize,
            "terminalGroupIds": self.terminalGroupsIds,
        }
