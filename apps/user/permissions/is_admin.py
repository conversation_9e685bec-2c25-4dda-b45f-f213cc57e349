"""
the is owner or admin permission
"""
from rest_framework import permissions

from apps.user.enum.role import UserRoleEnums


class IsAdminRole(permissions.BasePermission):
    """
    Custom permission to only allow admin users to edit objects.
    """
    def has_permission(self, request, view):
        is_admin = all([
            request.user,
            request.user.is_authenticated,
            request.user.role == UserRoleEnums.ADMIN.value
        ])
        return is_admin
