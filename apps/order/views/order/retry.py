from rest_framework import views
from rest_framework import response

from apps.order.models import Order
from apps.core.views import ServiceBaseView
from apps.order.service import OrderService
from apps.order.exception import TerminalDisabledError
from apps.user.permissions.is_operator import IsOperatorRole


class OrderRetryAPIView(views.APIView, ServiceBaseView):
    """
    API view for retrying failed orders.
    """
    permission_classes = [IsOperatorRole]

    def post(self, request, *args, **kwargs):
        """
        The POST method for retrying failed orders.
        """
        order_id = kwargs.get("order_id")

        try:
            OrderService.retry(order_id=order_id)

        except Order.DoesNotExist as exc:
            raise self.call_service_exception(
                error_type="not_found",
                message=f"Order not found: {order_id} for retry",
                status_code=404,
                notify_admin=True
            ) from exc

        except TerminalDisabledError as exc:
            raise self.call_service_exception(
                error_type="terminal_disabled",
                message="You cannot retry terminal is disabled",
                status_code=500,
                notify_admin=True,
            ) from exc

        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"An error occurred: {exc}",
                status_code=500,
            ) from exc

        return response.Response()
