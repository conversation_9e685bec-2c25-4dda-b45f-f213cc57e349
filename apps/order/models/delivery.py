"""
This module defines models related to delivery pricing in the order application.
It includes a DeliveryPrice model for managing different pricing tiers based on delivery distance.
"""

from django.db import models

from apps.core.models.base import BaseModel


class DeliveryPrice(BaseModel):
    """
    Represents a delivery price tier based on distance.

    This model stores information about different delivery price categories,
    including an external identifier, distance type, and active status.

    Attributes:
        external_id (CharField): A unique identifier for the delivery price.
        distance_type (CharField): The type of delivery based on distance.
        is_active (BooleanField): Indicates whether this delivery price is currently active.
    """

    external_id = models.CharField(max_length=255, unique=True, help_text="Unique identifier for the delivery price")

    class DeliveryDistanceType(models.TextChoices):
        """
        Enumeration of delivery distance types.

        This inner class defines the various distance categories for delivery,
        each with a code and human-readable description.

        Choices:
            SLIGHTLY_NEAR = 'SN', 'Slightly Near (0-2 km)'
            NEARBY = 'NB', 'Nearby (2-4 km)'
            LOCAL_CLOSE = 'LC', 'Local Close (4-6 km)'
            LOCAL_MID = 'LM', 'Local Mid (6-8 km)'
            LOCAL_FAR = 'LF', 'Local Far (8-10 km)'
            DISTANT_CLOSE = 'DC', 'Distant Close (10-12 km)'
            DISTANT_MID = 'DM', 'Distant Mid (12-15 km)'
            DISTANT_FAR = 'DF', 'Distant Far (15-18 km)'
            REMOTE = 'RM', 'Remote (18-20 km)'
        """
        # VERY_NEARBY = 'VN', 'Very Nearby (0-1 km)'
        SLIGHTLY_NEAR = 'SN', 'Slightly Near (0-2km)'
        NEARBY = 'NB', 'Nearby (2-4 km)'
        LOCAL_CLOSE = 'LC', 'Local Close (4-6 km)'
        LOCAL_MID = 'LM', 'Local Mid (6-8 km)'
        LOCAL_FAR = 'LF', 'Local Far (8-10 km)'
        DISTANT_CLOSE = 'DC', 'Distant Close (10-12 km)'
        DISTANT_MID = 'DM', 'Distant Mid (12-15 km)'
        DISTANT_FAR = 'DF', 'Distant Far (15-18 km)'
        REMOTE = 'RM', 'Remote (18-20 km)'

    distance_type = models.CharField(
        max_length=8,
        choices=DeliveryDistanceType.choices,
        default=DeliveryDistanceType.NEARBY,
        unique=True,
        help_text="Type of delivery based on distance"
    )
    display_name = models.CharField(max_length=155, default="Доставка")
    price = models.FloatField(help_text="Price of delivery", default=0)
    is_active = models.BooleanField(default=True, help_text="Whether this delivery price is currently active")

    class Meta:
        """
        Meta class for the DeliveryPrice model.

        This class defines metadata for the DeliveryPrice model, including:
        - The database table name
        - Verbose names for singular and plural forms
        - Default ordering
        - Database indexes for optimized querying

        Attributes:
            db_table (str): The name of the database table for this model.
            verbose_name (str): A human-readable name for the model in singular form.
            verbose_name_plural (str): A human-readable name for the model in plural form.
            ordering (list): The default ordering for querysets of this model.
            indexes (list): A list of database indexes to be created for this model.
            delivery_distances (dict): A dictionary mapping distance ranges to prices in so'm.
                The keys are tuples representing the distance range (min, max) in km,
                and the values are the corresponding prices in so'm.
                Example: {(0, 3): 10000, (3, 5): 15000, ...}
        """
        db_table = 'delivery_prices'
        verbose_name = 'Delivery Price'
        verbose_name_plural = 'Delivery Prices'
        ordering = ['price']
        indexes = [
            models.Index(fields=['distance_type']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.get_distance_type_display()} - {'Active' if self.is_active else 'Inactive'}"

    def get_distance_type_display(self):
        """
        Returns the human-readable name for the distance type.

        This method overrides the default get_FOO_display() method provided by Django
        for fields with choices. It returns the full description of the distance type
        as defined in the DeliveryDistanceType choices.

        Returns:
            str: The human-readable description of the distance type.
        """
        return dict(self.DeliveryDistanceType.choices)[self.distance_type]

    def get_display_name(self):
        """
        Returns the display name for the delivery price.

        This method returns the custom display name if set, otherwise
        it falls back to the distance type display name.

        Returns:
            str: The display name for the delivery price.
        """
        return self.display_name if self.display_name != "Доставка" else self.get_distance_type_display()

    @classmethod
    def get_delivery_price(cls, distance_type: DeliveryDistanceType):
        """
        Get the delivery price object for a given distance type.

        This class method retrieves the active DeliveryPrice object for the specified
        distance type. If no active price is found for the given distance type,
        it returns None.

        Args:
            distance_type (str): The distance type code (e.g., 'NB', 'LC', 'DS', 'RM', 'RM_PLUS').

        Returns:
            DeliveryPrice: The active DeliveryPrice object for the given distance type,
                           or None if no active price is found.
        """
        try:
            return cls.objects.get(distance_type=distance_type, is_active=True)
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_all_active_prices(cls):
        """
        Get a list of all active delivery prices.

        This class method retrieves all active DeliveryPrice objects and returns them as a list.

        Returns:
            list: A list of all active DeliveryPrice objects.
        """
        return cls.objects.filter(is_active=True)

    @classmethod
    def get_by_id(cls, id):
        """
        Get a delivery price by its id.

        This class method retrieves a DeliveryPrice object by its id and returns it.

        Args:
            id (int): The id of the DeliveryPrice object.

        Returns:
            DeliveryPrice: The DeliveryPrice object with the given id, or None if not found.
        """
        try:
            return cls.objects.get(id=id)
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_by_external_id(cls, external_id):
        """
        Get a delivery price by its external id.

        This class method retrieves a DeliveryPrice object by its external id and returns it.

        Args:
            external_id (str): The external id of the DeliveryPrice object.

        Returns:
            DeliveryPrice: The DeliveryPrice object with the given external id, or None if not found.
        """
        try:
            return cls.objects.get(external_id=external_id)
        except cls.DoesNotExist:
            return None

    def to_dict(self):
        """
        Convert DeliveryPrice objects to a dictionary.

        This class method converts all DeliveryPrice objects to a dictionary,
        with the distance type as the key and the price as the value.

        Returns:
            dict: A dictionary mapping distance types to prices.
        """
        return {
            "id": self.external_id,
            "distance_type": self.distance_type,
            "display_name": self.display_name,
            "price": self.price,
            "currency": "UZS"
        }
