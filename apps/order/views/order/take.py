"""
taking order and bind for operator apiview
"""
from rest_framework import views, status
from rest_framework.response import Response

from apps.order.service import OrderService
from apps.core.exceptions import ServiceAPIException
from apps.operations.permissions.manager import IsAdminOrManagerOrOperator


class TakeOrderAPIView(views.APIView):
    """
    The operator take order API view
    """
    permission_classes = [IsAdminOrManagerOrOperator]

    def post(self, request, *args, **kwargs):
        """
        The post method for taking an order.
        """
        order_id = kwargs.get("order_id")
        if not order_id:
            raise ServiceAPIException(
                error_type="bad_request",
                message="Missing required parameter: order_id",
            )

        try:
            OrderService.bind_to_taker(
                order_id=order_id,
                taker_id=request.user.id,
                taker_type=request.user.role
            )

            return Response({"detail": "Order taken successfully."}, status=status.HTTP_200_OK)

        except Exception as exc:
            msg = f"Take order failed: {exc}"
            raise ServiceAPIException(
                error_type="internal error",
                message=msg
            ) from exc
