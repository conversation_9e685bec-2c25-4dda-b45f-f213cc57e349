"""
Kitchen Events Enumerations
"""
from enum import Enum


class ProcessingStatusEnum(str, Enum):
    """
    Enum for kitchen order processing statuses.

    Attributes:
        IDLE (str): Order item was printed, but cooking didn't start yet.
        PROCESSING1 (str): Cooking is in first step progress.
        PROCESSING2 (str): Cooking is in second step progress.
        PROCESSING3 (str): Cooking is in third step progress.
        PROCESSING4 (str): Cooking is in fourth step progress.
        PROCESSED (str): Cooking completed, dish is ready to be served.
        SERVED (str): Item was cooked and served.
    """

    IDLE = 0
    PROCESSING1 = 1
    PROCESSING2 = 2
    PROCESSING3 = 3
    PROCESSING4 = 4
    PROCESSED = 5
    SERVED = 6

    def __str__(self):
        """
        Returns the string value of the enum member.
        """
        return self.value

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.

        Returns:
            list: A list of tuples where each tuple contains the enum value and its label.
        """
        return [(member.value, member.name.replace('_', ' ').capitalize()) for member in cls]
