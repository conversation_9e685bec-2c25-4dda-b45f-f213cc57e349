"""
init iiko nomenclature class
"""
from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import NomenclatureRequestBody
from apps.iiko.providers.iiko.http.response import NomenclatureResponseBody


class IIKONomenclatureAPI:
    """
    IIKO getting nomenclature API client
    """
    def __init__(self, http: BaseHTTPClient):
        self.http = http

    def nomenclature(self, organization_id: str, headers: dict) -> NomenclatureResponseBody:
        """
        Based on the Organization ID you received above, request a menu.
        """
        endpoint = "/api/1/nomenclature"
        request_body = NomenclatureRequestBody(organization_id=organization_id).as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)
        response_body = NomenclatureResponseBody(**response_body)
        return response_body


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
nomenclature = IIKONomenclatureAPI(http=http_base_client)
