"""
The shift system for couriers.
"""
from django.utils import timezone
from django.db import models, transaction

from apps.courier.models.courier import Courier
from apps.organization.models.organtzation import Organization


class CourierShifts(models.Model):
    """
    Model representing a courier's work shift.

    Attributes:
        courier (ForeignKey): Reference to the courier who is on this shift.
        organization (ForeignKey): Reference to the organization associated with this shift.
        start_time (DateTimeField): The time when the shift started.
        end_time (DateTimeField): The time when the shift ended. Can be null for active shifts.
        is_active (BooleanField): Status indicating if the shift is currently active.
        created_at (DateTimeField): Timestamp when the shift was created.
        updated_at (DateTimeField): Timestamp when the shift was last updated.
    """

    class Meta:
        """
        The courier shifts meta class fields.
        """
        db_table = 'courier_shifts'
        ordering = ['-start_time', '-end_time']
        verbose_name = 'Courier Shift'
        verbose_name_plural = 'Courier Shifts'

    courier = models.ForeignKey(Courier, on_delete=models.CASCADE)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.courier} - from {self.start_time} to {self.end_time} ({self.organization})"

    @classmethod
    def open_shift(cls, courier: Courier, organization):
        """
        Opens a new shift for the given courier under a specific organization.

        This method closes any existing active shifts for the courier within the same organization
        before creating a new one with the current time as the start time.

        Args:
            courier (Courier): The courier for whom the shift is being opened.
            organization (Organization): The organization for which the shift is being opened.

        Returns:
            CourierShifts: The newly created shift instance.
        """
        # Close any existing active shifts for this courier within the same organization
        cls.close_all_shifts_courier(courier_id=courier.id)

        with transaction.atomic():
            # Open a new shift
            new_shift = cls.objects.create(courier=courier, organization=organization, start_time=timezone.now())
            courier.mark_as_is_shift_active()

        return new_shift

    def close_shift(self):
        """
        Closes the current shift.

        This method sets the end time to the current time and marks the shift as inactive.

        Returns:
            None
        """
        self.end_time = timezone.now()
        self.is_active = False
        self.save()

    @classmethod
    def close_all_shifts_courier(cls, courier_id):
        """
        closing all shifts that belongs to courier
        """
        return cls.objects.filter(courier_id=courier_id, is_active=True).update(is_active=False, end_time=timezone.now()) # noqa

    @classmethod
    def close_all_shifts_for_organization(cls, organization):
        """
        Closes all active shifts for a given organization.

        This method sets the end time to the current time and marks all active shifts as inactive.

        Args:
            organization (Organization): The organization for which all active shifts should be closed.

        Returns:
            int: The number of shifts that were closed.
        """
        return cls.objects.filter(organization=organization, is_active=True).update(is_active=False, end_time=timezone.now()) # noqa

    @classmethod
    def get_active_shift(cls, courier_id):
        """
        Get the active shift for a given courier.

        Args:
            courier_id (int): The id of the courier.

        Returns:
            CourierShifts: The active shift for the given courier or None if no active shift exists.
        """
        return cls.objects.filter(courier_id=courier_id, is_active=True).first()

    @classmethod
    def get_last_shift(cls, courier_id):
        """
        Get the last shift for a given courier.

        Args:
            courier_id (int): The id of the courier.

        Returns:
            CourierShifts: The last shift for the given courier or None if no shifts exist.
        """
        return cls.objects.filter(courier_id=courier_id).order_by('-id').first()
