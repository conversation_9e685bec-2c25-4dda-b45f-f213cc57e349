# myapp/management/commands/purge_queues.py
"""
Django management command to purge specific Redis queues.

This command connects to Redis and clears specified queues by deleting
their entire content. It is useful for managing and clearing message
queues used by Celery or other applications.
"""
import redis
from django.core.management.base import BaseCommand


class Command(BaseCommand):
    """
    Command to purge specific Redis queues.

    Attributes:
        help (str): Description of the command.
    """

    help = 'Purge specific Redis queues'

    def handle(self, *args, **kwargs):
        """
        Connects to Redis and deletes specified queues.

        Iterates over a predefined list of queues, retrieves their length,
        and if they contain messages, deletes them. Outputs a message to
        the console indicating the result of the operation.
        """
        r = redis.Redis(host='localhost', port=6379, db=0)
        queues_to_clear = ['iiko', 'order', 'bot']

        for queue in queues_to_clear:
            length = r.llen(queue)
            if length > 0:
                r.delete(queue)
                self.stdout.write(self.style.SUCCESS(f"Cleared {length} messages from queue '{queue}'."))
            else:
                self.stdout.write(self.style.NOTICE(f"Queue '{queue}' is already empty."))
