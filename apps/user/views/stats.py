from datetime import datetime

from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from apps.user.service import UserService


class ClientStatisticsAPIView(APIView):
    """
    API view to get client statistics
    """
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        """
        Handle GET request to retrieve client statistics.
        """
        if request.user.role not in [
            'marketolog', 'director', 'managers'
        ]:
            return Response({'error': 'You are not authorized to access this resource.'}, status=403)

        current_date = datetime.now()
        default_start_date = datetime(current_date.year, current_date.month, 1)

        # Get query parameters
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        # If both dates are not provided, get statistics for all time
        if not start_date and not end_date:
            statistics = UserService.get_client_statistics(None, None)
            return Response(statistics)

        # Use default dates if only one parameter is missing
        if not start_date:
            start_date = default_start_date
        else:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')

        if not end_date:
            end_date = current_date
        else:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')

        statistics = UserService.get_client_statistics(start_date, end_date)
        return Response(statistics)
