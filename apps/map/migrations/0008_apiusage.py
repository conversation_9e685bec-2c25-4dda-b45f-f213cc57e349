# Generated by Django 5.0.6 on 2025-07-14 07:49

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('map', '0007_mapapikey_usage_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='ApiUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True, db_index=True)),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('daily_limit', models.PositiveIntegerField(default=1000)),
                ('api_key', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usage_records', to='map.mapapikey')),
            ],
            options={
                'verbose_name': 'API Usage Record',
                'verbose_name_plural': 'API Usage Records',
                'db_table': 'map_api_usage',
                'indexes': [models.Index(fields=['api_key', 'date'], name='map_api_usa_api_key_91bbe0_idx'), models.Index(fields=['date'], name='map_api_usa_date_a7abe0_idx')],
                'unique_together': {('api_key', 'date')},
            },
        ),
    ]
