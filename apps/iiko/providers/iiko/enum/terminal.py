"""
the enumeration of terminal
"""
from enum import Enum


class TerminalGroupIDSEnum(str, Enum):
    """
    The enumeration of terminal group ids.
    """
    DEVELOPER = "ec9f109d-5917-45ad-beee-5b2e7904df55"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
