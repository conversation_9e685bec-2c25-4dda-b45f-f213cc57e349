"""
init the category serialization
"""
from rest_framework import serializers

from apps.product.models import Category


class CategorySerializer(serializers.ModelSerializer):
    """
    the category serialization
    """
    class Meta:
        """
        the meta fields
        """
        model = Category
        fields = "__all__"


class CategoryBulkUpdateSerializer(serializers.Serializer):
    """
    Serializer for bulk updating categories
    """
    id = serializers.IntegerField()
    title_uz = serializers.CharField(required=False, allow_null=True)
    title_ru = serializers.CharField(required=False, allow_null=True)
    title_en = serializers.CharField(required=False, allow_null=True)
    position_id = serializers.IntegerField(required=False)


class CategoryBulkUpdateInputSerializer(serializers.Serializer):
    """
    Input serializer for bulk category updates
    """
    categories = CategoryBulkUpdateSerializer(many=True)
