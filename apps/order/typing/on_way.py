"""
init order typing
"""
from typing import Literal

from pydantic import BaseModel


class OrderData(BaseModel):
    """
    new order message data field
    """
    order_id: int
    status: Literal["on_way"]


class OrderOnWay(BaseModel):
    """
    new order message model type
    """
    type: Literal['order_on_way']
    data: OrderData

    @classmethod
    def make_message(cls, order_id):
        """
        make order on way message
        """
        return cls(
            type="order_on_way",
            data=OrderData(
                order_id=order_id,
                status="on_way"
            )
        )

    def to_firebase(self):
        """
        convert to firebase message
        """
        return {
            'type': str(self.type),
            'order_id': str(self.data.order_id),
            'status': str(self.data.status)
        }


class OrderCancelled(BaseModel):
    """
    new order message model type
    """
    type: Literal['order_on_way']
    data: OrderData

    @classmethod
    def make_message(cls, order_id):
        """
        make order on way message
        """
        return cls(
            type="order_cancelled",
            data=OrderData(
                order_id=order_id,
                status="cancelled"
            )
        )
