"""
the order signals
"""
import logging

from django.conf import settings
from django.dispatch import receiver
from django.db.models.signals import post_save

from apps.order.models import Order
from apps.bot.enum import OrderStates
from apps.loyalty.models import PromoCodeUsage
from apps.order.service import OrderService
from apps.bot.tasks import send_message_task
from apps.order.enums.delivery import StreetEnum
from apps.order.enums.delivery import DeliveryType
from apps.payment.models.method import PaymentMethod
from apps.core.exceptions import ServiceAPIException
from apps.iiko.providers.iiko import enum as iiko_enums
from apps.product.models.modifier import ModifierItems
from apps.organization.models.terminal import TerminalGroup
from apps.product.models.modifier import ModiferProductPrices
from apps.core.enums.initiator import Initiator as InitiatorEnum
from apps.order.enums.order import OrderStatus as OrderStatusEnum
from apps.product.models.orderd_product import OrderedProductModifier
from apps.order.helper.order_with_provider import order_with_provider_helper
from apps.loyalty.service import LoyaltyService
from apps.iiko.providers.iiko.http.response import OrderCreateWithoutGhostResponsetBody
from apps.iiko.providers.iiko.http.request import Order as OrderType, Payment, Item, DeliveryPoint, Coordinates, Address, Street, Modifier, LoyaltyInfo# noqa

logger = logging.getLogger(__name__)

ORDER_CREATED_TEXT = {
    "ru": "🥘 Для вас создан заказ № {ID}. Наши операторы свяжутся с вами в ближайшее время.",
    "uz": "🥘 Siz uchun № {ID} raqamli buyurtma yaratildi. Yaqin orada bizning operatorlarimiz siz bilan bog'lanishadi" # noqa
}

COURIER_ON_WAY = {
    "uz": "🚀 Kuryer siz tomon shoshilmoqda\n\n📞 +{courier_number}\n👨‍✈️ Kuryer ismi: {courier_name}",
    "ru": "🚀 Курьер уже спешит к вам\n\n📞 +{courier_number}\n👨‍✈️ Имя курьера: {courier_name}"
}

ORDER_APPROVED_DELIVERY = {
    "uz": "🍔 № {ID} raqamli buyurtma qabul qilindi. 🛵 Taxminiy yetkazish vaqti 30 daqiqa",
    "ru": "🍔 Заказ № {ID} принят. 🛵 Ожидаемое время доставки 30 минут"
}

ORDER_APPROVED_PICKUP = {
    "uz": "🍔 № {ID} raqamli buyurtma qabul qilindi.",
    "ru": "🍔 Заказ № {ID} принят. "
}

ORDER_TIMEOUT = {
    "uz": "🚫 № {ID} raqamli buyurtma bekor qilindi",
    "ru": "🚫 Заказ № {ID} отменен."
}

COURIER_ARRIVED = {
    "uz": "🏁 Kuryer manzilga yetib keldi\n👨‍✈️ Kuryer ismi: {courier_name}\n📞 +{courier_phone}",
    "ru": "🏁 Курьер прибыл на место\n👨‍✈️ Имя курьера: {courier_name}\n📞 +{courier_phone}"
}

ORDER_DELIVERED = {
    "uz": "😋Buyurtmangiz uchun rahmat. Yoqimli ishtaha!",
    "ru": "😋 Спасибо за ваш заказ. Приятного аппетита!"
}

# handling order status change signals


def prepare_and_create_iiko(instance):
    """
    Process order updates
    """
    if instance.status == OrderStatusEnum.APPROVED:
        try:
            order_with_provider_helper.is_can_create_order(organization=instance.organization)
        except Exception as exc:
            raise ServiceAPIException(
                error_type="internal_error",
                message=f"Check can create order failed exc: {exc}",
                status_code=500
            ) from exc

        # Notification is now handled by the signal handler
        create_delivery_for_iiko(instance)
        return

    # Notification is now handled by the signal handler


def create_delivery_for_iiko(instance: Order) -> OrderCreateWithoutGhostResponsetBody:
    """
    Create delivery for iiko and handle the response
    """
    cleaned_phone = ''.join(filter(str.isdigit, instance.order_detail.phone))

    if int(cleaned_phone) == 998990000000:
        logger.info("Order %s blocked for phone %s",
                    instance.id, instance.order_detail.phone)

        return None

    items = []
    for item in instance.order_items.all():
        for attribute in item.product.attributes_all:
            modifers = []
            comment = item.product.comment

            ordered_modifers = OrderedProductModifier.get_by_ordered_product_id(
                item.product.id
            )

            for ordered_modifer in ordered_modifers:
                modifer_id = ordered_modifer.modifier_item.id

                price = ModiferProductPrices.get_ordered_modifer_item_price(
                    organization_id=instance.organization.id,
                    modifer_id=modifer_id
                )
                group_id, product_id = ModifierItems.get_item_group_and_item_id(
                    modifier_id=modifer_id
                )
                modifers.append(
                    Modifier.make_obj(
                        product_id=product_id,
                        amount=ordered_modifer.quantity,
                        product_group_id=group_id,
                        price=price.price
                    )
                )

            if attribute.size != "Default":
                comment = f"({attribute.size}) - {item.product.comment}"

            item_data = Item(
                productId=attribute.external_id,
                amount=item.quantity,
                type="Product",
                comment=comment,
                productSizeId=attribute.size_id or None,
                modifiers=modifers
            )
            items.append(item_data)

    # adding modifiers to items
    delivery_point = None
    total_cost = instance.order_detail.total_cost
    order_type_id = iiko_enums.OrderTypesEnum.DELIVERY_BY_PICKUP

    if instance.order_detail.delivery_type == DeliveryType.DELIVERY:
        street_id = instance.order_detail.street_id
        street_name = instance.order_detail.delivery_address

        if instance.order_detail.subaddress:
            street_name = instance.order_detail.subaddress

        if not street_id:
            street_id = StreetEnum.DEFAULT_STREET_ID.value

        address = Address(
            street=Street(
                name=street_name,
                id=street_id,
            ),
            type="legacy"
        )
        if instance.order_detail.house:
            address.house = instance.order_detail.house

        else:
            address.house = "-"

        delivery_point = DeliveryPoint(
            address=address,
            coordinates=Coordinates(
                latitude=instance.order_detail.latitude,
                longitude=instance.order_detail.longitude
            ),
            comment=instance.order_detail.subaddress
        )
        order_type_id = iiko_enums.OrderTypesEnum.DELIVERY_BY_AGENT

        if instance.initiator == InitiatorEnum.BOT.value:
            delivery_point.comment = instance.order_detail.delivery_address

        items.append(Item(
            productId=instance.order_detail.delivery_cost_id,
            amount=1,
            type="Product"
        ))

    order_comment = instance.order_detail.comment.capitalize()
    payment = create_payment(instance, total_cost)

    # Check for promo code - either from order_detail.promo or active loyalty program
    promo_usage = None
    loyalty_info = None

    # If order already has a promo applied during creation, use that
    if instance.order_detail.promo:
        coupon_name = instance.order_detail.promo.name
        loyalty_info = LoyaltyInfo(coupon=coupon_name)

        logger.info(
            "Using promo code %s already applied during order creation for order %s",
            coupon_name, instance.id
        )

        # Find the promo usage record for this promo
        if instance.user:
            promo_usage = PromoCodeUsage.objects.filter(
                user=instance.user,
                promo=instance.order_detail.promo,
                is_used=False
            ).first()

    order = OrderType(
        sourceKey="telegram bot",
        phone=instance.order_detail.phone,
        orderTypeId=order_type_id,
        payments=[payment],
        items=items,
        completeBefore=instance.order_detail.complete_before,
        deliveryPoint=delivery_point,
        deliveryDuration=instance.order_detail.delivery_duration,
        externalNumber=str(instance.id),
        comment=order_comment,
        loyaltyInfo=loyalty_info
    )

    organization_id = instance.organization.external_id
    terminal_group_id = TerminalGroup.get_active_terminal_group(instance.organization.id).external_id

    response = order_with_provider_helper.create_delivery(
        organization_id=organization_id,
        terminal_group_id=terminal_group_id,
        order=order
    )

    OrderService.set_order_corelation_id(instance.id, response.correlationId)

    if promo_usage and response.orderInfo.creationStatus == iiko_enums.OrderStatus.IN_PROGRESS:
        try:
            # If promo usage is not already marked as used, mark it now
            if not promo_usage.is_used:
                promo_usage.mark_as_used(
                    order_id=instance.id,
                    discount_amount=instance.order_detail.promo.promo_amount if instance.order_detail.promo else 0,
                    order_total=total_cost
                )
                logger.info(
                    "Marked promo code %s as used for user %s on order %s",
                    promo_usage.promo.name, instance.user.id, instance.id
                )
        except Exception as e:
            logger.error("Failed to mark promo code as used: %s", e)

    update_order_status_with_iiko_response(instance, response)
    send_notification_for_delivery_creation(instance, response)

    return response


def create_payment(instance, total_cost):
    """
    Creates a Payment object based on the instance and total cost.
    """
    payment_method = PaymentMethod.get_by_code(
        code=instance.order_detail.payment_method,
        organization_id=instance.organization.id
    )

    return Payment(
        sum=total_cost,
        isFiscalizedExternally=False,
        isProcessedExternally=payment_method.is_processed_externally,
        paymentTypeKind=payment_method.payment_type_kind,
        paymentTypeId=payment_method.external_id
    )


def update_order_status_with_iiko_response(
    instance: Order,
    response: OrderCreateWithoutGhostResponsetBody
):
    """
    Update the order status based on the iiko response.
    """
    if response.orderInfo.creationStatus == iiko_enums.OrderStatus.IN_PROGRESS:
        instance.status = OrderStatusEnum.IN_PROGRESS
        instance.external_id = response.orderInfo.id
        instance.save()

    elif response.orderInfo.errorInfo:
        instance.mark_as_creation_error()
        instance.save()


def send_notification_for_delivery_creation(
    instance: Order,
    response: OrderCreateWithoutGhostResponsetBody,
):
    """
    Send a notification for delivery creation to the orders channel.
    """
    iiko_order_id = response.orderInfo.id
    iiko_creation_status = response.orderInfo.creationStatus
    error = response.orderInfo.errorInfo

    message = (
        "✅ Order has been sent successfully to iiko Front\n\n"
        "OrderId: {}\nIIKO creation status: {}\n"
        "IIKO OrderID: {}\nIIKO error: {}"
    ).format(instance.id, iiko_creation_status, iiko_order_id, error)

    send_message_task.delay(
        chat_id=settings.ORDERS_CHANNEL,
        message=message
    )


@receiver(post_save, sender=Order)
def handle_order_signal(sender, instance: Order, created=False, **kwargs):
    """
    Handle post-save signals for the Order model.

    This signal triggers when an Order object is saved and sends
    appropriate notifications to Telegram users based on the order status.
    It also handles promo code usage when an order is canceled.

    Args:
        sender: The model class that sent the signal (Order in this case).
        instance: The instance of the Order being saved.
        created: Boolean indicating if this is a new instance.
        kwargs: Additional arguments passed with the signal.
    """
    # Call the function to send update notification for telegram user
    # Unused parameters are required by the signal receiver
    send_update_notification_for_telegram_user(instance)

    # Handle promo code reversion when order is canceled
    if instance.status in [OrderStatusEnum.CANCELLED, OrderStatusEnum.TIMEOUT]:
        try:
            # Revert promo code usage for this order using the LoyaltyService
            success = LoyaltyService.revert_promo_code_for_order(instance.id)
            if success:
                logger.info(f"Reverted promo code usage for canceled order {instance.id}")
        except Exception as e:
            logger.error(f"Error handling promo code reversion for order {instance.id}: {e}")


def send_update_notification_for_telegram_user(instance: Order):
    """
    Send update notifications to the Telegram user based on order status changes.
    """
    state = None
    message = None

    if instance.status not in [
        OrderStatusEnum.CREATED,
        OrderStatusEnum.APPROVED,
        OrderStatusEnum.ON_WAY,
        OrderStatusEnum.ARRIVED,
        OrderStatusEnum.CLOSED,
        OrderStatusEnum.TIMEOUT,
        OrderStatusEnum.DELIVERED,
        OrderStatusEnum.CANCELLED,
    ] or instance.initiator != InitiatorEnum.BOT:
        return

    tg_user = instance.get_tg_user()
    chat_id = tg_user.id

    if instance.status in [
        OrderStatusEnum.APPROVED,
    ]:
        state = OrderStates.ORDER_APPROVED

        message = ORDER_APPROVED_PICKUP.get(tg_user.lang).format(ID=instance.id)

        if instance.order_detail.delivery_type == DeliveryType.DELIVERY:
            message = ORDER_APPROVED_DELIVERY.get(tg_user.lang).format(ID=instance.id)

    elif instance.status == OrderStatusEnum.ON_WAY:
        state = OrderStates.COURIER_ON_WAY
        courier_name = instance.delivery_agent.name
        courier_number = instance.delivery_agent.phone
        message = COURIER_ON_WAY.get(tg_user.lang).format(
            courier_number=courier_number,
            courier_name=courier_name
        )

    elif instance.status == OrderStatusEnum.ARRIVED:
        state = OrderStates.COURIER_ARRIVED
        courier_name = instance.delivery_agent.name
        courier_phone = instance.delivery_agent.phone

        message = COURIER_ARRIVED.get(tg_user.lang).format(
            courier_name=courier_name,
            courier_phone=courier_phone
        )

    elif instance.status == OrderStatusEnum.DELIVERED:
        state = OrderStates.ORDER_DELIVERED
        message = ORDER_DELIVERED.get(tg_user.lang)

    elif instance.status == OrderStatusEnum.CLOSED and instance.order_detail.is_self_call():
        state = OrderStates.ORDER_CLOSED
        message = ORDER_DELIVERED.get(tg_user.lang)

    elif instance.status in [
        OrderStatusEnum.TIMEOUT,
        OrderStatusEnum.CANCELLED,
    ]:
        state = OrderStates.ORDER_TIMEOUT
        message = ORDER_TIMEOUT.get(tg_user.lang).format(ID=instance.id)

    if not message:
        return

    task_id = f"order:{instance.status}:{instance.id}"
    send_message_task.apply_async(
        kwargs={
            'chat_id': chat_id,
            'message': message,
            "state": state,
            "order_id": instance.id,
        },
        task_id=task_id,
    )
