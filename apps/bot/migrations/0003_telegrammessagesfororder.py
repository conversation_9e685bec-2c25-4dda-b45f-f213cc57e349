# Generated by Django 5.0.6 on 2024-08-27 07:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bot', '0002_initial'),
        ('order', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='TelegramMessagesForOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('message_id', models.IntegerField()),
                ('message', models.TextField()),
                ('state', models.CharField(max_length=255)),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='order.order')),
            ],
            options={
                'verbose_name': 'Telegram Messages For Order',
                'verbose_name_plural': 'Telegram Messages For Orders',
                'db_table': 'telegram_messages_for_order',
                'ordering': ['-created_at'],
                'unique_together': {('order', 'message_id')},
            },
        ),
    ]
