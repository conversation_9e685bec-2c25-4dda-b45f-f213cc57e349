"""
Celery tasks for map application.

This module contains Celery tasks for map-related operations,
including daily API limit resets and usage monitoring.
"""

import logging
from celery import shared_task
from django.utils import timezone
from apps.map.models.api_keys import MapApiKey
from apps.map.models.api_usage import ApiUsage


logger = logging.getLogger(__name__)


@shared_task(bind=True, name='map.reset_daily_api_limits')
def reset_daily_api_limits(self):
    """
    Celery task to reset daily API limits for all map API keys.
    
    This task should be scheduled to run daily at midnight (or any preferred time)
    to reset the limit_expired flag for all API keys, allowing them to be used
    again for the new day.
    
    Returns:
        dict: Task execution results with statistics
    """
    try:
        current_time = timezone.now()
        logger.info(f"Starting daily API limit reset task at {current_time}")
        
        # Get statistics before reset
        total_keys = MapApiKey.objects.count()
        expired_keys_count = MapApiKey.objects.filter(limit_expired=True).count()
        
        # Reset the limits
        reset_count = MapApiKey.reset_daily_limits()
        
        # Log the results
        logger.info(f"Daily API limit reset completed:")
        logger.info(f"  - Total API keys: {total_keys}")
        logger.info(f"  - Keys that were expired: {expired_keys_count}")
        logger.info(f"  - Keys reset: {reset_count}")
        
        # Get Yandex Geosuggestion specific statistics
        yandex_geo_keys = MapApiKey.objects.filter(
            app_name=MapApiKey.YANDEX_MAP,
            usage_type=MapApiKey.YANDEX_GEOSUGGESTION
        )
        yandex_geo_total = yandex_geo_keys.count()
        yandex_geo_active = yandex_geo_keys.filter(limit_expired=False).count()
        
        logger.info(f"  - Yandex Geosuggestion keys: {yandex_geo_active}/{yandex_geo_total} active")
        
        return {
            'status': 'success',
            'timestamp': current_time.isoformat(),
            'total_keys': total_keys,
            'expired_keys': expired_keys_count,
            'reset_count': reset_count,
            'yandex_geosuggestion': {
                'total': yandex_geo_total,
                'active': yandex_geo_active
            }
        }
        
    except Exception as e:
        logger.error(f"Error in daily API limit reset task: {str(e)}")
        # Re-raise the exception so Celery can handle it properly
        raise self.retry(exc=e, countdown=300, max_retries=3)
