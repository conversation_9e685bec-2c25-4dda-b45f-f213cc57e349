"""
init order finish view for delivery agents
"""
from rest_framework import views
from rest_framework import response, permissions

from apps.core.views import ServiceBaseView
from apps.order.service import OrderService
from apps.order.helper import order_status_helper
from apps.order.serializers.order import OrderUpdateSerializer
from apps.operations.permissions import IsAdminOrManagerOrOperator


class OrderStatusAPIView(
    views.APIView, ServiceBaseView
):
    """
    the order finish api view for delivery agents
    """
    def get(self, request, *args, **kwargs):
        """
        the post method for finish an order.
        """
        order_id = kwargs.get("order_id")

        try:
            order = order_status_helper.get_order_by_id(order_id)

        except Exception as exc:
            msg = f"finish order failed exc: {exc}"
            raise self.call_service_exception(
                error_type="internal_error",
                message=msg,
                status_code=500,
                notify_admin=True,
            )

        return response.Response({
            "status": order.status,
        })

    def put(self, request, *args, **kwargs):
        """
        the post method for finish an order.
        """
        self.permission_classes = [permissions.IsAuthenticated, IsAdminOrManagerOrOperator]
        self.check_permissions(request)

        serializer = OrderUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        order_id = kwargs.get("order_id")
        stutus = serializer.validated_data.get("status")

        try:
            order = order_status_helper.get_order_by_id(order_id)

        except Exception as exc:
            msg = f"finish order failed exc: {exc}"
            raise self.call_service_exception(
                error_type="internal_error",
                message=msg,
                status_code=500,
                notify_admin=True,
            )

        try:
            order_status_helper.update_order_status(
                order=order,
                new_status=stutus
            )
            return response.Response()

        except Exception as exc:
            msg = f"finish order failed exc: {exc}"
            raise self.call_service_exception(
                error_type="internal_error",
                message=msg,
                status_code=500,
                notify_admin=True,
            )


class OrderCreationStatusAPIView(
    views.APIView, ServiceBaseView
):
    """
    the order creation status api view for delivery agents
    """
    def get(self, request, *args, **kwargs):
        """
        the post method for order creation status api view
        """
        order_id = kwargs.get("order_id")

        try:
            status = OrderService.check_creation_status(order_id)

        except Exception as exc:
            msg = f"get order creation status failed exc: {exc}"
            raise self.call_service_exception(
                error_type="internal_error",
                message=msg,
                status_code=500,
                notify_admin=True,
            )

        return response.Response({
            "status": status
        })
