"""
the not found exceptions of payme
"""
from apps.payment.providers.payme.exceptions.base import BasePaymeException # noqa


class MethodNotFound(BasePaymeException):
    """
    MethodNotFound APIException
    That is raised when the method does not exist.
    """
    status_code = 405
    error_code = -32601
    message = 'Method not found'


class TransactionDoesNotExist(BasePaymeException):
    """
    TransactionDoesNotExist APIException
    That is raised when a transaction does not exist or deleted.
    """
    status_code = 200
    error_code = -31050
    message = {
        "uz": "Buyurtma topilmadi",
        "ru": "Заказ не существует",
        "en": "Order does not exists"
    }


class InternalServiceError(BasePaymeException):
    """
    PerformTransactionFailed APIException
    That is raised when a transaction failed to perform.
    """
    status_code = 200
    error_code = -32400
    message = {
        "uz": "Tizimda xatolik yuzaga keldi",
        "ru": "Внутренняя ошибка сервиса",
        "en": "Internal service error"
    }
