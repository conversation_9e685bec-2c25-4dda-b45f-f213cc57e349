"""
Storage service for handling file operations
"""
import os
import uuid
import logging
from typing import Optional

from django.core.files.base import ContentFile
from django.core.files.storage import default_storage


logger = logging.getLogger(__name__)


class StorageService:
    """Service for handling storage operations"""

    BOT_PHOTOS_DIR = "bot_photos"

    @classmethod
    def save_file(cls, file_content: bytes, original_filename: str) -> str:
        """
        Save file to storage

        Args:
            file_content: File content
            original_filename: Original filename

        Returns:
            str: Path where file was saved

        Raises:
            Exception: If saving fails
        """
        file_ext = os.path.splitext(original_filename)[1]
        unique_filename = f"{cls.BOT_PHOTOS_DIR}/{uuid.uuid4()}{file_ext}"

        try:
            file_path = default_storage.save(
                unique_filename,
                ContentFile(file_content)
            )
            logger.info(f"Saved file to storage: {file_path}")
            return file_path
        except Exception as e:
            logger.error(f"Failed to save file: {str(e)}")
            raise

    @staticmethod
    def get_file_url(file_path: str) -> str:
        """
        Get URL for stored file

        Args:
            file_path: Path to file in storage

        Returns:
            str: Public URL for the file
        """
        return default_storage.url(file_path)

    @staticmethod
    def delete_file(file_path: str) -> None:
        """
        Delete file from storage

        Args:
            file_path: Path to file in storage
        """
        try:
            default_storage.delete(file_path)
            logger.info(f"Deleted file from storage: {file_path}")
        except Exception as e:
            logger.error(f"Failed to delete file {file_path}: {str(e)}")

    @classmethod
    def cleanup_file(cls, file_path: Optional[str], error_context: str = "") -> None:
        """
        Safely cleanup file with error logging

        Args:
            file_path: Path to file to cleanup
            error_context: Context for error logging
        """
        if not file_path:
            return

        try:
            cls.delete_file(file_path)
        except Exception as e:
            logger.error(
                f"Error cleaning up file {file_path} "
                f"during {error_context}: {str(e)}"
            )
