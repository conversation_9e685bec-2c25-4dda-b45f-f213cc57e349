"""
handling iiko updates requests with using queue management
"""
from celery import shared_task

from master_kebab.settings import ORDERS_CHANNEL

from apps.iiko import serializers
from apps.bot.tasks import send_message_task
from apps.iiko.helpers.updates import update_event_helper
from apps.iiko.providers.iiko.enum.event import EventTypesEnum


def process_update_event(event_type, event):
    event_serializer_map = {
        EventTypesEnum.PERSONAL_SHIFT: serializers.PersonalShiftUpdateEvent,
        EventTypesEnum.DELIVERY_ORDER_UPDATE: serializers.DeliveryOrderUpdateEvent,
        EventTypesEnum.KITCHEN_ORDER_UPDATE: serializers.KitchenOrderUpdateEvent,
        EventTypesEnum.DELIVERY_ORDER_ERROR: serializers.DeliveryOrderErrorEvent,
    }

    if event_type in event_serializer_map:
        update_event = event_serializer_map[event_type](event_body=[event])
        update_event_helper.process_update_event(update_event)

    elif event_type == EventTypesEnum.DELIVERY_ORDER_ERROR:
        update_event_helper.process_update_event(update_event)


@shared_task(queue='iiko_updates')
def process_update_event_task(event_type, event):
    try:
        process_update_event(event_type, event)
    except Exception as exc:
        send_message_task.delay(
            chat_id=ORDERS_CHANNEL,
            message=f"Error processing event: {event}\n\nError: {exc}"
        )
