"""
Throttle classes to limit requests per user and URL path:
- OneRequestPerFiveSecondsThrottle: 1 request per 5 seconds.
- OrderCreateThrottle: 1 request per 5 seconds for orders.
- OTPRequestThrottle: 1 request per 60 seconds for OTP.
"""
from time import time
from django.core.cache import cache
from rest_framework.throttling import BaseThrottle


class ServiceBaseThrottle(BaseThrottle):
    """
    A base throttle class that provides common functionality for throttling.
    """

    def allow_request(self, request, view):
        user_id = self.get_user_info(request)
        cache_key = self.get_cache_key(user_id, request.path)
        return self.is_request_allowed(cache_key)

    def wait(self):
        """
        Returns the throttling wait time in seconds.
        """
        return 5

    def get_user_info(self, request):
        """
        Returns the user ID if authenticated, or the remote address if unauthenticated.
        """
        return request.user.id if request.user.is_authenticated else request.META.get('REMOTE_ADDR')

    def get_cache_key(self, user_id, path):
        """
        Returns the cache key based on user ID and URL path.
        """
        raise NotImplementedError("Subclasses should implement this method.")

    def is_request_allowed(self, cache_key):
        """
        Checks if the request is allowed based on the cache key.
        """
        last_request_time = cache.get(cache_key)
        current_time = time()

        if last_request_time is None or current_time - last_request_time >= 1:
            cache.set(cache_key, current_time, timeout=5)
            return True

        return False


class OneRequestPerFiveSecondsThrottle(ServiceBaseThrottle):
    """
    A throttle class that limits each user to one request per 5 seconds for each specific URL path.
    """

    def get_cache_key(self, user_id, path):
        """
        Returns the cache key based on user ID and URL path.
        """
        return f"{user_id}:{path}"


class OrderCreateThrottle(ServiceBaseThrottle):
    """
    A throttle class that limits each user to one request per 5 seconds for creating orders.
    """
    def get_cache_key(self, user_id, path):
        """
        Returns the cache key for order creation based on user ID.
        """
        return f"order_create:{user_id}"


class OTPRequestThrottle(BaseThrottle):
    """
    A throttle class that limits each user to one request per 60 seconds for OTPRequest
    """
    rate = '1/60'

    def get_cache_key(self, request):
        phone = request.data.get('phone')

        return f"otp_request:{phone}"

    def allow_request(self, request, view):
        cache_key = self.get_cache_key(request)
        last_request_time = cache.get(cache_key)
        current_time = time()

        if last_request_time is None or current_time - last_request_time >= 60:
            cache.set(cache_key, current_time, timeout=60)
            return True

        return False


class TelegramOrderCreateThrottle(BaseThrottle):
    """
    A throttle class that limits each Telegram user (by chat_id) to one order creation request per 5 seconds.
    Uses chat_id from URL path parameters instead of authenticated user ID.
    """

    def get_cache_key(self, request, view):
        """
        Extract chat_id from URL path and create cache key.
        """
        # Get chat_id from URL kwargs
        chat_id = view.kwargs.get('chat_id')
        if not chat_id:
            # Fallback to remote address if chat_id is not available
            chat_id = request.META.get('REMOTE_ADDR', 'unknown')

        return f"telegram_order_create:{chat_id}"

    def allow_request(self, request, view):
        """
        Check if the request is allowed based on chat_id throttling.
        """
        cache_key = self.get_cache_key(request, view)
        last_request_time = cache.get(cache_key)
        current_time = time()

        # Allow one request per 5 seconds
        if last_request_time is None or current_time - last_request_time >= 5:
            cache.set(cache_key, current_time, timeout=5)
            return True

        return False

    def wait(self):
        """
        Returns the throttling wait time in seconds.
        """
        return 5
