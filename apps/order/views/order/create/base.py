"""
This module defines a base API view for creating orders in the system.
It handles various steps in the order creation process, including pre-creation checks,
order item initialization, and processing of delivery details and user roles.
"""
from django.db import transaction

from rest_framework import views, response, status
from rest_framework.permissions import IsAuthenticated

from apps.user.models.user import Users
from apps.order.service import OrderService
from apps.core.views import ServiceBaseView
from apps.user.enum.role import UserRoleEnums
from apps.order.models import Order, OrderItem
from apps.order.models.bookmarks import Bookmark
from apps.bot.models.telegram import TelegramUser
from apps.organization.models import Organization
from apps.payment.service import PaymentService
from apps.order.enums.delivery import DeliveryType
from apps.product.models.product import ProductAttribute
from apps.order.typing.order import OrderCreateSerializer
from apps.core.enums.initiator import Initiator as InitiatorEnum
from apps.core.enums.system import SystemParamsEnum
from apps.core.models.system.parameters import SystemParameter
from apps.core.throttling import OrderCreateThrottle
from apps.bot.tasks.message import send_message_task



ERROR_MESSAGE = "🌟 Botimizda hozirda yangilanish vaqti, ilti<PERSON> buyurtma berish uchun qaytadan /start ni bosing\n\n🌟 В нашем боте сейчас время обновления, пожалуйста, нажмите /start снова, чтобы сделать заказ" # noqa


class BaseOrderCreateAPIView(views.APIView, ServiceBaseView):
    """
    A base class for order creation views.

    This view handles the process of creating new orders, including validation
    of the order initiator, checking if the user is eligible to create an order,
    initializing order items, handling delivery details, and managing operator
    approvals. Subclasses must implement methods for obtaining the serializer and
    order details.
    """
    permission_classes = [IsAuthenticated]
    throttle_classes = [OrderCreateThrottle]

    def post(self, request, initiator, user=None, chat_id=None, operator=None):
        """
        Handles the creation of a new order, including pre-creation checks and order item initialization.

        Parameters:
            request (Request): The HTTP request object.
            initiator (InitiatorEnum): The entity initiating the order.
            user (User, optional): The user creating the order. Defaults to None.
            chat_id (int, optional): The Telegram chat ID, if applicable. Defaults to None.
            operator (User, optional): The operator managing the order, if applicable. Defaults to None.

        Returns:
            Response: The HTTP response containing the order details.

        Raises:
            ServiceAPIException: If validation fails or an error occurs during order creation.
        """

        try:
            self.validate_initiator(initiator)
            data = self.get_serializer(request.data)
            user, operator = self.get_user_and_operator(user, chat_id, data, operator)
            if user:
                self.can_create_order(user)

            order_items, organization = self.initialize_order_items(data)
            order_detail = self.get_order_detail(data, user, chat_id)
            order = self.create_order(order_items, initiator, order_detail, user, organization)

            self.handle_delivery(order)
            self.update_bookmarks(order, user)
            self.update_user_info(user, order_detail)
            self.process_operator_approval(order, operator)
            return self.build_response(order, user, chat_id)

        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to create order\n\nRequest Body\n{request.data}\n\nError Respponse\n: {exc}",
                status_code=500,
                notify_admin=True
            ) from exc

    def validate_initiator(self, initiator):
        """
        Validate the initiator of the order.

        Parameters:
            initiator (InitiatorEnum): The entity initiating the order.

        Raises:
            ServiceAPIException: If the initiator is unknown.
        """
        if initiator == InitiatorEnum.UNKNOWN:
            raise self.call_service_exception(
                error_type="bad_request",
                message="Invalid initiator",
                status_code=400
            )

    def can_create_order(self, user):
        """
        Check if the user can create an order, enforcing business rules like daily order limits.

        Parameters:
            user (User): The user attempting to create an order.

        Raises:
            ServiceAPIException: If the user has reached the daily order limit.
        """
        max_orders_per_day = SystemParameter.get_sys_params(
            SystemParamsEnum.MAX_ORDERS_PER_DAY.value
        )
        max_orders_per_day = int(max_orders_per_day.value)

        order_count = Order.can_create_order(user, max_orders_per_day)

        if order_count >= max_orders_per_day:
            message = {
                "en":  f"You have already reached the maximum number of orders per day ({max_orders_per_day}).",
                "ru":  f"Вы уже достигли предельного количества заказов за день ({max_orders_per_day}).",
                "uz":  f"Siz faqat bir kunda ({max_orders_per_day}) tagacha miqdorda buyurtma berishingiz mumkin."
            }
            raise self.call_service_exception(
                error_type="order_limit_exceeded",
                message=message,
                status_code=400
            )

    def initialize_order_items(self, data: OrderCreateSerializer, organization=None):
        """
        Initialize the order items and determine the associated organization.

        Parameters:
            data (dict): The data containing the order items.

        Returns:
            tuple: A tuple containing the list of initialized order items and the organization.

        Raises:
            ServiceAPIException: If an error occurs during initialization.
        """
        organization_id = data.order_detail.organization_id

        if organization_id:
            organization = Organization.get_by_id(organization_id)

        try:
            order_items = []

            with transaction.atomic():
                for item in data.items:
                    order_item = OrderItem.init_order_item(
                        product_id=item.id,
                        quantity=item.quantity,
                        comment=item.comment,
                        attribute=item.attribute,
                        modifiers=item.modifiers if hasattr(item, 'modifiers') else None
                    )
                    order_items.append(order_item)

                    if not organization:
                        organization = ProductAttribute.get_organization(item.attribute.id)

            return order_items, organization
        except Exception as exc:
            raise self.bad_request_exception(f"Failed to initialize order items: {str(exc)}") from exc

    def get_user_and_operator(self, user, chat_id, data, operator):
        """
        Determine the user and operator based on the context, such as the chat ID and user roles.

        Parameters:
            user (User): The user creating the order.
            chat_id (int): The Telegram chat ID, if applicable.
            data (dict): The order data.
            operator (User): The operator managing the order, if applicable.

        Returns:
            tuple: A tuple containing the user and operator.

        Raises:
            ServiceAPIException: If an error occurs while determining the user or operator.
        """
        try:
            if chat_id:
                user = TelegramUser.get_by_chat_id(chat_id).user

            if user.role in [
                UserRoleEnums.OPERATOR,
                UserRoleEnums.ADMIN,
                UserRoleEnums.MANAGERS
            ]:
                operator = user
                user = Users.get_by_id(data.order_detail.user_id)

            return user, operator

        except TelegramUser.DoesNotExist as exc:
            send_message_task.delay(
                message=ERROR_MESSAGE,
                chat_id=chat_id,
            )
            raise self.call_service_exception(
                error_type="not_found",
                message=f"Telegram user does not exist for create order chat_id: {chat_id}",
                status_code=404,
                notify_admin=True
            ) from exc

        except Exception as exc:
            raise self.bad_request_exception(f"Failed to determine user and operator: {str(exc)}") from exc

    def create_order(self, order_items, initiator, order_detail, user, organization):
        """
        Create a new order with the provided details.

        Parameters:
            order_items (list): The list of order items.
            initiator (InitiatorEnum): The entity initiating the order.
            order_detail (OrderDetail): The detailed information about the order.
            user (User): The user creating the order.
            organization (Organization): The organization associated with the order.

        Returns:
            Order: The created order instance.

        Raises:
            ServiceAPIException: If an error occurs during order creation.
        """
        try:
            return OrderService.create_order(
                order_items=order_items,
                initiator=initiator,
                order_detail=order_detail,
                user=user,
                organization=organization,
            )

        except Exception as exc:
            raise self.bad_request_exception(f"Failed to create order: {str(exc)}") from exc

    def handle_delivery(self, order: Order):
        """
        Handle the delivery details for the given order, including setting delivery duration and completion time.

        Parameters:
            order (Order): The order instance for which delivery details are to be handled.

        Raises:
            ServiceAPIException: If an error occurs during delivery handling.
        """
        try:
            order_detail = order.order_detail
            organization = order.organization

            # Determine the appropriate delivery duration based on delivery type
            if order_detail.delivery_type == DeliveryType.DELIVERY:
                delivery_duration = organization.delivery_duration
            elif order_detail.delivery_type == DeliveryType.SELF_CALL:
                delivery_duration = organization.self_service_duration

            else:
                raise self.bad_request_exception("Invalid delivery type.")

            # Set the delivery duration and completion time
            order_detail.delivery_duration = delivery_duration

            if not order_detail.complete_before:
                # order_detail.set_complete_before(delivery_duration)
                pass

            order_detail.save()

        except Exception as exc:
            raise self.bad_request_exception(f"Failed to handle delivery details: {str(exc)}") from exc

    def update_bookmarks(self, order: Order, user):
        """
        Update or create bookmarks for the user's delivery address.

        Parameters:
            order (Order): The created order instance.
            user (User): The user who created the order.

        Raises:
            ServiceAPIException: If an error occurs while updating bookmarks.
        """
        try:
            if order.order_detail.delivery_type != DeliveryType.DELIVERY:
                # we don't want to update bookmarks if delivery type is not specified deliery
                return

            Bookmark.get_or_update(
                user=user,
                address=order.order_detail.delivery_address,
                lat=order.order_detail.latitude,
                long=order.order_detail.longitude,
                order=order,
                street_id=order.order_detail.street_id
            )
        except Exception as exc:
            raise self.bad_request_exception(f"Failed to update bookmarks: {str(exc)}") from exc

    def process_operator_approval(self, order: Order, operator):
        """
        Automatically approve the order if the operator is involved.

        Parameters:
            order (Order): The created order instance.
            operator (User): The operator managing the order.

        Raises:
            ServiceAPIException: If an error occurs during operator approval processing.
        """
        if operator is not None:
            order.taker_id = operator.id
            order.taker_type = operator.role
            order.save()

        try:
            if order.is_cash and operator:
                if order.is_created():
                    order.mark_as_approved()

            if not order.is_cash and order.is_initiator_call_center:
                PaymentService.send_invoice(order_id=order.id)

        except Exception as exc:
            raise self.bad_request_exception(f"Failed to process operator approval: {str(exc)}") from exc

    def build_response(self, order, user, chat_id):
        """
        Build the response data for the created order.

        Parameters:
            order (Order): The created order instance.
            user (User): The user who created the order.
            chat_id (int, optional): The Telegram chat ID, if applicable.

        Returns:
            Response: The HTTP response containing the order details.
        """
        try:
            response_data = {
                "order_id": order.id,
                "total_cost": order.order_detail.total_cost,
                "status": order.status,
                "created_at": order.created_at
            }

            if user:
                response_data["user_id"] = user.id
            if chat_id:
                response_data["chat_id"] = chat_id

            return response.Response(response_data, status=status.HTTP_201_CREATED)
        except Exception as exc:
            return response.Response(
                {"error": f"Failed to build response: {str(exc)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_serializer(self, data):
        """
        Obtain the appropriate serializer for the incoming request data.

        Parameters:
            data (dict): The incoming request data.

        Returns:
            Serializer: The serializer instance.

        Raises:
            NotImplementedError: This method must be implemented in subclasses.
        """
        raise NotImplementedError("get_serializer() must be implemented in subclasses")

    def get_order_detail(self, data, user, chat_id):
        """
        Obtain the order details based on the incoming request data, user, and chat ID.

        Parameters:
            data (dict): The incoming request data.
            user (User): The user creating the order.
            chat_id (int, optional): The Telegram chat ID, if applicable.

        Returns:
            OrderDetail: The detailed information about the order.

        Raises:
            NotImplementedError: This method must be implemented in subclasses.
        """
        raise NotImplementedError("get_order_detail() must be implemented in subclasses")

    def update_user_info(self, user, order_detail):
        """
        Update the user's information based on the order details.

        Parameters:
            user (User): The user whose information needs to be updated.
            order_detail (OrderDetail): The detailed information about the order.
        """
        # Only update if order_detail.name is not None
        if order_detail.name is not None:
            # Handle case where user.name might be None
            user_name_upper = user.name.upper() if user.name is not None else ""
            order_name_upper = order_detail.name.upper()

            if user_name_upper != order_name_upper:
                user.name = order_detail.name
                user.save()
