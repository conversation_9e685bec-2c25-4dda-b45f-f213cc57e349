"""
init polling tasks
"""
import logging

from celery import Task

from master_kebab import celery_app
from apps.core.views import ServiceBaseView
from apps.product.service import ProductService


logger = logging.getLogger(__name__)
service_view = ServiceBaseView()


@celery_app.task(bind=True, acks_late=False, task_acks_on_failure_or_timeout=False)
def update_categories_and_products_task(self: Task, is_manual_update=False):
    try:
        product_service = ProductService()
        product_service.update_products_and_categories(is_manual_update)

    except Exception as exc:
        message = f"Failed to update categories and products exc: {exc}"
        service_view.send_notification(message)
