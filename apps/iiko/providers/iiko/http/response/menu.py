"""
the menu response model
"""
from typing import List, Optional, Any

from pydantic import BaseModel


class Price(BaseModel):
    """
    the price response model
    """
    organizationId: str
    price: Optional[float]


class Restrictions(BaseModel):
    """
    Represents restrictions for a modifier item.
    """
    minQuantity: int
    maxQuantity: int
    freeQuantity: int
    byDefault: int
    hideIfDefaultQuantity: bool


class ModifierItem(BaseModel):
    """
    Represents a modifier item in the menu.
    """
    sku: str
    name: str
    description: str
    restrictions: Restrictions
    allergenGroups: List[Any]
    nutritionPerHundredGrams: dict
    portionWeightGrams: int
    tags: List[Any]
    labels: List[Any]
    itemId: str
    isHidden: bool
    prices: List[Price]
    position: int
    independentQuantity: bool
    productCategoryId: Optional[str]
    customerTagGroups: List[Any]
    paymentSubject: Optional[str]
    outerEanCode: Optional[str]
    measureUnitType: str
    buttonImageUrl: Optional[str]


class ItemModifierGroup(BaseModel):
    """
    Represents a group of modifier items.
    """
    name: str
    description: str
    restrictions: Restrictions
    items: List[ModifierItem]
    canBeDivided: bool
    itemGroupId: Optional[str]
    isHidden: bool
    childModifiersHaveMinMaxRestrictions: bool
    sku: str


class ItemSize(BaseModel):
    """
    the item size response model
    """
    sku: str
    sizeCode: str
    sizeName: str
    isDefault: bool
    prices: List[Price]
    sizeId: Optional[str] = None
    buttonImageUrl: Optional[str] = None
    measureUnitType: Optional[str] = None
    itemModifierGroups: Optional[List[ItemModifierGroup]] = None
    buttonImageCroppedUrl: Optional[dict] = None


class Item(BaseModel):
    """
    the item response model
    """
    itemId: str
    sku: str
    name: str
    description: str
    allergens: List[Any]
    tags: List[Any]
    labels: List[Any]
    itemSizes: List[ItemSize]
    taxCategory: Optional[str]
    modifierSchemaName: str
    type: str
    canBeDivided: bool
    canSetOpenPrice: bool
    useBalanceForSell: bool
    measureUnit: str
    productCategoryId: Optional[str] = None
    customerTagGroups: List[Any]
    paymentSubject: Optional[str]
    outerEanCode: Optional[str]
    isHidden: bool
    orderItemType: str


class ItemCategory(BaseModel):
    """
    the item category response model
    """
    id: str
    name: str
    description: str
    buttonImageUrl: Optional[str]
    headerImageUrl: Optional[str]
    iikoGroupId: Optional[str] = None
    items: List[Item]
    scheduleId: Optional[str] = None
    scheduleName: Optional[str]
    schedules: List[Any]
    isHidden: bool


class MenuByIdResponseBody(BaseModel):
    """
    the menu response model
    """
    productCategories: List[Any]
    customerTagGroups: List[Any]
    name: str
    description: str
    intervals: List[Any]
    itemCategories: List[ItemCategory]


class ItemModel(BaseModel):
    """
    item models
    """
    balance: float
    productId: str
    sku: str
    dateAdd: str


class TerminalGroupItemModel(BaseModel):
    """
    terminal group item model
    """
    terminalGroupId: str
    items: List[ItemModel]


class TerminalGroupStopListModel(BaseModel):
    """
    terminal group stop list model
    """
    organizationId: str
    items: List[TerminalGroupItemModel]


class StopListResponseBody(BaseModel):
    """
    the stop list response model
    """
    correlationId: str
    terminalGroupStopLists: Optional[List[TerminalGroupStopListModel]] = None
