"""
Telegram bot message sender task
"""
import logging

from celery import Task
from telebot.apihelper import ApiTelegramException

from master_kebab import celery_app

from apps.bot.client import bot


logger = logging.getLogger(__name__)


@celery_app.task(
    bind=True,
    max_retries=3,
    default_retry_delay=5,
    queue='bot',
    autoretry_for=(ApiTelegramException,),
    name='apps.bot.tasks.delete.delete_message_task'
)
def delete_message_task(
    self: Task,
    chat_id: str,
    message_id: int
) -> None:
    """
    Delete a message from Telegram chat

    Args:
        chat_id: Telegram chat ID
        message_id: ID of the message to delete
    """
    try:
        bot.delete_message(
            chat_id=chat_id,
            message_id=message_id
        )
        logger.info(f"Successfully deleted message {message_id} from chat {chat_id}")

    except Exception as exc:
        logger.error(f"Unexpected error deleting message {message_id} from chat {chat_id}: {str(exc)}")
        self.retry(exc=exc)
