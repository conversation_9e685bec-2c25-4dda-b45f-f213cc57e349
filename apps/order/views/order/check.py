"""
This module provides an API endpoint for checking orders before creation.
It allows clients to validate order data and get pricing information
before actually creating the order in the system.
"""
import logging
import traceback
from rest_framework import views
from rest_framework.response import Response
from rest_framework import status
from django.core.cache import cache

from apps.core.views import ServiceBaseView
from apps.organization.models import Organization, TerminalGroup
from apps.product.models.product import ProductAttribute, get_product_by_id
from apps.order.service import OrderService
from apps.order.enums.delivery import DeliveryType
from apps.product.models.modifier import ModiferProductPrices
from apps.order.models.order_item import OrderItem
from apps.iiko.service import IIKOService
from apps.user.models import Users
from apps.bot.models.telegram import TelegramUser
from apps.loyalty.models import Promo
from apps.loyalty.serializers import PromoSerializer
from apps.loyalty.service import LoyaltyService


logger = logging.getLogger(__name__)


class OrderCheckAPIView(views.APIView, ServiceBaseView):
    """
    API view for checking orders before creation.

    This endpoint allows clients to validate order data and get pricing information
    before actually creating the order in the system. It returns a cheque with
    the list of products, prices, and total cost.

    Authentication is optional when a chat_id is provided, which allows Telegram users
    to use this endpoint without providing an authentication token.
    """
    permission_classes = []  # No authentication required, we'll handle it in the view

    def post(self, request):
        """
        Handle POST requests to check orders.

        This endpoint processes the request data and returns a cheque with the list of products and prices.

        Args:
            request: The HTTP request object containing order data.

        Returns:
            Response: A response containing the cheque information.
        """
        try:
            # Extract items and order_detail from request data
            items_data = request.data.get('items', [])
            order_detail_data = request.data.get('order_detail', {})

            if not items_data:
                raise ValueError("Items are required")

            if not order_detail_data:
                raise ValueError("Order details are required")

            # Process items and create order items
            order_items = []
            organization = None

            for item_data in items_data:
                # Validate required fields
                self._validate_item_data(item_data)

                # Get attribute
                attribute = self._get_attribute(item_data)

                # Get organization from the first attribute if not set yet
                if not organization:
                    organization = ProductAttribute.get_organization(attribute.id)

                # Get product
                product_obj = self._get_product(item_data)

                # Get quantity and comment
                quantity = item_data.get('quantity')
                comment = item_data.get('comment', '')

                # Process modifiers
                modifiers = self._process_modifiers(item_data)

                # Initialize order item
                order_item = OrderItem.init_order_item(
                    product_id=product_obj.id,
                    quantity=quantity,
                    attribute=attribute,
                    comment=comment,
                    modifiers=modifiers
                )

                order_items.append(order_item)

            # Create location object for delivery cost calculation
            order_detail = self._create_order_detail_object(order_detail_data)

            # Get user ID for promo code retrieval
            user_id = None
            if request.user and request.user.is_authenticated:
                user_id = request.user.id
            elif request.data.get('chat_id'):
                try:
                    telegram_user = TelegramUser.get_by_chat_id(request.data.get('chat_id'))
                    if telegram_user and telegram_user.user:
                        user_id = telegram_user.user.id
                except Exception as e:
                    logger.error(f"Failed to get user from chat_id: {e}")

            # If promo_code is not provided in order_detail and user is found, try to get it from cache
            if not hasattr(order_detail, 'promo_code') or not order_detail.promo_code:
                if user_id:
                    cached_promo = cache.get(f"{LoyaltyService.PROMO_CACHE_KEY_PREFIX}:{user_id}")
                    if cached_promo:
                        order_detail.promo_code = cached_promo
                        logger.info(f"Using cached promo code {cached_promo} for user {user_id} in check-in")

            # Try to get organization from order_detail if not already set
            if not organization and order_detail_data.get('organization_id'):
                try:
                    organization = Organization.get_by_id(order_detail_data.get('organization_id'))
                    logger.info(f"Using organization from order_detail: {organization.name}")
                except Exception as e:
                    logger.error(f"Failed to get organization from order_detail: {e}")

            # Calculate delivery cost
            delivery_cost, delivery_price_obj, distance = self.calculate_delivery_cost(order_detail, organization)

            # Calculate total cost
            total_cost = self.calculate_total_cost(order_items, delivery_cost)

            # Get active terminal group for the organization
            terminal_group = TerminalGroup.get_active_terminal_group(organization.id)
            terminal_group_external_id = terminal_group.external_id if terminal_group else None

            # Get user phone number from request or authentication
            phone_number = self._get_user_phone_number(request)

            # Get user's active promo code if available
            coupon = None
            user_id = None
            # Try to get user ID from authenticated user
            if request.user and request.user.is_authenticated:
                user_id = request.user.id
            # If not authenticated, try to get user from chat_id
            elif request.data.get('chat_id'):
                try:
                    telegram_user = TelegramUser.get_by_chat_id(request.data.get('chat_id'))
                    if telegram_user and telegram_user.user:
                        user_id = telegram_user.user.id
                except Exception as e:
                    logger.error(f"Failed to get user from chat_id: {e}")

            # Get active promo code if user is found
            if user_id:
                try:
                    # Get promo code from cache
                    user_promo = cache.get(f"{LoyaltyService.PROMO_CACHE_KEY_PREFIX}:{user_id}")
                    if user_promo:
                        coupon = user_promo
                        logger.info(f"Found active promo code {coupon} for user {user_id} in cache")

                except Exception as e:
                    logger.error(f"Failed to get active promo code: {e}")

            # Use promo code from cache or request
            promo_code_to_use = coupon or getattr(order_detail, 'promo_code', None)

            # Format order data for IIKO
            iiko_order_data = self._format_order_for_iiko(
                phone_number,
                order_items,
                order_detail_data.get('delivery_type', ''),
                organization,
                coupon=promo_code_to_use
            )

            # Get discount information from IIKO
            discount_info = None
            if organization and terminal_group:
                try:
                    # Add terminal group and organization IDs to the request
                    iiko_order_data["terminalGroupId"] = terminal_group.external_id
                    iiko_order_data["organizationId"] = organization.external_id

                    # Call IIKO service to calculate loyalty
                    discount_info = self._get_discount_info(
                        iiko_order_data,
                        terminal_group.external_id,
                        organization.external_id
                    )
                except Exception as e:
                    logger.error(f"Failed to get discount info from IIKO: {e}")

            # Format items for response with discount information
            formatted_items = self.format_items(order_items, discount_info)

            # Prepare the response with the cheque information
            response_data = {
                "total_cost": total_cost,
                "delivery_cost": delivery_cost,
                "items": formatted_items,
                "organization": {
                    "id": organization.id,
                    "name": organization.name,
                    "external_id": organization.external_id,
                    "terminal_group_id": terminal_group_external_id
                }
            }

            # Add detailed delivery information if available
            if delivery_price_obj and distance > 0:
                delivery_info = {
                    "cost": delivery_cost,
                    "distance": round(distance, 2),
                    "distance_type": delivery_price_obj.distance_type,
                    "display_name": delivery_price_obj.display_name,
                    "currency": "UZS"
                }
                response_data["delivery_info"] = delivery_info

            # Add coupon and promo information if it was used

            if promo_code_to_use:
                loyalty_info = {
                    "coupon": promo_code_to_use
                }

                # Try to get the promo object for additional information
                try:
                    promo_obj = Promo.get_by_name(promo_code_to_use)
                    if promo_obj:
                        # Add serialized promo object to response
                        promo_serializer = PromoSerializer(promo_obj)
                        loyalty_info["promo"] = promo_serializer.data
                        logger.info(f"Added promo object to response for coupon {promo_code_to_use}")
                except Exception as e:
                    logger.warning(f"Failed to get promo object for coupon {promo_code_to_use}: {e}")

                response_data["loyalty_info"] = loyalty_info

            # Add discount information if available
            if discount_info:
                response_data["discount_info"] = discount_info

                # Update total cost with discount
                if discount_info.get("total_discount", 0) > 0:
                    discounted_total = max(0, total_cost - discount_info["total_discount"])
                    response_data["original_total_cost"] = total_cost
                    response_data["total_cost"] = discounted_total
                    response_data["total_discount"] = discount_info["total_discount"]

                    # Calculate and add total discount percentage
                    if total_cost > 0:
                        discount_percentage = round((discount_info["total_discount"] / total_cost) * 100, 2)
                        response_data["total_discount_percentage"] = discount_percentage

            return Response(response_data, status=status.HTTP_200_OK)

        except ValueError as exc:
            # Handle validation errors
            logger.error(f"Validation error in order check: {exc}")
            raise self.call_service_exception(
                error_type="validation_error",
                message=str(exc),
                status_code=400
            )
        except Exception as exc:
            # Handle other errors
            logger.error(f"Error in order check: {exc}")
            logger.error(f"Traceback: {traceback.format_exc()}")

            # Handle ID type validation errors
            if "Field 'id' expected a number but got" in str(exc):
                error_message = (
                    f"Failed to check order due to ID type validation error: {str(exc)}. "
                    "This is likely caused by a mismatch between the expected ID type (number) "
                    "and the actual ID type (string/UUID)."
                )
                raise self.call_service_exception(
                    error_type="bad_request",
                    message=error_message,
                    status_code=400
                )

            # For other errors, return the original error message
            raise self.call_service_exception(
                error_type="bad_request",
                message=f"Failed to check order: {str(exc)}",
                status_code=400
            )

    def _validate_item_data(self, item_data):
        """Validate item data for required fields."""
        if not item_data.get('id'):
            raise ValueError("Product ID is required")

        if not item_data.get('quantity'):
            raise ValueError("Quantity is required")

        if not item_data.get('attribute', {}).get('id'):
            raise ValueError("Attribute ID is required")

    def _get_attribute(self, item_data):
        """Get product attribute from item data."""
        attribute_id = item_data.get('attribute', {}).get('id')
        product_id = item_data.get('id')

        try:
            # First try to get the attribute directly by ID
            return ProductAttribute.get_by_id(int(attribute_id))
        except Exception as e:
            logger.error(f"Failed to get attribute by ID: {e}")

            # If that fails, try to find the attribute by product ID and attribute ID
            try:
                product = get_product_by_id(int(product_id))
                attribute = ProductAttribute.objects.filter(
                    product=product,
                    id=int(attribute_id)
                ).first()

                if attribute:
                    return attribute

                # If still not found, check if the product has any attributes and return the default one
                attributes = ProductAttribute.objects.filter(product=product)
                if attributes.exists():
                    # Try to get the default attribute first
                    default_attr = attributes.filter(is_default=True).first()
                    if default_attr:
                        logger.warning(
                            f"Using default attribute {default_attr.id} instead of {attribute_id} "
                            f"for product {product_id}"
                        )
                        return default_attr

                    # If no default attribute, return the first one
                    logger.warning(
                        f"Using first attribute {attributes.first().id} instead of {attribute_id} "
                        f"for product {product_id}"
                    )
                    return attributes.first()
            except Exception as nested_e:
                logger.error(f"Failed to find alternative attribute: {nested_e}")

            # If all attempts fail, raise the original error
            raise ValueError(f"Attribute with ID {attribute_id} not found for product {product_id}") from e

    def _get_product(self, item_data):
        """Get product from item data."""
        product_id = item_data.get('id')
        try:
            return get_product_by_id(int(product_id))
        except Exception as e:
            logger.error(f"Failed to get product: {e}")
            raise ValueError(f"Product with ID {product_id} not found") from e

    def _process_modifiers(self, item_data):
        """Process modifiers from item data."""
        if not item_data.get('modifiers'):
            return None

        modifiers = []
        for modifier_data in item_data['modifiers']:
            # Validate modifier data
            modifier_id = modifier_data.get('id')
            if not modifier_id:
                raise ValueError("Modifier ID is required")

            # Create modifier object
            modifier = type('ModifierAttribute', (), {})()
            modifier.id = int(modifier_id)
            modifier.quantity = modifier_data.get('quantity', 1)
            modifiers.append(modifier)

        return modifiers

    def _create_order_detail_object(self, order_detail_data):
        """Create order detail object for delivery cost calculation."""
        order_detail = type('OrderDetail', (), {})()
        order_detail.delivery_type = order_detail_data.get('delivery_type', '')
        order_detail.promo_code = order_detail_data.get('promo_code')

        # Create location object
        order_detail.location = type('Location', (), {})()
        order_detail.location.lat = order_detail_data.get('location', {}).get('lat')
        order_detail.location.lng = order_detail_data.get('location', {}).get('lng')

        return order_detail

    def calculate_delivery_cost(self, order_detail, _):
        """
        Calculate the delivery cost based on the order details.

        Args:
            order_detail: The order detail data.
            _: Unused parameter (organization).

        Returns:
            tuple: A tuple containing (delivery_cost, delivery_price_obj, distance).
                  delivery_cost is the calculated delivery cost as a float.
                  delivery_price_obj is the DeliveryPrice object or None.
                  distance is the calculated distance in kilometers or 0.
        """
        # Return 0 if delivery type is not "Delivery"
        if not hasattr(order_detail, 'delivery_type') or not order_detail.delivery_type:
            return 0.0, None, 0

        if order_detail.delivery_type.lower() != DeliveryType.DELIVERY.value.lower():
            return 0.0, None, 0

        # Return 0 if location is not provided
        if not hasattr(order_detail, 'location') or not order_detail.location:
            return 0.0, None, 0

        if not hasattr(order_detail.location, 'lat') or not hasattr(order_detail.location, 'lng'):
            return 0.0, None, 0

        if not order_detail.location.lat or not order_detail.location.lng:
            return 0.0, None, 0

        try:
            # Find the nearest organization and calculate distance
            _, distance = Organization.find_nearest_organization(
                latitude=order_detail.location.lat,
                longitude=order_detail.location.lng,
                radius=50
            )

            # Calculate delivery price based on distance
            delivery_price = OrderService.calculate_delivery_price(distance)

            if not delivery_price:
                return 0.0, None, distance

            return delivery_price.price, delivery_price, distance
        except Exception as e:
            logger.error(f"Failed to calculate delivery cost: {e}")
            return 0.0, None, 0

    def calculate_total_cost(self, order_items, delivery_cost):
        """
        Calculate the total cost of the order.

        Args:
            order_items: The list of order items.
            delivery_cost: The delivery cost.

        Returns:
            float: The total cost of the order.
        """
        if not order_items:
            return delivery_cost

        total_cost = delivery_cost

        # Get organization from the first order item
        organization = None
        try:
            organization = ProductAttribute.get_organization(
                order_items[0].product.attributes.id
            )
        except Exception as e:
            logger.error(f"Failed to get organization from attribute: {e}")
            # Try to get organization from order_detail
            try:
                organization_id = self.request.data.get('order_detail', {}).get('organization_id')
                if organization_id:
                    organization = Organization.get_by_id(organization_id)
                    logger.info(f"Using organization from order_detail: {organization.name}")
            except Exception as nested_e:
                logger.error(f"Failed to get organization from order_detail: {nested_e}")

        if not organization:
            logger.warning("No organization found, returning only delivery cost")
            return delivery_cost

        for item in order_items:
            # Get the base price of the product
            item_total = item.product.attributes.price

            # Add modifier prices if any
            if hasattr(item.product, 'product_modifier'):
                ordered_modifiers = item.product.product_modifier.all()
                for ordered_modifier in ordered_modifiers:
                    try:
                        # Get modifier price using the organization ID
                        modifier_price = ModiferProductPrices.get_ordered_modifer_item_price(
                            modifer_id=ordered_modifier.modifier_item.id,
                            organization_id=organization.id
                        )

                        # Calculate modifier price considering free quantity
                        free_quantity = 0
                        if (hasattr(ordered_modifier.modifier_item, 'restrictions') and
                                ordered_modifier.modifier_item.restrictions):
                            free_quantity = ordered_modifier.modifier_item.restrictions.free_quantity

                        chargeable_quantity = ordered_modifier.quantity - free_quantity

                        if chargeable_quantity > 0:
                            item_total += modifier_price.price * chargeable_quantity
                    except Exception as e:
                        # If we can't get the modifier price, log the error and continue
                        logger.error(f"Failed to get modifier price: {e}")

            # Multiply by quantity and add to total
            total_cost += item_total * item.quantity

        return total_cost

    def format_items(self, order_items, discount_info=None):
        """
        Format the order items for the response.

        Args:
            order_items: The list of order items.
            discount_info: Optional discount information from IIKO.

        Returns:
            list: A list of formatted order items.
        """
        if not order_items:
            return []

        formatted_items = []

        # Get organization from the first order item
        organization = None
        try:
            organization = ProductAttribute.get_organization(
                order_items[0].product.attributes.id
            )
        except Exception as e:
            logger.error(f"Failed to get organization from attribute in format_items: {e}")
            # Try to get organization from order_detail
            try:
                organization_id = self.request.data.get('order_detail', {}).get('organization_id')
                if organization_id:
                    organization = Organization.get_by_id(organization_id)
                    logger.info(f"Using organization from order_detail in format_items: {organization.name}")
            except Exception as nested_e:
                logger.error(f"Failed to get organization from order_detail in format_items: {nested_e}")

        if not organization:
            logger.warning("No organization found in format_items, returning empty list")
            return formatted_items

        # Create a mapping of product external IDs to discount information
        discount_map = {}
        if discount_info and discount_info.get('programs'):
            for program in discount_info.get('programs', []):
                for item in program.get('discounted_items', []):
                    position_id = item.get('position_id')
                    if position_id:
                        if position_id not in discount_map:
                            discount_map[position_id] = 0
                        discount_map[position_id] += item.get('discount_sum', 0)

        for item in order_items:
            # Get base item price
            item_price = item.product.attributes.price

            # Format modifiers
            modifiers = self._format_modifiers(item, organization)

            # Get discount for this item
            discount_amount = 0
            if item.product.external_id in discount_map:
                discount_amount = discount_map[item.product.external_id]

            # Calculate discounted price
            discounted_price = max(0, item_price - discount_amount)

            # Calculate discount percentage if there's a discount
            discount_percentage = 0
            if item_price > 0 and discount_amount > 0:
                discount_percentage = round((discount_amount / item_price) * 100, 2)

            # Format item data
            formatted_item = {
                "id": item.product.id,
                "external_id": item.product.external_id,
                "title": item.product.title,
                "quantity": item.quantity,
                "price": item_price,
                "total_price": item_price * item.quantity,
                "attribute": {
                    "id": item.product.attributes.id,
                    "size": item.product.attributes.size,
                    "price": item.product.attributes.price
                },
                "modifiers": modifiers,
                "comment": getattr(item.product, 'comment', "")
            }

            # Add discount information if available
            if discount_amount > 0:
                formatted_item["discount_amount"] = discount_amount
                formatted_item["discount_percentage"] = discount_percentage
                formatted_item["discounted_price"] = discounted_price
                formatted_item["total_discounted_price"] = discounted_price * item.quantity

            formatted_items.append(formatted_item)

        return formatted_items

    def _format_modifiers(self, item, organization):
        """
        Format modifiers for an order item.

        Args:
            item: The order item.
            organization: The organization.

        Returns:
            list: A list of formatted modifiers.
        """
        modifiers = []

        # Skip if item has no product_modifier attribute or no modifiers
        if not hasattr(item.product, 'product_modifier'):
            return modifiers

        ordered_modifiers = item.product.product_modifier.all()
        if not ordered_modifiers:
            return modifiers

        # Process each modifier
        for ordered_modifier in ordered_modifiers:
            try:
                # Get modifier price
                modifier_price = ModiferProductPrices.get_ordered_modifer_item_price(
                    modifer_id=ordered_modifier.modifier_item.id,
                    organization_id=organization.id
                )

                price = modifier_price.price if modifier_price else 0.0
            except Exception as e:
                # If we can't get the modifier price, log the error and use 0.0
                logger.error(f"Failed to get modifier price: {e}")
                price = 0.0

            # Add modifier to the list
            modifiers.append({
                "id": ordered_modifier.modifier_item.id,
                "name": ordered_modifier.modifier_item.name,
                "quantity": ordered_modifier.quantity,
                "price": price
            })

        return modifiers

    def _get_user_phone_number(self, request):
        """
        Get the user's phone number from the request or authentication.

        Args:
            request: The HTTP request object.

        Returns:
            str: The user's phone number or None if not found.
        """
        # First try to get phone from chat_id in request
        chat_id = request.data.get('chat_id')
        if chat_id:
            try:
                telegram_user = TelegramUser.get_by_chat_id(chat_id)
                if telegram_user and telegram_user.phone:
                    return telegram_user.phone
            except Exception as e:
                logger.error(f"Failed to get phone number from chat_id: {e}")

        # Then try to get phone from authenticated user
        if request.user and request.user.is_authenticated:
            try:
                user = Users.objects.get(id=request.user.id)
                if user.phone:
                    return user.phone
            except Exception as e:
                logger.error(f"Failed to get phone number from authenticated user: {e}")

        # Finally, try to get phone from request data
        return request.data.get('phone')

    def _format_order_for_iiko(self, phone, order_items, delivery_type, organization, coupon=None):
        """
        Format order data for IIKO loyalty/calculate endpoint.

        Args:
            phone: User's phone number.
            order_items: List of order items.
            delivery_type: Delivery type (delivery or pickup).
            organization: The organization object.
            coupon: Optional coupon code for loyalty program.

        Returns:
            dict: Formatted order data for IIKO.
        """
        # Determine order service type based on delivery type
        order_service_type = "DeliveryByCourier"
        if delivery_type.lower() != DeliveryType.DELIVERY.value.lower():
            order_service_type = "DeliveryByClient"

        # Format phone number (ensure it starts with +)
        formatted_phone = phone
        if phone and not phone.startswith('+'):
            formatted_phone = f"+{phone}"

        # Format items for IIKO
        iiko_items = []
        for item in order_items:
            # Get product external ID
            product_external_id = item.product.external_id
            if not product_external_id:
                logger.warning(f"Product {item.product.id} has no external ID, skipping")
                continue

            # Get product price
            price = item.product.attributes.price

            # Add item to IIKO items
            iiko_items.append({
                "productId": product_external_id,
                "type": "Product",
                "amount": item.quantity,
                "price": price
            })

        # Add delivery cost as an item if delivery type is "Delivery"
        if delivery_type.lower() == DeliveryType.DELIVERY.value.lower():
            # Get delivery cost and delivery_cost_id
            order_detail = self._create_order_detail_object(self.request.data.get('order_detail', {}))
            delivery_cost, delivery_price_obj, _ = self.calculate_delivery_cost(order_detail, organization)

            if delivery_price_obj and delivery_price_obj.external_id:
                # Add delivery cost as an item
                iiko_items.append({
                    "productId": delivery_price_obj.external_id,
                    "type": "Product",
                    "amount": 1,
                    "price": delivery_cost
                })
                logger.info(
                    f"Added delivery cost {delivery_cost} with ID {delivery_price_obj.external_id} to IIKO items"
                )

        # Create order data structure
        order_obj = {
            "phone": formatted_phone,
            "orderServiceType": order_service_type,
            "items": iiko_items
        }

        # Create the full request structure with order inside
        order_data = {
            "order": order_obj
        }

        # Add coupon outside the order object if provided
        if coupon:
            order_data["coupon"] = coupon
            logger.info(f"Adding coupon {coupon} to IIKO order data for phone {formatted_phone}")

        return order_data

    def _get_discount_info(self, order_data, terminal_group_id, organization_id):
        """
        Get discount information from IIKO.

        Args:
            order_data: Formatted order data for IIKO with all required fields.
            terminal_group_id: Terminal group external ID (already included in order_data).
            organization_id: Organization external ID (already included in order_data).

        Returns:
            dict: Discount information.
        """
        try:
            # Call IIKO service to calculate loyalty with the complete request structure
            loyalty_result = IIKOService.check_order(
                order_data=order_data,
                terminal_group_id=terminal_group_id,
                organization_id=organization_id
            )

            # Process loyalty results
            discount_info = {
                "total_discount": 0.0,
                "programs": []
            }

            # Process each loyalty program result
            for program_result in loyalty_result.loyaltyProgramResults:
                program_discount = 0.0
                discounted_items = []

                # Calculate total discount for this program
                for discount in program_result.discounts:
                    program_discount += discount.discountSum

                    # Add discounted item info
                    discounted_items.append({
                        "position_id": discount.positionId,
                        "order_item_id": discount.orderItemId,
                        "discount_sum": discount.discountSum,
                        "amount": discount.amount,
                        "comment": discount.comment
                    })

                # Calculate program discount percentage
                program_discount_percentage = 0
                if program_discount > 0:
                    # Calculate percentage based on total order cost
                    # We'll use the order items total here as a base for percentage
                    order_items_total = sum(item.get("price", 0) * item.get("amount", 0)
                                            for item in order_data.get("items", []))
                    if order_items_total > 0:
                        program_discount_percentage = round((program_discount / order_items_total) * 100, 2)

                # Add program info to discount info
                discount_info["programs"].append({
                    "name": program_result.name,
                    "campaign_id": program_result.marketingCampaignId,
                    "discount_sum": program_discount,
                    "discount_percentage": program_discount_percentage,
                    "discounted_items": discounted_items
                })

                # Add to total discount
                discount_info["total_discount"] += program_discount

            return discount_info

        except Exception as e:
            logger.error(f"Error getting discount info from IIKO: {e}")
            return None
