"""
the job application model for hr bot
"""

from django.db import models

from apps.core.models.base import BaseModel
from apps.hr.enums.job_application import StatusEnum


class JobApplication(BaseModel):
    """
    the job application model
    """

    class Meta:
        """
        the meta fields
        """
        db_table = 'hr_job_application'
        ordering = ['-created_at', '-updated_at']

    chat_id = models.BigIntegerField(null=True)
    full_name = models.CharField(max_length=255)
    phone_number = models.CharField(max_length=20)
    job_position = models.CharField(max_length=100)
    working_hours = models.CharField(max_length=255, default="Kunduzgi smena (07:00 - 19:00)")
    birth_date = models.DateField()
    address = models.TextField()
    education = models.CharField(max_length=50)
    language_uz = models.TextField()
    language_ru = models.TextField()
    previous_experience = models.TextField(null=True, blank=True)
    health_issues = models.BooleanField(default=False)
    photo = models.ImageField(upload_to='applicant_photos/')
    gender = models.Char<PERSON>ield(max_length=10)
    status = models.CharField(max_length=20, choices=StatusEnum.choices(), default=StatusEnum.PENDING.value)

    def __str__(self):
        return f"{self.full_name}"

    @classmethod
    def get_by_chat_id(cls, chat_id):
        """
        Get telegram user by chat id
        """
        return cls.objects.get(chat_id=chat_id, is_active=True)
