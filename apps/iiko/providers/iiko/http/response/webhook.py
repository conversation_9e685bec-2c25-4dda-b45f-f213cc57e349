"""
the webook response model
"""
from typing import List, Optional

from pydantic import BaseModel


class DeliveryOrderFilter(BaseModel):
    """
    delivery order filter
    """
    orderStatuses: List[str]
    itemStatuses: List[str]
    errors: bool


class TableOrderFilter(BaseModel):
    """
    the table order filter model
    """
    orderStatuses: List[str]
    itemStatuses: List[str]
    errors: bool


class ReserveFilter(BaseModel):
    """
    reserve filter model
    """
    updates: bool
    errors: bool


class StopListUpdateFilter(BaseModel):
    """
    the stop list update filter model
    """
    updates: bool


class PersonalShiftFilter(BaseModel):
    """
    the personal shift filter model
    """
    updates: bool


class WebHooksFilter(BaseModel):
    """
    webhooks filter model
    """
    deliveryOrderFilter: DeliveryOrderFilter
    tableOrderFilter: TableOrderFilter
    reserveFilter: ReserveFilter
    stopListUpdateFilter: StopListUpdateFilter
    personalShiftFilter: PersonalShiftFilter


class WebHookSettingResponseBody(BaseModel):
    """
    the webhooks configuration model
    """
    correlationId: str
    apiLoginName: str
    webHooksUri: str
    authToken: Optional[str] = None
    webHooksFilter: WebHooksFilter
