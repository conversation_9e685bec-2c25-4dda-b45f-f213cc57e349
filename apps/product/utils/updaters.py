"""
This module provides synchronized utility methods for updating product modifiers.

It contains the ModifierUpdater class which encapsulates methods for processing and updating
item modifier groups, restrictions, and modifier items based on the provided response data.
"""
import logging

from django.db import transaction

from apps.iiko.providers.iiko.http.response import MenuByIdResponseBody

from apps.product.models.product import Product
from apps.product.models.modifier import ItemModifierGroups, Restrictions, ModifierItems, ModiferProductPrices


logger = logging.getLogger(__name__)


class ModifierUpdater:
    """
    A class to update product modifiers based on the provided response data.

    Attributes:
        response (MenuByIdResponseBody): The response data containing product and modifier information.
    """

    def __init__(self, response: MenuByIdResponseBody):
        """
        Initialize the ModifierUpdater with the provided response data.

        Args:
            response (MenuByIdResponseBody): The response data containing product and modifier information.
        """
        self.response = response

    @transaction.atomic
    def update_modifiers(self):
        """
        Update modifiers based on the response data.

        This method processes each category and product in the response data, updating or creating
        item modifier groups, restrictions, and modifier items as necessary.
        """
        categories = self.response.itemCategories
        for category in categories:
            for product in category.items:
                if product.isHidden:
                    # If product is Hidden, we should not update
                    continue

                item_sizes = product.itemSizes
                for item_size in item_sizes:
                    if item_size.itemModifierGroups:
                        product_external_id = product.itemId
                        product_obj = Product.get_by_external_id(product_external_id)

                        self.process_item_modifier_groups(
                            item_modifier_groups=item_size.itemModifierGroups,
                            product_obj=product_obj,
                            size_id=item_size.sizeId
                        )

    def process_item_modifier_groups(self, item_modifier_groups, product_obj, size_id):
        """
        Process and update item modifier groups for a given product.

        Args:
            item_modifier_groups (list): A list of item modifier groups to process.
            product_obj (Product): The product object to associate with the modifier groups.
        """
        for item_modifier_group in item_modifier_groups:
            item_modifier_group_obj, _ = ItemModifierGroups.update_or_create(
                product=product_obj,
                size_id=size_id,
                name=item_modifier_group.name,
                description=item_modifier_group.description,
                can_be_divided=item_modifier_group.canBeDivided,
                item_group_id=item_modifier_group.itemGroupId,
                is_hidden=item_modifier_group.isHidden,
                child_modifier_have_min_max_restrictions=(
                    item_modifier_group.childModifiersHaveMinMaxRestrictions
                ),
                sku=item_modifier_group.sku,
            )
            self.update_or_create_restrictions(item_modifier_group, item_modifier_group_obj)
            self.process_modifier_items(item_modifier_group.items, item_modifier_group_obj)

    def update_or_create_restrictions(self, item_modifier_group, item_modifier_group_obj):
        """
        Update or create restrictions for a given item modifier group.

        Args:
            item_modifier_group (ItemModifierGroups): The item modifier group containing restriction data.
            item_modifier_group_obj (ItemModifierGroups):
                The item modifier group object to update or associate with restrictions.
        """
        if item_modifier_group_obj.restrictions:
            logger.info("Updating restrictions for item_modifier_group")
            item_modifier_group_obj.restrictions.min_quantity = (
                item_modifier_group.restrictions.minQuantity
            )
            item_modifier_group_obj.restrictions.max_quantity = (
                item_modifier_group.restrictions.maxQuantity
            )
            item_modifier_group_obj.restrictions.free_quantity = (
                item_modifier_group.restrictions.freeQuantity
            )
            item_modifier_group_obj.restrictions.by_default = (
                item_modifier_group.restrictions.byDefault
            )
            item_modifier_group_obj.restrictions.hide_if_default_quantity = (
                item_modifier_group.restrictions.hideIfDefaultQuantity
            )
            item_modifier_group_obj.restrictions.save()
        else:
            logger.info("Creating restrictions for item_modifier_group")
            restrictions = Restrictions.create(
                min_quantity=item_modifier_group.restrictions.minQuantity,
                max_quantity=item_modifier_group.restrictions.maxQuantity,
                free_quantity=item_modifier_group.restrictions.freeQuantity,
                by_default=item_modifier_group.restrictions.byDefault,
                hide_if_default_quantity=item_modifier_group.restrictions.hideIfDefaultQuantity
            )
            item_modifier_group_obj.restrictions = restrictions
            item_modifier_group_obj.save()

    def process_modifier_items(self, modifier_items, item_modifier_group_obj):
        """
        Process and update modifier items for a given item modifier group.

        Args:
            modifier_items (list): A list of modifier items to process.
            item_modifier_group_obj (ItemModifierGroups):
                The item modifier group object to associate with the modifier items.
        """
        new_modifier_items = []

        for item in modifier_items:
            modifier_items_qs = item_modifier_group_obj.items.filter(item_id=item.itemId)

            if modifier_items_qs:
                logger.info("Updating modifier item")
                modifier_items_qs.update(
                    sku=item.sku,
                    name=item.name,
                    description=item.description,
                    portion_weight_grams=item.portionWeightGrams,
                    is_hidden=item.isHidden,
                    item_id=item.itemId,
                    position=item.position,
                    independent_quantity=item.independentQuantity,
                    measure_unit_type=item.measureUnitType,
                    button_image_url=item.buttonImageUrl,
                )
            else:
                modifier_item = ModifierItems.create(
                    sku=item.sku,
                    name=item.name,
                    description=item.description,
                    portion_weight_grams=item.portionWeightGrams,
                    is_hidden=item.isHidden,
                    item_id=item.itemId,
                    position=item.position,
                    independent_quantity=item.independentQuantity,
                    measure_unit_type=item.measureUnitType,
                    button_image_url=item.buttonImageUrl,
                )
                new_modifier_items.append(modifier_item)
                logger.info("Created a new modifier item")

            for modifier_item in modifier_items_qs:
                self.update_or_create_item_restrictions(item, modifier_item)

            self.update_or_create_prices(item.prices, modifier_item)

        item_modifier_group_obj.items.add(*new_modifier_items)

    def update_or_create_item_restrictions(self, item, modifier_item):
        """
        Update or create restrictions for a given modifier item.

        Args:
            item (ModifierItems): The modifier item containing restriction data.
            modifier_item (ModifierItems): The modifier item object to update or associate with restrictions.
        """
        if not modifier_item.restrictions:
            logger.info("Creating restrictions for modifier item")
            restrictions = Restrictions.create(
                min_quantity=item.restrictions.minQuantity,
                max_quantity=item.restrictions.maxQuantity,
                free_quantity=item.restrictions.freeQuantity,
                by_default=item.restrictions.byDefault,
                hide_if_default_quantity=item.restrictions.hideIfDefaultQuantity
            )
            modifier_item.restrictions = restrictions
            modifier_item.save()
        else:
            logger.info("Updating restrictions for modifier item")
            modifier_item.restrictions.min_quantity = item.restrictions.minQuantity
            modifier_item.restrictions.max_quantity = item.restrictions.maxQuantity
            modifier_item.restrictions.free_quantity = item.restrictions.freeQuantity
            modifier_item.restrictions.by_default = item.restrictions.byDefault
            modifier_item.restrictions.hide_if_default_quantity = item.restrictions.hideIfDefaultQuantity
            modifier_item.restrictions.save()

    def update_or_create_prices(self, prices, modifier_item):
        """
        Update or create prices for a given modifier item.

        Args:
            prices (list): A list of prices to process.
            modifier_item (ModifierItems): The modifier item object to associate with the prices.
        """
        for price in prices:
            ModiferProductPrices.update_or_create(
                organization_external_id=price.organizationId,
                modifer_item=modifier_item,
                price=price.price
            )
            logger.info("Updated or created price for modifier item")
