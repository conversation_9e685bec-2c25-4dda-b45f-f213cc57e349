"""
This module provides the serializer for the CourierPoint model,
which is used to convert model instances into JSON format and
validate incoming data.

The serializer includes fields for:
- `id`: The primary key of the CourierPoint instance.
- `courier`: The ID of the associated courier.
- `latitude`: The latitude of the courier's location.
- `longitude`: The longitude of the courier's location.

Usage:
    The serializer can be used in views to handle CourierPoint data.
"""

from rest_framework import serializers

from apps.courier.models import CourierPoint
from apps.courier.serializers.courier import CourierModelSerializer


class CourierModelSerializerUPT(CourierModelSerializer):
    """
    Serializer for the CourierPoint model, including the courier details.

    This serializer handles the conversion between CourierPoint model
    instances and JSON data, as well as validation of input data.
    Attributes:
        id (IntegerField): The primary key of the CourierPoint instance.
        courier (IntegerRelatedField): The ID of the associated courier.
        latitude (FloatField): The latitude of the courier's location.
        longitude (FloatField): The longitude of the courier's location.
    """
    class Meta(CourierModelSerializer.Meta):
        """
        Meta options for the CourierModelSerializerUPT.
        """
        fields = (
            'id',
            'name',
            'status'
        )


class CourierPointSerializer(serializers.ModelSerializer):
    """
    Serializer for the CourierPoint model.

    This serializer handles the conversion between CourierPoint model
    instances and JSON data, as well as validation of input data.

    Attributes:
        id (IntegerField): The primary key of the CourierPoint instance.
        courier (IntegerField): The ID of the associated courier.
        latitude (FloatField): The latitude of the courier's location.
        longitude (FloatField): The longitude of the courier's location.
    """
    courier = CourierModelSerializerUPT()

    class Meta:
        """
        Meta options for the CourierPointSerializer.

        Attributes:
            model (class): The model class to be serialized.
            fields (list): The fields to be included in the serialized data.
            read_only_fields (list): The fields to be included in the serialized data
            but not included in the update or create operations.
            extra_kwargs (dict): Additional keyword arguments to be passed to the serializer.
        """
        model = CourierPoint
        fields = '__all__'
