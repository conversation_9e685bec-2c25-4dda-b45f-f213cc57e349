"""
otp serialization
"""
import re
from rest_framework import serializers


class OTPSerializer(serializers.Serializer):
    """
    the otp serializer
    """
    phone = serializers.CharField()
    code = serializers.CharField(write_only=True, required=False)
    password = serializers.Char<PERSON><PERSON>(write_only=True, required=False)

    def validate_phone(self, phone) -> str:
        """
        Validates Uzbekistan phone numbers.
        """
        pattern = r'^(?:\+?998)[0-9]{2}[0-9]{7}$'
        if not re.match(pattern, phone):
            raise serializers.ValidationError("Invalid Uzbekistan phone number format.")

        return phone.replace('+', '').replace(' ', '')
