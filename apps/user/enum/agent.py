"""
the enumeration of delivery
"""
from enum import Enum


class AgentStatusEnums(str, Enum):
    """
    The enumeration of telegram user state.
    """
    AVAILABLE = "Available"
    UNAVAILABLE = "Unavailable"
    ON_WAY = "OnWay"
    WAITING = "Waiting"
    DELIVERED = "Delivered"
    IN_WORK = "InWork"
    IN_ORDER = "InOrder"
    NOT_IN_WORK = "NotInWork"
    BUSY = "Busy"
    ARRIVED = "Arrived"
    INACTIVE = "Inactive"
    IS_BLOCKED = "IsBlocked"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]

    @classmethod
    def allowed_values(cls):
        """
        Returns a list of allowed values.
        """
        return [member.value for member in cls]
