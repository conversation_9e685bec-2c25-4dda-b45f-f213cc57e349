"""
Custom caching decorator for Django model objects.

This module provides a decorator cached_object that can be used to cache the result of
functions that retrieve Django model objects. By caching these objects, it helps improve
performance by reducing redundant database queries for frequently accessed objects.

The cache key is dynamically built based on the provided prefix and the primary key (or
ID) of the object being retrieved. The cached objects have a configurable timeout and
can be easily invalidated when needed.

Dependencies:
    - Django's cache framework (e.g., Redis, Memcached, or another supported backend)
    - Django settings configured with a cache backend
"""

from functools import wraps
from django.core.cache import cache


def cached_object(cache_key_prefix, timeout=60 * 15):
    """
    Caches the result of a function that retrieves a Django model object.

    This decorator is designed to cache the result of a function that returns a Django model
    object, using Django's caching framework. It builds a cache key dynamically based on a
    specified key prefix and the `pk` (primary key) or `id` of the object being retrieved.

    If the object is found in the cache, it is returned directly without querying the
    database. If it is not in the cache, the original function is called, and the result
    is cached with the specified timeout.

    Args:
        cache_key_prefix (str): The prefix to be used when generating cache keys.
        timeout (int, optional): The time (in seconds) that the object should be cached.
                                 Defaults to 15 minutes (60 * 15).

    Returns:
        function: A wrapped function that first checks the cache before querying the database.

    Example:
        @cached_object(cache_key_prefix='my_model', timeout=60 * 10)
        def get_my_model_object(pk):
            return MyModel.objects.get(pk=pk)

        obj = get_my_model_object(pk=1)  # Caches the object for future use

    Notes:
        - The cache will store the object for the duration specified by `timeout`.
        - The cache key is generated as: "{cache_key_prefix}:{pk or id}"
        - You will need to manage cache invalidation manually when updating or deleting objects.

    Raises:
        ValueError: If the function being decorated does not receive a valid `pk` or `id`.
    """

    def decorator(func):
        @wraps(func)
        def wrapped(*args, **kwargs):
            # Build the cache key dynamically using the cache key prefix and object ID
            cache_key = f"{cache_key_prefix}:{kwargs.get('pk') or kwargs.get('id')}"
            cached_obj = cache.get(cache_key)

            if cached_obj:
                return cached_obj

            obj = func(*args, **kwargs)
            cache.set(cache_key, obj, timeout=timeout)

            return obj

        def clear_cache(object_id):
            cache_key = f"{cache_key_prefix}:{object_id}"
            cache.delete(cache_key)

        wrapped.clear_cache = clear_cache

        return wrapped
    return decorator
