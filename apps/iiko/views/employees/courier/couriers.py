"""
getting list of iiko couriers
"""
from rest_framework import views
from rest_framework import response

from apps.core.exceptions import ServiceAPIException
from apps.iiko.helpers.couriers import couriers_helper


class IIKOCourierAPIView(views.APIView):
    """
    The iiko couriers API view.
    """
    def get(self, request, *args, **kwargs):
        """
        The GET method for getting the list of iiko couriers.
        """
        organization_ids = request.query_params.getlist('organization_ids')

        if not organization_ids:
            raise ServiceAPIException(
                error_type="bad_request",
                message="No organization_ids provided"
            )

        try:
            response_data = couriers_helper.get_new_couriers(organization_ids)
            return response.Response(response_data)

        except Exception as exc:
            msg = f"Get new couriers failed exc: {exc}"
            raise ServiceAPIException(
                error_type="internal error",
                message=msg
            ) from exc
