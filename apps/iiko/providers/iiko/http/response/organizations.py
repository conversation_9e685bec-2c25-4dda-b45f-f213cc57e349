"""
the organization response model
"""
from typing import List, Optional
from pydantic import BaseModel, Field


class Organization(BaseModel):
    """
    the organization response model
    """
    id: str
    name: str
    code: Optional[str]
    response_type: str = Field(alias="responseType")


class OrganizationResponseBody(BaseModel):
    """
    the organization response body model
    """
    organizations: List[Organization]
    correlation_id: str = Field(alias="correlationId")
