"""
the bookmarks serialization
"""
from rest_framework import serializers

from apps.order.models.bookmarks import Bookmark
from apps.map.utility.status import is_within_circle


class BookmarkSerializer(serializers.ModelSerializer):
    """
    the bookmarks serialization
    """
    order_count = serializers.IntegerField(read_only=True)

    class Meta:
        """
        the meta fields
        """
        model = Bookmark
        fields = [
            'id', 'user', 'address',
            'lat', 'long', 'street_id',
            'subaddress', 'order_count', 'name', 'is_client_bookmarked'
        ]
        read_only_fields = ['id', 'user', 'order_count']


class BookMarkCreateSerializer(serializers.Serializer):
    """
    Serializer for creating a new bookmark.
    This serializer handles the input validation for bookmark creation.
    """
    bookmark_id = serializers.IntegerField(read_only=True)
    user_id = serializers.IntegerField(required=False)
    chat_id = serializers.IntegerField(required=False)
    lat = serializers.FloatField()
    long = serializers.FloatField()
    street_id = serializers.Char<PERSON>ield(required=False)
    subaddress = serializers.Char<PERSON>ield(required=False)
    address = serializers.CharField()
    name = serializers.CharField(required=False)
    order = serializers.IntegerField(required=False)

    def validate(self, attrs):
        """
        This method validates the input data for creating a new bookmark.
        It checks if either user_id or chat_id is provided.
        It also validates if the latitude and longitude are within a specific circle.
        """
        if not attrs.get('user_id') and not attrs.get('chat_id'):
            raise serializers.ValidationError("Either user_id or chat_id is required.")

        if not is_within_circle(
            lat_point=attrs['lat'],
            lon_point=attrs['long']
        ):
            raise serializers.ValidationError(
                'User is not within the circle Navoi'
            )

        return attrs

    def to_representation(self, instance):
        """
        Convert the instance to its display representation, including the bookmark_id
        """
        ret = super().to_representation(instance)

        representation = {
            'bookmark_id': instance.get('bookmark_id'),
            **ret
        }

        return representation
