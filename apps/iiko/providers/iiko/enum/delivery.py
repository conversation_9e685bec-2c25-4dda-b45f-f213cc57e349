"""
the enumeration of delivery status
"""
from enum import Enum


class DeliveryStatusEnum(str, Enum):
    """
    The enumeration of delivery status
    """
    DELIVERED = "Delivered"
    WAITING = "Waiting"
    ON_WAY = "OnWay"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
