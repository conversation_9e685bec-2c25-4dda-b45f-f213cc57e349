from rest_framework.permissions import BasePermission


class IsMarketologOrManager(BasePermission):
    """
    Custom permission to only allow marketologs and managers to update organizations.
    """

    def has_permission(self, request, view):
        # Check if the user is authenticated and has the required role
        return request.user.is_authenticated and request.user.role in ['marketolog', 'manager']
