"""
Bot user profile views
"""
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.core.views import ServiceBaseView

from apps.bot.models.telegram import TelegramUser
from apps.bot.serializers.profile import BotUserProfileSerializer


class BotUserProfileView(APIView, ServiceBaseView):
    """
    Telegram User miniapp profile view
    """
    def get(self, request, chat_id):
        try:
            user = TelegramUser.get_by_chat_id(int(chat_id))

            data = {
                "first_name": user.first_name,
                "last_name": user.last_name or "null",
                "gender": getattr(user.user, "gender", None),
                "lang": user.lang,
                "phone": user.phone
            }

            return Response(data)

        except (ValueError, TelegramUser.DoesNotExist) as exc:
            raise self.call_service_exception(
                error_type="not_found",
                message=f"Telegram user with id {chat_id} not found or inactive",
                status_code=404
            ) from exc

        except Exception as exc:
            raise self.call_service_exception(
                error_type="server_error",
                message="An unexpected error occurred",
                status_code=500
            ) from exc

    def patch(self, request, chat_id):
        try:
            telegram_user = TelegramUser.get_by_chat_id(int(chat_id))
        except (ValueError, TelegramUser.DoesNotExist):
            return Response(
                {"error": f"Telegram user with id {chat_id} not found or inactive"},
                status=404
            )

        serializer = BotUserProfileSerializer(data=request.data, partial=True)
        if not serializer.is_valid():
            return Response(
                {"error": serializer.errors},
                status=400
            )

        if 'gender' in serializer.validated_data:
            if not telegram_user.user:
                return Response(
                    {"error": "cannot update gender: user profile not found"},
                    status=400
                )
            telegram_user.user.gender = serializer.validated_data['gender']
            telegram_user.user.save()

        for field, value in serializer.validated_data.items():
            if field != 'gender':
                setattr(telegram_user, field, value)

        telegram_user.save()
        data = {
            "first_name": telegram_user.first_name,
            "last_name": telegram_user.last_name or "null",
            "gender": telegram_user.user.gender if telegram_user.user else None,
            "lang": telegram_user.lang,
            "phone": telegram_user.phone
        }

        return Response(data)
