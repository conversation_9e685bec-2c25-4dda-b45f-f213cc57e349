"""
Admin views for log file management.

This module provides admin views for accessing and downloading log files.
"""
import os
from datetime import datetime

from django.contrib import admin
from django.http import HttpResponse, FileResponse
from django.urls import path
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.views.decorators.csrf import csrf_protect

from apps.core.services.log_service import LogService
from apps.core.models.log import LogFile


class LogFileAdminView:
    """
    Admin view for log file management.

    This class provides methods to view and download log files from the admin interface.
    """

    @staticmethod
    @staff_member_required
    @csrf_protect
    def download_log_file(request):
        """
        Download the log file.

        Args:
            request: The HTTP request.

        Returns:
            FileResponse: A response containing the log file as a downloadable file.
        """
        try:
            if request.method == 'POST':
                download_type = request.POST.get('download_type', 'full')

                if download_type == 'partial':
                    max_lines_str = request.POST.get('max_lines')
                    max_lines = int(max_lines_str) if max_lines_str and max_lines_str.isdigit() else 100
                else:
                    max_lines = None
            else:
                max_lines_str = request.GET.get('max_lines')
                max_lines = int(max_lines_str) if max_lines_str and max_lines_str.isdigit() else None

            log_file_path = LogService.get_log_file_path()

            if not log_file_path or not os.path.exists(log_file_path):
                error_message = f"Log file not found at {log_file_path}"
                return render(request, 'admin/log_error.html', {
                    'error_message': error_message,
                    'title': 'Log File Error',
                })

            if max_lines:
                log_content = LogService.read_log_file(max_lines=max_lines)
                if log_content.startswith("Error"):
                    return render(request, 'admin/log_error.html', {
                        'error_message': log_content,
                        'title': 'Log File Error',
                    })

                response_obj = HttpResponse(log_content, content_type='text/plain')
                filename = f"log_last_{max_lines}_lines_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
                response_obj['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response_obj

            filename = f"log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

            with open(log_file_path, 'rb') as file:
                response_obj = FileResponse(
                    file,
                    content_type='text/plain'
                )
                response_obj['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response_obj

        except Exception as e:
            error_message = f"Error retrieving log file: {str(e)}"
            return render(request, 'admin/log_error.html', {
                'error_message': error_message,
                'title': 'Log File Error',
            })

    @staticmethod
    @staff_member_required
    @csrf_protect
    def log_file_view(request):
        """
        Render the log file view page.

        Args:
            request: The HTTP request.

        Returns:
            HttpResponse: A response containing the log file view page.
        """
        log_file_path = LogService.get_log_file_path()
        log_file_exists = log_file_path and os.path.exists(log_file_path)

        context = {
            'title': 'Log File Management',
            'log_file_path': log_file_path,
            'log_file_exists': log_file_exists,
        }

        if log_file_exists:
            file_stats = os.stat(log_file_path)
            last_modified = datetime.fromtimestamp(file_stats.st_mtime).strftime('%Y-%m-%d %H:%M:%S')

            size_bytes = file_stats.st_size
            if size_bytes < 1024:
                size_str = f"{size_bytes} bytes"
            elif size_bytes < 1024 * 1024:
                size_str = f"{size_bytes / 1024:.2f} KB"
            else:
                size_str = f"{size_bytes / (1024 * 1024):.2f} MB"

            log_preview = LogService.read_log_file(max_lines=1000)

            context.update({
                'log_file_last_modified': last_modified,
                'log_file_size': size_str,
                'log_preview': log_preview
            })

        return render(request, 'admin/log_file.html', context)


class LogFileAdmin(admin.ModelAdmin):
    """
    Admin interface for log file management.
    """

    def get_urls(self):
        """
        Add custom URLs to the admin interface.
        """
        urls = super().get_urls()
        custom_urls = [
            path('log-file/', LogFileAdminView.log_file_view, name='core_log_file'),
            path('log-file/download/', LogFileAdminView.download_log_file, name='core_log_file_download'),
        ]
        return custom_urls + urls

    def has_add_permission(self, *args, **kwargs):
        """
        Disable add permission for log files.
        """
        return False

    def has_delete_permission(self, *args, **kwargs):
        """
        Disable delete permission for log files.
        """
        return False

    def has_change_permission(self, *args, **kwargs):
        """
        Disable change permission for log files.
        """
        return False

    def changelist_view(self, request, *args, **kwargs):
        """
        Override the changelist view to redirect to our custom log file view.
        """
        return LogFileAdminView.log_file_view(request)


admin.site.register(LogFile, LogFileAdmin)
