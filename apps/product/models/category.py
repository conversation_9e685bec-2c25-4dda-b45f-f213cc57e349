"""
the category model
"""
from typing import List

from django.db import models

from apps.core.models.base import BaseModel
from apps.product.managers import CategoryManager


class Category(BaseModel):
    """
    The category model
    """
    title = models.CharField(max_length=150)
    image_url = models.CharField(max_length=255, null=True, blank=True)
    external_id = models.CharField(editable=False, unique=True, db_index=True)
    position_id = models.PositiveIntegerField(default=0, help_text="Position of the category in a list or sequence")

    objects = CategoryManager()

    class Meta:
        """
        The meta fields
        """
        db_table = 'category'
        verbose_name_plural = 'Categories'
        ordering = ['position_id']

    def __str__(self):
        return str(self.title)

    def save(self, *args, **kwargs):
        """
        Force saving only the desired translation field (e.g., Russian)
        """
        super().save(*args, **kwargs)

    @classmethod
    def build_category(cls, title, external_id,) -> "Category":
        """
        Build category
        """
        return cls.objects.build_category(title, external_id)

    @classmethod
    def get_all_categories(cls) -> List["Category"]:
        """
        Get all categories
        """
        return cls.objects.get_all_categories()

    @classmethod
    def update_categories(cls, categories) -> None:
        """
        Update categories
        """
        if not categories:
            return

        return cls.objects.update_categories(categories)
