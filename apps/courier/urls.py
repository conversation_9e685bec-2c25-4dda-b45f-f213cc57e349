"""
Courier module URL configuration.

This module sets up URL routing for various courier-related API endpoints.

Includes:
- Courier management
- Point management
- Status and authentication
- Reset password and shift management

Routes:
    - Reset password: /reset-password/
    - Status: /status/
    - Token operations: /token/ and /logout/
    - FCM device updates: /fcm/update/
    - Orders: /active-orders/
    - Point updates: /update-point/
    - Shift operations: /shift/is_open/ and /shift/close/

URL Patterns:
    Register viewsets and endpoints for courier operations, including CRUD operations
    and specific actions related to couriers, points, and authentication.
"""

# urls.py
from django.urls import path, include
from rest_framework.routers import DefaultRouter

# Import views
from apps.courier.views import point as points_view
from apps.courier.views import shift as shift_view
from apps.courier.views import token as token_view
from apps.courier.views import status as status_view
from apps.courier.views import orders as orders_view
from apps.courier.views.fmc import FCMDeviceAPIView
from apps.courier.views import courier as courier_view
from apps.courier.views import heat_map as heat_map_view
from apps.courier.views.reset_password import ResetPasswordAPIView
from apps.courier.views.profile import CourierProfileAPIView

# Initialize router
router = DefaultRouter()

# Register viewsets with the router
router.register(r'', courier_view.CourierViewSet, basename='courier')

# Define URL patterns
urlpatterns = [
    path('profile/', CourierProfileAPIView.as_view(), name='courier-profile'),

    # Authentication and password management
    path("reset-password/", ResetPasswordAPIView.as_view(), name='reset-password'),
    path("reset-password-confirm/", ResetPasswordAPIView.as_view(), name='reset-password-confirm'),
    path("token/", token_view.CourierTokenObtainPairView.as_view(), name='token-obtain'),
    path("logout/", token_view.CourierLogoutAPIView.as_view(), name='token-logout'),

    # Courier status and FCM device updates
    path("status/", status_view.CourierStatusAPIView.as_view(), name='courier-status'),
    path("fcm/update/", FCMDeviceAPIView.as_view(), name='fcm-update'),

    # Orders and points management
    path("active-orders/", orders_view.ActiveOrdersAPIView.as_view(), name='active-orders'),
    path("update-point/", points_view.PointUpdateOrCreateAPIView.as_view(), name='update-point'),

    # Shift operations
    path("shift/is_open/", shift_view.CourierIsShiftAPIView.as_view(), name='shift-is-open'),
    path("shift/close/", shift_view.CourierShiftCloseAPIView.as_view(), name='shift-close'),
    path('<int:courier_id>/shifts/', shift_view.CourierShiftsView.as_view(), name='courier-shifts'),
    path('shift/<int:shift_id>/orders/', shift_view.ShiftOrdersView.as_view(), name='shift-orders'),

    # Include point management URLs
    path("points/", points_view.CourierPointViewSet.as_view({'get': 'list'}), name='points'),
    path("heat-map/", heat_map_view.CourierHeatmapView.as_view(), name='heat-map'),
]

# Include router URLs
urlpatterns.extend([
    path('', include(router.urls)),  # Main router URL patterns
])
