"""
the enumeration of delivery
"""
from enum import Enum

from master_kebab.settings import env


class DeliveryType(str, Enum):
    """
    The enumeration of order status.
    """
    SELF_CALL = "SelfCall"
    DELIVERY = "Delivery"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]


class StreetEnum(Enum):
    """
    The enumeration of delivery price.
    """
    DEFAULT_STREET_ID = env.str("DEFAULT_STREET_ID", "fd70666b-b71a-435e-adc7-36778542f882")

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
