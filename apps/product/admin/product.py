"""
Module for managing the admin interface of the product-related models in the Django application.

This module defines custom admin classes for managing Products, Product Attributes,
Ordered Products, and Ordered Product Attributes. The admin classes use Django's
admin interface to provide enhanced functionality such as custom filters,
translation support via `TabbedTranslationAdmin`, and custom actions like deleting
unupdated products and updating products.

Classes:
    UnupdatedProductsFilter: Custom filter for identifying products that haven't been updated recently.
    ProductUI: Admin interface for managing Products, with custom filters and actions.
    ProductAttributeUI: Admin interface for managing Product Attributes.
    OrderedProductUI: Admin interface for managing Ordered Products.
    OrderedProductAttributeUI: Admin interface for managing Ordered Product Attributes.
"""

from datetime import timedelta
from django.contrib import admin
from django.utils import timezone

from modeltranslation.admin import TabbedTranslationAdmin

from apps.core.admin.modeladmin import ModelAdmin
from apps.product.models.product import Product, ProductAttribute
from apps.iiko.tasks.polling import update_categories_and_products_task
from apps.product.models.orderd_product import OrderedProduct, OrderedProductAttribute


class UnupdatedProductsFilter(admin.SimpleListFilter):
    """
    Custom filter for identifying products that haven't been updated in the last 10 hours.

    This filter adds an option in the admin interface to quickly filter and display
    products that have not been updated within the specified timeframe.

    Attributes:
        title (str): The title of the filter displayed in the admin interface.
        parameter_name (str): The query string parameter used for the filter.
    """

    title = 'Unupdated Products'
    parameter_name = 'unupdated'

    def lookups(self, request, model_admin):
        """
        Returns the available options for the filter.

        Args:
            request (HttpRequest): The current request object.
            model_admin (ModelAdmin): The current ModelAdmin instance.

        Returns:
            list: A list of tuples representing the filter options.
        """
        return (
            ('unupdated', 'Unupdated in last 10 hours'),
        )

    def queryset(self, request, queryset):
        """
        Filters the queryset based on the selected filter option.

        Args:
            request (HttpRequest): The current request object.
            queryset (QuerySet): The initial queryset of products.

        Returns:
            QuerySet: The filtered queryset with products that haven't been updated
                      within the last 10 hours.
        """
        if self.value() == 'unupdated':
            one_day_ago = timezone.now() - timedelta(hours=10)
            return queryset.filter(updated_at__lt=one_day_ago)
        return queryset


class ProductUI(TabbedTranslationAdmin, ModelAdmin):
    """
    Admin interface for managing Products.

    This class provides a custom admin interface for the Product model, including
    list display configuration, a custom filter for unupdated products, and custom
    actions for deleting products that haven't been updated recently and updating products.

    Attributes:
        list_display_links (tuple): Fields that link to the product detail page.
        list_display (tuple): Fields to display in the product list view.
        list_filter (tuple): Custom filters available in the admin interface.
        actions (list): Custom actions available in the admin interface.
    """

    list_display_links = ('title', )
    list_display = (
        'id', 'title', 'title_ru', 'title_uz', 'title_en',
        'description', 'description_ru', 'description_uz', 'description_en',
    )

    # Add the custom filter to the list_filter option
    list_filter = (UnupdatedProductsFilter,)

    # Add the custom action to the actions option
    actions = ['update_products']

    def update_products(self, request, queryset):
        """
        Custom action to update products.

        This action updates the selected products by setting their updated_at field to the current time.

        Args:
            request (HttpRequest): The current request object.
            queryset (QuerySet): The queryset of selected products.
        """
        update_categories_and_products_task.delay(is_manual_update=True)
        self.message_user(request, "Successfully updated products.")


class ProductAttributeUI(TabbedTranslationAdmin, ModelAdmin):
    """
    Admin interface for managing Product Attributes.

    This class provides a custom admin interface for the ProductAttribute model,
    allowing translation support and enhanced management of product attributes.
    """
    list_display = (
        'product',
        'is_available',
    )
    list_editable = (
        'is_available',
    )
    list_filter = (UnupdatedProductsFilter,)


class OrderedProductUI(ModelAdmin):
    """
    Admin interface for managing Ordered Products.

    This class provides a custom admin interface for the OrderedProduct model,
    enabling efficient management of customer orders and related data.
    """


class OrderedProductAttributeUI(ModelAdmin):
    """
    Admin interface for managing Ordered Product Attributes.

    This class provides a custom admin interface for the OrderedProductAttribute model,
    allowing detailed management of attributes related to ordered products.
    """


# Register the admin classes with the associated models
admin.site.register(Product, ProductUI)
admin.site.register(ProductAttribute, ProductAttributeUI)
admin.site.register(OrderedProduct, OrderedProductUI)
admin.site.register(OrderedProductAttribute, OrderedProductAttributeUI)
