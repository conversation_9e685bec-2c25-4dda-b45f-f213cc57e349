# Generated by Django 5.0.6 on 2024-08-20 11:29

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='WebhookUpdates',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('order_id', models.Char<PERSON>ield(max_length=255)),
                ('payload', models.J<PERSON><PERSON>ield()),
            ],
            options={
                'db_table': 'webhooks_updates',
            },
        ),
    ]
