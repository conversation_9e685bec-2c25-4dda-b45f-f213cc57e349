"""
the running faststream command
"""
import subprocess

from django.core.management.base import BaseCommand


COMMAND = ['faststream', 'run', 'apps.user.pubsub:app', '--reload']


class Command(BaseCommand):
    """
    Running command for subscriber.
    """
    help = "Starts event listener"

    def handle(self, *args, **options):
        """
        Main entry point for executing the command.
        """
        process = subprocess.Popen(COMMAND)
        try:
            process.communicate()
        except KeyboardInterrupt:
            process.terminate()
            process.wait()
