# Generated by Django 5.0.6 on 2025-02-01 14:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("product", "0021_product_recommended_products"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="productattribute",
            name="description_en",
        ),
        migrations.RemoveField(
            model_name="productattribute",
            name="description_ru",
        ),
        migrations.RemoveField(
            model_name="productattribute",
            name="description_uz",
        ),
        migrations.AddField(
            model_name="productattribute",
            name="size_en",
            field=models.CharField(
                default="Default", max_length=100, null=True, verbose_name="Size name"
            ),
        ),
        migrations.AddField(
            model_name="productattribute",
            name="size_ru",
            field=models.CharField(
                default="Default", max_length=100, null=True, verbose_name="Size name"
            ),
        ),
        migrations.AddField(
            model_name="productattribute",
            name="size_uz",
            field=models.Char<PERSON>ield(
                default="Default", max_length=100, null=True, verbose_name="Size name"
            ),
        ),
    ]
