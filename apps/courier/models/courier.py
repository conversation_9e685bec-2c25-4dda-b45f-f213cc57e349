"""
courier model
"""
from django.db import models, transaction
from django.contrib.auth.models import Group
from django.contrib.auth.hashers import make_password, check_password
from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin

from channels.db import database_sync_to_async

from apps.core.models.base import BaseModel
from apps.user.enum.role import UserRoleEnums
from apps.organization.models import Organization
from apps.user.enum.agent import AgentStatusEnums
from apps.courier.managers.courier import CourierManager


class Courier(BaseModel, AbstractBaseUser, PermissionsMixin):
    """
    The courier model
    """
    phone = models.Char<PERSON>ield(max_length=15, unique=True)
    name = models.Char<PERSON>ield(max_length=255, verbose_name="FIO")
    password = models.CharField(max_length=128, null=True, blank=True)
    passport = models.CharField(verbose_name="Passport", max_length=15, null=True, blank=True)
    address = models.CharField(max_length=255, verbose_name="Address", null=True, blank=True)
    status = models.CharField(
        max_length=50,
        choices=AgentStatusEnums.choices(),
        default=AgentStatusEnums.AVAILABLE.value,
    )
    external_id = models.CharField(max_length=255, null=True, db_index=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True)
    role = models.CharField(max_length=50, default="courier")
    is_shift_active = models.BooleanField(default=False)
    is_blocked = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    is_staff = models.BooleanField(default=False)

    objects = CourierManager()

    USERNAME_FIELD = 'phone'

    groups = models.ManyToManyField(
        'auth.Group',
        related_name='courier_user_set',  # related_name qo'shildi
        blank=True,
        verbose_name=('groups'),
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='courier_user_permissions',  # related_name qo'shildi
        blank=True,
        verbose_name=('user permissions'),
    )

    class Meta:
        """
        the courier meta clas
        """
        db_table = 'courier'
        ordering = ['-created_at', '-updated_at']

    def __str__(self):
        return str(self.name)

    def set_password(self, raw_password):
        """
        Set the password for the courier.
        """
        self.password = make_password(raw_password)

    def check_password(self, raw_password):
        """
        Check the password for the courier.
        """
        return check_password(raw_password, self.password)

    @classmethod
    def get_by_phone(cls, phone) -> "Courier":
        """
        Get courier by phone
        """
        return cls.objects.get(phone=phone, is_deleted=False)

    @classmethod
    def get_by_id(cls, courier_id) -> "Courier":
        """
        Get courier by id
        """
        return cls.objects.get(id=courier_id, is_deleted=False)

    @property
    def location(self):
        """
        Get the user latitude
        """
        from apps.courier.models.point import CourierPoint
        return CourierPoint.by_courier_id(self.id)

    def mark_as_waiting(self):
        """
        mar as waiting for courier
        """
        self.status = AgentStatusEnums.WAITING.value
        self.save()

    @database_sync_to_async
    def mark_as_available(self):
        """
        mark as available for courier
        """
        self.status = AgentStatusEnums.AVAILABLE.value
        self.save()

    def mark_as_arrived(self):
        """
        Marking the courier as arrived.
        """
        self.status = AgentStatusEnums.ARRIVED.value
        self.save()

    @classmethod
    def get_by_external_id(cls, external_id) -> "Courier":
        """
        Get courier by external id
        """
        return cls.objects.get(external_id=external_id, is_deleted=False)

    def mark_as_is_shift_active(self):
        """
        mark as is_shift_active field
        """
        self.is_shift_active = True
        self.save()

    def mark_as_not_is_shift_active(self):
        """
        mark as not is_shift_active field
        """
        self.is_shift_active = False
        self.status = AgentStatusEnums.NOT_IN_WORK
        self.save()

    def mark_as_in_order(self):
        """
        mark as in_order field
        """
        self.status = AgentStatusEnums.IN_ORDER.value
        self.save()

    def mark_as_logout(self):
        """
        mark as logout field
        """
        self.is_shift_active = False
        self.is_active = False
        self.is_verified = False
        self.status = AgentStatusEnums.NOT_IN_WORK
        self.save()

    def mark_as_login(self):
        """
        mark as login field
        """
        self.is_active = True
        self.is_verified = True
        self.save()

    @classmethod
    def get_available_agents(cls):
        """
        Get available courier objects who have points associated with them, excluding certain statuses.

        :return: QuerySet of Courier
        """
        statuses_to_exclude = [
            AgentStatusEnums.INACTIVE.value,
            AgentStatusEnums.IS_BLOCKED.value,
            AgentStatusEnums.BUSY.value
        ]

        available_agents = cls.objects.filter(
            status__in=[
                AgentStatusEnums.AVAILABLE.value,
                AgentStatusEnums.IN_WORK.value,
                AgentStatusEnums.WAITING.value,
                AgentStatusEnums.ARRIVED.value
            ]
        ).exclude(status__in=statuses_to_exclude).filter(
            courierpoint__isnull=False
        ).select_related("courierpoint")

        return available_agents

    @classmethod
    def change_status(cls, courier_id, status) -> "Courier":
        """
        change status of courier by courier_id id
        """
        courier = cls.objects.get(id=courier_id)
        courier.status = status
        courier.save()
        return courier

    @classmethod
    def delete_by_external_id(cls, external_id):
        """
        delete courier by external id
        """
        return cls.objects.get(external_id=external_id).delete()

    @classmethod
    def create(
        cls,
        name: str,
        phone: str,
        password: str,
        email: str = None,
        external_id: str = None,
        organization_id: str = None,
        passport: str = None,
        address: str = None,
    ) -> "Courier":
        """
        Create a new courier
        """
        with transaction.atomic():
            role, _ = Group.objects.get_or_create(name=UserRoleEnums.COURIER)

            organization = Organization.objects.get(
                external_id=organization_id
            ) if organization_id else None

            courier = Courier.objects.create(
                phone=phone,
                name=name,
                organization=organization,
                external_id=external_id,
                passport=passport,
                address=address,
                is_active=True,
                is_verified=False,
                is_staff=False,
            )
            courier.set_password(password)
            courier.save()

            courier.groups.add(role)
            courier.save()

            return courier

    @classmethod
    def set_by_external_id(cls, external_id, status):
        """
        set status by external id
        """
        courier = cls.objects.get(external_id=external_id)
        courier.status = status
        courier.save()
        return courier

    @classmethod
    def check_external_id_exists(cls, external_id) -> bool:
        """
        Check if the courier with the given external id exists
        """
        return cls.objects.filter(external_id=external_id, is_deleted=False).exists()
