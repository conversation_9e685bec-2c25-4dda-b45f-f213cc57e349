"""
This module defines the `IIKOCommandsAPI` class, which provides an interface for interacting with
the iiko API to retrieve the status of commands within the system.

The class leverages an HTTP client to send requests to the iiko API and retrieve responses
related to command statuses. The responses are parsed into Pydantic models for easier access and validation.
"""
from master_kebab import settings

from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import CommandsStatusRequestBody
from apps.iiko.providers.iiko.http.response import CommandsStatusResponseBody


class IIKOCommandsAPI:
    """
    IIKOCommandsAPI is responsible for handling requests related to command statuses
    within the iiko system. It allows users to check the status of a specific command
    by providing the organization ID and correlation ID.

    This class communicates with the iiko API and returns the command status in the form of a
    structured response body.

    Attributes:
        http (BaseHTTPClient): An instance of BaseHTTPClient to handle HTTP communication
                               with the iiko API.

    Methods:
        status(organization_id: str, corelation_id: str) -> CommandsStatusResponseBody:
            Sends a request to retrieve the status of a command and returns the parsed response.
    """
    def __init__(self, http: BaseHTTPClient):
        """
        Initializes the IIKOCommandsAPI instance with the provided HTTP client.

        Args:
            http (BaseHTTPClient): The HTTP client used to make requests to the iiko API.
        """
        self.http = http

    def status(self, organization_id: str, correlation_id: str, headers: dict) -> CommandsStatusResponseBody:
        """
        Sends a request to the iiko API to retrieve the status of a command.

        Args:
            organization_id (str): The unique identifier of the organization.
            corelation_id (str): The unique correlation identifier of the command.

        Returns:
            CommandsStatusResponseBody: A Pydantic model containing the command status and
                                        any error information, if applicable.
        """
        endpoint = "api/1/commands/status"

        request_body = CommandsStatusRequestBody(
            organizationId=organization_id,
            correlationId=correlation_id,
        )
        request_body = request_body.as_request_body()
        response_body = self.http.post(endpoint, json=request_body, headers=headers)

        return CommandsStatusResponseBody(**response_body)


http_base_client = BaseHTTPClient(settings.IIKO_NETWORK)
commands = IIKOCommandsAPI(http=http_base_client)
