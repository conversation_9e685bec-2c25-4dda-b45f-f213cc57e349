"""
implementation of tasks of organization
"""
from celery import Task
from master_kebab import celery_app

from apps.organization.helper.organization import organization_helper


@celery_app.task(bind=True, acks_late=False, task_acks_on_failure_or_timeout=False)
def update_terminal_group(self: Task):
    """
    Updating terminal groups for all organizations
    """
    organization_helper.update_terminal_groups()
