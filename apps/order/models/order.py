"""
the order category
"""
import logging
from datetime import timed<PERSON>ta

from django.db import models
from django.db import transaction
from django.utils import timezone

from simple_history.models import HistoricalRecords

from apps.user.models.user import Users

from apps.core.models.base import BaseModel
from apps.courier.models.courier import Courier
from apps.courier.models.shift import CourierShifts
from apps.operations.models.operations import Operations
from apps.bot.models.telegram import TelegramUser
from apps.order.enums.delivery import DeliveryType
from apps.organization.models import Organization
from apps.payment.enums.method import PaymentMethodEnum
from apps.core.enums.initiator import Initiator as InitiatorEnum
from apps.order.enums.order import OrderStatus as OrderStatusEnum, CourierFindingStatus


logger = logging.getLogger(__name__)


class Order(BaseModel):
    """
    The order model.
    """
    TAKER_TYPES = (
        ('OPERATORS', 'operator'),
        ('MANAGERS', 'managers')
    )

    class Meta:
        """
        The meta fields.
        """
        db_table = 'order'
        ordering = ['-created_at']

    user = models.ForeignKey(Users, on_delete=models.CASCADE, null=True, blank=True)
    delivery_agent = models.ForeignKey(Courier, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="Courier") # noqa
    operator = models.ForeignKey(Operations, on_delete=models.SET_NULL, null=True, blank=True)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE, null=True, blank=True)
    status = models.CharField(max_length=50, choices=OrderStatusEnum.choices(), default=OrderStatusEnum.CREATED.value)
    initiator = models.CharField(max_length=50, choices=InitiatorEnum.choices(), default=InitiatorEnum.CALL_CENTER.value) # noqa
    courier_shift = models.ForeignKey(CourierShifts, on_delete=models.CASCADE, related_name='orders', null=True, blank=True) # noqa
    external_id = models.CharField(null=True, blank=True, db_index=True)
    sent_to_couriers = models.ManyToManyField(Courier, related_name='sent_orders', blank=True)

    courier_search_task_id = models.CharField(max_length=255, null=True, blank=True)
    external_number = models.CharField(max_length=255, null=True, blank=True)
    courier_finding_status = models.CharField(
        max_length=50,
        choices=CourierFindingStatus.choices(),
        default=CourierFindingStatus.NOT_STARTED.value
    )
    taker_id = models.IntegerField(
        null=True,
        blank=True,
        verbose_name="Taker ID",
        help_text="Telegram user ID of the taker"
    )
    taker_type = models.CharField(
        max_length=10,
        choices=TAKER_TYPES,
        null=True,
        blank=True,
        verbose_name="Taker Type"
    )
    attempt_count = models.IntegerField(default=0)
    is_paid = models.BooleanField(default=False)
    log = models.TextField(null=True, blank=True)

    history = HistoricalRecords(
        excluded_fields=[
            'user',
            'delivery_agent',
            'operator',
            'organization',
            'courier_shift',
            'external_id',
            'courier_search_task_id',
            'external_number',
            'courier_finding_status',
            'attempt_count',
            'is_paid',
            'initiator',
            'log',
        ]
    )

    def __str__(self):
        return f"Order - {self.id}, Status: {self.status}" # noqa

    @property
    def is_cash(self):
        """
        getting payment type
        """
        if self.order_detail:
            payment_type = self.order_detail.payment_method
            return payment_type == PaymentMethodEnum.CASH.value

        return False

    @property
    def total_cost(self):
        """
        getting total cost
        """
        total_cost = 0
        if self.order_detail:
            total_cost = self.order_detail.total_cost

        return total_cost

    @property
    def _history_user(self):
        return None

    @_history_user.setter
    def _history_user(self, value):
        self.changed_by = None

    def is_courier_accepted(self) -> bool:
        """
        checks if courier is accepted
        """
        return self.courier_finding_status == CourierFindingStatus.FOUND.value

    @classmethod
    def can_create_order(cls, user: Users, max_orders_per_day=10) -> bool:
        """
        Checks if the user can create more orders today.
        Optimizes query to only count orders with statuses WaitCooking, CookingComplete, Waiting, and OnWay.
        """
        today = timezone.now().date()
        included_statuses = [
            OrderStatusEnum.WAIT_COOKING.value,
            OrderStatusEnum.COOKING_COMPLETED.value,
            OrderStatusEnum.WAITING.value,
            OrderStatusEnum.ON_WAY.value
        ]
        orders_today = cls.objects.filter(
            user=user,
            created_at__date=today,
            status__in=included_statuses
        ).count()
        return orders_today < max_orders_per_day

    def mark_as_in_searching(self):
        """
        Marking the order as searching for delivery agent.
        """
        self.courier_finding_status = CourierFindingStatus.IN_SEARCHING.value
        self.save()

    def mark_as_waiting(self, is_courier_accepted=None, courier_shift=None):
        """
        Marking the order as waiting for delivery agent.
        """
        self.status = OrderStatusEnum.WAITING.value

        if is_courier_accepted is not None:
            self.courier_finding_status = is_courier_accepted

        if courier_shift:
            self.courier_shift = courier_shift

        self.save()

    def mark_as_wait_cooking(self):
        """
        Marking the order as waiting for cooking.
        """
        self.status = OrderStatusEnum.WAIT_COOKING.value
        self.save()

    def mark_as_cooking_complete(self):
        """
        Marking the order as cooking complete.
        """
        self.status = OrderStatusEnum.COOKING_COMPLETED.value
        self.save()

    def mark_as_cooking_started(self):
        """
        Marking the order as cooking started.
        """
        self.status = OrderStatusEnum.COOKING_STARTED.value
        self.save()

    def mark_as_cancelled(self):
        """
        Marking the order as cancelled.
        """
        self.status = OrderStatusEnum.CANCELLED.value
        self.save()

    def mark_as_delivered(self):
        """
        Marking the order as delivered.
        """
        self.status = OrderStatusEnum.DELIVERED.value
        self.save()

    def mark_as_closed(self):
        """
        Marking the order as closed.
        """
        self.status = OrderStatusEnum.CLOSED.value

        if self.order_detail.payment_method == PaymentMethodEnum.CASH:
            self.is_paid = True

        self.save()

    def is_status_in_searching(self) -> bool:
        """
        Check if the order status is searching for delivery agent.
        """
        return self.courier_finding_status == CourierFindingStatus.IN_SEARCHING.value

    def is_status_in_waiting(self) -> bool:
        """
        Check if the order status is waiting for delivery agent.
        """
        return self.status == OrderStatusEnum.WAITING.value

    def is_status_in_wait_cooking(self) -> bool:
        """
        Check if the order status is waiting for cooking.
        """
        return self.status == OrderStatusEnum.WAIT_COOKING.value

    def is_status_in_cooking_started(self) -> bool:
        """
        Check if the order status is cooking started.
        """
        return self.status == OrderStatusEnum.COOKING_STARTED.value

    def get_tg_user(self) -> TelegramUser:
        return TelegramUser.get_by_user_id(self.user.id)

    def is_status_on_way(self) -> bool:
        """
        Check if the order status is on the way.
        """
        return self.status == OrderStatusEnum.ON_WAY.value

    def is_status_arrived(self) -> bool:
        """
        Check if the order status is arrived.
        """
        return self.status == OrderStatusEnum.ARRIVED.value

    def is_status_delivered(self) -> bool:
        """
        Check if the order status is delivered.
        """
        return self.status == OrderStatusEnum.DELIVERED.value

    def mark_as_courier_not_found(self):
        """
        Marking the order as delivery not found.
        """
        self.courier_finding_status = CourierFindingStatus.NOT_FOUND.value
        self.save()

    def mark_as_approved(self, is_operator=True):
        """
        Marking the order as approved.
        """
        self.status = OrderStatusEnum.APPROVED.value

        if not is_operator:
            if self.order_detail.payment_method != PaymentMethodEnum.CASH:
                self.is_paid = True

        # avoid circular import
        self.save()
        from apps.order.signals.order import create_delivery_for_iiko
        create_delivery_for_iiko(self)

    def mark_as_courier_finding_error(self, log):
        """
        Marking the order as courier finding error.
        """
        self.courier_finding_status = CourierFindingStatus.SEARCHING_ERROR.value
        self.log = log
        self.save()

    def mark_as_on_way(self):
        """
        Marking the order as on the way.
        """
        self.status = OrderStatusEnum.ON_WAY.value
        self.save()

    def mark_as_creation_error(self):
        """
        Marking the order as error.
        """
        self.status = OrderStatusEnum.CREATION_ERROR.value
        self.save()

    def is_order_type_delivery(self) -> bool:
        """
        checking for order type delivery
        """
        return self.order_detail.delivery_type == DeliveryType.DELIVERY.value

    @property
    def is_payme_order(self):
        """
        checking for order type payme
        """
        return self.order_detail.payment_method == PaymentMethodEnum.PAYME.value

    def mark_as_arrived(self):
        """
        Marking the order as arrived.
        """
        self.status = OrderStatusEnum.ARRIVED.value
        self.save()

    def mark_as_unconfirmed(self):
        """
        Marking the order as unconfirmed.
        """
        self.status = OrderStatusEnum.UNCONFIRMED.value
        self.save()

    def mark_as_connection_error(self):
        """
        Marking the order as connection error.
        """
        self.status = OrderStatusEnum.CONNECTION_ERROR.value
        self.save()

    def set_status(self, status):
        """
        Set the status of the order
        """
        self.status = status
        self.save()

    def is_approved(self):
        """
        Check if the order status is approved.
        """
        return self.status == OrderStatusEnum.APPROVED.value

    def is_created(self):
        """
        Check if the order status is created.
        """
        return self.status == OrderStatusEnum.CREATED.value

    @property
    def payment_phone(self):
        """
        Fetch the phone number of the user associated with the order.
        """
        return self.order_detail.payment_phone

    @classmethod
    def mark_as_timeout_for_pending_orders(cls, minute=10):
        timeout_threshold = timezone.now() - timedelta(minutes=minute)

        orders_to_timeout = cls.objects.filter(
            status=OrderStatusEnum.CREATED, updated_at__lt=timeout_threshold
        )

        for order in orders_to_timeout:
            order.status = OrderStatusEnum.TIMEOUT
            order.save(update_fields=['status', 'updated_at'])

    @classmethod
    def check_order_exists(cls, order_id) -> bool:
        """
        check order existence
        """
        return cls.objects.filter(id=order_id).exists()

    @classmethod
    def get_by_id(cls, order_id):
        """
        get order by id
        """
        return cls.objects.select_related('user', 'order_detail', 'organization').prefetch_related('order_items').get(id=order_id) # NOQA

    @classmethod
    def get_by_external_id(cls, external_id):
        """
        get order by external id
        """
        return cls.objects.select_related('user', 'order_detail', 'organization').prefetch_related('order_items').get(external_id=external_id) # NOQA

    @classmethod
    def mark_as_webook_error(cls, external_id, log):
        """
        marking as webhook error
        """
        order = cls.objects.get(external_id=external_id)
        order.status = OrderStatusEnum.WEBHOOK_ERROR.value
        order.log = log
        order.save(update_fields=['status', 'log'])
        return order

    @property
    def is_initiator_call_center(self):
        """
        get initiator
        """
        return self.initiator == InitiatorEnum.CALL_CENTER.value

    @classmethod
    def get_order_by_ids(cls, order_ids):
        """
        get orders by ids
        """
        return cls.objects.filter(id__in=order_ids).select_related('user', 'order_detail', 'organization').prefetch_related('order_items') # NOQA

    @classmethod
    def create(
        cls,
        order_detail,
        user=user,
        initiator=initiator,
        organization=organization
    ):
        """
        create order class method
        """
        with transaction.atomic():
            order = cls(
                user=user,
                initiator=initiator,
                organization=organization,
            )
            order.save()
            order_detail.order = order
            order_detail.save()
            return order

    @classmethod
    def active_orders(cls, role="client", object_owner_id=None):
        if role == "client":
            return cls.objects.filter(
                user_id=object_owner_id,
                status__in=[
                    OrderStatusEnum.WAITING.value,
                    OrderStatusEnum.ON_WAY.value,
                    OrderStatusEnum.ARRIVED.value,
                    OrderStatusEnum.CREATED.value,
                    OrderStatusEnum.APPROVED.value,
                ]).select_related('user', 'organization').prefetch_related('order_items')

    @classmethod
    def bind_to_taker(cls, order_id, taker_id, taker_type):
        """
        bind order to taker
        """
        order = cls.objects.get(id=order_id)
        order.taker_id = taker_id
        order.taker_type = taker_type
        order.save()


def get_order_by_id(order_id) -> Order:
    """
    get order by id
    """
    return Order.objects.get(id=order_id)


def get_orders_by_status(order_status) -> Order:
    """
    get orders by status
    """
    return Order.objects.filter(status=order_status)


def get_order_by_external_id(external_id):
    """
    Get order by external id.
    """
    return Order.objects.get(external_id=external_id)
