from django.core.cache import cache

from master_kebab import settings

from apps.iiko.providers.iiko.http.exception import Unauthorized
from apps.iiko.providers.iiko.http.client.base import BaseHTTPClient
from apps.iiko.providers.iiko.http.request import AccessTokenRequestBody
from apps.iiko.providers.iiko.http.request import TerminalGroupRequestBody
from apps.iiko.providers.iiko.http.response import AccessTokenResponseBody
from apps.iiko.providers.iiko.http.response import OrganizationResponseBody
from apps.iiko.providers.iiko.http.response import TerminalGroupResponseBody


class IIKOVariablesAPI:
    """
    IIKO getting variables API client.

    This class provides methods to interact with IIKO getting variables API
    for obtaining an access token and retrieving organizations associated with the API login.
    """
    TOKEN_EXPIRY_BUFFER = 60  # seconds
    TOKEN_CACHE_KEY = "iiko_access_token"

    def __init__(self, http: BaseHTTPClient, api_login: str):
        """
        Initializes IIKOVariablesAPI instance.

        Args:
            http (BaseHTTPClient): An instance of BaseHTTPClient used for HTTP requests.
            api_login (str): The API login created in the iikoWeb account.
        """
        self.http = http
        self.api_login = api_login

    def get_cached_token(self) -> str:
        """
        Retrieve the cached token or request a new one if expired/missing.

        Returns:
            str: The access token.
        """
        token = cache.get(self.TOKEN_CACHE_KEY)

        if not token:
            token = self.access_token().token
            cache.set(self.TOKEN_CACHE_KEY, token, timeout=self.TOKEN_EXPIRY_BUFFER)

        return token

    def access_token(self) -> AccessTokenResponseBody:
        """
        Obtain an access token required for iikoCloud API.

        Returns:
            AccessTokenResponseBody: The response body containing the access token.

        Raises:
            IIKOHTTPError: If an error occurs during the request.
            Unauthorized: If the request is unauthorized
        """
        endpoint = "/api/1/access_token"
        request_body = AccessTokenRequestBody(apiLogin=self.api_login).as_request_body()
        response_body = self.http.post(endpoint, json=request_body)
        response_body = AccessTokenResponseBody(**response_body)
        return response_body

    def organizations(self, headers) -> OrganizationResponseBody:
        """
        Retrieve a list of organizations associated with the API login.

        Returns:
            dict: A dictionary containing organization information.

        Raises:
            IIKOHTTPError: If an error occurs during the request.
            Unauthorized: If the request is unauthorized
        """
        endpoint = "/api/1/organizations"
        try:
            response_body = self.http.post(endpoint, json={}, headers=headers)
        except Unauthorized:
            self.update_token()
            response_body = self.http.post(endpoint, json={}, headers=headers)

        response_body = OrganizationResponseBody(**response_body)
        return response_body

    def terminal_groups(
        self,
        headers,
        organization_id=None,
    ) -> TerminalGroupResponseBody:
        """
        Retrieve a list of terminal groups associated with the API login.

        Args:
            headers (dict): The headers used for API requests.
            organization_id (str, optional): The ID of the organization. Defaults to None.

        Returns:
            dict: A dictionary containing terminal group information.

        Raises:
            IIKOHTTPError: If an error occurs during the request.
            Unauthorized: If the request is unauthorized
        """
        endpoint = "/api/1/terminal_groups"

        request_body = TerminalGroupRequestBody(
            organizationId=organization_id
        ).as_request_body()

        try:
            response_body = self.http.post(endpoint, json=request_body, headers=headers)

        except Unauthorized:
            self.update_token()
            response_body = self.http.post(endpoint, json=request_body, headers=headers)

        response_body = TerminalGroupResponseBody(**response_body)
        return response_body

    def update_token(self) -> None:
        """
        Update the request token and refresh the headers.
        """

    def get_headers(self) -> dict:
        """
        Get the current headers used for API requests.

        Returns:
            dict: A dictionary of the current headers.
        """
        token = self.get_cached_token()
        return {
            'Content-Type': 'application/json',
            "Authorization": f"Bearer {token}"
        }


variables = IIKOVariablesAPI(
    http=BaseHTTPClient(settings.IIKO_NETWORK),
    api_login=settings.IIKO_API_LOGIN
)
