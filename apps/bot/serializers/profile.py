"""
Serializers for telegram user profile in minapp
"""
from rest_framework import serializers


class BotUserProfileSerializer(serializers.Serializer):
    """
        Serializer for handling user's profile requests.
    """
    first_name = serializers.Char<PERSON>ield(max_length=200)
    last_name = serializers.CharField(allow_null=True)
    gender = serializers.ChoiceField(
        choices=['male', 'female'],
        required=False,
        error_messages={
            'invalid_choice': 'Gender must be either "male" or "female"'
        }
    )
    lang = serializers.CharField(max_length=20, default="ru")
    phone = serializers.CharField(max_length=16, allow_null=True)

    def validate_gender(self, value):
        if value:
            return value.lower().strip()
        return value
