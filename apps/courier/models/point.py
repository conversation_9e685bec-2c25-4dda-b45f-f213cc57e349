"""
This module defines the CourierPoint model, which represents a geographical
point associated with a courier, including their latitude and longitude.

The model includes:
- `courier`: A one-to-one relationship with the Courier model.
- `latitude`: The latitude of the courier's location.
- `longitude`: The longitude of the courier's location.

Usage:
    This model can be used to store and manage the location data of couriers.
"""

from django.db import models
from apps.core.models.base import BaseModel
from apps.courier.models.courier import Courier


class CourierPoint(BaseModel):
    """
    Represents a geographical point associated with a courier.

    Attributes:
        courier (OneToOneField): The courier associated with this point.
        latitude (FloatField): The latitude of the courier's location.
        longitude (FloatField): The longitude of the courier's location.
    """

    class Meta:
        """
        Meta options for the CourierPoint model.

        Attributes:
            db_table (str): The name of the database table.
            ordering (list): The default ordering of records.
        """
        db_table = 'courier_point'
        ordering = ['-created_at', '-updated_at']

    courier = models.OneToOneField(Courier, on_delete=models.CASCADE, null=True)
    latitude = models.FloatField()
    longitude = models.FloatField()

    @classmethod
    def update_or_create(cls, courier_id, latitude, longitude):
        """
        Update an existing point or create a new one if it doesn't exist.

        Args:
            courier_id (int): The ID of the courier.
            latitude (float): The latitude to set.
            longitude (float): The longitude to set.

        Returns:
            tuple: A tuple with the instance and a boolean indicating whether
                   a new instance was created.
        """
        return cls.objects.update_or_create(
            courier_id=courier_id,
            defaults={
                'latitude': latitude,
                'longitude': longitude
            }
        )

    @classmethod
    def by_courier_id(cls, courier_id):
        """
        Retrieve a CourierPoint instance by courier ID.

        Args:
            courier_id (int): The ID of the courier.

        Returns:
            CourierPoint: The CourierPoint instance associated with the given
                          courier ID.

        Raises:
            DoesNotExist: If no CourierPoint exists for the given courier ID.
        """
        return cls.objects.get(courier_id=courier_id)
