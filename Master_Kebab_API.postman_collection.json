{"info": {"_postman_id": "master-kebab-api-collection", "name": "Master Kebab API", "description": "Complete API collection for Master Kebab backend system including authentication, orders, products, payments, and more.", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://api.master-kebab.uz", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Request OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+998901234567\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/request-otp/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "request-otp", ""]}}}, {"name": "Verify OTP", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+998901234567\",\n    \"otp\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/verify-otp/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "verify-otp", ""]}}}, {"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+998901234567\",\n    \"name\": \"<PERSON>\",\n    \"password\": \"securepassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/register/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "register", ""]}}}, {"name": "Get Token (Login)", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('access_token', response.access);", "    pm.environment.set('refresh_token', response.refresh);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+998901234567\",\n    \"password\": \"securepassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/get/token/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "get", "token", ""]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refresh\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/logout/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "logout", ""]}}}]}, {"name": "User Management", "item": [{"name": "Get User Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "status", ""]}}}, {"name": "Check Telegram User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"chat_id\": \"*********\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/check-user/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "check-user", ""]}}}, {"name": "Get User Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/statistics/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "statistics", ""]}}}, {"name": "Delete Account", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/users/delete/", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "delete", ""]}}}]}, {"name": "Products", "item": [{"name": "Get Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/categories/", "host": ["{{base_url}}"], "path": ["api", "v1", "product", "categories", ""]}}}, {"name": "Get Category Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/categories/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "product", "categories", "1", ""]}}}, {"name": "Get Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/?category_id=1", "host": ["{{base_url}}"], "path": ["api", "v1", "product", ""], "query": [{"key": "category_id", "value": "1"}]}}}, {"name": "Get Product Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "product", "1", ""]}}}, {"name": "Get Modifiers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/modifiers/", "host": ["{{base_url}}"], "path": ["api", "v1", "product", "modifiers", ""]}}}, {"name": "Get Banners", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/banners/", "host": ["{{base_url}}"], "path": ["api", "v1", "product", "banners", ""]}}}, {"name": "Get Banner Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/banners/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "product", "banners", "1", ""]}}}, {"name": "Get Stop List", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/product/stop-list/", "host": ["{{base_url}}"], "path": ["api", "v1", "product", "stop-list", ""]}}}]}, {"name": "Orders", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Initiator", "value": "web"}], "body": {"mode": "raw", "raw": "{\n    \"order_items\": [\n        {\n            \"product_id\": 1,\n            \"quantity\": 2,\n            \"modifiers\": [\n                {\n                    \"modifier_id\": 1,\n                    \"quantity\": 1\n                }\n            ]\n        }\n    ],\n    \"delivery_type\": \"delivery\",\n    \"address\": {\n        \"street\": \"Amir Temur Street\",\n        \"house_number\": \"123\",\n        \"apartment\": \"45\",\n        \"latitude\": 41.2995,\n        \"longitude\": 69.2401\n    },\n    \"payment_method\": \"cash\",\n    \"comment\": \"Please call when you arrive\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders/create/", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "create", ""]}}}, {"name": "Create Order for Telegram", "event": [{"listen": "test", "script": {"exec": ["// Check for throttling response", "if (pm.response.code === 429) {", "    console.log('Request was throttled. Each Telegram user (chat_id) can only make 1 order request per 5 seconds.');", "    pm.test('Throttling is working', function () {", "        pm.response.to.have.status(429);", "    });", "} else if (pm.response.code === 201) {", "    pm.test('Order created successfully', function () {", "        pm.response.to.have.status(201);", "    });", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_items\": [\n        {\n            \"product_id\": 1,\n            \"quantity\": 1\n        }\n    ],\n    \"delivery_type\": \"delivery\",\n    \"address\": {\n        \"street\": \"Amir Temur Street\",\n        \"house_number\": \"123\",\n        \"latitude\": 41.2995,\n        \"longitude\": 69.2401\n    },\n    \"payment_method\": \"cash\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/api/v1/orders/create/*********/telegram/", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "create", "*********", "telegram", ""]}, "description": "Creates an order for a Telegram user. This endpoint is throttled to allow only 1 request per 5 seconds per chat_id (from URL path). If you make requests too quickly, you'll receive a 429 status code."}}, {"name": "Get Order History", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/orders/history/", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "history", ""]}}}, {"name": "Get Order Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/orders/history/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "history", "1", ""]}}}, {"name": "Accept Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/v1/orders/1/accept/", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "1", "accept", ""]}}}, {"name": "Cancel Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"reason\": \"Customer requested cancellation\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/orders/1/cancel/", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "1", "cancel", ""]}}}, {"name": "Get Delivery Prices", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/orders/delivery-prices/?latitude=41.2995&longitude=69.2401", "host": ["{{base_url}}"], "path": ["api", "v1", "orders", "delivery-prices", ""], "query": [{"key": "latitude", "value": "41.2995"}, {"key": "longitude", "value": "69.2401"}]}}}]}, {"name": "Payment", "item": [{"name": "Get Payment Methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/payment/methods/", "host": ["{{base_url}}"], "path": ["api", "v1", "payment", "methods", ""]}}}, {"name": "Create Payment Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"order_id\": 1,\n    \"amount\": 50000,\n    \"payment_method\": \"payme\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/payment/payment-link/", "host": ["{{base_url}}"], "path": ["api", "v1", "payment", "payment-link", ""]}}}, {"name": "Update Payment Method", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"is_active\": true\n}"}, "url": {"raw": "{{base_url}}/api/v1/payment/methods/1/update", "host": ["{{base_url}}"], "path": ["api", "v1", "payment", "methods", "1", "update"]}}}, {"name": "Click SuperApp Auth", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+998901234567\",\n    \"code\": \"123456\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/payment/click/superapp/auth/", "host": ["{{base_url}}"], "path": ["api", "v1", "payment", "click", "superapp", "auth", ""]}}}]}, {"name": "Courier", "item": [{"name": "Courier Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('courier_access_token', response.access);", "    pm.environment.set('courier_refresh_token', response.refresh);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+998901234567\",\n    \"password\": \"courierpassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/courier/token/", "host": ["{{base_url}}"], "path": ["api", "v1", "courier", "token", ""]}}}, {"name": "Courier Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{courier_access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"refresh\": \"{{courier_refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/courier/logout/", "host": ["{{base_url}}"], "path": ["api", "v1", "courier", "logout", ""]}}}, {"name": "Get Courier Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{courier_access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/courier/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "courier", "status", ""]}}}, {"name": "Get Active Orders", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{courier_access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/courier/active-orders/", "host": ["{{base_url}}"], "path": ["api", "v1", "courier", "active-orders", ""]}}}, {"name": "Update Point", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{courier_access_token}}"}], "body": {"mode": "raw", "raw": "{\n    \"latitude\": 41.2995,\n    \"longitude\": 69.2401\n}"}, "url": {"raw": "{{base_url}}/api/v1/courier/update-point/", "host": ["{{base_url}}"], "path": ["api", "v1", "courier", "update-point", ""]}}}, {"name": "Check Shift Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{courier_access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/courier/shift/is_open/", "host": ["{{base_url}}"], "path": ["api", "v1", "courier", "shift", "is_open", ""]}}}, {"name": "Close Shift", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{courier_access_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/v1/courier/shift/close/", "host": ["{{base_url}}"], "path": ["api", "v1", "courier", "shift", "close", ""]}}}]}, {"name": "Organization", "item": [{"name": "Get Organizations", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/organization/", "host": ["{{base_url}}"], "path": ["api", "v1", "organization", ""]}}}, {"name": "Update Organization", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Organization Name\",\n    \"address\": \"New Address\",\n    \"phone\": \"+998712345678\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/organization/1/update", "host": ["{{base_url}}"], "path": ["api", "v1", "organization", "1", "update"]}}}]}, {"name": "Core Services", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/ping/", "host": ["{{base_url}}"], "path": ["api", "v1", "ping", ""]}}}, {"name": "Remote Config", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/remote-config/", "host": ["{{base_url}}"], "path": ["api", "v1", "remote-config", ""]}}}, {"name": "Get Logs", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/logs/", "host": ["{{base_url}}"], "path": ["api", "v1", "logs", ""]}}}]}, {"name": "Map Services", "item": [{"name": "Get Map API Keys", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/map/api-key/", "host": ["{{base_url}}"], "path": ["api", "v1", "map", "api-key", ""]}}}, {"name": "Check Geolocation Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/map/status/", "host": ["{{base_url}}"], "path": ["api", "v1", "map", "status", ""]}}}]}, {"name": "HR", "item": [{"name": "Get Job Positions", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/hr/job-positions/", "host": ["{{base_url}}"], "path": ["api", "v1", "hr", "job-positions", ""]}}}, {"name": "Get Job Applications", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/hr/job-applications/", "host": ["{{base_url}}"], "path": ["api", "v1", "hr", "job-applications", ""]}}}, {"name": "Create Job Application", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"position_id\": 1,\n    \"name\": \"<PERSON>\",\n    \"phone\": \"+998901234567\",\n    \"email\": \"<EMAIL>\",\n    \"experience\": \"2 years\",\n    \"cover_letter\": \"I am interested in this position...\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/hr/job-applications/", "host": ["{{base_url}}"], "path": ["api", "v1", "hr", "job-applications", ""]}}}, {"name": "Get Job Application Detail", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/hr/job-applications/1/", "host": ["{{base_url}}"], "path": ["api", "v1", "hr", "job-applications", "1", ""]}}}]}, {"name": "Notifications", "item": [{"name": "Send Notification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"New Promotion\",\n    \"message\": \"Check out our new kebab special!\",\n    \"user_ids\": [1, 2, 3]\n}"}, "url": {"raw": "{{base_url}}/api/v1/notify/send", "host": ["{{base_url}}"], "path": ["api", "v1", "notify", "send"]}}}, {"name": "Get Notifications", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/notify/notifications", "host": ["{{base_url}}"], "path": ["api", "v1", "notify", "notifications"]}}}]}, {"name": "Loyalty", "item": [{"name": "Apply Promo Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"promo_code\": \"KEBAB20\",\n    \"order_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/v1/loyalty/promo/", "host": ["{{base_url}}"], "path": ["api", "v1", "loyalty", "promo", ""]}}}, {"name": "Apply Telegram Promo Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"promo_code\": \"TELEGRAM10\",\n    \"chat_id\": \"*********\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/loyalty/telegram/promo/", "host": ["{{base_url}}"], "path": ["api", "v1", "loyalty", "telegram", "promo", ""]}}}]}, {"name": "Bot Operations", "item": [{"name": "Send Bot Message", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"title\": \"Special Offer\",\n    \"message\": \"Get 20% off on all kebabs today!\",\n    \"media_file\": \"https://example.com/image.jpg\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/bot/send-message/", "host": ["{{base_url}}"], "path": ["api", "v1", "bot", "send-message", ""]}}}, {"name": "Get Bot User Profile", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/bot/*********/profile/", "host": ["{{base_url}}"], "path": ["api", "v1", "bot", "*********", "profile", ""]}}}]}, {"name": "IIKO Integration", "item": [{"name": "Get IIKO Updates", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/iiko/updates/", "host": ["{{base_url}}"], "path": ["api", "v1", "iiko", "updates", ""]}}}, {"name": "Get IIKO Couriers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/iiko/couriers/", "host": ["{{base_url}}"], "path": ["api", "v1", "iiko", "couriers", ""]}}}, {"name": "Get Streets by City", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/iiko/streets/by_city?city_id=2189f314-64b4-458b-a04e-3328bf297d4c", "host": ["{{base_url}}"], "path": ["api", "v1", "iiko", "streets", "by_city"], "query": [{"key": "city_id", "value": "2189f314-64b4-458b-a04e-3328bf297d4c"}]}}}]}, {"name": "Operations Management", "item": [{"name": "Dashboard Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.environment.set('operations_access_token', response.access);", "    pm.environment.set('operations_refresh_token', response.refresh);", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"phone\": \"+998901234567\",\n    \"password\": \"operatorpassword123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/dashboard/login/", "host": ["{{base_url}}"], "path": ["api", "v1", "dashboard", "login", ""]}}}, {"name": "Get Managers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{operations_access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/managers/", "host": ["{{base_url}}"], "path": ["api", "v1", "managers", ""]}}}, {"name": "Get Operators", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{operations_access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/operators/", "host": ["{{base_url}}"], "path": ["api", "v1", "operators", ""]}}}, {"name": "Get Dispatchers", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{operations_access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/dispatchers/", "host": ["{{base_url}}"], "path": ["api", "v1", "dispatchers", ""]}}}, {"name": "Get Marketologs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{operations_access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/marketologs/", "host": ["{{base_url}}"], "path": ["api", "v1", "marketologs", ""]}}}]}]}