# Generated by Django 5.0.6 on 2025-02-01 14:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0032_alter_deliveryprice_distance_type"),
    ]

    operations = [
        migrations.CreateModel(
            name="Monitoring",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("user_id", models.IntegerField()),
                ("user_role", models.CharField()),
                ("action", models.CharField()),
                (
                    "order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="monitoring",
                        to="order.order",
                    ),
                ),
            ],
            options={
                "db_table": "monitoring",
            },
        ),
    ]
