"""
init delivery response model
"""
from typing import Optional, List, Any

from pydantic import UUID4
from pydantic import BaseModel

from apps.iiko.providers.iiko.http import exception as iiko_exception


class OrderInfo(BaseModel):
    """
    the order info model
    """
    id: str
    posId: Optional[str] = None
    externalNumber: Optional[str]
    organizationId: str
    timestamp: int
    creationStatus: str
    errorInfo: Optional[dict] = None
    order: Optional[str]


class OrderCreateWithoutGhostResponsetBody(BaseModel):
    """
    the request model
    """
    correlationId: str
    orderInfo: OrderInfo


class ConfirmResponseBody(BaseModel):
    """
    the confirm response model
    """
    correlationId: str


class UpdateOrderCourierResponseBody(BaseModel):
    """
    the confirm response model
    """
    correlationId: str
    errorDescription: str = None
    error: str = None

    def handle_error_response(self) -> None:
        if self.errorDescription or (self.error and self.error != "NO_ERROR"):
            if self.error == "DELIVERY_COURIER_NOT_FOUND":
                raise iiko_exception.DeliveryCourierNotFound(self)

            raise iiko_exception.ShiftClockInErrorResponse(self)


class UpdateOrderDeliveryStatusResponseBody(BaseModel):
    """
    the update order delivery status response model
    """
    correlationId: str


class CancelConfirmationResponseBody(BaseModel):
    """
    the cancel confirmation response model
    """
    correlationId: str


class Restriction(BaseModel):
    """
    restrictions response model
    """
    minSum: Optional[float] = None
    terminalGroupId: UUID4
    organizationId: UUID4
    zone: Optional[str] = None
    weekMap: int
    from_time: Optional[str] = None
    to: Optional[str] = None
    priority: int
    deliveryDurationInMinutes: int
    deliveryServiceProductId: Optional[UUID4] = None


class DeliveryRestriction(BaseModel):
    """
    delivery restriction response model
    """
    organizationId: str
    deliveryGeocodeServiceType: int
    deliveryRegionsMapUrl: Optional[str] = None
    defaultDeliveryDurationInMinutes: int
    defaultSelfServiceDurationInMinutes: int
    useSameDeliveryDuration: bool
    useSameMinSum: bool
    defaultMinSum: Optional[Any] = None
    useSameWorkTimeInterval: bool
    defaultFrom: Optional[str] = None
    defaultTo: Optional[str] = None
    useSameRestrictionsOnAllWeek: bool
    restrictions: List[Restriction] = []
    deliveryZones: List[dict] = []
    rejectOnGeocodingError: bool
    addDeliveryServiceCost: bool
    useSameDeliveryServiceProduct: bool
    defaultDeliveryServiceProductId: Optional[UUID4] = None
    useExternalAssignationService: bool
    frontTrustsCallCenterCheck: bool
    externalAssignationServiceUrl: Optional[str] = None
    requireExactAddressForGeocoding: bool
    zonesMode: int
    autoAssignExternalDeliveries: bool
    actionOnValidationRejection: int


class DeliveryRestrictionsResponseBody(BaseModel):
    """
    delivery restrictions response model
    """
    correlationId: str
    deliveryRestrictions: List[DeliveryRestriction]
