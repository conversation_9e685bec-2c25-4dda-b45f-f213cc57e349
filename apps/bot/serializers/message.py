"""
Serializers for bot message handling
"""
from rest_framework import serializers


class MessageSerializer(serializers.Serializer):
    """
    Serializer for handling message sending requests

    Fields:
        photo: Required image file to send
        message: Optional text message/caption
    """
    title = serializers.CharField()
    media_file = serializers.FileField(
        required=False,
        help_text="Media file to send to users (supports photos and videos: JPG, PNG, MP4, MOV, AVI, WebM, FLV, GIF)"
    )
    message = serializers.CharField(
        required=False,
        allow_blank=True,
        help_text="Optional text message or caption for the photo"
    )

    def validate_message(self, value):
        """
        Validate message text

        Args:
            value: Message text from request

        Returns:
            str: Validated message text

        Raises:
            ValidationError: If message is invalid
        """
        if value and len(value) > 1220:  # Telegram caption limit
            raise serializers.ValidationError(
                "Message length exceeds Telegram's limit of 1024 characters"
            )
        return value
