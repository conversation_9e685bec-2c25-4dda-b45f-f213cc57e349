"""
Frontend views for Softphone integration
"""
from django.shortcuts import render
from django.views.generic import TemplateView
from django.http import JsonResponse
from rest_framework.views import APIView
from rest_framework.response import Response

from apps.core.views import ServiceBaseView


class CallPopupView(TemplateView):
    """
    View for displaying call popup window
    """
    template_name = 'softphone/popup.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update({
            'caller_id': self.request.GET.get('callerid'),
            'did': self.request.GET.get('did'),
            'direction': self.request.GET.get('direction', 'inbound')
        })
        return context


class SoftphoneConfigView(APIView, ServiceBaseView):
    """
    View for providing Softphone.Pro configuration
    """
    
    def get(self, request):
        """
        Return Softphone.Pro configuration for integration
        """
        try:
            from apps.softphone.models.softphone_config import SoftphoneConfig
            
            config = SoftphoneConfig.get_active_config()
            
            if not config:
                # Return default configuration
                base_url = request.build_absolute_uri('/')[:-1]  # Remove trailing slash
                
                return Response({
                    "screen_popup_url": f"{base_url}/softphone/popup/",
                    "customer_data_url": f"{base_url}/api/v1/softphone/customer-data/",
                    "call_log_url": f"{base_url}/api/v1/softphone/log-call/",
                    "recording_upload_url": f"{base_url}/api/v1/softphone/upload-recording/",
                    "click_to_call_enabled": True,
                    "enable_screen_popup": True,
                    "enable_call_logging": True,
                    "enable_recording_upload": True,
                    "default_sip_account": "1"
                })
            
            # Return active configuration
            base_url = request.build_absolute_uri('/')[:-1]
            
            return Response({
                "name": config.name,
                "screen_popup_url": config.screen_popup_url.replace('localhost:8000', request.get_host()),
                "customer_data_url": config.customer_data_url.replace('localhost:8000', request.get_host()),
                "call_log_url": config.call_log_url.replace('localhost:8000', request.get_host()),
                "recording_upload_url": config.recording_upload_url.replace('localhost:8000', request.get_host()),
                "click_to_call_enabled": config.click_to_call_enabled,
                "enable_screen_popup": config.enable_screen_popup,
                "enable_call_logging": config.enable_call_logging,
                "enable_recording_upload": config.enable_recording_upload,
                "default_sip_account": config.default_sip_account or "1",
                "caller_id_regex": config.caller_id_regex
            })
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to get softphone configuration: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
