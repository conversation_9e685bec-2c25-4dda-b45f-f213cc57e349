# Generated by Django 5.0.6 on 2024-08-20 11:29

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SystemParameter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(choices=[('iiko', 'Iiko'), ('search_nearest_agent_scheduler', 'Search nearest agent scheduler')], max_length=50)),
                ('value', models.CharField(max_length=255)),
                ('is_enabled', models.BooleanField(default=False)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'db_table': 'sys_params',
            },
        ),
    ]
