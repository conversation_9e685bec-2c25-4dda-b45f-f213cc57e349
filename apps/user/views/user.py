"""
init registration
"""
from rest_framework import status
from rest_framework import viewsets
from rest_framework import filters
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated

from django_filters.rest_framework import DjangoFilterBackend

from apps.user.models import Users
from apps.core.views import ServiceBaseView
from apps.bot.models.telegram import TelegramUser
from apps.user.serializers.user import UserSerializer
from apps.user.permissions.is_owner import IsOwnerOrAdmin
from apps.user.filters.user import UsersFilter


class UserStatusAPIView(APIView):
    """
    the user register serialization view
    """
    def get(self, request, *args, **kwargs):
        """
        register allows only GET requests
        """
        chat_id = request.query_params.get('chat_id', None)

        if not chat_id:
            return Response({
                "message": "No query parameter provided"
            })

        result = TelegramUser.check_exsists(chat_id)

        if result:
            return Response({
                "status": result
            })

        return Response(status=status.HTTP_404_NOT_FOUND)


class IsTelegramUserExists(APIView, ServiceBaseView):
    """
    the user register serialization view
    """
    def get(self, request, *args, **kwargs):
        chat_id = request.query_params.get('chat_id', None)

        if not chat_id:
            raise self.call_service_exception(
                error_type="bad_request",
                message="No query parameter provided for chat_id",
                status_code=400,
                notify_admin=True,
            )

        try:
            result = TelegramUser.check_user_exists(chat_id)

        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to check telegram user existence: {exc}",
                status_code=500,
                notify_admin=True,
            )

        return Response({
            "result": result
        })


class UserViewSet(viewsets.ModelViewSet):
    """
    the user register serialization view.
    """
    queryset = Users.objects.all()
    serializer_class = UserSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrAdmin]
    filterset_class = UsersFilter
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
