"""
This module initializes the IIKO HTTP client interface, providing a centralized access point
for various IIKO API services.
"""

from apps.iiko.providers.iiko.http.client import menu
from apps.iiko.providers.iiko.http.client import terminal
from apps.iiko.providers.iiko.http.client import webhook
from apps.iiko.providers.iiko.http.client import delivery
from apps.iiko.providers.iiko.http.client import variables
from apps.iiko.providers.iiko.http.client import employees
from apps.iiko.providers.iiko.http.client import nomenclature
from apps.iiko.providers.iiko.http.client import address
from apps.iiko.providers.iiko.http.client import payment
from apps.iiko.providers.iiko.http.client import commands
from apps.iiko.providers.iiko.http.client import loyalty


class IIKOClientAPI:
    """
    The IIKO HTTP client interface.

    This class serves as a centralized interface for interacting with various IIKO API services.
    It aggregates different API clients, each responsible for specific functionalities within
    the IIKO system, providing a unified access point for all IIKO-related operations.

    Attributes:
        delivery (IIKODeliveryAPI): Handles delivery-related operations.
        variables (IIKOVariablesAPI): Manages system variables.
        nomenclature (IIKONomenclatureAPI): Deals with nomenclature-related methods.
        employees (IIKOEmployeeAPI): Manages employee-related operations.
        menu (IIKOMenuAPI): Handles menu-related functionalities.
        terminal_group (IIKOTerminalGroupAPI): Manages terminal group operations.
        address (IIKOAddressAPI): Handles address-related functionalities.
        webhook (IIKOWebHookAPI): Manages webhook operations.
        commands (IIKOCommandAPI): Handles commands related functionalities.
    """

    def __init__(
        self,
        delivery: delivery.IIKODeliveryAPI,
        variables: variables.IIKOVariablesAPI,
        nomenclature: nomenclature.IIKONomenclatureAPI,
        employees: employees.IIKOEmployeeAPI,
        menu: menu.IIKOMenuAPI,
        terminal_group: terminal.IIKOTerminalGroupAPI,
        address: address.IIKOAddressAPI,
        payment: payment.IIKOPaymentTypes,
        webhook: webhook.IIKOWebHookAPI,
        commands: commands.IIKOCommandsAPI,
        loyalty: loyalty.IIKOLoyaltyAPI,
    ):
        """
        Initialize the IIKO HTTP client interface.

        Args:
            delivery (IIKODeliveryAPI): Handles delivery-related operations.
            variables (IIKOVariablesAPI): Manages system variables.
            nomenclature (IIKONomenclatureAPI): Deals with nomenclature-related methods.
            employees (IIKOEmployeeAPI): Manages employee-related operations.
            menu (IIKOMenuAPI): Handles menu-related functionalities.
            terminal_group (IIKOTerminalGroupAPI): Manages terminal group operations.
            address (IIKOAddressAPI): Handles address-related functionalities.
            webhook (IIKOWebHookAPI): Manages webhook operations.
        """
        self.delivery = delivery
        self.variables = variables
        self.nomenclature = nomenclature
        self.employees = employees
        self.menu = menu
        self.terminal_group = terminal_group
        self.address = address
        self.webhook = webhook
        self.payment = payment
        self.commands = commands
        self.loyalty = loyalty


client = IIKOClientAPI(
    delivery=delivery.delivery,
    variables=variables.variables,
    nomenclature=nomenclature.nomenclature,
    employees=employees.employee,
    menu=menu.manu,
    terminal_group=terminal.terminal_group,
    webhook=webhook.webhook,
    address=address.address,
    payment=payment.payment_types,
    commands=commands.commands,
    loyalty=loyalty.loyalty,
)
