# Generated by Django 5.0.6 on 2024-12-21 15:44

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    operations = [
        migrations.CreateModel(
            name="Branch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("name", models.CharField(max_length=255)),
                ("address", models.CharField(blank=True, max_length=255, null=True)),
                ("latitude", models.FloatField(blank=True, null=True)),
                ("longitude", models.FloatField(blank=True, null=True)),
                ("opens_at", models.TimeField(blank=True, null=True)),
                ("closes_at", models.TimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "Branch",
                "verbose_name_plural": "Branches",
                "db_table": "hr_branch",
            },
        ),
        migrations.CreateModel(
            name="JobApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("full_name", models.CharField(max_length=255)),
                ("phone_number", models.CharField(max_length=20)),
                ("job_position", models.CharField(max_length=100)),
                ("birth_date", models.DateField()),
                ("address", models.TextField()),
                ("education", models.CharField(max_length=50)),
                ("language_uz", models.TextField()),
                ("language_ru", models.TextField()),
                ("language_en", models.TextField()),
                ("language_kk", models.TextField()),
                ("previous_experience", models.TextField(blank=True, null=True)),
                ("health_issues", models.BooleanField(default=False)),
                ("photo", models.ImageField(upload_to="applicant_photos/")),
                ("gender", models.CharField(max_length=10)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("APPROVED", "approved"),
                            ("REJECTED", "rejected"),
                            ("PENDING", "pending"),
                            ("IN_REVIEW", "in_review"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
            ],
            options={
                "db_table": "hr_job_application",
                "ordering": ["-created_at", "-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="User",
            fields=[
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("username", models.CharField(max_length=200, null=True)),
                ("first_name", models.CharField(max_length=200, null=True)),
                ("last_name", models.CharField(blank=True, max_length=200, null=True)),
                (
                    "language",
                    models.CharField(
                        choices=[("uz", "Uzbek"), ("ru", "Russian")],
                        default="uz",
                        max_length=2,
                    ),
                ),
            ],
            options={
                "verbose_name": "HR User",
                "verbose_name_plural": "HR Users",
                "db_table": "hr_users",
            },
        ),
        migrations.CreateModel(
            name="JobPosition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "organization",
                    models.ForeignKey(
                        default=2,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organization.organization",
                    ),
                ),
            ],
            options={
                "db_table": "hr_job_position",
            },
        ),
    ]
