"""
the delivery update event helpers
"""
from apps.iiko.helpers.common import CommonEventHelper
from apps.iiko.serializers.updates.shift import PersonalShiftUpdateEvent

from apps.courier.models.courier import Courier
from apps.courier.models.shift import CourierShifts
from apps.organization.models.organtzation import Organization


class PersonalShiftUpdateEventHelper(CommonEventHelper):
    """
    The delivery update event helper
    """
    @classmethod
    def process_update_event(cls, update_event: PersonalShiftUpdateEvent):
        """
        Process delivery update event
        """
        for event in update_event.event_body:
            organization_external_id = event.organizationId
            couner_external_id = event.eventInfo.id

            courier = Courier.get_by_external_id(couner_external_id)
            organization = Organization.get_by_external_id(organization_external_id)

            if event.eventInfo.opened is True:
                CourierShifts.open_shift(
                    courier=courier,
                    organization=organization,
                )

            elif event.eventInfo.opened is False:
                shift = CourierShifts.objects.filter(
                    courier=courier,
                    organization=organization,
                    is_active=True,
                ).first()
                if shift:
                    shift.close_shift()
                    shift.courier.mark_as_not_is_shift_active()
