"""
the enumeration of organizations
"""
from enum import Enum


class OrganizationIDSEnum(str, Enum):
    """
    The enumeration of role.
    """
    NIKITA = "f1b3b3bc-8d64-4c37-b6ab-2f2c898f68b0"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        Returns a list of tuples containing the enum values and their labels.
        """
        return [(member.value, member.value.replace('_', ' ').capitalize()) for member in cls]
