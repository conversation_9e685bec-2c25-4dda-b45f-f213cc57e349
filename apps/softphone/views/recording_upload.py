"""
Call recording upload functionality for Softphone.Pro integration
"""
import os
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormParser
from django.core.files.storage import default_storage
from django.conf import settings

from apps.core.views import ServiceBaseView
from apps.softphone.models.call_log import CallLog


class RecordingUploadAPIView(APIView, ServiceBaseView):
    """
    API view for uploading call recordings from Softphone.Pro
    """
    parser_classes = [<PERSON>Part<PERSON>ars<PERSON>, FormParser]
    
    def post(self, request):
        """
        Upload call recording file
        Expected form data:
        - file: Recording file (WAV, MP3, etc.)
        - external_id: External call ID from Softphone.Pro
        - caller_id: Phone number of caller
        - called_number: Phone number that was called
        - duration: Call duration in seconds
        - start_time: Call start time
        """
        try:
            # Get uploaded file
            recording_file = request.FILES.get('file')
            if not recording_file:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message="Recording file is required",
                    status_code=400
                )
            
            # Get call metadata
            external_id = request.data.get('external_id')
            caller_id = request.data.get('caller_id', '').replace('+', '')
            called_number = request.data.get('called_number', '').replace('+', '')
            duration = int(request.data.get('duration', 0))
            start_time = request.data.get('start_time')
            
            # Validate file type
            allowed_extensions = ['.wav', '.mp3', '.m4a', '.ogg']
            file_extension = os.path.splitext(recording_file.name)[1].lower()
            
            if file_extension not in allowed_extensions:
                raise self.call_service_exception(
                    error_type="bad_request",
                    message=f"Invalid file type. Allowed: {', '.join(allowed_extensions)}",
                    status_code=400
                )
            
            # Generate unique filename
            timestamp = start_time.replace(':', '-').replace(' ', '_') if start_time else 'unknown'
            filename = f"call_recording_{caller_id}_{called_number}_{timestamp}{file_extension}"
            
            # Save file
            file_path = f"call_recordings/{filename}"
            saved_path = default_storage.save(file_path, recording_file)
            
            # Try to find existing call log by external_id
            call_log = None
            if external_id:
                try:
                    call_log = CallLog.objects.get(external_id=external_id)
                    call_log.recording_file = saved_path
                    call_log.save()
                except CallLog.DoesNotExist:
                    pass
            
            # If no existing call log found, create a new one
            if not call_log:
                from django.utils.dateparse import parse_datetime
                
                call_log = CallLog.objects.create(
                    caller_id=caller_id,
                    called_number=called_number,
                    external_id=external_id,
                    duration=duration,
                    start_time=parse_datetime(start_time) if start_time else None,
                    recording_file=saved_path,
                    status='answered',  # Assume answered if recording exists
                    direction='inbound'  # Default direction
                )
            
            # Generate file URL
            if hasattr(settings, 'MEDIA_URL') and settings.MEDIA_URL:
                file_url = request.build_absolute_uri(settings.MEDIA_URL + saved_path)
            else:
                file_url = request.build_absolute_uri('/media/' + saved_path)
            
            return Response({
                "success": True,
                "call_log_id": call_log.id,
                "file_path": saved_path,
                "file_url": file_url,
                "file_size": recording_file.size,
                "duration": duration,
                "message": "Recording uploaded successfully"
            }, status=status.HTTP_201_CREATED)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to upload recording: {str(exc)}",
                status_code=500,
                notify_admin=True
            )


class RecordingDownloadAPIView(APIView, ServiceBaseView):
    """
    API view for downloading call recordings
    """
    
    def get(self, request, call_log_id):
        """
        Download call recording by call log ID
        """
        try:
            call_log = CallLog.objects.get(id=call_log_id)
            
            if not call_log.recording_file:
                raise self.call_service_exception(
                    error_type="not_found",
                    message="No recording file found for this call",
                    status_code=404
                )
            
            # Check if file exists
            if not default_storage.exists(call_log.recording_file.name):
                raise self.call_service_exception(
                    error_type="not_found",
                    message="Recording file not found on storage",
                    status_code=404
                )
            
            # Generate download URL
            if hasattr(settings, 'MEDIA_URL') and settings.MEDIA_URL:
                download_url = request.build_absolute_uri(
                    settings.MEDIA_URL + call_log.recording_file.name
                )
            else:
                download_url = request.build_absolute_uri(
                    '/media/' + call_log.recording_file.name
                )
            
            return Response({
                "call_log_id": call_log.id,
                "caller_id": call_log.caller_id,
                "called_number": call_log.called_number,
                "duration": call_log.duration,
                "formatted_duration": call_log.formatted_duration,
                "start_time": call_log.start_time.isoformat() if call_log.start_time else None,
                "download_url": download_url,
                "file_size": call_log.recording_file.size if call_log.recording_file else 0
            }, status=status.HTTP_200_OK)
            
        except CallLog.DoesNotExist:
            raise self.call_service_exception(
                error_type="not_found",
                message="Call log not found",
                status_code=404
            )
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to get recording download info: {str(exc)}",
                status_code=500,
                notify_admin=True
            )


class RecordingListAPIView(APIView, ServiceBaseView):
    """
    API view for listing call recordings
    """
    
    def get(self, request):
        """
        List call recordings with optional filters
        Expected parameters:
        - phone: Filter by phone number
        - date_from: Filter recordings from this date
        - date_to: Filter recordings to this date
        - has_recording: Filter calls that have recordings (true/false)
        - limit: Number of results (default: 20)
        - offset: Pagination offset (default: 0)
        """
        try:
            from django.utils.dateparse import parse_datetime
            from django.db.models import Q
            
            # Get filter parameters
            phone = request.query_params.get('phone')
            date_from = request.query_params.get('date_from')
            date_to = request.query_params.get('date_to')
            has_recording = request.query_params.get('has_recording')
            limit = int(request.query_params.get('limit', 20))
            offset = int(request.query_params.get('offset', 0))
            
            # Build queryset
            queryset = CallLog.objects.all().select_related('user', 'operator')
            
            if phone:
                clean_phone = phone.replace('+', '')
                queryset = queryset.filter(
                    Q(caller_id=clean_phone) | Q(called_number=clean_phone)
                )
            
            if date_from:
                queryset = queryset.filter(start_time__gte=parse_datetime(date_from))
            
            if date_to:
                queryset = queryset.filter(start_time__lte=parse_datetime(date_to))
            
            if has_recording == 'true':
                queryset = queryset.exclude(recording_file='')
            elif has_recording == 'false':
                queryset = queryset.filter(recording_file='')
            
            # Apply pagination
            total_count = queryset.count()
            recordings = queryset.order_by('-start_time')[offset:offset + limit]
            
            # Prepare results
            results = []
            for call_log in recordings:
                recording_data = {
                    "id": call_log.id,
                    "caller_id": call_log.caller_id,
                    "called_number": call_log.called_number,
                    "direction": call_log.direction,
                    "status": call_log.status,
                    "duration": call_log.duration,
                    "formatted_duration": call_log.formatted_duration,
                    "start_time": call_log.start_time.isoformat() if call_log.start_time else None,
                    "has_recording": bool(call_log.recording_file),
                    "recording_url": None,
                    "customer_name": call_log.user.name if call_log.user else None,
                    "operator_name": call_log.operator.name if call_log.operator else None
                }
                
                # Add recording URL if file exists
                if call_log.recording_file:
                    if hasattr(settings, 'MEDIA_URL') and settings.MEDIA_URL:
                        recording_data["recording_url"] = request.build_absolute_uri(
                            settings.MEDIA_URL + call_log.recording_file.name
                        )
                    else:
                        recording_data["recording_url"] = request.build_absolute_uri(
                            '/media/' + call_log.recording_file.name
                        )
                
                results.append(recording_data)
            
            return Response({
                "total_count": total_count,
                "limit": limit,
                "offset": offset,
                "recordings": results
            }, status=status.HTTP_200_OK)
            
        except Exception as exc:
            raise self.call_service_exception(
                error_type="internal_error",
                message=f"Failed to list recordings: {str(exc)}",
                status_code=500,
                notify_admin=True
            )
