"""
the delivery agent view is responsible for creating the delivery agent
"""
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework import filters

from django_filters.rest_framework import DjangoFilterBackend

from apps.courier.filters.courier import CourierFilter
from apps.core.views import ServiceBaseView
from apps.user.enum.role import UserRoleEnums
from apps.user.helper.agent import agent_helper
from apps.courier.models.courier import Courier
from apps.core.exceptions import ServiceAPIException
from apps.operations.permissions.dispatcher import IsAdminOrDispatcherOrManager
from apps.courier.serializers import courier as courier_serializer
from apps.core.throttling import OneRequestPerFiveSecondsThrottle


class CourierViewSet(viewsets.ModelViewSet, ServiceBaseView):
    """
    The delivery_agent_id viewset
    """
    filterset_class = CourierFilter
    queryset = Courier.objects.all()
    serializer_class = courier_serializer.CourierSerializer
    search_fields = ['name', 'phone', 'passport']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    throttle_classes = [OneRequestPerFiveSecondsThrottle]

    def get_permissions(self):
        """
        Set custom permissions based on action
        """
        if self.action == 'list':
            permission_classes = [IsAdminOrDispatcherOrManager]

        elif self.action == 'retrieve':
            permission_classes = [IsAdminOrDispatcherOrManager]

        else:
            permission_classes = [IsAdminOrDispatcherOrManager]

        return [permission() for permission in permission_classes]

    def retrieve(self, request, *args, **kwargs):
        """
        Restrict list view to admin users only. Normal users can only see their own data.
        """
        self.serializer_class = courier_serializer.CourierModelSerializer
        return super().retrieve(request, *args, **kwargs)

    def get_queryset(self):
        """
        Restrict list view to admin users only. Normal users can only see their own data.
        """
        user = self.request.user
        if user.role in [
            UserRoleEnums.DISPATCHER.value,
            UserRoleEnums.MANAGERS.value,
        ]:
            return Courier.objects.filter(
                organization=user.organization,
            )

        elif user.role == UserRoleEnums.ADMIN.value:
            return Courier.objects.all()

        courier = Courier.objects.filter(id=user.id)

        return courier

    def list(self, request, *args, **kwargs):
        """
        List all delivery agent or a specific delivery agent based on the user's role.
        """
        self.serializer_class = courier_serializer.CourierModelSerializer
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """
        Create a new delivery agent instance.
        """
        self.serializer_class = courier_serializer.CourierSerializerByExternalID
        try:
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            external_id = serializer.validated_data.get("id")

            if agent_helper.check_external_id_exists(external_id=external_id):
                raise ServiceAPIException(
                    error_type="bad_request",
                    message=f"External ID already exists - {external_id}",
                )

            courier = agent_helper.create_agent_with_external_id(
                serializer.validated_data,
            )

            response_data = {
                "id": courier.id,
                "role": courier.role,
                "name": courier.name,
                "phone": courier.phone,
                "passport": courier.passport,
                "address": courier.address,
                "external_id": courier.external_id,
            }
            return Response(response_data, status=status.HTTP_201_CREATED)

        except ServiceAPIException as exc:
            raise exc

        except Exception as exc:
            msg = f"delivery_agent_id creation failed exc: {exc}"
            raise ServiceAPIException(
                error_type="internal error",
                message=msg
            ) from exc

    def update(self, request, *args, **kwargs):
        """
        Update a courier's name, passport, and address only.
        """
        instance = self.get_object()

        try:
            serializer = courier_serializer.CourierUpdateSerializer(
                instance=instance,
                data=request.data,
                partial=True
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()

        except Exception as exc:
            msg = f"Courier update failed: {exc}"
            raise self.call_service_exception(
                error_type="internal_error",
                message=msg,
                status_code=500,
                notify_admin=True,
            )

        return Response()
