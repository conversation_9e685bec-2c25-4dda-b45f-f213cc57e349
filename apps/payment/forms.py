"""
Provider Credentials Form Module

This module defines the form for managing provider credentials within the
Django admin interface. The form includes fields for identification and key,
which are displayed using custom password input widgets for enhanced security.

The ProviderCredentialsForm class extends Django's ModelForm to provide a
customized form for the ProviderCredentials model, allowing for easy
integration and management of provider credentials in the admin interface.
"""

from django import forms

from apps.core.admin.modeladmin import PasswordInputForm
from apps.payment.models.credentials import ProviderCredentials


class ProviderCredentialsForm(forms.ModelForm):
    """
    Provider Credentials Form

    This form is used to manage provider credentials, with password fields
    displayed using custom password input widgets for enhanced security.
    """
    class Meta:
        """
        Meta Information

        This inner Meta class defines the model associated with the form,
        the fields to be included, and the custom widgets to be used for
        specific fields.
        """
        model = ProviderCredentials
        fields = '__all__'

        widgets = {
            'identification': PasswordInputForm(),
            'key': PasswordInputForm(),
        }
