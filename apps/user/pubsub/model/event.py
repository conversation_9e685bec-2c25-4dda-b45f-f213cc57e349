"""
the event model for pubsub
"""
import typing

from pydantic import BaseModel, Field


class CreateClientEventModel(BaseModel):
    """
    pydantic base model for User
    """
    chat_id: int
    first_name: typing.Optional[str]
    username: typing.Optional[str]
    last_name: typing.Optional[str]
    phone: typing.Optional[str] = Field(default=None, max_length=16)
    latitude: typing.Optional[float] = Field(default=None)
    longitude: typing.Optional[float] = Field(default=None)
