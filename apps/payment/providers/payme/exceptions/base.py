"""
init payme base exception
"""
from rest_framework.exceptions import APIException


class BasePaymeException(APIException):
    """
    BasePaymeException it's APIException.
    """
    status_code = 200
    error_code = None
    message = None

    # pylint: disable=super-init-not-called
    def __init__(self, error_message: str = None):
        detail: dict = {
            "error": {
                "code": self.error_code,
                "message": self.message,
                "data": error_message
            }
        }
        self.detail = detail
