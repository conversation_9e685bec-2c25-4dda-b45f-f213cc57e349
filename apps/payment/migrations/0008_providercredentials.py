# Generated by Django 5.0.6 on 2024-09-30 19:23

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("organization", "0004_alter_organization_created_at_and_more"),
        ("payment", "0007_alter_paymenttransaction_cancel_reason_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="ProviderCredentials",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                (
                    "payment_method",
                    models.CharField(
                        choices=[
                            ("Click", "Click"),
                            ("Payme", "Payme"),
                            ("Terminal", "Terminal"),
                            ("Cash", "Cash"),
                        ],
                        db_index=True,
                        max_length=20,
                        verbose_name="Payment Method",
                    ),
                ),
                (
                    "identification",
                    models.Char<PERSON>ield(max_length=255, verbose_name="Provider ID"),
                ),
                ("key", models.Char<PERSON>ield(max_length=255, verbose_name="Provider Key")),
                ("url", models.URLField(verbose_name="Provider URL")),
                (
                    "minimal_amount",
                    models.IntegerField(
                        default=0,
                        help_text="The minimal amount for this provider to accept payments",
                        verbose_name="Minimal Payment Amount",
                    ),
                ),
                (
                    "transaction_key",
                    models.CharField(
                        help_text="The transaction key for this provider",
                        max_length=50,
                        verbose_name="Transaction Key",
                    ),
                ),
                (
                    "organization",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="organization.organization",
                    ),
                ),
            ],
            options={
                "verbose_name": "Provider Credentials",
                "verbose_name_plural": "Provider Credentials",
                "db_table": "provider_credentials",
                "ordering": ["-created_at"],
            },
        ),
    ]
