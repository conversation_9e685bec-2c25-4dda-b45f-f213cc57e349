"""
the user permission
"""
from rest_framework import permissions


class IsClientRole(permissions.BasePermission):
    """
    Custom permission to only allow owners or admin users to edit objects.
    """
    def has_permission(self, request, view):
        is_operator = all([
            request.user,
            request.user.is_authenticated,
            request.user.role == "client"
        ])
        return is_operator
