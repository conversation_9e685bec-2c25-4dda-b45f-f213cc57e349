"""
the system parameters model manager
"""
import json

from django.db import models
from django.core.cache import cache

from apps.iiko.typing import system as system_typing


class SystemParameterManager(models.Manager):
    """
    The sys params manager
    """
    def get_sys_params(self, key) -> system_typing.SystemParameter:
        """
        Get system parameter value from cache or database.
        """
        cache_key = f'sys_param_{key}'
        cached_value = cache.get(cache_key)

        if cached_value is None:
            try:
                system_param = self.get(name=key)
                cached_value = json.dumps({
                    "name": system_param.name,
                    "value": system_param.value,
                    "is_enabled": system_param.is_enabled,
                    "description": system_param.description,
                })
                cache.set(cache_key, cached_value, timeout=60)
            except self.model.DoesNotExist:
                cached_value = None

        if cached_value:
            return system_typing.SystemParameter(**json.loads(cached_value))

        return None
