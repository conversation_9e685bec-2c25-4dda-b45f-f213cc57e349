"""
the hr bot enumurations
"""
from enum import Enum


class GenderEnum(str, Enum):
    """
    the enumeration of hr bot applicant gender
    """
    MALE = "male"
    FEMALE = "female"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        """
        choices
        """
        return [(tag.name, tag.value) for tag in cls]


class StatusEnum(str, Enum):
    """
    the enumeration for job application status
    """
    APPROVED = "approved"
    REJECTED = "rejected"
    PENDING = "pending"
    IN_REVIEW = "in_review"

    def __str__(self):
        return str(self.value)

    @classmethod
    def choices(cls):
        return [(status.name, status.value) for status in cls]
