# Generated by Django 5.0.6 on 2024-09-26 21:07

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("order", "0021_alter_bookmark_created_at_alter_bookmark_updated_at_and_more"),
        ("payment", "0002_alter_paymentmethod_created_at_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="PaymentTransaction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True, db_index=True)),
                ("updated_at", models.DateTimeField(auto_now=True, db_index=True)),
                ("transaction_id", models.CharField(max_length=50, unique=True)),
                ("amount", models.DecimalField(decimal_places=2, max_digits=10)),
                (
                    "state",
                    models.PositiveSmallIntegerField(
                        choices=[
                            ("Waiting", "Waiting"),
                            ("Processing", "Processing"),
                            ("Completed", "Completed"),
                            ("Refunded", "Refunded"),
                            ("Canceled", "Canceled"),
                            ("Expired", "Expired"),
                            ("Rejected", "Rejected"),
                            ("Pending", "Pending"),
                            ("Reversed", "Reversed"),
                            ("Reversed and Waiting", "Reversed and waiting"),
                        ],
                        default="Waiting",
                    ),
                ),
                (
                    "order",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_transactions",
                        to="order.order",
                    ),
                ),
                (
                    "payment_method",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="payment_transactions",
                        to="payment.paymentmethod",
                    ),
                ),
            ],
            options={
                "verbose_name": "Payment Transaction",
                "verbose_name_plural": "Payment Transactions",
                "db_table": "payment_transactions",
                "ordering": ["-created_at"],
                "unique_together": {("transaction_id", "payment_method", "order")},
            },
        ),
    ]
